-- `ruoyi-vue-pro`.system_application definition
CREATE TABLE `system_application` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '应用ID',
  `app_name` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '应用名称',
  `app_code` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '应用代码',
  `app_portal_url` varchar(500) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '应用入口地址',
  `force_invitation` bit(1) NOT NULL DEFAULT b'0' COMMENT '强制邀请',
  `app_description` text COLLATE utf8mb4_unicode_ci COMMENT '应用描述',
  `inviter_points` int NOT NULL DEFAULT '0' COMMENT '邀请积分',
  `newcomer_points` int DEFAULT '0' COMMENT '注册积分',
  `file_config_id` bigint DEFAULT '0' COMMENT '文件配置编号',
  `file_temp_duration` int DEFAULT '5' COMMENT '文件临时时间',
  `api_signature_secret` varchar(100) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT 'API签名密钥',
  `creator` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT '' COMMENT '创建者',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updater` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT '' COMMENT '更新者',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `deleted` bit(1) NOT NULL DEFAULT b'0' COMMENT '是否删除',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=15 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='系统应用表';


DROP TABLE IF EXISTS `system_environment`;
CREATE TABLE `system_environment`  (
                                       `id` int NOT NULL AUTO_INCREMENT COMMENT '参数主键',
                                       `config_key` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '参数键名',
                                       `config_value` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '参数键值',
                                       `creator` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT '' COMMENT '创建者',
                                       `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
                                       `updater` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT '' COMMENT '更新者',
                                       `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
                                       `deleted` bit(1) NOT NULL DEFAULT b'0' COMMENT '是否删除',
                                       `tenant_id` bigint NOT NULL DEFAULT 0 COMMENT '租户编号',
                                       PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 7 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci COMMENT = '租户配置表';



INSERT INTO system_menu
(id, name, permission, `type`, sort, parent_id, `path`, icon, component, component_name, status, visible, keep_alive, always_show, creator, create_time, updater, update_time, deleted)
VALUES(2953, '租户配置管理', '', 2, 0, 1, 'environment', '', 'system/environment/index', 'Environment', 0, 1, 1, 1, '', '2025-02-24 19:37:15', '', '2025-02-24 19:37:15', 0);
INSERT INTO system_menu
(id, name, permission, `type`, sort, parent_id, `path`, icon, component, component_name, status, visible, keep_alive, always_show, creator, create_time, updater, update_time, deleted)
VALUES(2954, '租户配置查询', 'system:environment:query', 3, 1, 2953, '', '', '', NULL, 0, 1, 1, 1, '', '2025-02-24 19:37:15', '', '2025-02-24 19:37:15', 0);
INSERT INTO system_menu
(id, name, permission, `type`, sort, parent_id, `path`, icon, component, component_name, status, visible, keep_alive, always_show, creator, create_time, updater, update_time, deleted)
VALUES(2955, '租户配置创建', 'system:environment:create', 3, 2, 2953, '', '', '', NULL, 0, 1, 1, 1, '', '2025-02-24 19:37:16', '', '2025-02-24 19:37:16', 0);
INSERT INTO system_menu
(id, name, permission, `type`, sort, parent_id, `path`, icon, component, component_name, status, visible, keep_alive, always_show, creator, create_time, updater, update_time, deleted)
VALUES(2956, '租户配置更新', 'system:environment:update', 3, 3, 2953, '', '', '', NULL, 0, 1, 1, 1, '', '2025-02-24 19:37:16', '', '2025-02-24 19:37:16', 0);
INSERT INTO system_menu
(id, name, permission, `type`, sort, parent_id, `path`, icon, component, component_name, status, visible, keep_alive, always_show, creator, create_time, updater, update_time, deleted)
VALUES(2957, '租户配置删除', 'system:environment:delete', 3, 4, 2953, '', '', '', NULL, 0, 1, 1, 1, '', '2025-02-24 19:37:16', '', '2025-02-24 19:37:16', 0);
INSERT INTO system_menu
(id, name, permission, `type`, sort, parent_id, `path`, icon, component, component_name, status, visible, keep_alive, always_show, creator, create_time, updater, update_time, deleted)
VALUES(2958, '租户配置导出', 'system:environment:export', 3, 5, 2953, '', '', '', NULL, 0, 1, 1, 1, '', '2025-02-24 19:37:16', '', '2025-02-24 19:37:16', 0);
INSERT INTO system_menu
(id, name, permission, `type`, sort, parent_id, `path`, icon, component, component_name, status, visible, keep_alive, always_show, creator, create_time, updater, update_time, deleted)
VALUES(2959, '系统应用管理', '', 2, 0, 1, 'application', '', 'system/application/index', 'Application', 0, 1, 1, 1, '', '2025-04-05 10:14:51', '', '2025-04-05 10:14:51', 0);
INSERT INTO system_menu
(id, name, permission, `type`, sort, parent_id, `path`, icon, component, component_name, status, visible, keep_alive, always_show, creator, create_time, updater, update_time, deleted)
VALUES(2960, '系统应用查询', 'system:application:query', 3, 1, 2959, '', '', '', NULL, 0, 1, 1, 1, '', '2025-04-05 10:14:52', '', '2025-04-05 10:14:52', 0);
INSERT INTO system_menu
(id, name, permission, `type`, sort, parent_id, `path`, icon, component, component_name, status, visible, keep_alive, always_show, creator, create_time, updater, update_time, deleted)
VALUES(2961, '系统应用创建', 'system:application:create', 3, 2, 2959, '', '', '', NULL, 0, 1, 1, 1, '', '2025-04-05 10:14:52', '', '2025-04-05 10:14:52', 0);
INSERT INTO system_menu
(id, name, permission, `type`, sort, parent_id, `path`, icon, component, component_name, status, visible, keep_alive, always_show, creator, create_time, updater, update_time, deleted)
VALUES(2962, '系统应用更新', 'system:application:update', 3, 3, 2959, '', '', '', NULL, 0, 1, 1, 1, '', '2025-04-05 10:14:52', '', '2025-04-05 10:14:52', 0);
INSERT INTO system_menu
(id, name, permission, `type`, sort, parent_id, `path`, icon, component, component_name, status, visible, keep_alive, always_show, creator, create_time, updater, update_time, deleted)
VALUES(2963, '系统应用删除', 'system:application:delete', 3, 4, 2959, '', '', '', NULL, 0, 1, 1, 1, '', '2025-04-05 10:14:52', '', '2025-04-05 10:14:52', 0);
INSERT INTO system_menu
(id, name, permission, `type`, sort, parent_id, `path`, icon, component, component_name, status, visible, keep_alive, always_show, creator, create_time, updater, update_time, deleted)
VALUES(2964, '系统应用导出', 'system:application:export', 3, 5, 2959, '', '', '', NULL, 0, 1, 1, 1, '', '2025-04-05 10:14:52', '', '2025-04-05 10:14:52', 0);


-- 为member_user表添加email字段
ALTER TABLE `member_user` ADD COLUMN `email` varchar(255) DEFAULT NULL COMMENT '邮箱' AFTER `mobile`;

-- 为email字段添加索引
ALTER TABLE `member_user` ADD INDEX `idx_email` (`email`) COMMENT '邮箱索引';


-- 邮件验证码表
CREATE TABLE IF NOT EXISTS `system_mail_code` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '编号',
  `email` varchar(255) NOT NULL COMMENT '邮箱',
  `code` varchar(6) NOT NULL COMMENT '验证码',
  `scene` tinyint NOT NULL COMMENT '发送场景',
  `create_ip` varchar(15) NOT NULL COMMENT '创建 IP',
  `today_index` tinyint NOT NULL COMMENT '今日发送的第几条',
  `used` bit(1) NOT NULL COMMENT '是否使用',
  `used_time` datetime DEFAULT NULL COMMENT '使用时间',
  `used_ip` varchar(255) DEFAULT NULL COMMENT '使用 IP',
  `creator` varchar(64) DEFAULT '' COMMENT '创建者',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updater` varchar(64) DEFAULT '' COMMENT '更新者',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `deleted` bit(1) NOT NULL DEFAULT b'0' COMMENT '是否删除',
  `tenant_id` bigint NOT NULL DEFAULT 0 COMMENT '租户编号',
  PRIMARY KEY (`id`),
  INDEX `idx_email` (`email`) COMMENT '邮箱索引'
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='邮件验证码表';




-- 会员用户 - 邮箱登陆
INSERT INTO `system_mail_template` (`name`, `code`, `account_id`, `nickname`, `title`, `content`, `params`, `status`, `remark`, `creator`, `create_time`, `updater`, `update_time`, `deleted`, `tenant_id`) VALUES
('会员用户 - 邮箱登陆', 'user-email-login', 1, '芋道源码', '邮箱验证码', '您的验证码是：{code}，有效期为10分钟，请勿泄露给他人。', '[\"code\"]', 0, '会员用户邮箱登陆时使用', '', NOW(), '', NOW(), 0, 1);

-- 会员用户 - 修改邮箱
INSERT INTO `system_mail_template` (`name`, `code`, `account_id`, `nickname`, `title`, `content`, `params`, `status`, `remark`, `creator`, `create_time`, `updater`, `update_time`, `deleted`, `tenant_id`) VALUES
('会员用户 - 修改邮箱', 'user-update-email', 1, '芋道源码', '邮箱验证码', '您正在修改邮箱，验证码是：{code}，有效期为10分钟，请勿泄露给他人。', '[\"code\"]', 0, '会员用户修改邮箱时使用', '', NOW(), '', NOW(), 0, 1);

-- 会员用户 - 修改密码
INSERT INTO `system_mail_template` (`name`, `code`, `account_id`, `nickname`, `title`, `content`, `params`, `status`, `remark`, `creator`, `create_time`, `updater`, `update_time`, `deleted`, `tenant_id`) VALUES
('会员用户 - 修改密码', 'user-update-password', 1, '芋道源码', '邮箱验证码', '您正在修改密码，验证码是：{code}，有效期为10分钟，请勿泄露给他人。', '[\"code\"]', 0, '会员用户修改密码时使用', '', NOW(), '', NOW(), 0, 1);

-- 会员用户 - 忘记密码
INSERT INTO `system_mail_template` (`name`, `code`, `account_id`, `nickname`, `title`, `content`, `params`, `status`, `remark`, `creator`, `create_time`, `updater`, `update_time`, `deleted`, `tenant_id`) VALUES
('会员用户 - 忘记密码', 'user-reset-password', 1, '芋道源码', '邮箱验证码', '您正在重置密码，验证码是：{code}，有效期为10分钟，请勿泄露给他人。', '[\"code\"]', 0, '会员用户忘记密码时使用', '', NOW(), '', NOW(), 0, 1);

-- 后台用户 - 邮箱登录
INSERT INTO `system_mail_template` (`name`, `code`, `account_id`, `nickname`, `title`, `content`, `params`, `status`, `remark`, `creator`, `create_time`, `updater`, `update_time`, `deleted`, `tenant_id`) VALUES
('后台用户 - 邮箱登录', 'admin-email-login', 1, '芋道源码', '邮箱验证码', '您的管理后台登录验证码是：{code}，有效期为10分钟，请勿泄露给他人。', '[\"code\"]', 0, '后台用户邮箱登录时使用', '', NOW(), '', NOW(), 0, 1);

