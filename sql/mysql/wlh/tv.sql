
-- tv_category definition

CREATE TABLE `tv_category` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '分类编号',
  `name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '分类名称',
  `parent_id` bigint DEFAULT '0' COMMENT '父分类编号',
  `sort` int DEFAULT '0' COMMENT '显示顺序',
  `status` tinyint NOT NULL DEFAULT '1' COMMENT '状态（0：禁用 1：启用）',
  `creator` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT '' COMMENT '创建者',
  `updater` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT '' COMMENT '更新者',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `deleted` bit(1) NOT NULL DEFAULT b'0' COMMENT '是否删除',
  `tenant_id` bigint NOT NULL DEFAULT '0' COMMENT '租户编号',
  PRIMARY KEY (`id`),
  KEY `idx_parent_id` (`parent_id`),
  KEY `idx_sort` (`sort`)
) ENGINE=InnoDB AUTO_INCREMENT=7 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='视频分类表';


-- tv_comment definition

CREATE TABLE `tv_comment` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '评论编号',
  `user_id` bigint NOT NULL COMMENT '用户编号',
  `video_id` bigint NOT NULL COMMENT '视频编号',
  `content` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '评论内容',
  `parent_id` bigint DEFAULT '0' COMMENT '父评论编号',
  `like_count` int NOT NULL DEFAULT '0' COMMENT '点赞数',
  `status` tinyint NOT NULL DEFAULT '0' COMMENT '状态（0：待审核 1：通过 2：拒绝）',
  `creator` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT '' COMMENT '创建者',
  `updater` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT '' COMMENT '更新者',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `deleted` bit(1) NOT NULL DEFAULT b'0' COMMENT '是否删除',
  `tenant_id` bigint NOT NULL DEFAULT '0' COMMENT '租户编号',
  PRIMARY KEY (`id`),
  KEY `idx_video_id` (`video_id`),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_parent_id` (`parent_id`),
  KEY `idx_create_time` (`create_time`)
) ENGINE=InnoDB AUTO_INCREMENT=4 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='评论表';


-- tv_favorite definition

CREATE TABLE `tv_favorite` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '收藏编号',
  `user_id` bigint NOT NULL COMMENT '用户编号',
  `video_id` bigint NOT NULL COMMENT '视频编号',
  `creator` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT '' COMMENT '创建者',
  `updater` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT '' COMMENT '更新者',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `deleted` bit(1) NOT NULL DEFAULT b'0' COMMENT '是否删除',
  `tenant_id` bigint NOT NULL DEFAULT '0' COMMENT '租户编号',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_user_video` (`user_id`,`video_id`),
  KEY `idx_video_id` (`video_id`),
  KEY `idx_create_time` (`create_time`)
) ENGINE=InnoDB AUTO_INCREMENT=12 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='收藏表';


-- tv_history definition

CREATE TABLE `tv_history` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '历史记录编号',
  `user_id` bigint NOT NULL COMMENT '用户编号',
  `video_id` bigint NOT NULL COMMENT '视频编号',
  `progress` int NOT NULL DEFAULT '0' COMMENT '播放进度（秒）',
  `finished` tinyint NOT NULL DEFAULT '0' COMMENT '是否看完（0：未看完 1：已看完）',
  `creator` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT '' COMMENT '创建者',
  `updater` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT '' COMMENT '更新者',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `deleted` bit(1) NOT NULL DEFAULT b'0' COMMENT '是否删除',
  `tenant_id` bigint NOT NULL DEFAULT '0' COMMENT '租户编号',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_user_video` (`user_id`,`video_id`),
  KEY `idx_video_id` (`video_id`),
  KEY `idx_create_time` (`create_time`)
) ENGINE=InnoDB AUTO_INCREMENT=5 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='播放历史表';


-- tv_like definition

CREATE TABLE `tv_like` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '点赞编号',
  `user_id` bigint NOT NULL COMMENT '用户编号',
  `video_id` bigint NOT NULL COMMENT '视频编号',
  `creator` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT '' COMMENT '创建者',
  `updater` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT '' COMMENT '更新者',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `deleted` bit(1) NOT NULL DEFAULT b'0' COMMENT '是否删除',
  `tenant_id` bigint NOT NULL DEFAULT '0' COMMENT '租户编号',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_user_video` (`user_id`,`video_id`),
  KEY `idx_video_id` (`video_id`),
  KEY `idx_create_time` (`create_time`)
) ENGINE=InnoDB AUTO_INCREMENT=12 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='点赞表';


-- tv_play_history definition

CREATE TABLE `tv_play_history` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '历史记录编号',
  `user_id` bigint NOT NULL COMMENT '用户编号',
  `video_id` bigint NOT NULL COMMENT '视频编号',
  `progress` int NOT NULL DEFAULT '0' COMMENT '播放进度（秒）',
  `finished` tinyint NOT NULL DEFAULT '0' COMMENT '是否看完（0：未看完 1：已看完）',
  `creator` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT '' COMMENT '创建者',
  `updater` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT '' COMMENT '更新者',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `deleted` bit(1) NOT NULL DEFAULT b'0' COMMENT '是否删除',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_user_video` (`user_id`,`video_id`),
  KEY `idx_video_id` (`video_id`),
  KEY `idx_create_time` (`create_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='播放历史表';


-- tv_user_video_unlock definition

CREATE TABLE `tv_user_video_unlock` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '编号',
  `user_id` bigint NOT NULL COMMENT '用户编号',
  `video_id` bigint NOT NULL COMMENT '视频编号',
  `points` int NOT NULL COMMENT '消费积分数',
  `unlock_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '解锁时间',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `creator` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT '' COMMENT '创建者',
  `updater` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT '' COMMENT '更新者',
  `deleted` bit(1) NOT NULL DEFAULT b'0' COMMENT '是否删除',
  `tenant_id` bigint NOT NULL DEFAULT '0' COMMENT '租户编号',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_user_video` (`user_id`,`video_id`,`deleted`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='用户视频解锁记录表';


-- tv_video definition

CREATE TABLE `tv_video` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '编号',
  `config_id` bigint DEFAULT NULL COMMENT '配置编号',
  `title` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '标题',
  `description` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '描述',
  `cover_url` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '封面图片',
  `type` tinyint NOT NULL DEFAULT '0' COMMENT '视频类型：0-单个视频，1-多个视频，2-图文混合',
  `video_url` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '主视频URL',
  `duration` int DEFAULT NULL COMMENT '视频时长（秒）',
  `play_count` int NOT NULL DEFAULT '0' COMMENT '播放次数',
  `like_count` int NOT NULL DEFAULT '0' COMMENT '点赞次数',
  `favorite_count` int NOT NULL DEFAULT '0' COMMENT '收藏次数',
  `comment_count` int NOT NULL DEFAULT '0' COMMENT '评论次数',
  `status` tinyint NOT NULL DEFAULT '0' COMMENT '状态：0-草稿，1-发布，2-下架',
  `category_id` bigint NOT NULL COMMENT '分类编号',
  `tags` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '标签列表，JSON 数组',
  `sort` int DEFAULT '0' COMMENT '排序',
  `creator` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT '' COMMENT '创建者',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updater` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT '' COMMENT '更新者',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `deleted` bit(1) NOT NULL DEFAULT b'0' COMMENT '是否删除',
  `tenant_id` bigint NOT NULL DEFAULT '0' COMMENT '租户编号',
  `is_paid` tinyint NOT NULL DEFAULT '0' COMMENT '是否付费视频：0-免费，1-付费',
  `price` decimal(10,2) NOT NULL DEFAULT '0.00' COMMENT '视频价格',
  `discount_price` decimal(10,2) DEFAULT NULL COMMENT '折扣价格',
  `discount_start_time` datetime DEFAULT NULL COMMENT '折扣开始时间',
  `discount_end_time` datetime DEFAULT NULL COMMENT '折扣结束时间',
  PRIMARY KEY (`id`),
  KEY `idx_category_id` (`category_id`),
  KEY `idx_status` (`status`),
  KEY `idx_type` (`type`)
) ENGINE=InnoDB AUTO_INCREMENT=96 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='视频表';


-- tv_video_media definition

CREATE TABLE `tv_video_media` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '编号',
  `video_id` bigint NOT NULL COMMENT '视频编号',
  `type` tinyint NOT NULL COMMENT '媒体类型：0-图片，1-视频',
  `url` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '媒体URL',
  `thumbnail_url` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '缩略图URL（视频特有）',
  `duration` int DEFAULT NULL COMMENT '时长（秒，视频特有）',
  `sort` int DEFAULT '0' COMMENT '排序',
  `status` tinyint NOT NULL DEFAULT '1' COMMENT '状态：0-禁用，1-启用',
  `creator` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT '' COMMENT '创建者',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updater` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT '' COMMENT '更新者',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `deleted` bit(1) NOT NULL DEFAULT b'0' COMMENT '是否删除',
  `tenant_id` bigint NOT NULL DEFAULT '0' COMMENT '租户编号',
  PRIMARY KEY (`id`),
  KEY `idx_video_id` (`video_id`)
) ENGINE=InnoDB AUTO_INCREMENT=7 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='视频媒体资源表';




-- 特权表
CREATE TABLE `tv_privilege` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '编号',
  `name` varchar(50) NOT NULL COMMENT '特权名称',
  `description` varchar(200) DEFAULT NULL COMMENT '特权描述',
  `price` decimal(10,2) NOT NULL COMMENT '价格',
  `duration` int NOT NULL COMMENT '有效期（天）',
  `status` tinyint NOT NULL DEFAULT '1' COMMENT '状态：0-禁用，1-启用',
  `creator` varchar(64) DEFAULT '' COMMENT '创建者',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updater` varchar(64) DEFAULT '' COMMENT '更新者',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `deleted` bit(1) NOT NULL DEFAULT b'0' COMMENT '是否删除',
  `tenant_id` bigint NOT NULL DEFAULT '0' COMMENT '租户编号',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='特权表';

-- 用户特权表
CREATE TABLE `tv_user_privilege` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '编号',
  `user_id` bigint NOT NULL COMMENT '用户编号',
  `privilege_id` bigint NOT NULL COMMENT '特权编号',
  `start_time` datetime NOT NULL COMMENT '开始时间',
  `end_time` datetime NOT NULL COMMENT '结束时间',
  `status` tinyint NOT NULL DEFAULT '1' COMMENT '状态：0-已过期，1-使用中',
  `creator` varchar(64) DEFAULT '' COMMENT '创建者',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updater` varchar(64) DEFAULT '' COMMENT '更新者',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `deleted` bit(1) NOT NULL DEFAULT b'0' COMMENT '是否删除',
  `tenant_id` bigint NOT NULL DEFAULT '0' COMMENT '租户编号',
  PRIMARY KEY (`id`),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_privilege_id` (`privilege_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='用户特权表';


-- 内容主体表
CREATE TABLE `tv_content` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '编号',
  `title` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '标题',
  `description` varchar(2000) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '描述',
  `cover_url` varchar(255) DEFAULT NULL COMMENT '视频封面图片',
  `type` tinyint NOT NULL DEFAULT '0' COMMENT '视频类型：0-单个视频，1-多个视频，2-图文混合,3-图文,4-合集',
  `play_count` int NOT NULL DEFAULT '0' COMMENT '播放次数',
  `like_count` int NOT NULL DEFAULT '0' COMMENT '点赞次数',
  `favorite_count` int NOT NULL DEFAULT '0' COMMENT '收藏次数',
  `comment_count` int NOT NULL DEFAULT '0' COMMENT '评论次数',
  `recommend_weight` int DEFAULT '0' COMMENT '推荐系统权重因子',
  `quality_score` int DEFAULT '0' COMMENT '内容质量评分（平台内部使用）',
  `status` tinyint NOT NULL DEFAULT '0' COMMENT '状态：0-草稿，1-发布，2-下架',
  `category_id` bigint NOT NULL COMMENT '分类编号',
  `publish_time` DATETIME DEFAULT NULL COMMENT '发布时间',
  `is_paid` tinyint NOT NULL DEFAULT '0' COMMENT '是否付费视频：0-免费，1-付费',
  `price` decimal(10,2) NOT NULL DEFAULT '0.00' COMMENT '视频价格',
  `discount_price` decimal(10,2) DEFAULT NULL COMMENT '折扣价格',
  `discount_start_time` datetime DEFAULT NULL COMMENT '折扣开始时间',
  `discount_end_time` datetime DEFAULT NULL COMMENT '折扣结束时间',
  `creator` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT '' COMMENT '创建者',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updater` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT '' COMMENT '更新者',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `deleted` bit(1) NOT NULL DEFAULT b'0' COMMENT '是否删除',
  `tenant_id` bigint NOT NULL DEFAULT '0' COMMENT '租户编号',
  PRIMARY KEY (`id`),
  KEY `idx_category_id` (`category_id`),
  KEY `idx_status` (`status`),
  KEY `idx_type` (`type`)
) ENGINE=InnoDB AUTO_INCREMENT=96 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='内容主体';


-- 单个视频内容表 type=0
CREATE TABLE `tv_content_video` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键',
  `content_id` bigint NOT NULL COMMENT '内容ID，关联tv_content.id',
  `video_url` varchar(255) NOT NULL COMMENT '视频地址',
  `duration` int DEFAULT NULL COMMENT '视频时长（秒）',
  `resolution` varchar(50) DEFAULT NULL COMMENT '分辨率（如1080p）',
  `creator` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT '' COMMENT '创建者',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updater` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT '' COMMENT '更新者',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `deleted` bit(1) NOT NULL DEFAULT b'0' COMMENT '是否删除',
  `tenant_id` bigint NOT NULL DEFAULT 0 COMMENT '租户编号',
  PRIMARY KEY (`id`),
  KEY `idx_content_id` (`content_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='单个视频内容表';

-- 图片和视频混合资源表 type=5
CREATE TABLE `tv_content_media` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键',
  `content_id` bigint NOT NULL COMMENT '内容ID，关联tv_content.id',
  `type` tinyint NOT NULL COMMENT '资源类型：0-图片，1-视频',
  `url` varchar(255) NOT NULL COMMENT '资源地址',
  `duration` int DEFAULT NULL COMMENT '视频时长（秒），图片为NULL',
  `sort` int DEFAULT 0 COMMENT '排序值，越小越靠前',
  `cover_url` varchar(255) DEFAULT NULL COMMENT '视频封面，图片为NULL',
  `is_preview` tinyint NOT NULL DEFAULT '0' COMMENT '是否试看内容：0-付费内容，1-试看内容',
  `preview_sort` int DEFAULT '0' COMMENT '试看排序值，越小越靠前',
  `creator` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT '' COMMENT '创建者',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updater` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT '' COMMENT '更新者',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `deleted` bit(1) NOT NULL DEFAULT b'0' COMMENT '是否删除',
  `tenant_id` bigint NOT NULL DEFAULT 0 COMMENT '租户编号',
  PRIMARY KEY (`id`),
  KEY `idx_content_id` (`content_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='图片和视频混合资源表';

-- 合集内容表 type=4
CREATE TABLE `tv_content_collection_item` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键',
  `content_id` bigint NOT NULL COMMENT '合集内容ID（tv_content.id，type=4）',
  `item_content_id` bigint NOT NULL COMMENT '子内容ID（tv_content.id，type≠4）',
  `sort` int DEFAULT 0 COMMENT '排序值，越小越靠前',
  `creator` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT '' COMMENT '创建者',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updater` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT '' COMMENT '更新者',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `deleted` bit(1) NOT NULL DEFAULT b'0' COMMENT '是否删除',
  `tenant_id` bigint NOT NULL DEFAULT 0 COMMENT '租户编号',
  PRIMARY KEY (`id`),
  KEY `idx_content_id` (`content_id`),
  KEY `idx_item_content_id` (`item_content_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='合集内容表';

-- 添加视频导入定时任务
INSERT INTO `infra_job` (
    `name`, `status`, `handler_name`, `handler_param`, `cron_expression`,
    `retry_count`, `retry_interval`, `monitor_timeout`, `creator`, `create_time`, `updater`, `update_time`, `deleted`
) VALUES (
    '视频导入任务', 1, 'videoImportJob', '', '0 0 2 * * ?',
    3, 60, 0, 'admin', NOW(), 'admin', NOW(), 0
);

-- 添加内容数据清理定时任务
INSERT INTO `infra_job` (
    `name`, `status`, `handler_name`, `handler_param`, `cron_expression`,
    `retry_count`, `retry_interval`, `monitor_timeout`, `creator`, `create_time`, `updater`, `update_time`, `deleted`
) VALUES (
    '内容数据清理任务', 1, 'contentDataCleanupJob', '', '0 0 3 * * ?',
    3, 60, 0, 'admin', NOW(), 'admin', NOW(), 0
);

-- 添加视频付费状态更新定时任务
INSERT INTO `infra_job` (
    `name`, `status`, `handler_name`, `handler_param`, `cron_expression`,
    `retry_count`, `retry_interval`, `monitor_timeout`, `creator`, `create_time`,
    `updater`, `update_time`, `deleted`
) VALUES (
    '视频付费状态更新任务', 1, 'videoPaidUpdateJob', '',
    '0 0 0 * * ?', -- 每天0点执行
    0, 0, 0,
    'admin', NOW(), 'admin', NOW(), 0
);




-- 菜单按钮情况：：
INSERT INTO system_menu (name, permission, `type`, sort, parent_id, `path`, icon, component, component_name, status, visible, keep_alive, always_show, creator, create_time, updater, update_time, deleted) VALUES('视频系统', '', 1, 1002, 0, '/tv', 'ep:video-camera', '', '', 0, 1, 1, 1, '1', now(), '1', now() , 0);
INSERT INTO system_menu (name, permission, `type`, sort, parent_id, `path`, icon, component, component_name, status, visible, keep_alive, always_show, creator, create_time, updater, update_time, deleted) VALUES('视频管理', '', 2, 0, 2972, 'video', '', 'tv/video/index', 'Video', 0, 1, 1, 1, '', now(), '', now(), 0);
INSERT INTO system_menu (name, permission, `type`, sort, parent_id, `path`, icon, component, component_name, status, visible, keep_alive, always_show, creator, create_time, updater, update_time, deleted) VALUES('视频查询', 'tv:video:query', 3, 1, 2973, '', '', '', NULL, 0, 1, 1, 1, '', now(), '', now(), 0);
INSERT INTO system_menu (name, permission, `type`, sort, parent_id, `path`, icon, component, component_name, status, visible, keep_alive, always_show, creator, create_time, updater, update_time, deleted) VALUES('视频创建', 'tv:video:create', 3, 2, 2973, '', '', '', NULL, 0, 1, 1, 1, '', now(), '', now(), 0);
INSERT INTO system_menu (name, permission, `type`, sort, parent_id, `path`, icon, component, component_name, status, visible, keep_alive, always_show, creator, create_time, updater, update_time, deleted) VALUES('视频更新', 'tv:video:update', 3, 3, 2973, '', '', '', NULL, 0, 1, 1, 1, '', now(), '', now(), 0);
INSERT INTO system_menu (name, permission, `type`, sort, parent_id, `path`, icon, component, component_name, status, visible, keep_alive, always_show, creator, create_time, updater, update_time, deleted) VALUES('视频删除', 'tv:video:delete', 3, 4, 2973, '', '', '', NULL, 0, 1, 1, 1, '', now(), '', now(), 0);
INSERT INTO system_menu (name, permission, `type`, sort, parent_id, `path`, icon, component, component_name, status, visible, keep_alive, always_show, creator, create_time, updater, update_time, deleted) VALUES('视频导出', 'tv:video:export', 3, 5, 2973, '', '', '', NULL, 0, 1, 1, 1, '', now(), '', now(), 0);


-- 菜单 SQL
INSERT INTO system_menu(
    name, permission, type, sort, parent_id,
    path, icon, component, status, component_name
)
VALUES (
    '视频分类管理', '', 2, 0, 2972,
    'category', '', 'tv/category/index', 0, 'Category'
);

-- 按钮父菜单ID
-- 暂时只支持 MySQL。如果你是 Oracle、PostgreSQL、SQLServer 的话，需要手动修改 @parentId 的部分的代码
SELECT @parentId := LAST_INSERT_ID();

-- 按钮 SQL
INSERT INTO system_menu(
    name, permission, type, sort, parent_id,
    path, icon, component, status
)
VALUES (
    '视频分类查询', 'tv:category:query', 3, 1, @parentId,
    '', '', '', 0
);
INSERT INTO system_menu(
    name, permission, type, sort, parent_id,
    path, icon, component, status
)
VALUES (
    '视频分类创建', 'tv:category:create', 3, 2, @parentId,
    '', '', '', 0
);
INSERT INTO system_menu(
    name, permission, type, sort, parent_id,
    path, icon, component, status
)
VALUES (
    '视频分类更新', 'tv:category:update', 3, 3, @parentId,
    '', '', '', 0
);
INSERT INTO system_menu(
    name, permission, type, sort, parent_id,
    path, icon, component, status
)
VALUES (
    '视频分类删除', 'tv:category:delete', 3, 4, @parentId,
    '', '', '', 0
);
INSERT INTO system_menu(
    name, permission, type, sort, parent_id,
    path, icon, component, status
)
VALUES (
    '视频分类导出', 'tv:category:export', 3, 5, @parentId,
    '', '', '', 0
);


-- 字典会员类型：
INSERT INTO system_dict_data
( sort, label, value, dict_type, status, color_type, css_class, remark, creator, create_time, updater, update_time, deleted)
VALUES( 901, '付费视频', '901', 'member_point_biz_type', 0, 'warning', '', '', '1', now(), '1', now(), 0);



-- ====================================
-- TV内容搜索功能 - 数据库索引优化SQL
-- ====================================

-- 1. 基础单字段索引
CREATE INDEX idx_tv_content_status ON tv_content(status);
CREATE INDEX idx_tv_content_category_id ON tv_content(category_id);
CREATE INDEX idx_tv_content_type ON tv_content(type);
CREATE INDEX idx_tv_content_is_paid ON tv_content(is_paid);

-- 2. 排序字段索引
CREATE INDEX idx_tv_content_create_time ON tv_content(create_time DESC);
CREATE INDEX idx_tv_content_publish_time ON tv_content(publish_time DESC);
CREATE INDEX idx_tv_content_play_count ON tv_content(play_count DESC);
CREATE INDEX idx_tv_content_like_count ON tv_content(like_count DESC);
CREATE INDEX idx_tv_content_favorite_count ON tv_content(favorite_count DESC);
CREATE INDEX idx_tv_content_price ON tv_content(price);
CREATE INDEX idx_tv_content_recommend_weight ON tv_content(recommend_weight DESC);

-- 3. 核心复合索引（最重要）
CREATE INDEX idx_tv_content_status_category_type ON tv_content(status, category_id, type, create_time DESC);
CREATE INDEX idx_tv_content_status_paid_price ON tv_content(status, is_paid, price);
CREATE INDEX idx_tv_content_status_publish_time ON tv_content(status, publish_time DESC);
CREATE INDEX idx_tv_content_status_play_count ON tv_content(status, play_count DESC);

-- 4. 范围查询优化索引
CREATE INDEX idx_tv_content_price_range ON tv_content(status, price, create_time DESC);
CREATE INDEX idx_tv_content_publish_range ON tv_content(status, publish_time, create_time DESC);
CREATE INDEX idx_tv_content_play_range ON tv_content(status, play_count DESC, create_time DESC);
CREATE INDEX idx_tv_content_like_range ON tv_content(status, like_count DESC, create_time DESC);
CREATE INDEX idx_tv_content_favorite_range ON tv_content(status, favorite_count DESC, create_time DESC);

-- 5. 全文搜索索引（关键词搜索）
CREATE FULLTEXT INDEX idx_tv_content_title_fulltext ON tv_content(title);
CREATE FULLTEXT INDEX idx_tv_content_description_fulltext ON tv_content(description);
CREATE FULLTEXT INDEX idx_tv_content_title_desc_fulltext ON tv_content(title, description);

-- 6. 覆盖索引（性能最优，包含常用查询字段）
CREATE INDEX idx_tv_content_list_cover ON tv_content(
    status,
    category_id,
    type,
    is_paid,
    create_time DESC,
    id,
    play_count,
    like_count,
    favorite_count,
    price
);

-- ====================================
-- 通用举报反馈表
-- ====================================

-- 通用举报反馈表
CREATE TABLE `tv_report` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键',
  `business_type` varchar(50) NOT NULL COMMENT '业务类型：content-内容举报，user-用户举报等',
  `business_id` bigint NOT NULL COMMENT '业务ID，如内容ID、用户ID等',
  `report_type` tinyint NOT NULL COMMENT '举报类型：1-色情低俗，2-违法违规，3-虚假信息，4-恶意营销，5-侵权盗版，6-其他',
  `report_reason` varchar(500) DEFAULT NULL COMMENT '举报原因描述',
  `report_images` text DEFAULT NULL COMMENT '举报截图，JSON数组格式',
  `reporter_id` bigint DEFAULT NULL COMMENT '举报人ID，匿名举报时为NULL',
  `reporter_ip` varchar(50) DEFAULT NULL COMMENT '举报人IP地址',
  `status` tinyint NOT NULL DEFAULT '0' COMMENT '处理状态：0-待处理，1-处理中，2-已处理，3-已驳回',
  `handle_result` varchar(500) DEFAULT NULL COMMENT '处理结果说明',
  `handle_user_id` bigint DEFAULT NULL COMMENT '处理人ID',
  `handle_time` datetime DEFAULT NULL COMMENT '处理时间',
  `creator` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT '' COMMENT '创建者',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updater` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT '' COMMENT '更新者',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `deleted` bit(1) NOT NULL DEFAULT b'0' COMMENT '是否删除',
  `tenant_id` bigint NOT NULL DEFAULT 0 COMMENT '租户编号',
  PRIMARY KEY (`id`),
  KEY `idx_business` (`business_type`, `business_id`),
  KEY `idx_reporter` (`reporter_id`),
  KEY `idx_status` (`status`),
  KEY `idx_create_time` (`create_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='通用举报反馈表';

-- ====================================
-- 任务系统相关表
-- ====================================

-- 任务定义表
CREATE TABLE `tv_task` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键',
  `name` varchar(100) NOT NULL COMMENT '任务名称',
  `description` varchar(500) DEFAULT NULL COMMENT '任务描述',
  `type` tinyint NOT NULL COMMENT '任务类型：1-分享任务，2-短视频任务',
  `reward_points` int NOT NULL DEFAULT '0' COMMENT '奖励积分',
  `daily_limit` int NOT NULL DEFAULT '1' COMMENT '每日完成次数限制',
  `total_limit` int DEFAULT NULL COMMENT '总完成次数限制，NULL表示无限制',
  `status` tinyint NOT NULL DEFAULT '1' COMMENT '任务状态：0-禁用，1-启用',
  `start_time` datetime DEFAULT NULL COMMENT '任务开始时间',
  `end_time` datetime DEFAULT NULL COMMENT '任务结束时间',
  `sort` int DEFAULT '0' COMMENT '排序值，越小越靠前',
  `creator` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT '' COMMENT '创建者',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updater` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT '' COMMENT '更新者',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `deleted` bit(1) NOT NULL DEFAULT b'0' COMMENT '是否删除',
  `tenant_id` bigint NOT NULL DEFAULT 0 COMMENT '租户编号',
  PRIMARY KEY (`id`),
  KEY `idx_type` (`type`),
  KEY `idx_status` (`status`),
  KEY `idx_sort` (`sort`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='任务定义表';

-- 用户任务记录表
CREATE TABLE `tv_user_task` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键',
  `user_id` bigint NOT NULL COMMENT '用户ID',
  `task_id` bigint NOT NULL COMMENT '任务ID',
  `completed_count` int NOT NULL DEFAULT '0' COMMENT '已完成次数',
  `last_completed_time` datetime DEFAULT NULL COMMENT '最后完成时间',
  `total_reward_points` int NOT NULL DEFAULT '0' COMMENT '总获得积分',
  `creator` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT '' COMMENT '创建者',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updater` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT '' COMMENT '更新者',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `deleted` bit(1) NOT NULL DEFAULT b'0' COMMENT '是否删除',
  `tenant_id` bigint NOT NULL DEFAULT 0 COMMENT '租户编号',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_user_task` (`user_id`, `task_id`),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_task_id` (`task_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='用户任务记录表';

-- 任务提交记录表
CREATE TABLE `tv_task_submission` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键',
  `user_id` bigint NOT NULL COMMENT '用户ID',
  `task_id` bigint NOT NULL COMMENT '任务ID',
  `screenshot_base64` longtext NOT NULL COMMENT '截图base64数据',
  `screenshot_hash` varchar(64) NOT NULL COMMENT '截图哈希值',
  `description` varchar(500) DEFAULT NULL COMMENT '提交说明',
  `status` tinyint NOT NULL DEFAULT '0' COMMENT '审核状态：0-待审核，1-审核通过，2-审核拒绝',
  `audit_result` varchar(500) DEFAULT NULL COMMENT '审核结果说明',
  `audit_user_id` bigint DEFAULT NULL COMMENT '审核人ID',
  `audit_time` datetime DEFAULT NULL COMMENT '审核时间',
  `reward_points` int DEFAULT NULL COMMENT '实际奖励积分',
  `creator` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT '' COMMENT '创建者',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updater` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT '' COMMENT '更新者',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `deleted` bit(1) NOT NULL DEFAULT b'0' COMMENT '是否删除',
  `tenant_id` bigint NOT NULL DEFAULT 0 COMMENT '租户编号',
  PRIMARY KEY (`id`),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_task_id` (`task_id`),
  KEY `idx_status` (`status`),
  KEY `idx_screenshot_hash` (`screenshot_hash`),
  KEY `idx_create_time` (`create_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='任务提交记录表';

-- ====================================
-- 任务系统相关表
-- ====================================

-- 任务定义表
CREATE TABLE `tv_task` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键',
  `name` varchar(100) NOT NULL COMMENT '任务名称',
  `description` varchar(500) DEFAULT NULL COMMENT '任务描述',
  `type` tinyint NOT NULL COMMENT '任务类型：1-分享任务，2-短视频任务',
  `reward_points` int NOT NULL DEFAULT '0' COMMENT '奖励积分',
  `daily_limit` int NOT NULL DEFAULT '1' COMMENT '每日完成次数限制',
  `total_limit` int DEFAULT NULL COMMENT '总完成次数限制，NULL表示无限制',
  `status` tinyint NOT NULL DEFAULT '1' COMMENT '任务状态：0-禁用，1-启用',
  `start_time` datetime DEFAULT NULL COMMENT '任务开始时间',
  `end_time` datetime DEFAULT NULL COMMENT '任务结束时间',
  `sort` int DEFAULT '0' COMMENT '排序值，越小越靠前',
  `creator` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT '' COMMENT '创建者',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updater` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT '' COMMENT '更新者',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `deleted` bit(1) NOT NULL DEFAULT b'0' COMMENT '是否删除',
  `tenant_id` bigint NOT NULL DEFAULT 0 COMMENT '租户编号',
  PRIMARY KEY (`id`),
  KEY `idx_type` (`type`),
  KEY `idx_status` (`status`),
  KEY `idx_sort` (`sort`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='任务定义表';

-- 用户任务记录表
CREATE TABLE `tv_user_task` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键',
  `user_id` bigint NOT NULL COMMENT '用户ID',
  `task_id` bigint NOT NULL COMMENT '任务ID',
  `completed_count` int NOT NULL DEFAULT '0' COMMENT '已完成次数',
  `last_completed_time` datetime DEFAULT NULL COMMENT '最后完成时间',
  `total_reward_points` int NOT NULL DEFAULT '0' COMMENT '总获得积分',
  `creator` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT '' COMMENT '创建者',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updater` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT '' COMMENT '更新者',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `deleted` bit(1) NOT NULL DEFAULT b'0' COMMENT '是否删除',
  `tenant_id` bigint NOT NULL DEFAULT 0 COMMENT '租户编号',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_user_task` (`user_id`, `task_id`),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_task_id` (`task_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='用户任务记录表';

-- 任务提交记录表
CREATE TABLE `tv_task_submission` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键',
  `user_id` bigint NOT NULL COMMENT '用户ID',
  `task_id` bigint NOT NULL COMMENT '任务ID',
  `screenshot_base64` longtext NOT NULL COMMENT '截图base64数据',
  `screenshot_hash` varchar(64) NOT NULL COMMENT '截图哈希值',
  `description` varchar(500) DEFAULT NULL COMMENT '提交说明',
  `status` tinyint NOT NULL DEFAULT '0' COMMENT '审核状态：0-待审核，1-审核通过，2-审核拒绝',
  `audit_result` varchar(500) DEFAULT NULL COMMENT '审核结果说明',
  `audit_user_id` bigint DEFAULT NULL COMMENT '审核人ID',
  `audit_time` datetime DEFAULT NULL COMMENT '审核时间',
  `reward_points` int DEFAULT NULL COMMENT '实际奖励积分',
  `creator` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT '' COMMENT '创建者',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updater` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT '' COMMENT '更新者',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `deleted` bit(1) NOT NULL DEFAULT b'0' COMMENT '是否删除',
  `tenant_id` bigint NOT NULL DEFAULT 0 COMMENT '租户编号',
  PRIMARY KEY (`id`),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_task_id` (`task_id`),
  KEY `idx_status` (`status`),
  KEY `idx_screenshot_hash` (`screenshot_hash`),
  KEY `idx_create_time` (`create_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='任务提交记录表';

-- ====================================
-- 任务系统相关表
-- ====================================

-- 任务定义表
CREATE TABLE `tv_task` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键',
  `name` varchar(100) NOT NULL COMMENT '任务名称',
  `description` varchar(500) DEFAULT NULL COMMENT '任务描述',
  `type` tinyint NOT NULL COMMENT '任务类型：1-分享任务，2-短视频任务',
  `reward_points` int NOT NULL DEFAULT '0' COMMENT '奖励积分',
  `daily_limit` int NOT NULL DEFAULT '1' COMMENT '每日完成次数限制',
  `total_limit` int DEFAULT NULL COMMENT '总完成次数限制，NULL表示无限制',
  `status` tinyint NOT NULL DEFAULT '1' COMMENT '任务状态：0-禁用，1-启用',
  `start_time` datetime DEFAULT NULL COMMENT '任务开始时间',
  `end_time` datetime DEFAULT NULL COMMENT '任务结束时间',
  `sort` int DEFAULT '0' COMMENT '排序值，越小越靠前',
  `creator` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT '' COMMENT '创建者',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updater` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT '' COMMENT '更新者',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `deleted` bit(1) NOT NULL DEFAULT b'0' COMMENT '是否删除',
  `tenant_id` bigint NOT NULL DEFAULT 0 COMMENT '租户编号',
  PRIMARY KEY (`id`),
  KEY `idx_type` (`type`),
  KEY `idx_status` (`status`),
  KEY `idx_sort` (`sort`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='任务定义表';

-- 用户任务记录表
CREATE TABLE `tv_user_task` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键',
  `user_id` bigint NOT NULL COMMENT '用户ID',
  `task_id` bigint NOT NULL COMMENT '任务ID',
  `completed_count` int NOT NULL DEFAULT '0' COMMENT '已完成次数',
  `last_completed_time` datetime DEFAULT NULL COMMENT '最后完成时间',
  `total_reward_points` int NOT NULL DEFAULT '0' COMMENT '总获得积分',
  `creator` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT '' COMMENT '创建者',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updater` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT '' COMMENT '更新者',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `deleted` bit(1) NOT NULL DEFAULT b'0' COMMENT '是否删除',
  `tenant_id` bigint NOT NULL DEFAULT 0 COMMENT '租户编号',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_user_task` (`user_id`, `task_id`),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_task_id` (`task_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='用户任务记录表';

-- 任务提交记录表
CREATE TABLE `tv_task_submission` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键',
  `user_id` bigint NOT NULL COMMENT '用户ID',
  `task_id` bigint NOT NULL COMMENT '任务ID',
  `screenshot_base64` longtext NOT NULL COMMENT '截图base64数据',
  `screenshot_hash` varchar(64) NOT NULL COMMENT '截图哈希值',
  `description` varchar(500) DEFAULT NULL COMMENT '提交说明',
  `status` tinyint NOT NULL DEFAULT '0' COMMENT '审核状态：0-待审核，1-审核通过，2-审核拒绝',
  `audit_result` varchar(500) DEFAULT NULL COMMENT '审核结果说明',
  `audit_user_id` bigint DEFAULT NULL COMMENT '审核人ID',
  `audit_time` datetime DEFAULT NULL COMMENT '审核时间',
  `reward_points` int DEFAULT NULL COMMENT '实际奖励积分',
  `creator` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT '' COMMENT '创建者',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updater` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT '' COMMENT '更新者',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `deleted` bit(1) NOT NULL DEFAULT b'0' COMMENT '是否删除',
  `tenant_id` bigint NOT NULL DEFAULT 0 COMMENT '租户编号',
  PRIMARY KEY (`id`),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_task_id` (`task_id`),
  KEY `idx_status` (`status`),
  KEY `idx_screenshot_hash` (`screenshot_hash`),
  KEY `idx_create_time` (`create_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='任务提交记录表';

-- ====================================
-- 初始化任务数据
-- ====================================

-- 插入默认任务
INSERT INTO `tv_task` (`id`, `name`, `description`, `type`, `reward_points`, `daily_limit`, `total_limit`, `status`, `start_time`, `end_time`, `sort`, `creator`, `create_time`, `updater`, `update_time`, `deleted`, `tenant_id`) VALUES
(1, '分享网站到微信群', '分享网站到微信群/微博并截图上传', 1, 20, 1, NULL, 1, NULL, NULL, 1, 'system', NOW(), 'system', NOW(), b'0', 0),
(2, '发布短视频体验', '发布关于网站使用体验的短视频截图', 2, 40, 1, NULL, 1, NULL, NULL, 2, 'system', NOW(), 'system', NOW(), b'0', 0);

-- ====================================
-- 本地消息表
-- ====================================

-- 本地消息表
CREATE TABLE `tv_local_message` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键',
  `message_id` varchar(64) NOT NULL COMMENT '消息唯一标识',
  `topic` varchar(100) NOT NULL COMMENT '消息主题/队列名称',
  `message_type` varchar(50) NOT NULL COMMENT '消息类型',
  `payload` longtext NOT NULL COMMENT '消息内容(JSON格式)',
  `status` tinyint NOT NULL DEFAULT '0' COMMENT '消息状态：0-待发送，1-发送中，2-发送成功，3-发送失败',
  `retry_count` int NOT NULL DEFAULT '0' COMMENT '重试次数',
  `max_retry_count` int NOT NULL DEFAULT '3' COMMENT '最大重试次数',
  `next_retry_time` datetime DEFAULT NULL COMMENT '下次重试时间',
  `error_message` text DEFAULT NULL COMMENT '错误信息',
  `business_id` varchar(100) DEFAULT NULL COMMENT '业务ID',
  `business_type` varchar(50) DEFAULT NULL COMMENT '业务类型',
  `creator` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT '' COMMENT '创建者',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updater` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT '' COMMENT '更新者',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `deleted` bit(1) NOT NULL DEFAULT b'0' COMMENT '是否删除',
  `tenant_id` bigint NOT NULL DEFAULT 0 COMMENT '租户编号',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_message_id` (`message_id`),
  KEY `idx_status_retry_time` (`status`, `next_retry_time`),
  KEY `idx_topic` (`topic`),
  KEY `idx_business` (`business_type`, `business_id`),
  KEY `idx_create_time` (`create_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='本地消息表';

-- 消息处理记录表（用于幂等性控制）
CREATE TABLE `tv_message_process_record` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键',
  `message_id` varchar(64) NOT NULL COMMENT '消息ID',
  `consumer_group` varchar(100) NOT NULL COMMENT '消费者组',
  `consumer_id` varchar(100) NOT NULL COMMENT '消费者ID',
  `process_status` tinyint NOT NULL DEFAULT '0' COMMENT '处理状态：0-处理中，1-处理成功，2-处理失败',
  `process_result` text DEFAULT NULL COMMENT '处理结果',
  `start_time` datetime NOT NULL COMMENT '开始处理时间',
  `end_time` datetime DEFAULT NULL COMMENT '结束处理时间',
  `creator` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT '' COMMENT '创建者',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updater` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT '' COMMENT '更新者',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `deleted` bit(1) NOT NULL DEFAULT b'0' COMMENT '是否删除',
  `tenant_id` bigint NOT NULL DEFAULT 0 COMMENT '租户编号',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_message_consumer` (`message_id`, `consumer_group`),
  KEY `idx_status` (`process_status`),
  KEY `idx_create_time` (`create_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='消息处理记录表';

-- ====================================
-- 执行完成后可以运行以下语句检查索引创建情况
-- ====================================
-- SHOW INDEX FROM tv_content;
-- SHOW INDEX FROM tv_report;
-- SHOW INDEX FROM tv_task;
-- SHOW INDEX FROM tv_user_task;
-- SHOW INDEX FROM tv_task_submission;
-- SHOW INDEX FROM tv_task;
-- SHOW INDEX FROM tv_user_task;
-- SHOW INDEX FROM tv_task_submission;
-- SHOW INDEX FROM tv_task;
-- SHOW INDEX FROM tv_user_task;
-- SHOW INDEX FROM tv_task_submission;
