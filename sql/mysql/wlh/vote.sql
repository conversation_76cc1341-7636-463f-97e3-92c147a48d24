-- `ruoyi-vue-pro`.vote_cyber_star definition

CREATE TABLE `vote_cyber_star` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '文件编号',
  `nickname` varchar(64) NOT NULL COMMENT '昵称',
  `user_id` varchar(64) NOT NULL COMMENT '用户ID',
  `type` tinyint DEFAULT NULL COMMENT '用户类型',
  `img` varchar(464) NOT NULL COMMENT '头像图片URL',
  `cookie` text COMMENT 'Cookie信息',
  `dive_amount` int NOT NULL DEFAULT '100000' COMMENT '跳水金额',
  `gmv_today_amount_double` double DEFAULT '0' COMMENT 'gmv当天金额',
  `gift_today_amount_double` double DEFAULT '0' COMMENT 'gift当天金额',
  `creator` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT '' COMMENT '创建者',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updater` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT '' COMMENT '更新者',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `deleted` bit(1) NOT NULL DEFAULT b'0' COMMENT '是否删除',
  `tenant_id` bigint NOT NULL DEFAULT '0' COMMENT '租户编号',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=56 DEFAULT CHARSET=utf8mb3 COMMENT='网红主播';

-- vote_job_order definition

CREATE TABLE `vote_job_order` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '订单编号',
  `user_id` bigint unsigned NOT NULL COMMENT '用户编号',
  `spu_id` bigint NOT NULL COMMENT '商品编号',
  `spu_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '商品名字',
  `price` int NOT NULL COMMENT '价格，单位：分',
  `pay_status` bit(1) NOT NULL DEFAULT b'0' COMMENT '是否已支付：[0:未支付 1:已经支付过]',
  `pay_order_id` bigint DEFAULT NULL COMMENT '支付订单编号',
  `pay_channel_code` varchar(16) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '支付成功的支付渠道',
  `pay_time` datetime DEFAULT NULL COMMENT '订单支付时间',
  `pay_refund_id` bigint DEFAULT NULL COMMENT '退款订单编号',
  `refund_price` int NOT NULL DEFAULT '0' COMMENT '退款金额，单位：分',
  `refund_time` datetime DEFAULT NULL COMMENT '退款时间',
  `creator` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT '' COMMENT '创建者',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updater` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT '' COMMENT '更新者',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `deleted` bit(1) NOT NULL DEFAULT b'0' COMMENT '是否删除',
  `tenant_id` bigint NOT NULL DEFAULT '0' COMMENT '租户编号',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=204 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_bin COMMENT='会员充值订单';


-- vote_job_positions definition

CREATE TABLE `vote_job_positions` (
  `position_id` int NOT NULL COMMENT '职位ID',
  `company_id` int NOT NULL COMMENT '公司ID',
  `company_name` varchar(255) NOT NULL COMMENT '公司名称',
  `anno_id` int DEFAULT NULL COMMENT '公告ID',
  `anno_name` varchar(255) DEFAULT NULL COMMENT '公告名称',
  `anno_link` varchar(255) DEFAULT NULL COMMENT '公告链接',
  `type` varchar(50) DEFAULT NULL COMMENT '类型',
  `title` varchar(255) DEFAULT NULL COMMENT '职位名称',
  `location` varchar(2999) DEFAULT NULL COMMENT '工作地点',
  `link` varchar(255) DEFAULT NULL COMMENT '链接',
  `specialty` varchar(2999) DEFAULT NULL COMMENT '专业要求',
  `education` varchar(50) DEFAULT NULL COMMENT '学历要求',
  `education_cnlist` varchar(255) DEFAULT NULL COMMENT '学历列表 (使用 JSON 类型存储)',
  `experience` varchar(255) DEFAULT NULL COMMENT '工作经验要求',
  `salary_range` varchar(50) DEFAULT NULL COMMENT '薪资范围',
  `content` text COMMENT '职位描述',
  `job_start_time` datetime DEFAULT NULL COMMENT '职位开始时间',
  `job_end_time` datetime DEFAULT NULL COMMENT '职位结束时间',
  `job_start_time_cn` varchar(50) DEFAULT NULL COMMENT '职位开始时间 (中文格式)',
  `job_end_time_cn` varchar(50) DEFAULT NULL COMMENT '职位结束时间 (中文格式)',
  `is_collect` tinyint(1) DEFAULT '0' COMMENT '是否被收藏 (布尔类型)',
  `creator` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT '' COMMENT '创建者',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updater` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT '' COMMENT '更新者',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `deleted` bit(1) NOT NULL DEFAULT b'0' COMMENT '是否删除',
  `tenant_id` bigint NOT NULL DEFAULT '0' COMMENT '租户编号',
  PRIMARY KEY (`position_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='职位信息表';


-- vote_lottery definition

CREATE TABLE `vote_lottery` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '标识符ID',
  `efficacy` int DEFAULT NULL COMMENT '效果标识，用于表示某种效果或状态的整数类型',
  `full_expect` bigint DEFAULT NULL COMMENT '完整预期值，用于存储特定的大整数值，如期号等',
  `num_sort` varchar(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '数字排序，用于表示开奖号码按某种规则排序后的结果，字符串类型',
  `num_type` varchar(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '数字类型，例如单双、大小等分类，字符串类型',
  `open_code` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '开奖号码，实际开奖的号码组合，以逗号分隔的字符串形式存储',
  `open_time` datetime DEFAULT NULL COMMENT '开奖时间，记录具体的开奖日期和时间',
  `sum` int DEFAULT NULL COMMENT '和数值，所有开奖号码相加得到的总和，整数类型',
  `type_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '类型名称，用于描述彩票类型的名称，字符串类型',
  `variety_type` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '品种类型，用于区分不同种类的游戏，例如jnd28, 字符串类型',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=582306 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_bin COMMENT='kuai28.com';


-- vote_macaujc definition

CREATE TABLE `vote_macaujc` (
  `expect` int NOT NULL COMMENT '期数',
  `open_code` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '开奖号码',
  `zodiac` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '生肖',
  `wave` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '波色',
  `open_time` datetime DEFAULT NULL COMMENT '开奖时间',
  `kind` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '类型',
  `creator` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT '' COMMENT '创建者',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updater` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT '' COMMENT '更新者',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `deleted` bit(1) NOT NULL DEFAULT b'0' COMMENT '是否删除',
  `tenant_id` bigint NOT NULL DEFAULT '0' COMMENT '租户编号',
  PRIMARY KEY (`expect`,`kind`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_bin COMMENT='彩票';


-- vote_raybet_match definition

CREATE TABLE `vote_raybet_match` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '编号',
  `game_id` bigint NOT NULL COMMENT '游戏ID',
  `game_name` varchar(255) NOT NULL COMMENT '游戏名称',
  `match_name` varchar(255) NOT NULL COMMENT '比赛名称',
  `match_short_name` varchar(255) DEFAULT NULL COMMENT '比赛简称',
  `round` varchar(255) DEFAULT NULL COMMENT '轮次',
  `tournament_name` varchar(255) DEFAULT NULL COMMENT '赛事名称',
  `tournament_short_name` varchar(255) DEFAULT NULL COMMENT '赛事简称',
  `start_time` datetime NOT NULL COMMENT '开始时间',
  `end_time` datetime NOT NULL COMMENT '结束时间',
  `score` varchar(255) DEFAULT NULL COMMENT '比分',
  `creator` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT '' COMMENT '创建者',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updater` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT '' COMMENT '更新者',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `deleted` bit(1) NOT NULL DEFAULT b'0' COMMENT '是否删除',
  `tenant_id` bigint NOT NULL DEFAULT '0' COMMENT '租户编号',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='投票活动表';


-- vote_raybet_winner definition

CREATE TABLE `vote_raybet_winner` (
  `match_id` varchar(255) NOT NULL COMMENT '比赛ID',
  `match_fork` bigint DEFAULT NULL COMMENT '外键',
  `team1_id` varchar(255) NOT NULL COMMENT '队伍1 ID',
  `team2_id` varchar(255) NOT NULL COMMENT '队伍2 ID',
  `winner` varchar(255) DEFAULT NULL COMMENT '获胜方',
  `remark` varchar(255) DEFAULT NULL COMMENT '备注',
  `status` int DEFAULT NULL COMMENT '状态标识',
  `creator` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT '' COMMENT '创建者',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updater` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT '' COMMENT '更新者',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `deleted` bit(1) NOT NULL DEFAULT b'0' COMMENT '是否删除',
  `tenant_id` bigint NOT NULL DEFAULT '0' COMMENT '租户编号',
  PRIMARY KEY (`match_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='投票活动获胜方表';



-- 后台管理的菜单目录：：
INSERT INTO system_menu
(id, name, permission, `type`, sort, parent_id, `path`, icon, component, component_name, status, visible, keep_alive, always_show, creator, create_time, updater, update_time, deleted)
VALUES(2965, '投票模块', '', 1, 1001, 0, '/vote', 'fa:bomb', '', '', 0, 1, 1, 1, '1', '2025-04-15 12:59:25', '1', '2025-04-15 12:59:49', 0);
INSERT INTO system_menu
(id, name, permission, `type`, sort, parent_id, `path`, icon, component, component_name, status, visible, keep_alive, always_show, creator, create_time, updater, update_time, deleted)
VALUES(2966, '网红主播管理', '', 2, 0, 2965, 'cyber-star', '', 'vote/cyberstar/index', 'CyberStar', 0, 1, 1, 1, '', '2025-04-15 13:36:07', '', '2025-04-15 13:36:07', 0);
INSERT INTO system_menu
(id, name, permission, `type`, sort, parent_id, `path`, icon, component, component_name, status, visible, keep_alive, always_show, creator, create_time, updater, update_time, deleted)
VALUES(2967, '网红主播查询', 'vote:cyber-star:query', 3, 1, 2966, '', '', '', NULL, 0, 1, 1, 1, '', '2025-04-15 13:36:07', '', '2025-04-15 13:36:07', 0);
INSERT INTO system_menu
(id, name, permission, `type`, sort, parent_id, `path`, icon, component, component_name, status, visible, keep_alive, always_show, creator, create_time, updater, update_time, deleted)
VALUES(2968, '网红主播创建', 'vote:cyber-star:create', 3, 2, 2966, '', '', '', NULL, 0, 1, 1, 1, '', '2025-04-15 13:36:07', '', '2025-04-15 13:36:07', 0);
INSERT INTO system_menu
(id, name, permission, `type`, sort, parent_id, `path`, icon, component, component_name, status, visible, keep_alive, always_show, creator, create_time, updater, update_time, deleted)
VALUES(2969, '网红主播更新', 'vote:cyber-star:update', 3, 3, 2966, '', '', '', NULL, 0, 1, 1, 1, '', '2025-04-15 13:36:07', '', '2025-04-15 13:36:07', 0);
INSERT INTO system_menu
(id, name, permission, `type`, sort, parent_id, `path`, icon, component, component_name, status, visible, keep_alive, always_show, creator, create_time, updater, update_time, deleted)
VALUES(2970, '网红主播删除', 'vote:cyber-star:delete', 3, 4, 2966, '', '', '', NULL, 0, 1, 1, 1, '', '2025-04-15 13:36:08', '', '2025-04-15 13:36:08', 0);
INSERT INTO system_menu
(id, name, permission, `type`, sort, parent_id, `path`, icon, component, component_name, status, visible, keep_alive, always_show, creator, create_time, updater, update_time, deleted)
VALUES(2971, '网红主播导出', 'vote:cyber-star:export', 3, 5, 2966, '', '', '', NULL, 0, 1, 1, 1, '', '2025-04-15 13:36:08', '', '2025-04-15 13:36:08', 0);

