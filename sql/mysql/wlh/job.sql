
-- job_courses definition

CREATE TABLE `job_courses` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '课程id',
  `title` varchar(256) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '标题',
  `description` varchar(512) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '描述',
  `img` varchar(1024) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '图片URL',
  `price` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '价格模式',
  `lessons` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '课时',
  `creator` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT '' COMMENT '创建者',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updater` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT '' COMMENT '更新者',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `deleted` bit(1) NOT NULL DEFAULT b'0' COMMENT '是否删除',
  `tenant_id` bigint NOT NULL DEFAULT '0' COMMENT '租户编号',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=1573 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='工作课程';


-- job_courses_lessons definition

CREATE TABLE `job_courses_lessons` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '课时id',
  `courses_id` bigint DEFAULT NULL COMMENT '课程id',
  `title` varchar(256) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '标题',
  `duration` varchar(512) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '时长',
  `locked` bit(1) NOT NULL DEFAULT b'0' COMMENT '锁定',
  `video_url` varchar(1024) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '视频URL',
  `creator` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT '' COMMENT '创建者',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updater` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT '' COMMENT '更新者',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `deleted` bit(1) NOT NULL DEFAULT b'0' COMMENT '是否删除',
  `tenant_id` bigint NOT NULL DEFAULT '0' COMMENT '租户编号',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=1573 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='工作课程课时';


-- job_positions definition

CREATE TABLE `job_positions` (
  `position_id` int NOT NULL AUTO_INCREMENT COMMENT '职位ID',
  `company_id` int NOT NULL COMMENT '公司ID',
  `company_name` varchar(255) NOT NULL COMMENT '公司名称',
  `anno_id` int DEFAULT NULL COMMENT '公告ID',
  `anno_name` varchar(255) DEFAULT NULL COMMENT '公告名称',
  `anno_link` varchar(255) DEFAULT NULL COMMENT '公告链接',
  `type` varchar(50) DEFAULT NULL COMMENT '类型',
  `title` varchar(255) DEFAULT NULL COMMENT '职位名称',
  `location` varchar(2999) DEFAULT NULL COMMENT '工作地点',
  `link` varchar(255) DEFAULT NULL COMMENT '链接',
  `specialty` varchar(2999) DEFAULT NULL COMMENT '专业要求',
  `education` varchar(50) DEFAULT NULL COMMENT '学历要求',
  `education_cnlist` varchar(255) DEFAULT NULL COMMENT '学历列表 (使用 JSON 类型存储)',
  `experience` varchar(255) DEFAULT NULL COMMENT '工作经验要求',
  `salary_range` varchar(50) DEFAULT NULL COMMENT '薪资范围',
  `content` text COMMENT '职位描述',
  `job_start_time` datetime DEFAULT NULL COMMENT '职位开始时间',
  `job_end_time` datetime DEFAULT NULL COMMENT '职位结束时间',
  `job_start_time_cn` varchar(50) DEFAULT NULL COMMENT '职位开始时间 (中文格式)',
  `job_end_time_cn` varchar(50) DEFAULT NULL COMMENT '职位结束时间 (中文格式)',
  `is_collect` tinyint(1) DEFAULT '0' COMMENT '是否被收藏 (布尔类型)',
  `creator` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT '' COMMENT '创建者',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updater` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT '' COMMENT '更新者',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `deleted` bit(1) NOT NULL DEFAULT b'0' COMMENT '是否删除',
  `tenant_id` bigint NOT NULL DEFAULT '0' COMMENT '租户编号',
  PRIMARY KEY (`position_id`)
) ENGINE=InnoDB AUTO_INCREMENT=359875 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='职位信息表';


-- job_question definition

CREATE TABLE `job_question` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '题库id',
  `title` varchar(256) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '标题',
  `description` varchar(512) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '描述',
  `img` varchar(1024) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '图片URL',
  `url` varchar(1024) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '文件URL',
  `price` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '价格模式',
  `category` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '类别',
  `creator` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT '' COMMENT '创建者',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updater` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT '' COMMENT '更新者',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `deleted` bit(1) NOT NULL DEFAULT b'0' COMMENT '是否删除',
  `tenant_id` bigint NOT NULL DEFAULT '0' COMMENT '租户编号',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=1574 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='工作题库';



-- 后台管理的菜单目录：：
INSERT INTO system_menu
(id, name, permission, `type`, sort, parent_id, `path`, icon, component, component_name, status, visible, keep_alive, always_show, creator, create_time, updater, update_time, deleted)
VALUES(2912, '找工作系统', '', 1, 1000, 0, '/job', 'ep:trophy', '', '', 0, 1, 1, 1, '140', '2025-01-27 15:26:58', '140', '2025-01-27 15:27:32', 0);
INSERT INTO system_menu
(id, name, permission, `type`, sort, parent_id, `path`, icon, component, component_name, status, visible, keep_alive, always_show, creator, create_time, updater, update_time, deleted)
VALUES(2923, '职位信息管理', '', 2, 0, 2912, 'positions', '', 'job/positions/index', 'Positions', 0, 1, 1, 1, '', '2025-01-27 15:38:26', '', '2025-01-27 15:38:26', 0);
INSERT INTO system_menu
(id, name, permission, `type`, sort, parent_id, `path`, icon, component, component_name, status, visible, keep_alive, always_show, creator, create_time, updater, update_time, deleted)
VALUES(2924, '职位信息查询', 'job:positions:query', 3, 1, 2923, '', '', '', NULL, 0, 1, 1, 1, '', '2025-01-27 15:38:26', '', '2025-01-27 15:38:26', 0);
INSERT INTO system_menu
(id, name, permission, `type`, sort, parent_id, `path`, icon, component, component_name, status, visible, keep_alive, always_show, creator, create_time, updater, update_time, deleted)
VALUES(2925, '职位信息创建', 'job:positions:create', 3, 2, 2923, '', '', '', NULL, 0, 1, 1, 1, '', '2025-01-27 15:38:26', '', '2025-01-27 15:38:26', 0);
INSERT INTO system_menu
(id, name, permission, `type`, sort, parent_id, `path`, icon, component, component_name, status, visible, keep_alive, always_show, creator, create_time, updater, update_time, deleted)
VALUES(2926, '职位信息更新', 'job:positions:update', 3, 3, 2923, '', '', '', NULL, 0, 1, 1, 1, '', '2025-01-27 15:38:27', '', '2025-01-27 15:38:27', 0);
INSERT INTO system_menu
(id, name, permission, `type`, sort, parent_id, `path`, icon, component, component_name, status, visible, keep_alive, always_show, creator, create_time, updater, update_time, deleted)
VALUES(2927, '职位信息删除', 'job:positions:delete', 3, 4, 2923, '', '', '', NULL, 0, 1, 1, 1, '', '2025-01-27 15:38:27', '', '2025-01-27 15:38:27', 0);
INSERT INTO system_menu
(id, name, permission, `type`, sort, parent_id, `path`, icon, component, component_name, status, visible, keep_alive, always_show, creator, create_time, updater, update_time, deleted)
VALUES(2928, '职位信息导出', 'job:positions:export', 3, 5, 2923, '', '', '', NULL, 0, 1, 1, 1, '', '2025-01-27 15:38:27', '', '2025-01-27 15:38:27', 0);
INSERT INTO system_menu
(id, name, permission, `type`, sort, parent_id, `path`, icon, component, component_name, status, visible, keep_alive, always_show, creator, create_time, updater, update_time, deleted)
VALUES(2929, '工作课程管理', '', 2, 0, 2912, 'courses', '', 'job/courses/index', 'Courses', 0, 1, 1, 1, '', '2025-01-28 09:24:05', '', '2025-01-28 09:24:05', 0);
INSERT INTO system_menu
(id, name, permission, `type`, sort, parent_id, `path`, icon, component, component_name, status, visible, keep_alive, always_show, creator, create_time, updater, update_time, deleted)
VALUES(2930, '工作课程查询', 'job:courses:query', 3, 1, 2929, '', '', '', NULL, 0, 1, 1, 1, '', '2025-01-28 09:24:05', '', '2025-01-28 09:24:05', 0);
INSERT INTO system_menu
(id, name, permission, `type`, sort, parent_id, `path`, icon, component, component_name, status, visible, keep_alive, always_show, creator, create_time, updater, update_time, deleted)
VALUES(2931, '工作课程创建', 'job:courses:create', 3, 2, 2929, '', '', '', NULL, 0, 1, 1, 1, '', '2025-01-28 09:24:05', '', '2025-01-28 09:24:05', 0);
INSERT INTO system_menu
(id, name, permission, `type`, sort, parent_id, `path`, icon, component, component_name, status, visible, keep_alive, always_show, creator, create_time, updater, update_time, deleted)
VALUES(2932, '工作课程更新', 'job:courses:update', 3, 3, 2929, '', '', '', NULL, 0, 1, 1, 1, '', '2025-01-28 09:24:05', '', '2025-01-28 09:24:05', 0);
INSERT INTO system_menu
(id, name, permission, `type`, sort, parent_id, `path`, icon, component, component_name, status, visible, keep_alive, always_show, creator, create_time, updater, update_time, deleted)
VALUES(2933, '工作课程删除', 'job:courses:delete', 3, 4, 2929, '', '', '', NULL, 0, 1, 1, 1, '', '2025-01-28 09:24:05', '', '2025-01-28 09:24:05', 0);
INSERT INTO system_menu
(id, name, permission, `type`, sort, parent_id, `path`, icon, component, component_name, status, visible, keep_alive, always_show, creator, create_time, updater, update_time, deleted)
VALUES(2934, '工作课程导出', 'job:courses:export', 3, 5, 2929, '', '', '', NULL, 0, 1, 1, 1, '', '2025-01-28 09:24:05', '', '2025-01-28 09:24:05', 0);
INSERT INTO system_menu
(id, name, permission, `type`, sort, parent_id, `path`, icon, component, component_name, status, visible, keep_alive, always_show, creator, create_time, updater, update_time, deleted)
VALUES(2935, '工作题库管理', '', 2, 0, 2912, 'question', '', 'job/question/index', 'Question', 0, 1, 1, 1, '', '2025-01-28 18:10:34', '', '2025-01-28 18:10:34', 0);
INSERT INTO system_menu
(id, name, permission, `type`, sort, parent_id, `path`, icon, component, component_name, status, visible, keep_alive, always_show, creator, create_time, updater, update_time, deleted)
VALUES(2936, '工作题库查询', 'job:question:query', 3, 1, 2935, '', '', '', NULL, 0, 1, 1, 1, '', '2025-01-28 18:10:34', '', '2025-01-28 18:10:34', 0);
INSERT INTO system_menu
(id, name, permission, `type`, sort, parent_id, `path`, icon, component, component_name, status, visible, keep_alive, always_show, creator, create_time, updater, update_time, deleted)
VALUES(2937, '工作题库创建', 'job:question:create', 3, 2, 2935, '', '', '', NULL, 0, 1, 1, 1, '', '2025-01-28 18:10:34', '', '2025-01-28 18:10:34', 0);
INSERT INTO system_menu
(id, name, permission, `type`, sort, parent_id, `path`, icon, component, component_name, status, visible, keep_alive, always_show, creator, create_time, updater, update_time, deleted)
VALUES(2938, '工作题库更新', 'job:question:update', 3, 3, 2935, '', '', '', NULL, 0, 1, 1, 1, '', '2025-01-28 18:10:34', '', '2025-01-28 18:10:34', 0);
INSERT INTO system_menu
(id, name, permission, `type`, sort, parent_id, `path`, icon, component, component_name, status, visible, keep_alive, always_show, creator, create_time, updater, update_time, deleted)
VALUES(2939, '工作题库删除', 'job:question:delete', 3, 4, 2935, '', '', '', NULL, 0, 1, 1, 1, '', '2025-01-28 18:10:34', '', '2025-01-28 18:10:34', 0);
INSERT INTO system_menu
(id, name, permission, `type`, sort, parent_id, `path`, icon, component, component_name, status, visible, keep_alive, always_show, creator, create_time, updater, update_time, deleted)
VALUES(2940, '工作题库导出', 'job:question:export', 3, 5, 2935, '', '', '', NULL, 0, 1, 1, 1, '', '2025-01-28 18:10:35', '', '2025-01-28 18:10:35', 0);
