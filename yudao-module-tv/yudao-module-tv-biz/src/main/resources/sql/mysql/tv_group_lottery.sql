-- 拼团抽奖相关表结构

-- 1. 拼团信息表
CREATE TABLE `tv_group_info` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '拼团ID',
  `creator_id` bigint NOT NULL COMMENT '发起人用户ID',
  `prize_id` bigint NOT NULL COMMENT '奖品ID',
  `prize_name` varchar(255) NOT NULL COMMENT '奖品名称',
  `prize_image` varchar(500) DEFAULT NULL COMMENT '奖品图片',
  `prize_value` decimal(10,2) DEFAULT NULL COMMENT '奖品价值',
  `target_size` int NOT NULL COMMENT '目标人数',
  `current_size` int NOT NULL DEFAULT '0' COMMENT '当前参与人数',
  `entry_fee` decimal(10,2) NOT NULL COMMENT '参团费用',
  `status` tinyint NOT NULL DEFAULT '0' COMMENT '状态：0-待拼团，1-拼团中，2-已开奖，3-已退款',
  `winner_id` bigint DEFAULT NULL COMMENT '中奖用户ID（开奖后更新）',
  `random_seed` varchar(255) DEFAULT NULL COMMENT '随机种子（开奖时生成，方便公示验证）',
  `draw_time` datetime DEFAULT NULL COMMENT '开奖时间',
  `expire_time` datetime NOT NULL COMMENT '截止时间',
  `creator` varchar(64) DEFAULT '' COMMENT '创建者',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updater` varchar(64) DEFAULT '' COMMENT '更新者',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `deleted` bit(1) NOT NULL DEFAULT b'0' COMMENT '是否删除',
  `tenant_id` bigint NOT NULL DEFAULT '0' COMMENT '租户编号',
  PRIMARY KEY (`id`),
  KEY `idx_creator_id` (`creator_id`),
  KEY `idx_prize_id` (`prize_id`),
  KEY `idx_status` (`status`),
  KEY `idx_expire_time` (`expire_time`),
  KEY `idx_tenant_id` (`tenant_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='拼团信息表';

-- 2. 参与记录表
CREATE TABLE `tv_group_order` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '参与记录ID',
  `group_id` bigint NOT NULL COMMENT '拼团ID',
  `user_id` bigint NOT NULL COMMENT '用户ID',
  `order_no` varchar(64) NOT NULL COMMENT '支付订单号',
  `amount` decimal(10,2) NOT NULL COMMENT '支付金额',
  `seq_no` int NOT NULL COMMENT '用户在团内的序号',
  `join_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '参与时间',
  `creator` varchar(64) DEFAULT '' COMMENT '创建者',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updater` varchar(64) DEFAULT '' COMMENT '更新者',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `deleted` bit(1) NOT NULL DEFAULT b'0' COMMENT '是否删除',
  `tenant_id` bigint NOT NULL DEFAULT '0' COMMENT '租户编号',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_group_user` (`group_id`, `user_id`),
  KEY `idx_group_id` (`group_id`),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_order_no` (`order_no`),
  KEY `idx_tenant_id` (`tenant_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='拼团参与记录表';

-- 3. 开奖记录表
CREATE TABLE `tv_group_result` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '记录ID',
  `group_id` bigint NOT NULL COMMENT '拼团ID',
  `winner_id` bigint NOT NULL COMMENT '中奖用户ID',
  `prize_id` bigint NOT NULL COMMENT '奖品ID',
  `prize_name` varchar(255) NOT NULL COMMENT '奖品名称',
  `random_seed` varchar(255) NOT NULL COMMENT '随机种子',
  `algorithm_type` tinyint NOT NULL DEFAULT '1' COMMENT '算法类型：1-权重随机，2-保底必中',
  `draw_details` text COMMENT '开奖详情（JSON格式，包含参与用户权重等信息）',
  `creator` varchar(64) DEFAULT '' COMMENT '创建者',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updater` varchar(64) DEFAULT '' COMMENT '更新者',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `deleted` bit(1) NOT NULL DEFAULT b'0' COMMENT '是否删除',
  `tenant_id` bigint NOT NULL DEFAULT '0' COMMENT '租户编号',
  PRIMARY KEY (`id`),
  KEY `idx_group_id` (`group_id`),
  KEY `idx_winner_id` (`winner_id`),
  KEY `idx_prize_id` (`prize_id`),
  KEY `idx_tenant_id` (`tenant_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='拼团开奖记录表';

-- 4. 用户抽奖统计表
CREATE TABLE `tv_user_lottery_stat` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `user_id` bigint NOT NULL COMMENT '用户ID',
  `total_join` int NOT NULL DEFAULT '0' COMMENT '累计参与次数',
  `total_win` int NOT NULL DEFAULT '0' COMMENT '累计中奖次数',
  `fail_count` int NOT NULL DEFAULT '0' COMMENT '连续未中奖次数',
  `weight` int NOT NULL DEFAULT '1' COMMENT '当前权重',
  `last_join_time` datetime DEFAULT NULL COMMENT '上次参与时间',
  `last_win_time` datetime DEFAULT NULL COMMENT '上次中奖时间',
  `creator` varchar(64) DEFAULT '' COMMENT '创建者',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updater` varchar(64) DEFAULT '' COMMENT '更新者',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `deleted` bit(1) NOT NULL DEFAULT b'0' COMMENT '是否删除',
  `tenant_id` bigint NOT NULL DEFAULT '0' COMMENT '租户编号',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_user_id` (`user_id`, `tenant_id`),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_tenant_id` (`tenant_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='用户抽奖统计表';

-- 5. 奖品表（如果不存在的话）
CREATE TABLE IF NOT EXISTS `tv_prize` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '奖品ID',
  `name` varchar(255) NOT NULL COMMENT '奖品名称',
  `description` text COMMENT '奖品描述',
  `image_url` varchar(500) DEFAULT NULL COMMENT '奖品图片',
  `type` tinyint NOT NULL DEFAULT '1' COMMENT '奖品类型：1-实物，2-虚拟（积分），3-虚拟（余额），4-优惠券',
  `value` decimal(10,2) DEFAULT NULL COMMENT '奖品价值',
  `stock` int NOT NULL DEFAULT '0' COMMENT '库存数量，-1表示无限制',
  `status` tinyint NOT NULL DEFAULT '1' COMMENT '状态：0-禁用，1-启用',
  `sort` int NOT NULL DEFAULT '0' COMMENT '排序',
  `creator` varchar(64) DEFAULT '' COMMENT '创建者',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updater` varchar(64) DEFAULT '' COMMENT '更新者',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `deleted` bit(1) NOT NULL DEFAULT b'0' COMMENT '是否删除',
  `tenant_id` bigint NOT NULL DEFAULT '0' COMMENT '租户编号',
  PRIMARY KEY (`id`),
  KEY `idx_type` (`type`),
  KEY `idx_status` (`status`),
  KEY `idx_sort` (`sort`),
  KEY `idx_tenant_id` (`tenant_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='奖品表';

Tenant-Id 1
Authorization Bearer 7ac444b8da554d12b15b1c952d9a30d6
app-id baidu
terminal 31