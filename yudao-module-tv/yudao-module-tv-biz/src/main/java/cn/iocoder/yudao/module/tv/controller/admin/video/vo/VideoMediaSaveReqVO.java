package cn.iocoder.yudao.module.tv.controller.admin.video.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;

@Schema(description = "管理后台 - 视频媒体资源保存 Request VO")
@Data
public class VideoMediaSaveReqVO {

    @Schema(description = "编号", example = "1024")
    private Long id;

    @Schema(description = "媒体类型：0-图片，1-视频", required = true, example = "0")
    @NotNull(message = "媒体类型不能为空")
    private Integer type;

    @Schema(description = "媒体URL", required = true, example = "https://www.iocoder.cn/media.jpg")
    @NotEmpty(message = "媒体URL不能为空")
    private String url;

    @Schema(description = "缩略图URL（视频特有）", example = "https://www.iocoder.cn/thumbnail.jpg")
    private String thumbnailUrl;

    @Schema(description = "时长（秒，视频特有）", example = "120")
    private Integer duration;

    @Schema(description = "排序", example = "1024")
    private Integer sort;

    @Schema(description = "状态：0-禁用，1-启用", required = true, example = "1")
    @NotNull(message = "状态不能为空")
    private Integer status;
} 