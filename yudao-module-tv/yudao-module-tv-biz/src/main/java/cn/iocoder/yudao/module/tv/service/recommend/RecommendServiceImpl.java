package cn.iocoder.yudao.module.tv.service.recommend;

import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.module.tv.dal.dataobject.history.HistoryDO;
import cn.iocoder.yudao.module.tv.dal.dataobject.video.VideoDO;
import cn.iocoder.yudao.module.tv.dal.mysql.history.HistoryMapper;
import cn.iocoder.yudao.module.tv.dal.mysql.video.VideoMapper;
import cn.iocoder.yudao.module.tv.service.video.VideoService;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 视频推荐 Service 实现类
 */
@Service
public class RecommendServiceImpl implements RecommendService {

    @Resource
    private VideoMapper videoMapper;

    @Resource
    private HistoryMapper historyMapper;

    @Resource
    private VideoService videoService;

    @Override
    public PageResult<VideoDO> getRecommendVideos(Long userId, Integer count) {
        // 1. 获取用户观看历史
        List<HistoryDO> userHistory = historyMapper.selectListByUserId(userId);
        
        // 2. 如果用户没有观看历史，返回热门推荐
        if (userHistory.isEmpty()) {
            return getHotRecommendations(count);
        }

        // 3. 计算各种推荐算法的权重
        double collaborativeWeight = 0.4;
        double contentWeight = 0.3;
        double hotWeight = 0.2;
        double interestWeight = 0.1;

        // 4. 获取各种推荐结果
        PageResult<VideoDO> collaborativeResults = getCollaborativeFilteringRecommendations(userId, count);
        PageResult<VideoDO> contentResults = getContentBasedRecommendations(userId, count);
        PageResult<VideoDO> hotResults = getHotRecommendations(count);
        PageResult<VideoDO> interestResults = getInterestBasedRecommendations(userId, count);

        // 5. 合并推荐结果并计算得分
        Map<Long, Double> videoScores = new HashMap<>();
        
        // 协同过滤得分
        collaborativeResults.getList().forEach(video -> 
            videoScores.merge(video.getId(), collaborativeWeight, Double::sum));
        
        // 基于内容得分
        contentResults.getList().forEach(video -> 
            videoScores.merge(video.getId(), contentWeight, Double::sum));
        
        // 热度得分
        hotResults.getList().forEach(video -> 
            videoScores.merge(video.getId(), hotWeight, Double::sum));
        
        // 兴趣得分
        interestResults.getList().forEach(video -> 
            videoScores.merge(video.getId(), interestWeight, Double::sum));

        // 6. 按得分排序并返回结果
        List<VideoDO> recommendedVideos = videoScores.entrySet().stream()
            .sorted(Map.Entry.<Long, Double>comparingByValue().reversed())
            .limit(count)
            .map(entry -> videoService.getVideo(entry.getKey()))
            .collect(Collectors.toList());

        return new PageResult<>(recommendedVideos, (long) recommendedVideos.size());
    }

    @Override
    public PageResult<VideoDO> getCollaborativeFilteringRecommendations(Long userId, Integer count) {
        // 1. 获取用户观看历史
        List<HistoryDO> userHistory = historyMapper.selectListByUserId(userId);
        
        // 2. 获取相似用户的观看历史
        Map<Long, List<HistoryDO>> similarUserHistory = new HashMap<>();
        for (HistoryDO history : userHistory) {
            List<HistoryDO> otherUserHistory = historyMapper.selectListByVideoId(history.getVideoId());
            for (HistoryDO otherHistory : otherUserHistory) {
                if (!otherHistory.getUserId().equals(userId)) {
                    similarUserHistory.computeIfAbsent(otherHistory.getUserId(), k -> new ArrayList<>())
                        .add(otherHistory);
                }
            }
        }

        // 3. 计算用户相似度
        Map<Long, Double> userSimilarities = new HashMap<>();
        for (Map.Entry<Long, List<HistoryDO>> entry : similarUserHistory.entrySet()) {
            double similarity = calculateUserSimilarity(userHistory, entry.getValue());
            userSimilarities.put(entry.getKey(), similarity);
        }

        // 4. 获取推荐视频
        Map<Long, Double> videoScores = new HashMap<>();
        for (Map.Entry<Long, Double> entry : userSimilarities.entrySet()) {
            List<HistoryDO> similarUserHistoryList = similarUserHistory.get(entry.getKey());
            for (HistoryDO history : similarUserHistoryList) {
                if (!userHistory.stream().anyMatch(h -> h.getVideoId().equals(history.getVideoId()))) {
                    videoScores.merge(history.getVideoId(), entry.getValue(), Double::sum);
                }
            }
        }

        // 5. 按得分排序并返回结果
        List<VideoDO> recommendedVideos = videoScores.entrySet().stream()
            .sorted(Map.Entry.<Long, Double>comparingByValue().reversed())
            .limit(count)
            .map(entry -> videoService.getVideo(entry.getKey()))
            .collect(Collectors.toList());

        return new PageResult<>(recommendedVideos, (long) recommendedVideos.size());
    }

    @Override
    public PageResult<VideoDO> getContentBasedRecommendations(Long userId, Integer count) {
        // 1. 获取用户观看历史
        List<HistoryDO> userHistory = historyMapper.selectListByUserId(userId);
        
        // 2. 获取用户观看过的视频分类
        Map<Long, Integer> categoryWeights = new HashMap<>();
        for (HistoryDO history : userHistory) {
            VideoDO video = videoService.getVideo(history.getVideoId());
            categoryWeights.merge(video.getCategoryId(), 1, Integer::sum);
        }

        // 3. 获取同分类的视频
        List<VideoDO> recommendedVideos = new ArrayList<>();
        for (Map.Entry<Long, Integer> entry : categoryWeights.entrySet()) {
            List<VideoDO> categoryVideos = videoMapper.selectListByCategoryId(entry.getKey());
            recommendedVideos.addAll(categoryVideos.stream()
                .filter(video -> !userHistory.stream().anyMatch(h -> h.getVideoId().equals(video.getId())))
                .limit(count / categoryWeights.size())
                .collect(Collectors.toList()));
        }

        return new PageResult<>(recommendedVideos, (long) recommendedVideos.size());
    }

    @Override
    public PageResult<VideoDO> getHotRecommendations(Integer count) {
        // 1. 获取热门视频（按播放次数排序）
        List<VideoDO> hotVideos = videoMapper.selectListOrderByPlayCount(count);
        return new PageResult<>(hotVideos, (long) hotVideos.size());
    }

    @Override
    public PageResult<VideoDO> getInterestBasedRecommendations(Long userId, Integer count) {
        // 1. 获取用户观看历史
        List<HistoryDO> userHistory = historyMapper.selectListByUserId(userId);
        
        // 2. 分析用户观看行为
        Map<String, Integer> tagWeights = new HashMap<>();
        for (HistoryDO history : userHistory) {
            VideoDO video = videoService.getVideo(history.getVideoId());
            // 假设视频有标签字段，这里需要根据实际情况调整
            String[] tags = video.getTags().split(",");
            for (String tag : tags) {
                tagWeights.merge(tag.trim(), 1, Integer::sum);
            }
        }

        // 3. 获取相似标签的视频
        List<VideoDO> recommendedVideos = new ArrayList<>();
        for (Map.Entry<String, Integer> entry : tagWeights.entrySet()) {
            List<VideoDO> tagVideos = videoMapper.selectListByTag(entry.getKey());
            recommendedVideos.addAll(tagVideos.stream()
                .filter(video -> !userHistory.stream().anyMatch(h -> h.getVideoId().equals(video.getId())))
                .limit(count / tagWeights.size())
                .collect(Collectors.toList()));
        }

        return new PageResult<>(recommendedVideos, (long) recommendedVideos.size());
    }

    /**
     * 计算用户相似度
     *
     * @param user1History 用户1的观看历史
     * @param user2History 用户2的观看历史
     * @return 相似度得分
     */
    private double calculateUserSimilarity(List<HistoryDO> user1History, List<HistoryDO> user2History) {
        // 1. 获取共同观看的视频
        Set<Long> commonVideos = user1History.stream()
            .map(HistoryDO::getVideoId)
            .filter(videoId -> user2History.stream().anyMatch(h -> h.getVideoId().equals(videoId)))
            .collect(Collectors.toSet());

        if (commonVideos.isEmpty()) {
            return 0.0;
        }

        // 2. 计算观看进度的相似度
        double similarity = 0.0;
        for (Long videoId : commonVideos) {
            HistoryDO history1 = user1History.stream()
                .filter(h -> h.getVideoId().equals(videoId))
                .findFirst()
                .get();
            HistoryDO history2 = user2History.stream()
                .filter(h -> h.getVideoId().equals(videoId))
                .findFirst()
                .get();

            // 计算观看进度的差异
            double progressDiff = Math.abs(history1.getProgress() - history2.getProgress());
            similarity += 1.0 / (1.0 + progressDiff);
        }

        return similarity / commonVideos.size();
    }
} 