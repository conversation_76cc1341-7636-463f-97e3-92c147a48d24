package cn.iocoder.yudao.module.tv.dal.mysql.task;

import cn.iocoder.yudao.framework.mybatis.core.query.LambdaQueryWrapperX;
import cn.iocoder.yudao.framework.mybatis.core.mapper.BaseMapperX;
import cn.iocoder.yudao.module.tv.dal.dataobject.task.UserTaskDO;
import org.apache.ibatis.annotations.Mapper;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 用户任务记录 Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface UserTaskMapper extends BaseMapperX<UserTaskDO> {

    /**
     * 根据用户ID和任务ID查询用户任务记录
     *
     * @param userId 用户ID
     * @param taskId 任务ID
     * @return 用户任务记录
     */
    default UserTaskDO selectByUserIdAndTaskId(Long userId, Long taskId) {
        return selectOne(new LambdaQueryWrapperX<UserTaskDO>()
                .eq(UserTaskDO::getUserId, userId)
                .eq(UserTaskDO::getTaskId, taskId));
    }

    /**
     * 查询用户的任务记录列表
     *
     * @param userId 用户ID
     * @return 用户任务记录列表
     */
    default List<UserTaskDO> selectByUserId(Long userId) {
        return selectList(new LambdaQueryWrapperX<UserTaskDO>()
                .eq(UserTaskDO::getUserId, userId)
                .orderByDesc(UserTaskDO::getLastCompletedTime));
    }

    /**
     * 统计用户今日完成的任务次数
     *
     * @param userId 用户ID
     * @param taskId 任务ID
     * @param startOfDay 今日开始时间
     * @param endOfDay 今日结束时间
     * @return 今日完成次数
     */
    default Integer countTodayCompleted(Long userId, Long taskId, LocalDateTime startOfDay, LocalDateTime endOfDay) {
        UserTaskDO userTask = selectByUserIdAndTaskId(userId, taskId);
        if (userTask == null || userTask.getLastCompletedTime() == null) {
            return 0;
        }
        
        // 如果最后完成时间在今天范围内，返回1，否则返回0
        // 注意：这里简化处理，实际应该查询提交记录表统计今日通过的次数
        LocalDateTime lastCompleted = userTask.getLastCompletedTime();
        if (lastCompleted.isAfter(startOfDay) && lastCompleted.isBefore(endOfDay)) {
            return 1;
        }
        return 0;
    }

}
