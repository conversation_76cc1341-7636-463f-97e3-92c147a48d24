package cn.iocoder.yudao.module.tv.dal.mysql.group;

import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.mybatis.core.mapper.BaseMapperX;
import cn.iocoder.yudao.framework.mybatis.core.query.LambdaQueryWrapperX;
import cn.iocoder.yudao.module.tv.controller.admin.group.vo.GroupOrderPageReqVO;
import cn.iocoder.yudao.module.tv.dal.dataobject.group.GroupOrderDO;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
 * 拼团参与记录 Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface GroupOrderMapper extends BaseMapperX<GroupOrderDO> {

    default PageResult<GroupOrderDO> selectPage(GroupOrderPageReqVO reqVO) {
        return selectPage(reqVO, new LambdaQueryWrapperX<GroupOrderDO>()
                .eqIfPresent(GroupOrderDO::getGroupId, reqVO.getGroupId())
                .eqIfPresent(GroupOrderDO::getUserId, reqVO.getUserId())
                .likeIfPresent(GroupOrderDO::getOrderNo, reqVO.getOrderNo())
                .betweenIfPresent(GroupOrderDO::getJoinTime, reqVO.getJoinTime())
                .orderByDesc(GroupOrderDO::getId));
    }

    default List<GroupOrderDO> selectListByGroupId(Long groupId) {
        return selectList(new LambdaQueryWrapperX<GroupOrderDO>()
                .eq(GroupOrderDO::getGroupId, groupId)
                .orderByAsc(GroupOrderDO::getSeqNo));
    }

    default List<GroupOrderDO> selectListByUserId(Long userId) {
        return selectList(new LambdaQueryWrapperX<GroupOrderDO>()
                .eq(GroupOrderDO::getUserId, userId)
                .orderByDesc(GroupOrderDO::getJoinTime));
    }

    default GroupOrderDO selectByGroupIdAndUserId(Long groupId, Long userId) {
        return selectOne(new LambdaQueryWrapperX<GroupOrderDO>()
                .eq(GroupOrderDO::getGroupId, groupId)
                .eq(GroupOrderDO::getUserId, userId));
    }

    default GroupOrderDO selectByOrderNo(String orderNo) {
        return selectOne(new LambdaQueryWrapperX<GroupOrderDO>()
                .eq(GroupOrderDO::getOrderNo, orderNo));
    }

    default Long countByGroupId(Long groupId) {
        return selectCount(new LambdaQueryWrapperX<GroupOrderDO>()
                .eq(GroupOrderDO::getGroupId, groupId));
    }

    default Long countByUserId(Long userId) {
        return selectCount(new LambdaQueryWrapperX<GroupOrderDO>()
                .eq(GroupOrderDO::getUserId, userId));
    }

    default Integer selectMaxSeqNoByGroupId(Long groupId) {
        GroupOrderDO order = selectOne(new LambdaQueryWrapperX<GroupOrderDO>()
                .eq(GroupOrderDO::getGroupId, groupId)
                .orderByDesc(GroupOrderDO::getSeqNo)
                .last("LIMIT 1"));
        return order != null ? order.getSeqNo() : 0;
    }
}
