package cn.iocoder.yudao.module.tv.controller.admin.privilege.vo;

import cn.iocoder.yudao.framework.common.pojo.PageParam;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

@Schema(description = "管理后台 - 特权分页 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class PrivilegePageReqVO extends PageParam {

    @Schema(description = "特权名称", example = "VIP会员")
    private String name;

    @Schema(description = "状态", example = "1")
    private Integer status;

} 