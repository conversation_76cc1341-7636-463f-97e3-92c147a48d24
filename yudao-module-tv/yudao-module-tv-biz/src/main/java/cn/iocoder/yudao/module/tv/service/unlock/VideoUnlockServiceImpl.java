package cn.iocoder.yudao.module.tv.service.unlock;

import cn.iocoder.yudao.framework.common.pojo.PageParam;
import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.module.tv.controller.admin.video.vo.VideoPageReqVO;
import cn.iocoder.yudao.module.tv.dal.dataobject.unlock.UserVideoUnlockDO;
import cn.iocoder.yudao.module.tv.dal.dataobject.video.VideoDO;
import cn.iocoder.yudao.module.tv.dal.mysql.unlock.UserVideoUnlockMapper;
import cn.iocoder.yudao.module.tv.dal.mysql.video.VideoMapper;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * 视频解锁 Service 实现类
 */
@Service
public class VideoUnlockServiceImpl implements VideoUnlockService {

    @Resource
    private UserVideoUnlockMapper userVideoUnlockMapper;

    @Resource
    private VideoMapper videoMapper;

    @Override
    public boolean isVideoUnlocked(Long userId, Long videoId) {
        // 查询解锁记录
        UserVideoUnlockDO unlock = userVideoUnlockMapper.selectByUserIdAndVideoId(userId, videoId);
        // 如果存在解锁记录，则表示已解锁
        return unlock != null;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Long unlockVideo(Long userId, Long videoId, Integer points) {
        // 查询是否已解锁
        UserVideoUnlockDO existingUnlock = userVideoUnlockMapper.selectByUserIdAndVideoId(userId, videoId);
        if (existingUnlock != null) {
            return existingUnlock.getId(); // 已解锁，直接返回
        }

        // 创建解锁记录
        UserVideoUnlockDO unlock = new UserVideoUnlockDO()
                .setUserId(userId)
                .setVideoId(videoId)
                .setPoints(points)
                .setUnlockTime(LocalDateTime.now());
        userVideoUnlockMapper.insert(unlock);

        return unlock.getId();
    }

    @Override
    public Set<Long> getUserUnlockedVideoIds(Long userId) {
        // 查询用户解锁记录
        List<UserVideoUnlockDO> unlockList = userVideoUnlockMapper.selectListByUserId(userId);
        // 提取视频ID
        return unlockList.stream()
                .map(UserVideoUnlockDO::getVideoId)
                .collect(Collectors.toSet());
    }

    @Override
    public PageResult<UserVideoUnlockDO> selectPageByUserId(Long userId, PageParam pageReqVO) {
        // 查询用户解锁记录分页
         return userVideoUnlockMapper.selectPageByUserId(userId, pageReqVO);
    }

    @Override
    public UserVideoUnlockDO getUserVideoUnlock(Long userId, Long videoId) {
        return userVideoUnlockMapper.selectByUserIdAndVideoId(userId, videoId);
    }
}
