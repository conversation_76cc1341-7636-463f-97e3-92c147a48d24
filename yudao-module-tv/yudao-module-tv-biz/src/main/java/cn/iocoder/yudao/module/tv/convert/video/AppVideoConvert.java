package cn.iocoder.yudao.module.tv.convert.video;

import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.module.tv.controller.app.video.vo.AppVideoDetailRespVO;
import cn.iocoder.yudao.module.tv.controller.app.video.vo.AppVideoRespVO;
import cn.iocoder.yudao.module.tv.dal.dataobject.video.VideoDO;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.Named;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * 视频 Convert
 */
@Mapper
public interface AppVideoConvert {

    AppVideoConvert INSTANCE = Mappers.getMapper(AppVideoConvert.class);

    AppVideoRespVO convert(VideoDO bean);

    AppVideoDetailRespVO convertDetail(VideoDO bean);

    List<AppVideoRespVO> convertList(List<VideoDO> list);

    PageResult<AppVideoRespVO> convertPage(PageResult<VideoDO> page);
}