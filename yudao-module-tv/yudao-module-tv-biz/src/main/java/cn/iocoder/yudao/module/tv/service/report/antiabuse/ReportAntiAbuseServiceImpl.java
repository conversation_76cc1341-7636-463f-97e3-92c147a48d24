package cn.iocoder.yudao.module.tv.service.report.antiabuse;

import cn.iocoder.yudao.framework.mybatis.core.query.LambdaQueryWrapperX;
import cn.iocoder.yudao.module.tv.dal.dataobject.report.ReportDO;
import cn.iocoder.yudao.module.tv.dal.mysql.report.ReportMapper;
import cn.iocoder.yudao.module.tv.enums.report.ReportStatusEnum;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.time.LocalDateTime;

/**
 * 举报反滥用服务实现类
 *
 * <AUTHOR>
 */
@Service
@Slf4j
public class ReportAntiAbuseServiceImpl implements ReportAntiAbuseService {

    @Resource
    private ReportMapper reportMapper;

    // TODO: 需要注入积分服务API
    // @Resource
    // private MemberPointApi memberPointApi;

    @Override
    public boolean isMaliciousReporter(Long userId) {
        // 1. 检查最近7天的举报次数
        LocalDateTime sevenDaysAgo = LocalDateTime.now().minusDays(7);
        Long recentReports = reportMapper.countUserRecentReports(userId, sevenDaysAgo);
        
        // 2. 检查被驳回的举报比例
        boolean hasExcessiveRejections = hasExcessiveRejections(userId);
        
        // 3. 综合判断：7天内举报超过20次，或者被驳回比例超过80%
        boolean isMalicious = recentReports > 20 || hasExcessiveRejections;
        
        if (isMalicious) {
            log.warn("[isMaliciousReporter][检测到恶意举报用户] userId={}, recentReports={}, hasExcessiveRejections={}", 
                    userId, recentReports, hasExcessiveRejections);
        }
        
        return isMalicious;
    }

    @Override
    public void handleMaliciousReporter(Long userId, Long reportCount, String reason) {
        log.warn("[handleMaliciousReporter][处理恶意举报用户] userId={}, reportCount={}, reason={}", 
                userId, reportCount, reason);
        
        // 1. 扣除积分（根据举报次数计算扣除数量）
        int pointsToDeduct = calculatePenaltyPoints(reportCount);
        if (pointsToDeduct > 0) {
            try {
                // TODO: 调用积分服务扣除积分
                // memberPointApi.reducePoint(userId, pointsToDeduct, 
                //     MemberPointBizTypeEnum.MALICIOUS_REPORT_PENALTY.getType(), 
                //     "恶意举报扣除积分：" + reason);
                
                log.info("[handleMaliciousReporter][扣除恶意举报用户积分] userId={}, points={}", 
                        userId, pointsToDeduct);
            } catch (Exception e) {
                log.error("[handleMaliciousReporter][扣除积分失败] userId={}, points={}", 
                        userId, pointsToDeduct, e);
            }
        }
        
        // 2. 记录处理日志
        // TODO: 可以考虑记录到专门的处罚表中
        
        // 3. 如果情况严重，可以考虑临时限制举报功能
        if (reportCount > 50) {
            log.warn("[handleMaliciousReporter][严重恶意举报，建议人工审核] userId={}, reportCount={}", 
                    userId, reportCount);
            // TODO: 可以设置用户举报冷却期或者发送通知给管理员
        }
    }

    @Override
    public void recordReportBehavior(ReportDO report) {
        // 记录举报行为，用于后续分析
        log.debug("[recordReportBehavior][记录举报行为] userId={}, businessType={}, businessId={}", 
                report.getReporterId(), report.getBusinessType(), report.getBusinessId());
        
        // 检查是否为恶意举报
        if (report.getReporterId() != null && isMaliciousReporter(report.getReporterId())) {
            // 统计用户最近的举报次数
            LocalDateTime sevenDaysAgo = LocalDateTime.now().minusDays(7);
            Long recentCount = reportMapper.countUserRecentReports(report.getReporterId(), sevenDaysAgo);
            
            // 处理恶意举报
            handleMaliciousReporter(report.getReporterId(), recentCount, "频繁举报检测");
        }
    }

    @Override
    public boolean hasExcessiveRejections(Long userId) {
        // 查询用户最近30天的举报记录
        LocalDateTime thirtyDaysAgo = LocalDateTime.now().minusDays(30);
        
        // 总举报数
        Long totalReports = reportMapper.selectCount(new LambdaQueryWrapperX<ReportDO>()
                .eq(ReportDO::getReporterId, userId)
                .ge(ReportDO::getCreateTime, thirtyDaysAgo));
        
        if (totalReports < 5) {
            // 举报次数太少，不判断为恶意
            return false;
        }
        
        // 被驳回的举报数
        Long rejectedReports = reportMapper.selectCount(new LambdaQueryWrapperX<ReportDO>()
                .eq(ReportDO::getReporterId, userId)
                .eq(ReportDO::getStatus, ReportStatusEnum.REJECTED.getStatus())
                .ge(ReportDO::getCreateTime, thirtyDaysAgo));
        
        // 计算被驳回比例
        double rejectionRate = (double) rejectedReports / totalReports;
        
        // 被驳回比例超过80%认为是恶意举报
        return rejectionRate > 0.8;
    }

    /**
     * 计算惩罚积分
     *
     * @param reportCount 举报次数
     * @return 扣除积分数
     */
    private int calculatePenaltyPoints(Long reportCount) {
        if (reportCount <= 10) {
            return 0; // 轻微违规，不扣分
        } else if (reportCount <= 20) {
            return 10; // 中等违规，扣10分
        } else if (reportCount <= 50) {
            return 50; // 严重违规，扣50分
        } else {
            return 100; // 极其严重，扣100分
        }
    }

}
