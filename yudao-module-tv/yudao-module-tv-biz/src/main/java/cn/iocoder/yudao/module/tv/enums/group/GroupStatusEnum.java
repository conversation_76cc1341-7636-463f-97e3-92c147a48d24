package cn.iocoder.yudao.module.tv.enums.group;

import cn.iocoder.yudao.framework.common.core.IntArrayValuable;
import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Arrays;

/**
 * 拼团状态枚举
 *
 * <AUTHOR>
 */
@Getter
@AllArgsConstructor
public enum GroupStatusEnum implements IntArrayValuable {

    WAITING(0, "待拼团"),
    IN_PROGRESS(1, "拼团中"),
    DRAWN(2, "已开奖"),
    REFUNDED(3, "已退款");

    public static final int[] ARRAYS = Arrays.stream(values()).mapToInt(GroupStatusEnum::getStatus).toArray();

    /**
     * 状态
     */
    private final Integer status;
    /**
     * 名字
     */
    private final String name;

    @Override
    public int[] array() {
        return ARRAYS;
    }

    public static GroupStatusEnum valueOf(Integer status) {
        return Arrays.stream(values()).filter(item -> item.getStatus().equals(status)).findFirst().orElse(null);
    }

    /**
     * 判断是否可以参与拼团
     */
    public static boolean canJoin(Integer status) {
        return WAITING.getStatus().equals(status) || IN_PROGRESS.getStatus().equals(status);
    }

    /**
     * 判断是否已结束（开奖或退款）
     */
    public static boolean isFinished(Integer status) {
        return DRAWN.getStatus().equals(status) || REFUNDED.getStatus().equals(status);
    }
}
