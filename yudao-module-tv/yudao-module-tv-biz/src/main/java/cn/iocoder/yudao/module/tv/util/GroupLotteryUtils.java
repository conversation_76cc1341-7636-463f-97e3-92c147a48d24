package cn.iocoder.yudao.module.tv.util;

import cn.hutool.core.util.RandomUtil;
import cn.hutool.core.util.StrUtil;
import cn.iocoder.yudao.module.tv.dal.dataobject.group.GroupOrderDO;
import cn.iocoder.yudao.module.tv.dal.dataobject.group.UserLotteryStatDO;
import cn.iocoder.yudao.module.tv.enums.group.DrawAlgorithmEnum;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;

import java.security.MessageDigest;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.Comparator;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 拼团抽奖算法工具类
 *
 * <AUTHOR>
 */
@Slf4j
public class GroupLotteryUtils {

    /**
     * 保底必中阈值（连续未中奖次数）
     */
    public static final int GUARANTEE_THRESHOLD = 50;

    /**
     * 默认权重
     */
    public static final int DEFAULT_WEIGHT = 1;

    /**
     * 权重增长因子
     */
    public static final int WEIGHT_GROWTH_FACTOR = 1;

    /**
     * 开奖结果
     */
    @Data
    public static class DrawResult {
        /**
         * 中奖用户ID
         */
        private Long winnerId;
        
        /**
         * 使用的算法类型
         */
        private Integer algorithmType;
        
        /**
         * 随机种子
         */
        private String randomSeed;
        
        /**
         * 开奖详情（JSON格式）
         */
        private String drawDetails;
    }

    /**
     * 用户权重信息
     */
    @Data
    public static class UserWeight {
        private Long userId;
        private Integer weight;
        private Integer failCount;
        private LocalDateTime lastJoinTime;
    }

    /**
     * 执行开奖
     *
     * @param participants 参与用户列表
     * @param userStats 用户统计信息
     * @return 开奖结果
     */
    public static DrawResult drawWinner(List<GroupOrderDO> participants, Map<Long, UserLotteryStatDO> userStats) {
        if (participants == null || participants.isEmpty()) {
            throw new IllegalArgumentException("参与用户列表不能为空");
        }

        // 构建用户权重信息
        List<UserWeight> userWeights = buildUserWeights(participants, userStats);
        
        // 生成随机种子
        String randomSeed = generateRandomSeed();
        
        // 检查是否有满足保底条件的用户
        List<UserWeight> guaranteeUsers = userWeights.stream()
                .filter(user -> user.getFailCount() >= GUARANTEE_THRESHOLD)
                .sorted(Comparator.comparing(UserWeight::getFailCount).reversed()
                        .thenComparing(UserWeight::getLastJoinTime))
                .collect(Collectors.toList());

        DrawResult result = new DrawResult();
        result.setRandomSeed(randomSeed);

        if (!guaranteeUsers.isEmpty()) {
            // 保底必中
            UserWeight winner = guaranteeUsers.get(0);
            result.setWinnerId(winner.getUserId());
            result.setAlgorithmType(DrawAlgorithmEnum.GUARANTEED_WIN.getType());
            result.setDrawDetails(buildDrawDetails(userWeights, winner.getUserId(), "保底必中"));
            
            log.info("[drawWinner][保底必中] winnerId={}, failCount={}", 
                    winner.getUserId(), winner.getFailCount());
        } else {
            // 权重随机
            Long winnerId = weightedRandomDraw(userWeights, randomSeed);
            result.setWinnerId(winnerId);
            result.setAlgorithmType(DrawAlgorithmEnum.WEIGHTED_RANDOM.getType());
            result.setDrawDetails(buildDrawDetails(userWeights, winnerId, "权重随机"));
            
            log.info("[drawWinner][权重随机] winnerId={}, randomSeed={}", winnerId, randomSeed);
        }

        return result;
    }

    /**
     * 构建用户权重信息
     */
    private static List<UserWeight> buildUserWeights(List<GroupOrderDO> participants, 
                                                    Map<Long, UserLotteryStatDO> userStats) {
        List<UserWeight> userWeights = new ArrayList<>();
        
        for (GroupOrderDO participant : participants) {
            UserWeight userWeight = new UserWeight();
            userWeight.setUserId(participant.getUserId());
            
            UserLotteryStatDO stat = userStats.get(participant.getUserId());
            if (stat != null) {
                userWeight.setWeight(stat.getWeight());
                userWeight.setFailCount(stat.getFailCount());
                userWeight.setLastJoinTime(stat.getLastJoinTime());
            } else {
                // 新用户默认权重
                userWeight.setWeight(DEFAULT_WEIGHT);
                userWeight.setFailCount(0);
                userWeight.setLastJoinTime(participant.getJoinTime());
            }
            
            userWeights.add(userWeight);
        }
        
        return userWeights;
    }

    /**
     * 权重随机抽奖
     */
    private static Long weightedRandomDraw(List<UserWeight> userWeights, String randomSeed) {
        // 计算总权重
        int totalWeight = userWeights.stream().mapToInt(UserWeight::getWeight).sum();
        
        // 使用随机种子生成随机数
        int randomValue = generateRandomValue(randomSeed, totalWeight);
        
        // 根据权重选择中奖用户
        int currentWeight = 0;
        for (UserWeight userWeight : userWeights) {
            currentWeight += userWeight.getWeight();
            if (randomValue <= currentWeight) {
                return userWeight.getUserId();
            }
        }
        
        // 理论上不会到达这里，但为了安全起见返回第一个用户
        return userWeights.get(0).getUserId();
    }

    /**
     * 生成随机种子
     */
    private static String generateRandomSeed() {
        // 使用当前时间戳 + 随机数生成种子
        String timestamp = LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyyMMddHHmmssSSS"));
        String random = RandomUtil.randomString(8);
        return timestamp + random;
    }

    /**
     * 基于随机种子生成随机值
     */
    private static int generateRandomValue(String seed, int bound) {
        try {
            MessageDigest md = MessageDigest.getInstance("MD5");
            byte[] hash = md.digest(seed.getBytes());
            
            // 将hash转换为正整数
            int value = 0;
            for (int i = 0; i < 4 && i < hash.length; i++) {
                value = (value << 8) + (hash[i] & 0xFF);
            }
            
            // 确保为正数并取模
            return Math.abs(value) % bound + 1;
        } catch (Exception e) {
            log.error("[generateRandomValue][生成随机值失败] seed={}, bound={}", seed, bound, e);
            // 降级使用普通随机数
            return RandomUtil.randomInt(1, bound + 1);
        }
    }

    /**
     * 构建开奖详情JSON
     */
    private static String buildDrawDetails(List<UserWeight> userWeights, Long winnerId, String algorithm) {
        StringBuilder details = new StringBuilder();
        details.append("{");
        details.append("\"algorithm\":\"").append(algorithm).append("\",");
        details.append("\"totalUsers\":").append(userWeights.size()).append(",");
        details.append("\"winnerId\":").append(winnerId).append(",");
        details.append("\"users\":[");
        
        for (int i = 0; i < userWeights.size(); i++) {
            UserWeight user = userWeights.get(i);
            if (i > 0) {
                details.append(",");
            }
            details.append("{");
            details.append("\"userId\":").append(user.getUserId()).append(",");
            details.append("\"weight\":").append(user.getWeight()).append(",");
            details.append("\"failCount\":").append(user.getFailCount()).append(",");
            details.append("\"isWinner\":").append(user.getUserId().equals(winnerId));
            details.append("}");
        }
        
        details.append("]}");
        return details.toString();
    }

    /**
     * 计算用户新权重（参与后增加权重）
     */
    public static int calculateNewWeight(int currentWeight, int failCount) {
        // 基础权重 + 连续失败次数 * 增长因子
        return Math.max(DEFAULT_WEIGHT, currentWeight + WEIGHT_GROWTH_FACTOR);
    }

    /**
     * 重置中奖用户权重
     */
    public static int resetWinnerWeight() {
        return DEFAULT_WEIGHT;
    }
}
