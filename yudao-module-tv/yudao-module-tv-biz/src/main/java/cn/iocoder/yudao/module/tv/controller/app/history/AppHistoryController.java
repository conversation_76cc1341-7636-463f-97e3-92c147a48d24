package cn.iocoder.yudao.module.tv.controller.app.history;

import cn.iocoder.yudao.framework.common.pojo.CommonResult;
import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.module.tv.controller.app.history.vo.AppHistoryCreateReqVO;
import cn.iocoder.yudao.module.tv.controller.app.history.vo.AppHistoryPageReqVO;
import cn.iocoder.yudao.module.tv.controller.app.history.vo.AppHistoryPlayReqVO;
import cn.iocoder.yudao.module.tv.controller.app.history.vo.AppHistoryRespVO;
import cn.iocoder.yudao.module.tv.controller.app.history.vo.AppHistoryVideoPageReqVO;
import cn.iocoder.yudao.module.tv.controller.app.history.vo.AppHistoryVideoRespVO;
import cn.iocoder.yudao.module.tv.convert.history.AppHistoryConvert;
import cn.iocoder.yudao.module.tv.dal.dataobject.history.HistoryDO;
import cn.iocoder.yudao.module.tv.service.history.HistoryService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;

import static cn.iocoder.yudao.framework.common.pojo.CommonResult.success;
import static cn.iocoder.yudao.framework.security.core.util.SecurityFrameworkUtils.getLoginUserId;

@Tag(name = "用户 APP - 观看历史")
@RestController
@RequestMapping("/tv/history")
@Validated
public class AppHistoryController {

    @Resource
    private HistoryService historyService;

    @PostMapping("/create")
    @Operation(summary = "创建观看历史")
    public CommonResult<Long> createHistory(@Valid @RequestBody AppHistoryCreateReqVO createReqVO) {
        return success(historyService.createHistory(createReqVO.getUserId(), createReqVO.getVideoId(),
                createReqVO.getProgress(), createReqVO.getFinished()));
    }

    @PostMapping("/update")
    @Operation(summary = "更新观看历史")
    public CommonResult<Boolean> updateHistory(@Valid @RequestBody AppHistoryCreateReqVO createReqVO) {
        historyService.updateHistory(createReqVO.getUserId(), createReqVO.getVideoId(),
                createReqVO.getProgress(), createReqVO.getFinished());
        return success(true);
    }

    @DeleteMapping("/delete")
    @Operation(summary = "删除观看历史")
    @Parameter(name = "userId", description = "用户编号", required = true, example = "1024")
    @Parameter(name = "videoId", description = "视频编号", required = true, example = "2048")
    public CommonResult<Boolean> deleteHistory(@RequestParam("userId") Long userId,
                                             @RequestParam("videoId") Long videoId) {
        historyService.deleteHistory(userId, videoId);
        return success(true);
    }

    @GetMapping("/get")
    @Operation(summary = "获得观看历史")
    @Parameter(name = "id", description = "观看历史编号", required = true, example = "1024")
    public CommonResult<AppHistoryRespVO> getHistory(@RequestParam("id") Long id) {
        HistoryDO history = historyService.getHistory(id);
        return success(AppHistoryConvert.INSTANCE.convert(history));
    }

    @GetMapping("/page")
    @Operation(summary = "获得观看历史分页")
    public CommonResult<PageResult<AppHistoryRespVO>> getHistoryPage(@Valid AppHistoryPageReqVO pageVO) {
        PageResult<HistoryDO> pageResult = historyService.getHistoryPage(pageVO);
        return success(AppHistoryConvert.INSTANCE.convertPage(pageResult));
    }

    @GetMapping("/list")
    @Operation(summary = "获得用户观看历史列表")
    @Parameter(name = "userId", description = "用户编号", required = true, example = "1024")
    public CommonResult<PageResult<AppHistoryRespVO>> getHistoryList(@RequestParam("userId") Long userId) {
        PageResult<HistoryDO> pageResult = historyService.getHistoryPage(new AppHistoryPageReqVO().setUserId(userId));
        return success(AppHistoryConvert.INSTANCE.convertPage(pageResult));
    }

    @GetMapping("/get-by-video")
    @Operation(summary = "获得用户观看历史")
    @Parameter(name = "userId", description = "用户编号", required = true, example = "1024")
    @Parameter(name = "videoId", description = "视频编号", required = true, example = "2048")
    public CommonResult<AppHistoryRespVO> getHistoryByVideo(@RequestParam("userId") Long userId,
                                                           @RequestParam("videoId") Long videoId) {
        HistoryDO history = historyService.getHistoryByUserIdAndVideoId(userId, videoId);
        return success(AppHistoryConvert.INSTANCE.convert(history));
    }

    @GetMapping("/video-page")
    @Operation(summary = "获得历史观看的视频分页")
    public CommonResult<PageResult<AppHistoryVideoRespVO>> getHistoryVideoPage(@Valid AppHistoryVideoPageReqVO pageVO) {
        PageResult<AppHistoryVideoRespVO> page = historyService.getHistoryVideoPage(getLoginUserId(), pageVO);
        return success(page);
    }

    @PostMapping("/play")
    @Operation(summary = "播放视频历史")
    public CommonResult<Long> playHistory(@Valid @RequestBody AppHistoryPlayReqVO playReqVO) {
        Long historyId = historyService.playHistory(getLoginUserId(), playReqVO.getVideoId(),
                playReqVO.getProgress(), playReqVO.getFinished());
        return success(historyId);
    }
}