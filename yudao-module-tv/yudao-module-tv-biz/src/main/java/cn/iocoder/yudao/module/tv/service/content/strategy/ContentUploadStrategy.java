package cn.iocoder.yudao.module.tv.service.content.strategy;

import cn.iocoder.yudao.module.tv.controller.admin.content.vo.ContentUploadReqVO;

import java.io.InputStream;
import java.util.Map;

/**
 * 内容上传策略接口
 *
 * <AUTHOR>
 */
public interface ContentUploadStrategy {

    /**
     * 获取支持的内容类型
     *
     * @return 内容类型
     */
    Integer getSupportedType();

    /**
     * 处理内容文件上传
     *
     * @param contentId 内容ID
     * @param configId 配置ID
     * @param contentInputStream 内容文件输入流
     * @param fileName 文件名
     * @param fileSize 文件大小
     * @param extParams 扩展参数
     * @param syncCompress 是否同步压缩
     * @param compressConfig 压缩配置
     * @return 处理结果信息
     */
    ContentUploadResult processContentFile(Long contentId, Long configId,
                                         InputStream contentInputStream,
                                         String fileName,
                                         Long fileSize,
                                         Map<String, Object> extParams,
                                         Boolean syncCompress,
                                         String compressConfig);

    /**
     * 内容上传结果
     */
    class ContentUploadResult {
        private boolean success;
        private String message;
        private Map<String, Object> data;

        public ContentUploadResult(boolean success, String message) {
            this.success = success;
            this.message = message;
        }

        public ContentUploadResult(boolean success, String message, Map<String, Object> data) {
            this.success = success;
            this.message = message;
            this.data = data;
        }

        // Getters and Setters
        public boolean isSuccess() { return success; }
        public void setSuccess(boolean success) { this.success = success; }
        public String getMessage() { return message; }
        public void setMessage(String message) { this.message = message; }
        public Map<String, Object> getData() { return data; }
        public void setData(Map<String, Object> data) { this.data = data; }
    }

}
