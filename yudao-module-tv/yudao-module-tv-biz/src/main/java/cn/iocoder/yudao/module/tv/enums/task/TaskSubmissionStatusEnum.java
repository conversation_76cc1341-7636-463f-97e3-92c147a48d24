package cn.iocoder.yudao.module.tv.enums.task;

import cn.iocoder.yudao.framework.common.core.IntArrayValuable;
import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Arrays;

/**
 * 任务提交审核状态枚举
 *
 * <AUTHOR>
 */
@Getter
@AllArgsConstructor
public enum TaskSubmissionStatusEnum implements IntArrayValuable {

    PENDING(0, "待审核"),
    APPROVED(1, "审核通过"),
    REJECTED(2, "审核拒绝");

    public static final int[] ARRAYS = Arrays.stream(values()).mapToInt(TaskSubmissionStatusEnum::getStatus).toArray();

    /**
     * 状态
     */
    private final Integer status;
    /**
     * 名字
     */
    private final String name;

    @Override
    public int[] array() {
        return ARRAYS;
    }

    public static TaskSubmissionStatusEnum valueOf(Integer status) {
        return Arrays.stream(values()).filter(item -> item.getStatus().equals(status)).findFirst().orElse(null);
    }

}
