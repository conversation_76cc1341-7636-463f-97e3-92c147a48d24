package cn.iocoder.yudao.module.tv.controller.app.linking.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotNull;

@Schema(description = "用户 APP - 深度链接 Request VO")
@Data
public class AppDeepLinkingCreateReqVO {

    @Schema(description = "短链接uuid", example = "userId+flag+serviceId")
    private String linking;

    @Schema(description = "重定向url", example = "https://baidu.com")
    @NotNull(message = "重定向url不能为空")
    private String redirectUrl;

    @Schema(description = "应用code", example = "baidu")
    @NotNull(message = "应用code不能为空")
    private String appCode;

    @Schema(description = "过期时间,分钟", example = "60")
    @NotNull(message = "过期时间不能为空")
    private long expire = 60 * 24;
} 