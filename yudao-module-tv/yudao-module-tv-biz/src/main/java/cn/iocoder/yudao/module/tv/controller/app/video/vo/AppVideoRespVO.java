package cn.iocoder.yudao.module.tv.controller.app.video.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;

@Schema(description = "用户 APP - 视频 Response VO")
@Data
public class AppVideoRespVO {

    @Schema(description = "视频编号", required = true, example = "1024")
    private Long id;

    @Schema(description = "视频标题", required = true, example = "示例视频")
    private String title;

    @Schema(description = "视频描述", example = "这是一个示例视频")
    private String description;

    @Schema(description = "视频封面URL", example = "https://www.example.com/cover.jpg")
    private String coverUrl;

    @Schema(description = "视频URL", example = "https://www.example.com/video.mp4")
    private String videoUrl;

    @Schema(description = "视频时长（秒）", example = "3600")
    private Integer duration;

    @Schema(description = "视频标签", example = "动作,冒险")
    private String tags;

    @Schema(description = "视频分类ID", example = "1024")
    private Long categoryId;

    @Schema(description = "播放次数", example = "1000")
    private Integer playCount;

    @Schema(description = "点赞数", example = "100")
    private Integer likeCount;

    @Schema(description = "收藏数", example = "50")
    private Integer favoriteCount;

    @Schema(description = "评论数", example = "30")
    private Integer commentCount;

    @Schema(description = "是否付费视频", required = true, example = "true")
    private Integer isPaid;

    @Schema(description = "视频价格", example = "9.99")
    private BigDecimal price;

    @Schema(description = "折扣价格", example = "6.99")
    private BigDecimal discountPrice;

    @Schema(description = "折扣开始时间", example = "2023-05-01 00:00:00")
    private LocalDateTime discountStartTime;

    @Schema(description = "折扣结束时间", example = "2023-05-31 23:59:59")
    private LocalDateTime discountEndTime;

    @Schema(description = "创建时间", required = true)
    private LocalDateTime createTime;
} 