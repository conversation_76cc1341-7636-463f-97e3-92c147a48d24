package cn.iocoder.yudao.module.tv.dal.mysql.prize;

import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.mybatis.core.mapper.BaseMapperX;
import cn.iocoder.yudao.framework.mybatis.core.query.LambdaQueryWrapperX;
import cn.iocoder.yudao.module.tv.controller.admin.prize.vo.PrizePageReqVO;
import cn.iocoder.yudao.module.tv.dal.dataobject.prize.PrizeDO;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
 * 奖品 Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface PrizeMapper extends BaseMapperX<PrizeDO> {

    default PageResult<PrizeDO> selectPage(PrizePageReqVO reqVO) {
        return selectPage(reqVO, new LambdaQueryWrapperX<PrizeDO>()
                .likeIfPresent(PrizeDO::getName, reqVO.getName())
                .eqIfPresent(PrizeDO::getType, reqVO.getType())
                .eqIfPresent(PrizeDO::getStatus, reqVO.getStatus())
                .betweenIfPresent(PrizeDO::getCreateTime, reqVO.getCreateTime())
                .orderByAsc(PrizeDO::getSort)
                .orderByDesc(PrizeDO::getId));
    }

    default List<PrizeDO> selectListByStatus(Integer status) {
        return selectList(new LambdaQueryWrapperX<PrizeDO>()
                .eq(PrizeDO::getStatus, status)
                .orderByAsc(PrizeDO::getSort)
                .orderByDesc(PrizeDO::getId));
    }

    default List<PrizeDO> selectListByType(Integer type) {
        return selectList(new LambdaQueryWrapperX<PrizeDO>()
                .eq(PrizeDO::getType, type)
                .eq(PrizeDO::getStatus, 1) // 只查询启用的
                .orderByAsc(PrizeDO::getSort)
                .orderByDesc(PrizeDO::getId));
    }

    default List<PrizeDO> selectAvailablePrizes() {
        return selectList(new LambdaQueryWrapperX<PrizeDO>()
                .eq(PrizeDO::getStatus, 1) // 启用状态
                .and(wrapper -> wrapper
                        .eq(PrizeDO::getStock, -1) // 无限制库存
                        .or()
                        .gt(PrizeDO::getStock, 0)) // 或者有库存
                .orderByAsc(PrizeDO::getSort)
                .orderByDesc(PrizeDO::getId));
    }

    default Long countByType(Integer type) {
        return selectCount(new LambdaQueryWrapperX<PrizeDO>()
                .eq(PrizeDO::getType, type));
    }

    default Long countByStatus(Integer status) {
        return selectCount(new LambdaQueryWrapperX<PrizeDO>()
                .eq(PrizeDO::getStatus, status));
    }
}
