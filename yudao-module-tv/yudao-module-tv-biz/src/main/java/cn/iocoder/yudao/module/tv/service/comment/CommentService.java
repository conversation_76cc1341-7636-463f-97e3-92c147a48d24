package cn.iocoder.yudao.module.tv.service.comment;

import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.module.tv.controller.app.comment.vo.AppTvCommentPageReqVO;
import cn.iocoder.yudao.module.tv.dal.dataobject.comment.CommentDO;

import java.util.List;

/**
 * 评论 Service 接口
 */
public interface CommentService {

    /**
     * 创建评论
     *
     * @param userId 用户编号
     * @param videoId 视频编号
     * @param parentId 父评论编号
     * @param content 评论内容
     * @return 评论编号
     */
    Long createComment(Long userId, Long videoId, Long parentId, String content);

    /**
     * 删除评论
     *
     * @param id 评论编号
     */
    void deleteComment(Long id);

    /**
     * 获得评论
     *
     * @param id 评论编号
     * @return 评论
     */
    CommentDO getComment(Long id);

    /**
     * 获得评论分页
     *
     * @param pageReqVO 分页查询
     * @return 评论分页
     */
    PageResult<CommentDO> getCommentPage(AppTvCommentPageReqVO pageReqVO);

    /**
     * 获得视频评论列表
     *
     * @param videoId 视频编号
     * @return 评论列表
     */
    List<CommentDO> getCommentListByVideoId(Long videoId);

    /**
     * 获得评论回复列表
     *
     * @param parentId 父评论编号
     * @return 评论列表
     */
    List<CommentDO> getCommentListByParentId(Long parentId);

    /**
     * 增加评论点赞数
     *
     * @param id 评论编号
     */
    void incrementLikeCount(Long id);

    /**
     * 减少评论点赞数
     *
     * @param id 评论编号
     */
    void decrementLikeCount(Long id);

    /**
     * 增加评论回复数
     *
     * @param id 评论编号
     */
    void incrementReplyCount(Long id);

    /**
     * 减少评论回复数
     *
     * @param id 评论编号
     */
    void decrementReplyCount(Long id);
} 