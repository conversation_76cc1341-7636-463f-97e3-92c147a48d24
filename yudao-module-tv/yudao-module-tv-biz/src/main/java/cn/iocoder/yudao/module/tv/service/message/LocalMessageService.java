package cn.iocoder.yudao.module.tv.service.message;

import cn.iocoder.yudao.module.tv.dal.dataobject.message.LocalMessageDO;

import java.util.List;

/**
 * 本地消息服务接口
 *
 * <AUTHOR>
 */
public interface LocalMessageService {

    /**
     * 发送消息（保存到本地消息表）
     *
     * @param topic 主题
     * @param messageType 消息类型
     * @param payload 消息内容
     * @param businessId 业务ID
     * @param businessType 业务类型
     * @return 消息ID
     */
    String sendMessage(String topic, String messageType, Object payload, String businessId, String businessType);

    /**
     * 发送消息（保存到本地消息表）
     *
     * @param topic 主题
     * @param messageType 消息类型
     * @param payload 消息内容
     * @param businessId 业务ID
     * @param businessType 业务类型
     * @param maxRetryCount 最大重试次数
     * @return 消息ID
     */
    String sendMessage(String topic, String messageType, Object payload, String businessId, String businessType, int maxRetryCount);

    /**
     * 标记消息为发送中
     *
     * @param messageId 消息ID
     * @return 是否成功
     */
    boolean markMessageSending(String messageId);

    /**
     * 标记消息发送成功
     *
     * @param messageId 消息ID
     * @return 是否成功
     */
    boolean markMessageSuccess(String messageId);

    /**
     * 标记消息发送失败
     *
     * @param messageId 消息ID
     * @param errorMessage 错误信息
     * @return 是否成功
     */
    boolean markMessageFailed(String messageId, String errorMessage);

    /**
     * 获取待发送的消息
     *
     * @param limit 限制数量
     * @return 消息列表
     */
    List<LocalMessageDO> getPendingMessages(int limit);

    /**
     * 获取需要重试的消息
     *
     * @param limit 限制数量
     * @return 消息列表
     */
    List<LocalMessageDO> getRetryMessages(int limit);

    /**
     * 根据消息ID获取消息
     *
     * @param messageId 消息ID
     * @return 消息
     */
    LocalMessageDO getMessage(String messageId);

    /**
     * 清理过期的成功消息
     *
     * @param days 保留天数
     * @return 清理数量
     */
    int cleanExpiredSuccessMessages(int days);

}
