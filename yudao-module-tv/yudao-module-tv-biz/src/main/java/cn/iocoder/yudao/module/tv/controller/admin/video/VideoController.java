package cn.iocoder.yudao.module.tv.controller.admin.video;

import cn.hutool.core.bean.BeanUtil;
import cn.iocoder.yudao.framework.apilog.core.annotation.ApiAccessLog;
import cn.iocoder.yudao.framework.common.pojo.CommonResult;
import cn.iocoder.yudao.framework.common.pojo.PageParam;
import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.common.util.object.BeanUtils;
import cn.iocoder.yudao.framework.excel.core.util.ExcelUtils;
import cn.iocoder.yudao.module.tv.controller.admin.video.vo.VideoPageReqVO;
import cn.iocoder.yudao.module.tv.controller.admin.video.vo.VideoRespVO;
import cn.iocoder.yudao.module.tv.controller.admin.video.vo.VideoSaveReqVO;
import cn.iocoder.yudao.module.tv.controller.admin.video.vo.VideoUpdateStatusReqVO;
import cn.iocoder.yudao.module.tv.convert.video.VideoConvert;
import cn.iocoder.yudao.module.tv.dal.dataobject.video.VideoDO;
import cn.iocoder.yudao.module.tv.service.video.VideoService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import java.io.IOException;
import java.util.List;

import static cn.iocoder.yudao.framework.apilog.core.enums.OperateTypeEnum.EXPORT;
import static cn.iocoder.yudao.framework.common.pojo.CommonResult.success;

@Tag(name = "管理后台 - 视频")
@RestController
@RequestMapping("/tv/video")
@Validated
public class VideoController {

    @Resource
    private VideoService videoService;

    @PostMapping("/create")
    @Operation(summary = "创建视频")
    @PreAuthorize("@ss.hasPermission('tv:video:create')")
    public CommonResult<Long> createVideo(@Valid @RequestBody VideoSaveReqVO createReqVO) {
        return success(videoService.createVideo(createReqVO));
    }

    @PutMapping("/update")
    @Operation(summary = "更新视频")
    @PreAuthorize("@ss.hasPermission('tv:video:update')")
    public CommonResult<Boolean> updateVideo(@Valid @RequestBody VideoSaveReqVO updateReqVO) {
        videoService.updateVideo(updateReqVO);
        return success(true);
    }

    @PutMapping("/update-status")
    @Operation(summary = "更新视频状态")
    @PreAuthorize("@ss.hasPermission('tv:video:update')")
    public CommonResult<Boolean> updateStatusVideo(@Valid @RequestBody VideoUpdateStatusReqVO updateReqVO) {
        videoService.updateVideo(BeanUtils.toBean(updateReqVO, VideoDO.class));
        return success(true);
    }

    @DeleteMapping("/delete")
    @Operation(summary = "删除视频")
    @Parameter(name = "id", description = "编号", required = true)
    @PreAuthorize("@ss.hasPermission('tv:video:delete')")
    public CommonResult<Boolean> deleteVideo(@RequestParam("id") Long id) {
        videoService.deleteVideo(id);
        return success(true);
    }

    @GetMapping("/get")
    @Operation(summary = "获得视频")
    @Parameter(name = "id", description = "编号", required = true, example = "1024")
    @PreAuthorize("@ss.hasPermission('tv:video:query')")
    public CommonResult<VideoRespVO> getVideo(@RequestParam("id") Long id) {
        VideoDO video = videoService.getVideo(id);
        return success(BeanUtils.toBean(video, VideoRespVO.class));
    }

    @GetMapping("/page")
    @Operation(summary = "获得视频分页")
    @PreAuthorize("@ss.hasPermission('tv:video:query')")
    public CommonResult<PageResult<VideoRespVO>> getVideoPage(@Valid VideoPageReqVO pageReqVO) {
        PageResult<VideoDO> pageResult = videoService.getVideoPage(pageReqVO);
        return success(BeanUtils.toBean(pageResult, VideoRespVO.class));
    }

    public static void main(String[] args) {
        VideoDO videoDO = new VideoDO();
        VideoRespVO videoRespVO = new VideoRespVO();
        VideoRespVO bean = BeanUtil.toBean(videoDO,VideoRespVO::new,null );
        System.out.println(bean);
    }

    @GetMapping("/export-excel")
    @Operation(summary = "导出视频 Excel")
    @PreAuthorize("@ss.hasPermission('tv:video:export')")
    @ApiAccessLog(operateType = EXPORT)
    public void exportVideoExcel(@Valid VideoPageReqVO pageReqVO,
                                 HttpServletResponse response) throws IOException {
        pageReqVO.setPageSize(PageParam.PAGE_SIZE_NONE);
        List<VideoDO> list = videoService.getVideoPage(pageReqVO).getList();
        // 导出 Excel
        ExcelUtils.write(response, "视频.xls", "数据", VideoRespVO.class,
                BeanUtils.toBean(list, VideoRespVO.class));
    }

    @GetMapping("/list-by-category")
    @Operation(summary = "获得分类视频列表")
    @Parameter(name = "categoryId", description = "分类编号", required = true, example = "1024")
    public CommonResult<List<VideoRespVO>> getVideoListByCategory(@RequestParam("categoryId") Long categoryId) {
        List<VideoDO> list = videoService.getVideoListByCategory(categoryId);
        return success(VideoConvert.INSTANCE.convertList(list));
    }

    @GetMapping("/list-by-tag")
    @Operation(summary = "获得标签视频列表")
    @Parameter(name = "tag", description = "标签", required = true, example = "动作")
    public CommonResult<List<VideoRespVO>> getVideoListByTag(@RequestParam("tag") String tag) {
        List<VideoDO> list = videoService.getVideoListByTag(tag);
        return success(VideoConvert.INSTANCE.convertList(list));
    }

    @GetMapping("/recommend")
    @Operation(summary = "获得推荐视频列表")
    @Parameter(name = "userId", description = "用户编号", required = true, example = "1024")
    @Parameter(name = "limit", description = "限制数量", required = true, example = "10")
    public CommonResult<PageResult<VideoDO>> getRecommendVideos(@RequestParam("userId") Long userId,
                                                             @RequestParam("limit") Integer limit) {
        PageResult<VideoDO> recommendVideos = videoService.getRecommendVideos(userId, limit);
        return success(recommendVideos);
    }
}