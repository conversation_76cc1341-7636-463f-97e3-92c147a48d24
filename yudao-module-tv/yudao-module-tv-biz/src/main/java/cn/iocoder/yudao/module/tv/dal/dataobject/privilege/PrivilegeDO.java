package cn.iocoder.yudao.module.tv.dal.dataobject.privilege;

import cn.iocoder.yudao.framework.mybatis.core.dataobject.BaseDO;
import com.baomidou.mybatisplus.annotation.KeySequence;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.*;
import lombok.experimental.Accessors;

import java.math.BigDecimal;

/**
 * 特权 DO
 *
 * <AUTHOR>
 */
@TableName("tv_privilege")
@KeySequence("tv_privilege_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Accessors(chain = true)
public class PrivilegeDO extends BaseDO {

    /**
     * 编号
     */
    @TableId
    private Long id;
    /**
     * 特权名称
     */
    private String name;
    /**
     * 特权描述
     */
    private String description;
    /**
     * 价格
     */
    private BigDecimal price;
    /**
     * 有效期（天）
     */
    private Integer duration;
    /**
     * 状态：0-禁用，1-启用
     */
    private Integer status;

} 