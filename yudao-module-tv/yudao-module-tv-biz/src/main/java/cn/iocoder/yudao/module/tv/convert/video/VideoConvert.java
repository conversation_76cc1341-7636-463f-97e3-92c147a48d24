package cn.iocoder.yudao.module.tv.convert.video;

import cn.iocoder.yudao.module.tv.controller.admin.video.vo.VideoCreateReqVO;
import cn.iocoder.yudao.module.tv.controller.admin.video.vo.VideoRespVO;
import cn.iocoder.yudao.module.tv.controller.admin.video.vo.VideoUpdateReqVO;
import cn.iocoder.yudao.module.tv.dal.dataobject.video.VideoDO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * 视频 Convert
 */
@Mapper
public interface VideoConvert {

    VideoConvert INSTANCE = Mappers.getMapper(VideoConvert.class);

    VideoDO convert(VideoCreateReqVO bean);

    VideoDO convert(VideoUpdateReqVO bean);

    VideoRespVO convert(VideoDO bean);

    List<VideoRespVO> convertList(List<VideoDO> list);
} 