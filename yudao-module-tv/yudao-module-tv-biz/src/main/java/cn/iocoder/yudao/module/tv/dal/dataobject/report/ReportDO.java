package cn.iocoder.yudao.module.tv.dal.dataobject.report;

import cn.iocoder.yudao.framework.tenant.core.db.TenantBaseDO;
import com.baomidou.mybatisplus.annotation.*;
import lombok.*;

import java.time.LocalDateTime;

/**
 * 举报反馈 DO
 *
 * <AUTHOR>
 */
@TableName("tv_report")
@KeySequence("tv_report_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ReportDO extends TenantBaseDO {

    /**
     * 主键
     */
    @TableId
    private Long id;

    /**
     * 业务类型：content-内容举报，user-用户举报等
     */
    private String businessType;

    /**
     * 业务ID，如内容ID、用户ID等
     */
    private Long businessId;

    /**
     * 举报类型：1-色情低俗，2-违法违规，3-虚假信息，4-恶意营销，5-侵权盗版，6-其他
     */
    private Integer reportType;

    /**
     * 举报原因描述
     */
    private String reportReason;

    /**
     * 举报截图，JSON数组格式
     */
    private String reportImages;

    /**
     * 举报人ID，匿名举报时为NULL
     */
    private Long reporterId;

    /**
     * 举报人IP地址
     */
    private String reporterIp;

    /**
     * 处理状态：0-待处理，1-处理中，2-已处理，3-已驳回
     */
    private Integer status;

    /**
     * 处理结果说明
     */
    private String handleResult;

    /**
     * 处理人ID
     */
    private Long handleUserId;

    /**
     * 处理时间
     */
    private LocalDateTime handleTime;

}
