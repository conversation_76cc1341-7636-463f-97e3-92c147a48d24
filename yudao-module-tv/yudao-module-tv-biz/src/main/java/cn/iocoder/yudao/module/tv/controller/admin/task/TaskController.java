package cn.iocoder.yudao.module.tv.controller.admin.task;

import cn.iocoder.yudao.framework.common.pojo.CommonResult;
import cn.iocoder.yudao.framework.common.util.object.BeanUtils;
import cn.iocoder.yudao.module.tv.controller.admin.task.vo.TaskSaveReqVO;
import cn.iocoder.yudao.module.tv.controller.admin.task.vo.TaskSubmissionAuditReqVO;
import cn.iocoder.yudao.module.tv.dal.dataobject.task.TaskDO;
import cn.iocoder.yudao.module.tv.dal.dataobject.task.TaskSubmissionDO;
import cn.iocoder.yudao.module.tv.service.task.TaskService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.List;

import static cn.iocoder.yudao.framework.common.pojo.CommonResult.success;
import static cn.iocoder.yudao.framework.security.core.util.SecurityFrameworkUtils.getLoginUserId;

@Tag(name = "管理后台 - 任务系统")
@RestController
@RequestMapping("/tv/task")
@Validated
public class TaskController {

    @Resource
    private TaskService taskService;

    @PostMapping("/create")
    @Operation(summary = "创建任务")
    @PreAuthorize("@ss.hasPermission('tv:task:create')")
    public CommonResult<Long> createTask(@Valid @RequestBody TaskSaveReqVO createReqVO) {
        return success(taskService.createTask(createReqVO));
    }

    @PutMapping("/update")
    @Operation(summary = "更新任务")
    @PreAuthorize("@ss.hasPermission('tv:task:update')")
    public CommonResult<Boolean> updateTask(@Valid @RequestBody TaskSaveReqVO updateReqVO) {
        taskService.updateTask(updateReqVO);
        return success(true);
    }

    @DeleteMapping("/delete")
    @Operation(summary = "删除任务")
    @Parameter(name = "id", description = "编号", required = true)
    @PreAuthorize("@ss.hasPermission('tv:task:delete')")
    public CommonResult<Boolean> deleteTask(@RequestParam("id") Long id) {
        taskService.deleteTask(id);
        return success(true);
    }

    @GetMapping("/get")
    @Operation(summary = "获得任务")
    @Parameter(name = "id", description = "编号", required = true, example = "1024")
    @PreAuthorize("@ss.hasPermission('tv:task:query')")
    public CommonResult<TaskDO> getTask(@RequestParam("id") Long id) {
        TaskDO task = taskService.getTask(id);
        return success(task);
    }

    @GetMapping("/list")
    @Operation(summary = "获得任务列表")
    @PreAuthorize("@ss.hasPermission('tv:task:query')")
    public CommonResult<List<TaskDO>> getTaskList() {
        List<TaskDO> list = taskService.getTaskList();
        return success(list);
    }

    @GetMapping("/submission/pending")
    @Operation(summary = "获得待审核的任务提交列表")
    @PreAuthorize("@ss.hasPermission('tv:task:audit')")
    public CommonResult<List<TaskSubmissionDO>> getPendingTaskSubmissions() {
        List<TaskSubmissionDO> list = taskService.getPendingTaskSubmissions();
        return success(list);
    }

    @GetMapping("/submission/get")
    @Operation(summary = "获得任务提交记录")
    @Parameter(name = "id", description = "编号", required = true, example = "1024")
    @PreAuthorize("@ss.hasPermission('tv:task:query')")
    public CommonResult<TaskSubmissionDO> getTaskSubmission(@RequestParam("id") Long id) {
        TaskSubmissionDO submission = taskService.getTaskSubmission(id);
        return success(submission);
    }

    @PutMapping("/submission/audit")
    @Operation(summary = "审核任务提交")
    @PreAuthorize("@ss.hasPermission('tv:task:audit')")
    public CommonResult<Boolean> auditTaskSubmission(@Valid @RequestBody TaskSubmissionAuditReqVO auditReqVO) {
        taskService.auditTaskSubmission(auditReqVO, getLoginUserId());
        return success(true);
    }

}
