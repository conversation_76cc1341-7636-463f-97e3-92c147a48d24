package cn.iocoder.yudao.module.tv.controller.admin.content.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import org.springframework.web.multipart.MultipartFile;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.Map;

/**
 * TV内容上传请求VO
 *
 * <AUTHOR>
 */
@Schema(description = "管理后台 - TV内容上传 Request VO")
@Data
public class ContentUploadReqVO {

    @Schema(description = "标题", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotEmpty(message = "标题不能为空")
    private String title;

    @Schema(description = "描述", example = "随便")
    private String description;

    @Schema(description = "分类编号", requiredMode = Schema.RequiredMode.REQUIRED, example = "3708")
    @NotNull(message = "分类编号不能为空")
    private Long categoryId;

    @Schema(description = "发布时间")
    private LocalDateTime publishTime = LocalDateTime.now();

    @Schema(description = "是否付费视频：0-免费，1-付费", requiredMode = Schema.RequiredMode.REQUIRED, example = "1458")
    @NotNull(message = "是否付费视频：0-免费，1-付费不能为空")
    private Integer isPaid;

    @Schema(description = "视频价格", requiredMode = Schema.RequiredMode.REQUIRED, example = "19267")
    @NotNull(message = "视频价格不能为空")
    private BigDecimal price;


    @Schema(description = "内容类型：0-单个视频，1-多个视频，2-图集，3-图文，4-合集，5-图片和视频混合", 
            requiredMode = Schema.RequiredMode.REQUIRED, example = "0")
    @NotNull(message = "内容类型不能为空")
    private Integer type;

    @Schema(description = "封面文件", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "封面文件不能为空")
    private MultipartFile coverFile;

    @Schema(description = "内容文件（视频/压缩包等）", requiredMode = Schema.RequiredMode.REQUIRED)
    private MultipartFile contentFile;

    @Schema(description = "扩展参数（JSON格式，用于不同类型的特殊配置）", example = "{\"collectionId\":123}")
    private Map<String, Object> extParams;

    @Schema(description = "是否启用同步压缩", example = "true")
    private Boolean syncCompress = true;

    @Schema(description = "压缩配置（JSON格式）",
            example = "{\"videoBitrate\":1500,\"audioBitrate\":128,\"resolution\":\"1920x1080\"}")
    private String compressConfig;

    @Schema(description = "存储配置ID", example = "1")
    private Long configId;
}
