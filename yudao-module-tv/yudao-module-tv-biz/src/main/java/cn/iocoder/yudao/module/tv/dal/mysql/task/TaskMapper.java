package cn.iocoder.yudao.module.tv.dal.mysql.task;

import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.mybatis.core.query.LambdaQueryWrapperX;
import cn.iocoder.yudao.framework.mybatis.core.mapper.BaseMapperX;
import cn.iocoder.yudao.module.tv.dal.dataobject.task.TaskDO;
import cn.iocoder.yudao.module.tv.enums.task.TaskStatusEnum;
import org.apache.ibatis.annotations.Mapper;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 任务定义 Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface TaskMapper extends BaseMapperX<TaskDO> {

    /**
     * 查询启用的任务列表
     *
     * @return 启用的任务列表
     */
    default List<TaskDO> selectEnabledTasks() {
        return selectList(new LambdaQueryWrapperX<TaskDO>()
                .eq(TaskDO::getStatus, TaskStatusEnum.ENABLED.getStatus())
                .and(wrapper -> wrapper
                        .isNull(TaskDO::getStartTime)
                        .or()
                        .le(TaskDO::getStartTime, LocalDateTime.now()))
                .and(wrapper -> wrapper
                        .isNull(TaskDO::getEndTime)
                        .or()
                        .ge(TaskDO::getEndTime, LocalDateTime.now()))
                .orderByAsc(TaskDO::getSort)
                .orderByDesc(TaskDO::getCreateTime));
    }

    /**
     * 根据类型查询启用的任务
     *
     * @param type 任务类型
     * @return 任务列表
     */
    default List<TaskDO> selectEnabledTasksByType(Integer type) {
        return selectList(new LambdaQueryWrapperX<TaskDO>()
                .eq(TaskDO::getType, type)
                .eq(TaskDO::getStatus, TaskStatusEnum.ENABLED.getStatus())
                .and(wrapper -> wrapper
                        .isNull(TaskDO::getStartTime)
                        .or()
                        .le(TaskDO::getStartTime, LocalDateTime.now()))
                .and(wrapper -> wrapper
                        .isNull(TaskDO::getEndTime)
                        .or()
                        .ge(TaskDO::getEndTime, LocalDateTime.now()))
                .orderByAsc(TaskDO::getSort));
    }

}
