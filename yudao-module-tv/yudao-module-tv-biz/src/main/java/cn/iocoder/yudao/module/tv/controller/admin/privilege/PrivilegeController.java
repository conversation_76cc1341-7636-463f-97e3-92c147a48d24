package cn.iocoder.yudao.module.tv.controller.admin.privilege;

import cn.iocoder.yudao.framework.common.pojo.CommonResult;
import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.module.tv.controller.admin.privilege.vo.PrivilegePageReqVO;
import cn.iocoder.yudao.module.tv.controller.admin.privilege.vo.PrivilegeSaveReqVO;
import cn.iocoder.yudao.module.tv.dal.dataobject.privilege.PrivilegeDO;
import cn.iocoder.yudao.module.tv.service.privilege.PrivilegeService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;

import static cn.iocoder.yudao.framework.common.pojo.CommonResult.success;

@Tag(name = "管理后台 - 特权")
@RestController
@RequestMapping("/tv/privilege")
@Validated
public class PrivilegeController {

    @Resource
    private PrivilegeService privilegeService;

    @PostMapping("/create")
    @Operation(summary = "创建特权")
    @PreAuthorize("@ss.hasPermission('tv:privilege:create')")
    public CommonResult<Long> createPrivilege(@Valid @RequestBody PrivilegeSaveReqVO createReqVO) {
        return success(privilegeService.createPrivilege(createReqVO));
    }

    @PutMapping("/update")
    @Operation(summary = "更新特权")
    @PreAuthorize("@ss.hasPermission('tv:privilege:update')")
    public CommonResult<Boolean> updatePrivilege(@Valid @RequestBody PrivilegeSaveReqVO updateReqVO) {
        privilegeService.updatePrivilege(updateReqVO);
        return success(true);
    }

    @DeleteMapping("/delete")
    @Operation(summary = "删除特权")
    @Parameter(name = "id", description = "编号", required = true)
    @PreAuthorize("@ss.hasPermission('tv:privilege:delete')")
    public CommonResult<Boolean> deletePrivilege(@RequestParam("id") Long id) {
        privilegeService.deletePrivilege(id);
        return success(true);
    }

    @GetMapping("/get")
    @Operation(summary = "获得特权")
    @Parameter(name = "id", description = "编号", required = true, example = "1")
    @PreAuthorize("@ss.hasPermission('tv:privilege:query')")
    public CommonResult<PrivilegeDO> getPrivilege(@RequestParam("id") Long id) {
        return success(privilegeService.getPrivilege(id));
    }

    @GetMapping("/page")
    @Operation(summary = "获得特权分页")
    @PreAuthorize("@ss.hasPermission('tv:privilege:query')")
    public CommonResult<PageResult<PrivilegeDO>> getPrivilegePage(@Valid PrivilegePageReqVO pageVO) {
        return success(privilegeService.getPrivilegePage(pageVO));
    }

} 