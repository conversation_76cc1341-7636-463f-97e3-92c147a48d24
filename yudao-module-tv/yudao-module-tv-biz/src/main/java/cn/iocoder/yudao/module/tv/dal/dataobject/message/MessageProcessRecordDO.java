package cn.iocoder.yudao.module.tv.dal.dataobject.message;

import cn.iocoder.yudao.framework.tenant.core.db.TenantBaseDO;
import com.baomidou.mybatisplus.annotation.*;
import lombok.*;

import java.time.LocalDateTime;

/**
 * 消息处理记录 DO
 *
 * <AUTHOR>
 */
@TableName("tv_message_process_record")
@KeySequence("tv_message_process_record_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class MessageProcessRecordDO extends TenantBaseDO {

    /**
     * 主键
     */
    @TableId
    private Long id;

    /**
     * 消息ID
     */
    private String messageId;

    /**
     * 消费者组
     */
    private String consumerGroup;

    /**
     * 消费者ID
     */
    private String consumerId;

    /**
     * 处理状态：0-处理中，1-处理成功，2-处理失败
     */
    private Integer processStatus;

    /**
     * 处理结果
     */
    private String processResult;

    /**
     * 开始处理时间
     */
    private LocalDateTime startTime;

    /**
     * 结束处理时间
     */
    private LocalDateTime endTime;

}
