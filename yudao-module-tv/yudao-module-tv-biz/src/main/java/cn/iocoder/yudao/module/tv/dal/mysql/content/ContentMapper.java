package cn.iocoder.yudao.module.tv.dal.mysql.content;

import java.time.LocalDateTime;
import java.util.*;

import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.mybatis.core.query.LambdaQueryWrapperX;
import cn.iocoder.yudao.framework.mybatis.core.mapper.BaseMapperX;
import cn.iocoder.yudao.module.tv.dal.dataobject.content.ContentDO;
import org.apache.ibatis.annotations.Mapper;
import cn.iocoder.yudao.module.tv.controller.admin.content.vo.*;
import cn.iocoder.yudao.module.tv.controller.app.content.vo.AppContentSearchReqVO;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Update;
import cn.hutool.core.util.StrUtil;

/**
 * 内容主体 Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface ContentMapper extends BaseMapperX<ContentDO> {


    /**
     * 增加播放次数
     *
     * @param id 视频编号
     * @return 影响行数
     */
    @Update("UPDATE tv_content SET play_count = play_count + 1 WHERE id = #{id}")
    void incrementPlayCount(@Param("id") Long id);

    /**
     * 增加点赞次数
     *
     * @param id 视频编号
     * @return 影响行数
     */
    @Update("UPDATE tv_content SET like_count = like_count + 1 WHERE id = #{id}")
    void incrementLikeCount(@Param("id") Long id);

    /**
     * 减少点赞次数
     *
     * @param id 视频编号
     * @return 影响行数
     */
    @Update("UPDATE tv_content SET like_count = like_count - 1 WHERE id = #{id} AND like_count > 0")
    void decrementLikeCount(@Param("id") Long id);

    /**
     * 增加收藏次数
     *
     * @param id 视频编号
     * @return 影响行数
     */
    @Update("UPDATE tv_content SET favorite_count = favorite_count + 1 WHERE id = #{id}")
    void incrementFavoriteCount(@Param("id") Long id);

    /**
     * 减少收藏次数
     *
     * @param id 视频编号
     * @return 影响行数
     */
    @Update("UPDATE tv_content SET favorite_count = favorite_count - 1 WHERE id = #{id} AND favorite_count > 0")
    void decrementFavoriteCount(@Param("id") Long id);

    default int updatePublishTime(Long id, LocalDateTime publishTime) {
        return updateById(new ContentDO().setId(id).setPublishTime(publishTime));
    }

    default PageResult<ContentDO> selectPage(ContentPageReqVO reqVO) {
        return selectPage(reqVO, new LambdaQueryWrapperX<ContentDO>()
                .eqIfPresent(ContentDO::getTitle, reqVO.getTitle())
                .eqIfPresent(ContentDO::getDescription, reqVO.getDescription())
                .eqIfPresent(ContentDO::getType, reqVO.getType())
                .eqIfPresent(ContentDO::getPlayCount, reqVO.getPlayCount())
                .eqIfPresent(ContentDO::getLikeCount, reqVO.getLikeCount())
                .eqIfPresent(ContentDO::getFavoriteCount, reqVO.getFavoriteCount())
                .eqIfPresent(ContentDO::getCommentCount, reqVO.getCommentCount())
                .eqIfPresent(ContentDO::getRecommendWeight, reqVO.getRecommendWeight())
                .eqIfPresent(ContentDO::getQualityScore, reqVO.getQualityScore())
                .eqIfPresent(ContentDO::getStatus, reqVO.getStatus())
                .eqIfPresent(ContentDO::getCategoryId, reqVO.getCategoryId())
                .betweenIfPresent(ContentDO::getPublishTime, reqVO.getPublishTime())
                .eqIfPresent(ContentDO::getIsPaid, reqVO.getIsPaid())
                .eqIfPresent(ContentDO::getPrice, reqVO.getPrice())
                .eqIfPresent(ContentDO::getDiscountPrice, reqVO.getDiscountPrice())
                .betweenIfPresent(ContentDO::getDiscountStartTime, reqVO.getDiscountStartTime())
                .betweenIfPresent(ContentDO::getDiscountEndTime, reqVO.getDiscountEndTime())
                .betweenIfPresent(ContentDO::getCreateTime, reqVO.getCreateTime())
                .orderByDesc(ContentDO::getPublishTime));
    }

    /**
     * 搜索内容分页
     */
    default PageResult<ContentDO> selectSearchPage(AppContentSearchReqVO reqVO) {
        LambdaQueryWrapperX<ContentDO> queryWrapper = new LambdaQueryWrapperX<ContentDO>();

        // 关键词搜索：标题或描述包含关键词
        if (StrUtil.isNotBlank(reqVO.getKeyword())) {
            queryWrapper.and(wrapper ->
                wrapper.like(ContentDO::getTitle, reqVO.getKeyword())
                       .or()
                       .like(ContentDO::getDescription, reqVO.getKeyword()));
        }

        // 基础条件过滤
        queryWrapper.eqIfPresent(ContentDO::getType, reqVO.getType())
                .eqIfPresent(ContentDO::getCategoryId, reqVO.getCategoryId())
                .eqIfPresent(ContentDO::getStatus, reqVO.getStatus())
                .eqIfPresent(ContentDO::getIsPaid, reqVO.getIsPaid())
                // 价格范围过滤
                .geIfPresent(ContentDO::getPrice, reqVO.getMinPrice())
                .leIfPresent(ContentDO::getPrice, reqVO.getMaxPrice())
                // 发布时间范围过滤
                .geIfPresent(ContentDO::getPublishTime, reqVO.getPublishTimeStart())
                .leIfPresent(ContentDO::getPublishTime, reqVO.getPublishTimeEnd())
                // 统计数据过滤
                .geIfPresent(ContentDO::getPlayCount, reqVO.getMinPlayCount())
                .geIfPresent(ContentDO::getLikeCount, reqVO.getMinLikeCount())
                .eqIfPresent(ContentDO::getCreator, reqVO.getCreator())
                .geIfPresent(ContentDO::getFavoriteCount, reqVO.getMinFavoriteCount());

        // 动态排序
        applySorting(queryWrapper, reqVO.getSortField(), reqVO.getSortOrder());

        return selectPage(reqVO, queryWrapper);
    }

    /**
     * 应用排序条件
     */
    default void applySorting(LambdaQueryWrapperX<ContentDO> queryWrapper, String sortField, String sortOrder) {
        if (StrUtil.isBlank(sortField)) {
            sortField = "createTime";
        }
        if (StrUtil.isBlank(sortOrder)) {
            sortOrder = "desc";
        }

        boolean isAsc = "asc".equalsIgnoreCase(sortOrder);

        switch (sortField) {
            case "createTime":
                if (isAsc) queryWrapper.orderByAsc(ContentDO::getCreateTime);
                else queryWrapper.orderByDesc(ContentDO::getCreateTime);
                break;
            case "publishTime":
                if (isAsc) queryWrapper.orderByAsc(ContentDO::getPublishTime);
                else queryWrapper.orderByDesc(ContentDO::getPublishTime);
                break;
            case "playCount":
                if (isAsc) queryWrapper.orderByAsc(ContentDO::getPlayCount);
                else queryWrapper.orderByDesc(ContentDO::getPlayCount);
                break;
            case "likeCount":
                if (isAsc) queryWrapper.orderByAsc(ContentDO::getLikeCount);
                else queryWrapper.orderByDesc(ContentDO::getLikeCount);
                break;
            case "favoriteCount":
                if (isAsc) queryWrapper.orderByAsc(ContentDO::getFavoriteCount);
                else queryWrapper.orderByDesc(ContentDO::getFavoriteCount);
                break;
            case "price":
                if (isAsc) queryWrapper.orderByAsc(ContentDO::getPrice);
                else queryWrapper.orderByDesc(ContentDO::getPrice);
                break;
            case "recommendWeight":
                if (isAsc) queryWrapper.orderByAsc(ContentDO::getRecommendWeight);
                else queryWrapper.orderByDesc(ContentDO::getRecommendWeight);
                break;
            default:
                // 默认按创建时间降序
                queryWrapper.orderByDesc(ContentDO::getCreateTime);
                break;
        }
    }

}