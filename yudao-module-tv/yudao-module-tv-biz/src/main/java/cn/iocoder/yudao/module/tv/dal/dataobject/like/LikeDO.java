package cn.iocoder.yudao.module.tv.dal.dataobject.like;

import cn.iocoder.yudao.framework.mybatis.core.dataobject.BaseDO;
import cn.iocoder.yudao.framework.mybatis.core.dataobject.PhysicDo;
import com.baomidou.mybatisplus.annotation.KeySequence;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 点赞 DO
 */
@TableName("tv_like")
@KeySequence("tv_like_seq")
@Data
@EqualsAndHashCode(callSuper = true)
public class LikeDO extends PhysicDo {

    /**
     * 点赞编号
     */
    @TableId
    private Long id;

    /**
     * 用户编号
     */
    private Long userId;

    /**
     * 视频编号
     */
    private Long videoId;
} 