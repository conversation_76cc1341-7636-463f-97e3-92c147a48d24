package cn.iocoder.yudao.module.tv.controller.app.comment;

import cn.iocoder.yudao.framework.common.pojo.CommonResult;
import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.module.tv.controller.app.comment.vo.AppCommentCreateReqVO;
import cn.iocoder.yudao.module.tv.controller.app.comment.vo.AppTvCommentPageReqVO;
import cn.iocoder.yudao.module.tv.controller.app.comment.vo.AppCommentRespVO;
import cn.iocoder.yudao.module.tv.convert.comment.AppCommentConvert;
import cn.iocoder.yudao.module.tv.dal.dataobject.comment.CommentDO;
import cn.iocoder.yudao.module.tv.service.comment.CommentService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;

import static cn.iocoder.yudao.framework.common.pojo.CommonResult.success;

@Tag(name = "用户 APP - 评论")
@RestController
@RequestMapping("/tv/comment")
@Validated
public class AppCommentController {

    @Resource
    private CommentService commentService;

    @PostMapping("/create")
    @Operation(summary = "创建评论")
    public CommonResult<Long> createComment(@Valid @RequestBody AppCommentCreateReqVO createReqVO) {
        return success(commentService.createComment(createReqVO.getUserId(), createReqVO.getVideoId(),
                createReqVO.getParentId(), createReqVO.getContent()));
    }

    @DeleteMapping("/delete")
    @Operation(summary = "删除评论")
    @Parameter(name = "id", description = "评论编号", required = true, example = "1024")
    public CommonResult<Boolean> deleteComment(@RequestParam("id") Long id) {
        commentService.deleteComment(id);
        return success(true);
    }

    @GetMapping("/get")
    @Operation(summary = "获得评论")
    @Parameter(name = "id", description = "评论编号", required = true, example = "1024")
    public CommonResult<AppCommentRespVO> getComment(@RequestParam("id") Long id) {
        CommentDO comment = commentService.getComment(id);
        return success(AppCommentConvert.INSTANCE.convert(comment));
    }

    @GetMapping("/page")
    @Operation(summary = "获得评论分页")
    public CommonResult<PageResult<AppCommentRespVO>> getCommentPage(@Valid AppTvCommentPageReqVO pageVO) {
        PageResult<CommentDO> pageResult = commentService.getCommentPage(pageVO);
        return success(AppCommentConvert.INSTANCE.convertPage(pageResult));
    }

    @GetMapping("/list-by-video")
    @Operation(summary = "获得视频评论列表")
    @Parameter(name = "videoId", description = "视频编号", required = true, example = "1024")
    public CommonResult<PageResult<AppCommentRespVO>> getCommentListByVideo(@RequestParam("videoId") Long videoId) {
        PageResult<CommentDO> pageResult = commentService.getCommentPage(new AppTvCommentPageReqVO().setVideoId(videoId));
        return success(AppCommentConvert.INSTANCE.convertPage(pageResult));
    }

    @GetMapping("/list-by-parent")
    @Operation(summary = "获得评论回复列表")
    @Parameter(name = "parentId", description = "父评论编号", required = true, example = "1024")
    public CommonResult<PageResult<AppCommentRespVO>> getCommentListByParent(@RequestParam("parentId") Long parentId) {
        PageResult<CommentDO> pageResult = commentService.getCommentPage(new AppTvCommentPageReqVO().setParentId(parentId));
        return success(AppCommentConvert.INSTANCE.convertPage(pageResult));
    }
} 