package cn.iocoder.yudao.module.tv.service.group;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.StrUtil;
import cn.iocoder.yudao.framework.common.enums.CommonStatusEnum;
import cn.iocoder.yudao.framework.common.exception.util.ServiceExceptionUtil;
import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.module.member.api.point.MemberPointApi;
import cn.iocoder.yudao.module.member.api.user.MemberUserApi;
import cn.iocoder.yudao.module.member.api.user.dto.MemberUserRespDTO;
import cn.iocoder.yudao.module.member.enums.point.MemberPointBizTypeEnum;
import cn.iocoder.yudao.module.pay.api.wallet.PayWalletApi;
import cn.iocoder.yudao.module.tv.controller.admin.group.vo.GroupInfoPageReqVO;
import cn.iocoder.yudao.module.tv.controller.admin.group.vo.GroupOrderPageReqVO;
import cn.iocoder.yudao.module.tv.controller.admin.group.vo.GroupResultPageReqVO;
import cn.iocoder.yudao.module.tv.controller.app.group.vo.AppGroupCreateReqVO;
import cn.iocoder.yudao.module.tv.controller.app.group.vo.AppGroupDetailRespVO;
import cn.iocoder.yudao.module.tv.controller.app.group.vo.AppGroupJoinReqVO;
import cn.iocoder.yudao.module.tv.dal.dataobject.group.GroupInfoDO;
import cn.iocoder.yudao.module.tv.dal.dataobject.group.GroupOrderDO;
import cn.iocoder.yudao.module.tv.dal.dataobject.group.GroupResultDO;
import cn.iocoder.yudao.module.tv.dal.dataobject.group.UserLotteryStatDO;
import cn.iocoder.yudao.module.tv.dal.dataobject.prize.PrizeDO;
import cn.iocoder.yudao.module.tv.dal.mysql.group.GroupInfoMapper;
import cn.iocoder.yudao.module.tv.dal.mysql.group.GroupOrderMapper;
import cn.iocoder.yudao.module.tv.dal.mysql.group.GroupResultMapper;
import cn.iocoder.yudao.module.tv.dal.mysql.group.UserLotteryStatMapper;
import cn.iocoder.yudao.module.tv.enums.group.DrawAlgorithmEnum;
import cn.iocoder.yudao.module.tv.enums.group.GroupStatusEnum;
import cn.iocoder.yudao.module.tv.enums.prize.PrizeStatusEnum;
import cn.iocoder.yudao.module.tv.enums.prize.PrizeTypeEnum;
import cn.iocoder.yudao.module.tv.service.prize.PrizeService;
import cn.iocoder.yudao.module.tv.util.GroupLotteryUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;

import static cn.iocoder.yudao.module.tv.enums.ErrorCodeConstants.*;

/**
 * 拼团抽奖 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Slf4j
public class GroupLotteryServiceImpl implements GroupLotteryService {

    @Resource
    private GroupInfoMapper groupInfoMapper;
    @Resource
    private GroupOrderMapper groupOrderMapper;
    @Resource
    private GroupResultMapper groupResultMapper;
    @Resource
    private UserLotteryStatMapper userLotteryStatMapper;

    @Resource
    private PrizeService prizeService;
    @Resource
    private MemberUserApi memberUserApi;
    @Resource
    private MemberPointApi memberPointApi;
    @Resource
    private PayWalletApi payWalletApi;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Long createGroup(Long userId, @Valid AppGroupCreateReqVO createReqVO) {
        // 1. 校验奖品
        PrizeDO prize = prizeService.getPrize(createReqVO.getPrizeId());
        if (prize == null) {
            throw ServiceExceptionUtil.exception(PRIZE_NOT_EXISTS);
        }
        if (!PrizeStatusEnum.ENABLED.getStatus().equals(prize.getStatus())) {
            throw ServiceExceptionUtil.exception(PRIZE_NOT_ENABLED);
        }
        if (prize.getStock() != null && prize.getStock() != -1 && prize.getStock() <= 0) {
            throw ServiceExceptionUtil.exception(PRIZE_STOCK_NOT_ENOUGH);
        }

        // 2. 校验用户是否有足够余额支付参团费用
        // TODO: 这里应该调用支付模块进行预支付，暂时简化处理

        // 3. 创建拼团
        GroupInfoDO groupInfo = GroupInfoDO.builder()
                .creatorId(userId)
                .prizeId(prize.getId())
                .prizeName(prize.getName())
                .prizeImage(prize.getImageUrl())
                .prizeValue(prize.getValue())
                .targetSize(createReqVO.getTargetSize())
                .currentSize(1) // 发起人自动参与
                .entryFee(createReqVO.getEntryFee())
                .status(GroupStatusEnum.IN_PROGRESS.getStatus())
                .expireTime(LocalDateTime.now().plusHours(createReqVO.getDurationHours()))
                .build();
        groupInfoMapper.insert(groupInfo);

        // 4. 创建发起人的参与记录
        insertGroupOrder(groupInfo.getId(), createReqVO.getOrderNo(), userId, createReqVO.getEntryFee(), 1);

        // 5. 更新用户抽奖统计
        updateUserLotteryStat(userId);

        log.info("[createGroup][创建拼团成功] userId={}, groupId={}, prizeId={}", 
                userId, groupInfo.getId(), prize.getId());

        return groupInfo.getId();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Long joinGroup(Long userId, @Valid AppGroupJoinReqVO joinReqVO) {
        // 1. 校验拼团是否存在且可参与
        GroupInfoDO groupInfo = groupInfoMapper.selectById(joinReqVO.getGroupId());
        if (groupInfo == null) {
            throw ServiceExceptionUtil.exception(GROUP_NOT_EXISTS);
        }
        if (!GroupStatusEnum.canJoin(groupInfo.getStatus())) {
            throw ServiceExceptionUtil.exception(GROUP_NOT_JOINABLE);
        }
        if (groupInfo.getExpireTime().isBefore(LocalDateTime.now())) {
            throw ServiceExceptionUtil.exception(GROUP_EXPIRED);
        }
        if (groupInfo.getCurrentSize() >= groupInfo.getTargetSize()) {
            throw ServiceExceptionUtil.exception(GROUP_FULL);
        }

        // 2. 校验用户是否已参与
        GroupOrderDO existingOrder = groupOrderMapper.selectByGroupIdAndUserId(joinReqVO.getGroupId(), userId);
        if (existingOrder != null) {
            throw ServiceExceptionUtil.exception(GROUP_ALREADY_JOINED);
        }

        // 3. 校验用户是否有足够余额支付参团费用
        // TODO: 这里应该调用支付模块进行预支付，暂时简化处理

        // 4. 创建参与记录
        Integer nextSeqNo = groupOrderMapper.selectMaxSeqNoByGroupId(joinReqVO.getGroupId()) + 1;
        GroupOrderDO groupOrder = insertGroupOrder(joinReqVO.getGroupId(), joinReqVO.getOrderNo(),userId, groupInfo.getEntryFee(), nextSeqNo);

        // 5. 更新拼团当前人数
        groupInfo.setCurrentSize(groupInfo.getCurrentSize() + 1);
        groupInfoMapper.updateById(groupInfo);

        // 6. 更新用户抽奖统计
        updateUserLotteryStat(userId);

        // 7. 检查是否满员，满员则自动开奖
        if (groupInfo.getCurrentSize() >= groupInfo.getTargetSize()) {
            drawLottery(joinReqVO.getGroupId());
        }

        log.info("[joinGroup][参与拼团成功] userId={}, groupId={}, currentSize={}/{}",
                userId, joinReqVO.getGroupId(), groupInfo.getCurrentSize(), groupInfo.getTargetSize());

        return groupOrder.getId();
    }

    private GroupOrderDO insertGroupOrder(Long joinReqVO,String orderNo, Long userId, BigDecimal groupInfo, Integer nextSeqNo) {
        GroupOrderDO groupOrder = GroupOrderDO.builder()
                .groupId(joinReqVO)
                .userId(userId)
                .orderNo(Optional.ofNullable(orderNo).orElseGet(this::generateOrderNo))
                .amount(groupInfo)
                .seqNo(nextSeqNo)
                .joinTime(LocalDateTime.now())
                .build();
        groupOrderMapper.insert(groupOrder);
        return groupOrder;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Long drawLottery(Long groupId) {
        // 1. 校验拼团状态
        GroupInfoDO groupInfo = groupInfoMapper.selectById(groupId);
        if (groupInfo == null) {
            throw ServiceExceptionUtil.exception(GROUP_NOT_EXISTS);
        }
        if (!GroupStatusEnum.IN_PROGRESS.getStatus().equals(groupInfo.getStatus())) {
            throw ServiceExceptionUtil.exception(GROUP_NOT_IN_PROGRESS);
        }
        if (groupInfo.getCurrentSize() < groupInfo.getTargetSize()) {
            throw ServiceExceptionUtil.exception(GROUP_NOT_FULL);
        }

        // 2. 获取参与用户列表
        List<GroupOrderDO> participants = groupOrderMapper.selectListByGroupId(groupId);
        if (CollUtil.isEmpty(participants)) {
            throw ServiceExceptionUtil.exception(GROUP_NO_PARTICIPANTS);
        }

        // 3. 获取用户抽奖统计信息
        List<Long> userIds = participants.stream().map(GroupOrderDO::getUserId).collect(Collectors.toList());
        List<UserLotteryStatDO> userStats = userLotteryStatMapper.selectListByUserIds(userIds);
        Map<Long, UserLotteryStatDO> userStatMap = userStats.stream()
                .collect(Collectors.toMap(UserLotteryStatDO::getUserId, stat -> stat));

        // 4. 执行开奖算法
        GroupLotteryUtils.DrawResult drawResult = GroupLotteryUtils.drawWinner(participants, userStatMap);

        // 5. 更新拼团状态
        groupInfo.setStatus(GroupStatusEnum.DRAWN.getStatus());
        groupInfo.setWinnerId(drawResult.getWinnerId());
        groupInfo.setRandomSeed(drawResult.getRandomSeed());
        groupInfo.setDrawTime(LocalDateTime.now());
        groupInfoMapper.updateById(groupInfo);

        // 6. 保存开奖记录
        GroupResultDO groupResult = GroupResultDO.builder()
                .groupId(groupId)
                .winnerId(drawResult.getWinnerId())
                .prizeId(groupInfo.getPrizeId())
                .prizeName(groupInfo.getPrizeName())
                .randomSeed(drawResult.getRandomSeed())
                .algorithmType(drawResult.getAlgorithmType())
                .drawDetails(drawResult.getDrawDetails())
                .build();
        groupResultMapper.insert(groupResult);

        // 7. 更新所有参与用户的统计信息
        for (GroupOrderDO participant : participants) {
            if (participant.getUserId().equals(drawResult.getWinnerId())) {
                // 中奖用户
                updateUserWinStat(participant.getUserId());
            } else {
                // 未中奖用户，增加失败次数和权重
                updateUserFailStat(participant.getUserId());
            }
        }

        // 8. 发放奖励
        grantPrize(drawResult.getWinnerId(), groupInfo.getPrizeId());

        log.info("[drawLottery][开奖成功] groupId={}, winnerId={}, algorithm={}",
                groupId, drawResult.getWinnerId(), drawResult.getAlgorithmType());

        return groupResult.getId();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void handleExpiredGroup(Long groupId) {
        // 1. 校验拼团状态
        GroupInfoDO groupInfo = groupInfoMapper.selectById(groupId);
        if (groupInfo == null || GroupStatusEnum.isFinished(groupInfo.getStatus())) {
            return;
        }

        // 2. 更新拼团状态为已退款
        groupInfo.setStatus(GroupStatusEnum.REFUNDED.getStatus());
        groupInfoMapper.updateById(groupInfo);

        // 3. 获取参与用户列表并退款
        List<GroupOrderDO> participants = groupOrderMapper.selectListByGroupId(groupId);
        for (GroupOrderDO participant : participants) {
            // TODO: 调用支付模块进行退款
            log.info("[handleExpiredGroup][退款] groupId={}, userId={}, amount={}",
                    groupId, participant.getUserId(), participant.getAmount());
        }

        log.info("[handleExpiredGroup][处理超时拼团] groupId={}, participantCount={}",
                groupId, participants.size());
    }

    @Override
    public int handleExpiredGroups() {
        List<GroupInfoDO> expiredGroups = groupInfoMapper.selectExpiredGroups();
        for (GroupInfoDO group : expiredGroups) {
            try {
                handleExpiredGroup(group.getId());
            } catch (Exception e) {
                log.error("[handleExpiredGroups][处理超时拼团失败] groupId={}", group.getId(), e);
            }
        }
        return expiredGroups.size();
    }

    @Override
    public int handleFullGroups() {
        List<GroupInfoDO> fullGroups = groupInfoMapper.selectFullGroups();
        for (GroupInfoDO group : fullGroups) {
            try {
                drawLottery(group.getId());
            } catch (Exception e) {
                log.error("[handleFullGroups][处理满员拼团开奖失败] groupId={}", group.getId(), e);
            }
        }
        return fullGroups.size();
    }

    /**
     * 生成订单号
     */
    private String generateOrderNo() {
        return "GROUP" + IdUtil.getSnowflakeNextIdStr();
    }

    /**
     * 更新用户失败统计
     */
    private void updateUserFailStat(Long userId) {
        UserLotteryStatDO stat = getOrCreateUserLotteryStat(userId);
        stat.setFailCount(stat.getFailCount() + 1);
        stat.setWeight(GroupLotteryUtils.calculateNewWeight(stat.getWeight(), stat.getFailCount()));
        userLotteryStatMapper.updateById(stat);
    }

    /**
     * 发放奖励
     */
    private void grantPrize(Long userId, Long prizeId) {
        PrizeDO prize = prizeService.getPrize(prizeId);
        if (prize == null) {
            log.error("[grantPrize][奖品不存在] userId={}, prizeId={}", userId, prizeId);
            return;
        }

        try {
            if (PrizeTypeEnum.POINTS.getType().equals(prize.getType())) {
                // 积分奖励
                memberPointApi.addPoint(userId, prize.getValue().intValue(),
                        MemberPointBizTypeEnum.SIGN.getType(), "拼团中奖奖励");
            } else if (PrizeTypeEnum.BALANCE.getType().equals(prize.getType())) {
                // 余额奖励
                // TODO: 调用钱包API增加余额
            }
            // 实物奖品和优惠券需要其他处理方式

            log.info("[grantPrize][发放奖励成功] userId={}, prizeId={}, prizeType={}, value={}",
                    userId, prizeId, prize.getType(), prize.getValue());
        } catch (Exception e) {
            log.error("[grantPrize][发放奖励失败] userId={}, prizeId={}", userId, prizeId, e);
        }
    }

    @Override
    public AppGroupDetailRespVO getGroupDetail(Long userId, Long groupId) {
        // 1. 获取拼团信息
        GroupInfoDO groupInfo = groupInfoMapper.selectById(groupId);
        if (groupInfo == null) {
            throw ServiceExceptionUtil.exception(GROUP_NOT_EXISTS);
        }

        // 2. 构建响应对象
        AppGroupDetailRespVO respVO = new AppGroupDetailRespVO();
        respVO.setId(groupInfo.getId());
        respVO.setCreatorId(groupInfo.getCreatorId());
        respVO.setPrizeId(groupInfo.getPrizeId());
        respVO.setPrizeName(groupInfo.getPrizeName());
        respVO.setPrizeImage(groupInfo.getPrizeImage());
        respVO.setPrizeValue(groupInfo.getPrizeValue());
        respVO.setTargetSize(groupInfo.getTargetSize());
        respVO.setCurrentSize(groupInfo.getCurrentSize());
        respVO.setEntryFee(groupInfo.getEntryFee());
        respVO.setStatus(groupInfo.getStatus());
        respVO.setStatusName(GroupStatusEnum.valueOf(groupInfo.getStatus()).getName());
        respVO.setWinnerId(groupInfo.getWinnerId());
        respVO.setRandomSeed(groupInfo.getRandomSeed());
        respVO.setDrawTime(groupInfo.getDrawTime());
        respVO.setExpireTime(groupInfo.getExpireTime());
        respVO.setCreateTime(groupInfo.getCreateTime());

        // 3. 获取参与用户信息
        List<GroupOrderDO> participants = groupOrderMapper.selectListByGroupId(groupId);
        List<Long> userIds = participants.stream().map(GroupOrderDO::getUserId).collect(Collectors.toList());

        if (CollUtil.isNotEmpty(userIds)) {
            Map<Long, MemberUserRespDTO> userMap = memberUserApi.getUserMap(userIds);
            List<AppGroupDetailRespVO.ParticipantInfo> participantInfos = new ArrayList<>();

            for (GroupOrderDO participant : participants) {
                AppGroupDetailRespVO.ParticipantInfo info = new AppGroupDetailRespVO.ParticipantInfo();
                info.setUserId(participant.getUserId());
                info.setJoinTime(participant.getJoinTime());
                info.setSeqNo(participant.getSeqNo());

                MemberUserRespDTO user = userMap.get(participant.getUserId());
                if (user != null) {
                    info.setNickname(user.getNickname());
                    info.setAvatar(user.getAvatar());
                }

                participantInfos.add(info);
            }

            respVO.setParticipants(participantInfos);
        }

        // 4. 设置发起人和中奖者信息
        if (groupInfo.getCreatorId() != null) {
            MemberUserRespDTO creator = memberUserApi.getUser(groupInfo.getCreatorId());
            if (creator != null) {
                respVO.setCreatorNickname(creator.getNickname());
                respVO.setCreatorAvatar(creator.getAvatar());
            }
        }

        if (groupInfo.getWinnerId() != null) {
            MemberUserRespDTO winner = memberUserApi.getUser(groupInfo.getWinnerId());
            if (winner != null) {
                respVO.setWinnerNickname(winner.getNickname());
            }
        }

        // 5. 判断当前用户是否已参与
        if (userId != null) {
            GroupOrderDO userOrder = groupOrderMapper.selectByGroupIdAndUserId(groupId, userId);
            respVO.setHasJoined(userOrder != null);
        }

        return respVO;
    }

    @Override
    public List<AppGroupDetailRespVO> getUserGroups(Long userId) {
        List<GroupOrderDO> userOrders = groupOrderMapper.selectListByUserId(userId);
        if (CollUtil.isEmpty(userOrders)) {
            return new ArrayList<>();
        }

        List<Long> groupIds = userOrders.stream().map(GroupOrderDO::getGroupId).collect(Collectors.toList());
        List<AppGroupDetailRespVO> result = new ArrayList<>();

        for (Long groupId : groupIds) {
            try {
                AppGroupDetailRespVO detail = getGroupDetail(userId, groupId);
                result.add(detail);
            } catch (Exception e) {
                log.error("[getUserGroups][获取拼团详情失败] userId={}, groupId={}", userId, groupId, e);
            }
        }

        return result;
    }

    @Override
    public List<AppGroupDetailRespVO> getHotGroups(Integer limit) {
        List<GroupInfoDO> hotGroups = groupInfoMapper.selectListByStatus(GroupStatusEnum.IN_PROGRESS.getStatus());
        if (CollUtil.isEmpty(hotGroups)) {
            return new ArrayList<>();
        }

        // 按参与人数排序，取前N个
        hotGroups = hotGroups.stream()
                .sorted((a, b) -> b.getCurrentSize().compareTo(a.getCurrentSize()))
                .limit(limit != null ? limit : 10)
                .collect(Collectors.toList());

        List<AppGroupDetailRespVO> result = new ArrayList<>();
        for (GroupInfoDO group : hotGroups) {
            try {
                AppGroupDetailRespVO detail = getGroupDetail(null, group.getId());
                result.add(detail);
            } catch (Exception e) {
                log.error("[getHotGroups][获取拼团详情失败] groupId={}", group.getId(), e);
            }
        }

        return result;
    }

    @Override
    public void updateUserLotteryStat(Long userId) {
        UserLotteryStatDO stat = getOrCreateUserLotteryStat(userId);
        stat.setTotalJoin(stat.getTotalJoin() + 1);
        stat.setLastJoinTime(LocalDateTime.now());
        userLotteryStatMapper.updateById(stat);
    }

    @Override
    public void updateUserWinStat(Long userId) {
        UserLotteryStatDO stat = getOrCreateUserLotteryStat(userId);
        stat.setTotalWin(stat.getTotalWin() + 1);
        stat.setFailCount(0); // 重置连续失败次数
        stat.setWeight(GroupLotteryUtils.resetWinnerWeight()); // 重置权重
        stat.setLastWinTime(LocalDateTime.now());
        userLotteryStatMapper.updateById(stat);
    }

    /**
     * 获取或创建用户抽奖统计
     */
    private UserLotteryStatDO getOrCreateUserLotteryStat(Long userId) {
        UserLotteryStatDO stat = userLotteryStatMapper.selectByUserId(userId);
        if (stat == null) {
            stat = UserLotteryStatDO.builder()
                    .userId(userId)
                    .totalJoin(0)
                    .totalWin(0)
                    .failCount(0)
                    .weight(GroupLotteryUtils.DEFAULT_WEIGHT)
                    .build();
            userLotteryStatMapper.insert(stat);
        }
        return stat;
    }

    // 管理后台相关方法
    @Override
    public PageResult<GroupInfoDO> getGroupInfoPage(GroupInfoPageReqVO pageReqVO) {
        return groupInfoMapper.selectPage(pageReqVO);
    }

    @Override
    public PageResult<GroupOrderDO> getGroupOrderPage(GroupOrderPageReqVO pageReqVO) {
        return groupOrderMapper.selectPage(pageReqVO);
    }

    @Override
    public PageResult<GroupResultDO> getGroupResultPage(GroupResultPageReqVO pageReqVO) {
        return groupResultMapper.selectPage(pageReqVO);
    }

    @Override
    public GroupInfoDO getGroupInfo(Long id) {
        return groupInfoMapper.selectById(id);
    }

    @Override
    public GroupResultDO getGroupResult(Long id) {
        return groupResultMapper.selectById(id);
    }
}
