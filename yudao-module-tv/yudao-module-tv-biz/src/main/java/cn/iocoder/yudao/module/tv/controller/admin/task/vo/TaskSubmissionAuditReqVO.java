package cn.iocoder.yudao.module.tv.controller.admin.task.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;

import javax.validation.constraints.*;

@Schema(description = "管理后台 - 任务提交审核 Request VO")
@Data
public class TaskSubmissionAuditReqVO {

    @Schema(description = "提交ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "1024")
    @NotNull(message = "提交ID不能为空")
    private Long id;

    @Schema(description = "审核结果", requiredMode = Schema.RequiredMode.REQUIRED, example = "approve")
    @NotEmpty(message = "审核结果不能为空")
    private String action; // approve-通过，reject-拒绝

    @Schema(description = "审核说明", requiredMode = Schema.RequiredMode.REQUIRED, example = "截图清晰，分享内容符合要求")
    @NotEmpty(message = "审核说明不能为空")
    private String auditResult;

    @Schema(description = "实际奖励积分", example = "20")
    private Integer rewardPoints;

}
