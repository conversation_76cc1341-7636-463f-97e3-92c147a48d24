package cn.iocoder.yudao.module.tv.service.message.handler.impl;

import cn.hutool.core.io.FileUtil;
import cn.hutool.json.JSONUtil;
import cn.iocoder.yudao.module.tv.dal.dataobject.message.LocalMessageDO;
import cn.iocoder.yudao.module.tv.dal.mysql.content.ContentMapper;
import cn.iocoder.yudao.module.tv.enums.message.MessageTypeEnum;
import cn.iocoder.yudao.module.tv.service.content.strategy.ContentUploadStrategy;
import cn.iocoder.yudao.module.tv.service.message.dto.ContentUploadMessageDTO;
import cn.iocoder.yudao.module.tv.service.message.handler.MessageHandler;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import java.io.File;
import java.io.FileInputStream;
import java.io.IOException;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 内容上传消息处理器
 *
 * <AUTHOR>
 */
@Component
@Slf4j
public class ContentUploadMessageHandler implements MessageHandler {

    @Resource
    private ContentMapper contentMapper;

    @Resource
    private List<ContentUploadStrategy> uploadStrategies;

    // 策略映射表
    private final Map<Integer, ContentUploadStrategy> strategyMap = new HashMap<>();

    @PostConstruct
    public void initStrategies() {
        for (ContentUploadStrategy strategy : uploadStrategies) {
            strategyMap.put(strategy.getSupportedType(), strategy);
            log.info("[ContentUploadMessageHandler][注册上传策略] type: {}, strategy: {}", 
                strategy.getSupportedType(), strategy.getClass().getSimpleName());
        }
    }

    @Override
    public String getSupportedMessageType() {
        return MessageTypeEnum.CONTENT_UPLOAD.getType();
    }

    @Override
    public MessageHandleResult handleMessage(LocalMessageDO message) {
        try {
            // 解析消息内容
            ContentUploadMessageDTO uploadMessage = JSONUtil.toBean(message.getPayload(), ContentUploadMessageDTO.class);
            
            log.info("[handleMessage][开始处理内容上传消息] messageId: {}, contentId: {}, fileName: {}", 
                    message.getMessageId(), uploadMessage.getContentId(), uploadMessage.getFileName());

            // 检查临时文件是否存在
            File tempFile = new File(uploadMessage.getTempFilePath());
            if (!tempFile.exists()) {
                return MessageHandleResult.failure("临时文件不存在: " + uploadMessage.getTempFilePath());
            }

            // 获取对应的上传策略
            ContentUploadStrategy strategy = strategyMap.get(uploadMessage.getContentType());
            if (strategy == null) {
                return MessageHandleResult.failure("不支持的内容类型: " + uploadMessage.getContentType());
            }

            // 处理文件上传
            ContentUploadStrategy.ContentUploadResult result;
            try (FileInputStream inputStream = new FileInputStream(tempFile)) {
                result = strategy.processContentFile(
                        uploadMessage.getContentId(),
                        uploadMessage.getConfigId(),
                        inputStream,
                        uploadMessage.getFileName(),
                        uploadMessage.getFileSize(),
                        uploadMessage.getExtParams(),
                        uploadMessage.getSyncCompress(),
                        uploadMessage.getCompressConfig()
                );
            }

            if (!result.isSuccess()) {
                return MessageHandleResult.failure("内容处理失败: " + result.getMessage());
            }

            // 更新内容状态为已发布
            updateContentStatus(uploadMessage.getContentId(), 1); // 1-已发布

            // 删除临时文件，这里不删啦。 fix:  重复加水印导致视频文件损坏的bug需要保留原文件。
//            boolean deleted = tempFile.delete();
//            if (!deleted) {
//                log.warn("[handleMessage][临时文件删除失败] filePath: {}", uploadMessage.getTempFilePath());
//                // 标记文件为待删除，可以通过定时任务清理
//                tempFile.deleteOnExit();
//            }

            log.info("[handleMessage][内容上传处理成功] messageId: {}, contentId: {}, result: {}", 
                    message.getMessageId(), uploadMessage.getContentId(), result.getData());

            return MessageHandleResult.success("内容上传处理成功", result.getData());

        } catch (Exception e) {
            log.error("[handleMessage][内容上传处理失败] messageId: {}", message.getMessageId(), e);
            return MessageHandleResult.failure("内容上传处理异常: " + e.getMessage());
        }
    }

    /**
     * 更新内容状态
     */
    private void updateContentStatus(Long contentId, Integer status) {
        try {
            contentMapper.updateById(new cn.iocoder.yudao.module.tv.dal.dataobject.content.ContentDO()
                    .setId(contentId)
                    .setStatus(status));
            log.debug("[updateContentStatus][更新内容状态成功] contentId: {}, status: {}", contentId, status);
        } catch (Exception e) {
            log.error("[updateContentStatus][更新内容状态失败] contentId: {}, status: {}", contentId, status, e);
        }
    }

}
