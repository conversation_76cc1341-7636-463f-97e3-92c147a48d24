package cn.iocoder.yudao.module.tv.service.report;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.json.JSONUtil;
import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.common.util.object.BeanUtils;
import cn.iocoder.yudao.framework.mybatis.core.query.LambdaQueryWrapperX;
import cn.iocoder.yudao.module.tv.controller.admin.report.vo.*;
import cn.iocoder.yudao.module.tv.controller.admin.report.vo.ReportPageReqVO;
import cn.iocoder.yudao.module.tv.controller.admin.report.vo.ReportRespVO;
import cn.iocoder.yudao.module.tv.controller.admin.report.vo.ReportSaveReqVO;
import cn.iocoder.yudao.module.tv.controller.app.report.vo.AppReportCreateReqVO;
import cn.iocoder.yudao.module.tv.controller.app.report.vo.AppReportPageReqVO;
import cn.iocoder.yudao.module.tv.controller.app.report.vo.AppReportRespVO;
import cn.iocoder.yudao.module.tv.dal.dataobject.report.ReportDO;
import cn.iocoder.yudao.module.tv.dal.mysql.report.ReportMapper;
import cn.iocoder.yudao.module.tv.enums.report.ReportBusinessTypeEnum;
import cn.iocoder.yudao.module.tv.enums.report.ReportStatusEnum;
import cn.iocoder.yudao.module.tv.enums.report.ReportTypeEnum;
import cn.iocoder.yudao.module.tv.service.report.antiabuse.ReportAntiAbuseService;
import cn.iocoder.yudao.module.tv.service.report.handler.ReportHandler;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.validation.annotation.Validated;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

import static cn.iocoder.yudao.framework.common.exception.util.ServiceExceptionUtil.exception;
import static cn.iocoder.yudao.module.tv.enums.ErrorCodeConstants.*;
import static cn.iocoder.yudao.module.tv.enums.ErrorCodeConstants.REPORT_DUPLICATE_OR_FREQUENT;
import static cn.iocoder.yudao.module.tv.enums.ErrorCodeConstants.REPORT_NOT_EXISTS;

/**
 * 举报反馈 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
@Slf4j
public class ReportServiceImpl implements ReportService {

    @Resource
    private ReportMapper reportMapper;

    @Resource
    private List<ReportHandler> reportHandlers;

    @Resource
    private ReportAntiAbuseService antiAbuseService;

    /**
     * 获取举报处理器映射
     */
    private Map<String, ReportHandler> getReportHandlerMap() {
        return reportHandlers.stream()
                .collect(Collectors.toMap(ReportHandler::getBusinessType, Function.identity()));
    }

    @Override
    public Long createReport(ReportSaveReqVO createReqVO) {
        // 插入
        ReportDO report = BeanUtils.toBean(createReqVO, ReportDO.class);
        if (CollUtil.isNotEmpty(createReqVO.getReportImages())) {
            report.setReportImages(JSONUtil.toJsonStr(createReqVO.getReportImages()));
        }
        if (report.getStatus() == null) {
            report.setStatus(ReportStatusEnum.PENDING.getStatus());
        }
        reportMapper.insert(report);

        // 执行业务处理器
        executeReportHandler(report, "afterReportCreate");

        return report.getId();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Long createAppReport(Long userId, AppReportCreateReqVO createReqVO, String userIp) {
        // 1. 反滥用检查
        if (!canUserReport(userId, createReqVO.getBusinessType(), createReqVO.getBusinessId())) {
            throw exception(REPORT_DUPLICATE_OR_FREQUENT);
        }

        // 2. 构建举报对象
        ReportDO report = BeanUtils.toBean(createReqVO, ReportDO.class);
        report.setReporterId(userId);
        report.setReporterIp(userIp);
        report.setStatus(ReportStatusEnum.PENDING.getStatus());
        if (CollUtil.isNotEmpty(createReqVO.getReportImages())) {
            report.setReportImages(JSONUtil.toJsonStr(createReqVO.getReportImages()));
        }

        // 3. 插入数据库
        reportMapper.insert(report);

        // 4. 记录举报行为（反滥用分析）
        antiAbuseService.recordReportBehavior(report);

        // 5. 执行业务处理器
        executeReportHandler(report, "afterReportCreate");

        // 6. 检查是否需要自动处理
        checkAutoHandle(report);

        return report.getId();
    }

    @Override
    public void updateReport(ReportSaveReqVO updateReqVO) {
        // 校验存在
        validateReportExists(updateReqVO.getId());
        // 更新
        ReportDO updateObj = BeanUtils.toBean(updateReqVO, ReportDO.class);
        if (CollUtil.isNotEmpty(updateReqVO.getReportImages())) {
            updateObj.setReportImages(JSONUtil.toJsonStr(updateReqVO.getReportImages()));
        }
        reportMapper.updateById(updateObj);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void handleReport(Long id, Long handleUserId, String handleResult, boolean isViolation) {
        // 校验存在
        ReportDO report = validateReportExists(id);
        
        // 更新处理状态
        ReportDO updateObj = new ReportDO();
        updateObj.setId(id);
        updateObj.setStatus(ReportStatusEnum.PROCESSED.getStatus());
        updateObj.setHandleResult(handleResult);
        updateObj.setHandleUserId(handleUserId);
        updateObj.setHandleTime(LocalDateTime.now());
        reportMapper.updateById(updateObj);

        // 执行业务处理器
        report.setHandleResult(handleResult);
        report.setStatus(ReportStatusEnum.PROCESSED.getStatus());
        executeReportHandler(report, "afterReportHandle", isViolation);
    }

    @Override
    public void deleteReport(Long id) {
        // 校验存在
        validateReportExists(id);
        // 删除
        reportMapper.deleteById(id);
    }

    @Override
    public ReportDO getReport(Long id) {
        return reportMapper.selectById(id);
    }

    @Override
    public PageResult<ReportRespVO> getReportPage(ReportPageReqVO pageReqVO) {
        PageResult<ReportDO> pageResult = reportMapper.selectPage(pageReqVO);

        // 转换为响应VO
        List<ReportRespVO> respList = BeanUtils.toBean(pageResult.getList(), ReportRespVO.class);

        // 设置枚举名称
        respList.forEach(resp -> {
            ReportTypeEnum reportType = ReportTypeEnum.valueOf(resp.getReportType());
            if (reportType != null) {
                resp.setReportTypeName(reportType.getName());
            }

            ReportStatusEnum status = ReportStatusEnum.valueOf(resp.getStatus());
            if (status != null) {
                resp.setStatusName(status.getName());
            }

            // 解析举报图片
            if (resp.getReportImages() != null && resp.getReportImages() instanceof String) {
                try {
                    String imageJson = (String) resp.getReportImages();
                    List<String> imageList = JSONUtil.toList(imageJson, String.class);
                    resp.setReportImages(imageList);
                } catch (Exception e) {
                    log.warn("[getReportPage][解析举报图片失败] reportId={}", resp.getId(), e);
                }
            }
        });

        return new PageResult<>(respList, pageResult.getTotal());
    }

    @Override
    public List<ReportDO> getReportListByBusiness(String businessType, Long businessId) {
        return reportMapper.selectListByBusiness(businessType, businessId);
    }

    @Override
    public Long countReportsByBusiness(String businessType, Long businessId) {
        return reportMapper.countByBusiness(businessType, businessId);
    }

    /**
     * 校验举报反馈是否存在
     */
    private ReportDO validateReportExists(Long id) {
        ReportDO report = reportMapper.selectById(id);
        if (report == null) {
            throw exception(REPORT_NOT_EXISTS);
        }
        return report;
    }

    /**
     * 执行举报处理器
     */
    private void executeReportHandler(ReportDO report, String method, Object... args) {
        Map<String, ReportHandler> handlerMap = getReportHandlerMap();
        ReportHandler handler = handlerMap.get(report.getBusinessType());
        if (handler == null) {
            log.warn("[executeReportHandler][未找到业务类型({})的处理器]", report.getBusinessType());
            return;
        }

        try {
            switch (method) {
                case "afterReportCreate":
                    handler.afterReportCreate(report);
                    break;
                case "afterReportHandle":
                    if (args.length > 0 && args[0] instanceof Boolean) {
                        handler.afterReportHandle(report, (Boolean) args[0]);
                    }
                    break;
                default:
                    log.warn("[executeReportHandler][未知的处理方法：{}]", method);
            }
        } catch (Exception e) {
            log.error("[executeReportHandler][执行处理器失败] businessType={}, method={}",
                    report.getBusinessType(), method, e);
        }
    }

    /**
     * 检查是否需要自动处理
     */
    private void checkAutoHandle(ReportDO report) {
        Map<String, ReportHandler> handlerMap = getReportHandlerMap();
        ReportHandler handler = handlerMap.get(report.getBusinessType());
        if (handler == null) {
            return;
        }

        try {
            // 统计举报次数
            Long reportCount = reportMapper.countByBusiness(report.getBusinessType(), report.getBusinessId());

            // 检查是否需要自动处理
            if (handler.shouldAutoHandle(report.getBusinessId(), reportCount)) {
                handler.autoHandle(report.getBusinessId(), reportCount);
                log.info("[checkAutoHandle][自动处理举报] businessType={}, businessId={}, reportCount={}",
                        report.getBusinessType(), report.getBusinessId(), reportCount);
            }
        } catch (Exception e) {
            log.error("[checkAutoHandle][自动处理检查失败] businessType={}, businessId={}",
                    report.getBusinessType(), report.getBusinessId(), e);
        }
    }

    @Override
    public PageResult<AppReportRespVO> getUserReportPage(Long userId, AppReportPageReqVO pageReqVO) {
        PageResult<ReportDO> pageResult = reportMapper.selectUserReportPage(userId, pageReqVO);

        // 转换为响应VO
        List<AppReportRespVO> respList = BeanUtils.toBean(pageResult.getList(), AppReportRespVO.class);

        // 设置枚举名称和业务详情
        Map<String, ReportHandler> handlerMap = getReportHandlerMap();
        respList.forEach(resp -> {
            // 设置举报类型名称
            ReportTypeEnum reportType = ReportTypeEnum.valueOf(resp.getReportType());
            if (reportType != null) {
                resp.setReportTypeName(reportType.getName());
            }

            // 设置处理状态名称
            ReportStatusEnum status = ReportStatusEnum.valueOf(resp.getStatus());
            if (status != null) {
                resp.setStatusName(status.getName());
            }

            // 设置业务类型名称
            ReportBusinessTypeEnum businessType = ReportBusinessTypeEnum.valueOf(resp.getBusinessType());
            if (businessType != null) {
                resp.setBusinessTypeName(businessType.getName());
            }

            // 解析举报图片
            if (resp.getReportImages() != null && resp.getReportImages() instanceof String) {
                try {
                    String imageJson = (String) resp.getReportImages();
                    List<String> imageList = JSONUtil.toList(imageJson, String.class);
                    resp.setReportImages(imageList);
                } catch (Exception e) {
                    log.warn("[getUserReportPage][解析举报图片失败] reportId={}", resp.getId(), e);
                }
            }

            // 获取业务对象详情
            ReportHandler handler = handlerMap.get(resp.getBusinessType());
            if (handler != null) {
                try {
                    Object businessDetail = handler.getBusinessDetail(resp.getBusinessId());
                    resp.setBusinessDetail(businessDetail);
                } catch (Exception e) {
                    log.warn("[getUserReportPage][获取业务详情失败] businessType={}, businessId={}",
                            resp.getBusinessType(), resp.getBusinessId(), e);
                }
            }
        });

        return new PageResult<>(respList, pageResult.getTotal());
    }

    @Override
    public boolean canUserReport(Long userId, String businessType, Long businessId) {
        // 1. 检查是否已经举报过
        if (reportMapper.existsUserReport(userId, businessType, businessId)) {
            log.warn("[canUserReport][用户已举报过该对象] userId={}, businessType={}, businessId={}",
                    userId, businessType, businessId);
            return false;
        }

        // 2. 检查是否为恶意举报用户
        if (antiAbuseService.isMaliciousReporter(userId)) {
            log.warn("[canUserReport][检测到恶意举报用户，拒绝举报] userId={}", userId);
            return false;
        }

        // 3. 检查今日举报次数限制（最多10次）
        Long todayCount = countUserTodayReports(userId);
        if (todayCount >= 10) {
            log.warn("[canUserReport][用户今日举报次数超限] userId={}, todayCount={}", userId, todayCount);
            return false;
        }

        // 4. 检查最近1小时举报次数（最多3次，防止恶意刷举报）
        LocalDateTime oneHourAgo = LocalDateTime.now().minusHours(1);
        Long recentCount = reportMapper.countUserRecentReports(userId, oneHourAgo);
        if (recentCount >= 3) {
            log.warn("[canUserReport][用户最近1小时举报次数超限] userId={}, recentCount={}", userId, recentCount);
            return false;
        }

        return true;
    }

    @Override
    public Long countUserTodayReports(Long userId) {
        LocalDateTime startOfDay = LocalDateTime.now().withHour(0).withMinute(0).withSecond(0).withNano(0);
        LocalDateTime endOfDay = startOfDay.plusDays(1).minusNanos(1);
        return reportMapper.countUserTodayReports(userId, startOfDay, endOfDay);
    }

    @Override
    public ReportDetailRespVO getReportDetail(Long id) {
        // 1. 获取举报基本信息
        ReportDO report = validateReportExists(id);
        ReportRespVO reportResp = BeanUtils.toBean(report, ReportRespVO.class);

        // 设置枚举名称
        ReportTypeEnum reportType = ReportTypeEnum.valueOf(reportResp.getReportType());
        if (reportType != null) {
            reportResp.setReportTypeName(reportType.getName());
        }
        ReportStatusEnum status = ReportStatusEnum.valueOf(reportResp.getStatus());
        if (status != null) {
            reportResp.setStatusName(status.getName());
        }

        ReportDetailRespVO detailResp = new ReportDetailRespVO();
        detailResp.setReport(reportResp);

        // 2. 获取举报人信息（如果不是匿名举报）
        if (report.getReporterId() != null) {
            ReportDetailRespVO.ReporterInfo reporterInfo = new ReportDetailRespVO.ReporterInfo();
            reporterInfo.setUserId(report.getReporterId());
            // TODO: 这里需要调用用户服务获取用户详细信息
            // 暂时只设置基本信息
            reporterInfo.setNickname("用户" + report.getReporterId());

            // 统计举报人的举报次数
            LocalDateTime oneWeekAgo = LocalDateTime.now().minusWeeks(1);
            reporterInfo.setRecentReportCount(reportMapper.countUserRecentReports(report.getReporterId(), oneWeekAgo));
            reporterInfo.setTotalReportCount(reportMapper.selectCount(new LambdaQueryWrapperX<ReportDO>()
                    .eq(ReportDO::getReporterId, report.getReporterId())));

            detailResp.setReporter(reporterInfo);
        }

        // 3. 获取被举报对象详情
        Map<String, ReportHandler> handlerMap = getReportHandlerMap();
        ReportHandler handler = handlerMap.get(report.getBusinessType());
        if (handler != null) {
            try {
                Object businessDetail = handler.getBusinessDetail(report.getBusinessId());
                detailResp.setBusinessDetail(businessDetail);
            } catch (Exception e) {
                log.warn("[getReportDetail][获取业务详情失败] businessType={}, businessId={}",
                        report.getBusinessType(), report.getBusinessId(), e);
            }
        }

        // 4. 获取历史举报记录
        List<ReportDO> historyReports = reportMapper.selectListByBusiness(report.getBusinessType(), report.getBusinessId());
        List<ReportRespVO> historyRespList = BeanUtils.toBean(historyReports, ReportRespVO.class);
        historyRespList.forEach(resp -> {
            ReportTypeEnum historyReportType = ReportTypeEnum.valueOf(resp.getReportType());
            if (historyReportType != null) {
                resp.setReportTypeName(historyReportType.getName());
            }
            ReportStatusEnum historyStatus = ReportStatusEnum.valueOf(resp.getStatus());
            if (historyStatus != null) {
                resp.setStatusName(historyStatus.getName());
            }
        });
        detailResp.setHistoryReports(historyRespList);

        return detailResp;
    }

    @Override
    public ReportStatisticsRespVO getReportStatistics(ReportStatisticsReqVO reqVO) {
        // TODO: 实现统计查询逻辑
        // 这里需要根据时间范围、业务类型等条件进行复杂的统计查询
        // 暂时返回基本统计信息
        ReportStatisticsRespVO resp = new ReportStatisticsRespVO();

        // 基本统计
        resp.setTotalCount(reportMapper.selectCount());
        resp.setPendingCount(reportMapper.selectCount(new LambdaQueryWrapperX<ReportDO>()
                .eq(ReportDO::getStatus, ReportStatusEnum.PENDING.getStatus())));
        resp.setProcessedCount(reportMapper.selectCount(new LambdaQueryWrapperX<ReportDO>()
                .eq(ReportDO::getStatus, ReportStatusEnum.PROCESSED.getStatus())));
        resp.setRejectedCount(reportMapper.selectCount(new LambdaQueryWrapperX<ReportDO>()
                .eq(ReportDO::getStatus, ReportStatusEnum.REJECTED.getStatus())));

        // TODO: 实现更详细的统计逻辑

        return resp;
    }

    @Override
    public PageResult<ReportRespVO> getPendingReportPage(ReportPageReqVO pageReqVO) {
        // 强制设置状态为待处理
        pageReqVO.setStatus(ReportStatusEnum.PENDING.getStatus());
        return getReportPage(pageReqVO);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void auditReport(ReportAuditReqVO auditReqVO, Long auditorId) {
        // 1. 校验举报存在
        ReportDO report = validateReportExists(auditReqVO.getId());

        // 2. 根据审核动作处理
        switch (auditReqVO.getAction()) {
            case "adopt":
                // 采纳举报
                handleReportAdopt(report, auditReqVO.getHandleResult(), auditorId, auditReqVO.getNeedConfirm());
                break;
            case "reject":
                // 驳回举报
                handleReportReject(report, auditReqVO.getHandleResult(), auditorId);
                break;
            case "confirm":
                // 二次确认
                handleReportConfirm(report, auditReqVO.getHandleResult(), auditorId);
                break;
            default:
                throw exception(REPORT_INVALID_AUDIT_ACTION);
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void batchAuditReports(List<Long> ids, String action, String handleResult, Long auditorId) {
        for (Long id : ids) {
            try {
                ReportAuditReqVO auditReqVO = new ReportAuditReqVO();
                auditReqVO.setId(id);
                auditReqVO.setAction(action);
                auditReqVO.setHandleResult(handleResult);
                auditReport(auditReqVO, auditorId);
            } catch (Exception e) {
                log.error("[batchAuditReports][批量审核失败] id={}, action={}", id, action, e);
                // 继续处理其他举报，不中断批量操作
            }
        }
    }

    /**
     * 处理举报采纳
     */
    private void handleReportAdopt(ReportDO report, String handleResult, Long auditorId, Boolean needConfirm) {
        ReportDO updateObj = new ReportDO();
        updateObj.setId(report.getId());
        updateObj.setHandleResult(handleResult);
        updateObj.setHandleUserId(auditorId);
        updateObj.setHandleTime(LocalDateTime.now());

        if (needConfirm != null && needConfirm) {
            // 需要二次确认
            updateObj.setStatus(ReportStatusEnum.PENDING_CONFIRM.getStatus());
            log.info("[handleReportAdopt][举报采纳，等待二次确认] reportId={}", report.getId());
        } else {
            // 直接处理
            updateObj.setStatus(ReportStatusEnum.PROCESSED.getStatus());
            log.info("[handleReportAdopt][举报采纳，直接处理] reportId={}", report.getId());

            // 执行业务处理器
            report.setHandleResult(handleResult);
            report.setStatus(ReportStatusEnum.PROCESSED.getStatus());
            executeReportHandler(report, "afterReportHandle", true);
        }

        reportMapper.updateById(updateObj);
    }

    /**
     * 处理举报驳回
     */
    private void handleReportReject(ReportDO report, String handleResult, Long auditorId) {
        ReportDO updateObj = new ReportDO();
        updateObj.setId(report.getId());
        updateObj.setStatus(ReportStatusEnum.REJECTED.getStatus());
        updateObj.setHandleResult(handleResult);
        updateObj.setHandleUserId(auditorId);
        updateObj.setHandleTime(LocalDateTime.now());
        reportMapper.updateById(updateObj);

        log.info("[handleReportReject][举报驳回] reportId={}, reason={}", report.getId(), handleResult);

        // 执行业务处理器
        report.setHandleResult(handleResult);
        report.setStatus(ReportStatusEnum.REJECTED.getStatus());
        executeReportHandler(report, "afterReportHandle", false);
    }

    /**
     * 处理二次确认
     */
    private void handleReportConfirm(ReportDO report, String handleResult, Long auditorId) {
        // 只有待二次确认的举报才能进行确认操作
        if (!ReportStatusEnum.PENDING_CONFIRM.getStatus().equals(report.getStatus())) {
            throw exception(REPORT_NOT_PENDING_CONFIRM);
        }

        ReportDO updateObj = new ReportDO();
        updateObj.setId(report.getId());
        updateObj.setStatus(ReportStatusEnum.PROCESSED.getStatus());
        updateObj.setHandleResult(handleResult);
        updateObj.setHandleUserId(auditorId);
        updateObj.setHandleTime(LocalDateTime.now());
        reportMapper.updateById(updateObj);

        log.info("[handleReportConfirm][举报二次确认完成] reportId={}", report.getId());

        // 执行业务处理器
        report.setHandleResult(handleResult);
        report.setStatus(ReportStatusEnum.PROCESSED.getStatus());
        executeReportHandler(report, "afterReportHandle", true);
    }

}
