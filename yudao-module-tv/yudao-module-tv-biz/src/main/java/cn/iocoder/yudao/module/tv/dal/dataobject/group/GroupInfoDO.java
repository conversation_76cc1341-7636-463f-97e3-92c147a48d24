package cn.iocoder.yudao.module.tv.dal.dataobject.group;

import cn.iocoder.yudao.framework.mybatis.core.dataobject.BaseDO;
import com.baomidou.mybatisplus.annotation.KeySequence;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.ToString;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 拼团信息 DO
 *
 * <AUTHOR>
 */
@TableName("tv_group_info")
@KeySequence("tv_group_info_seq")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class GroupInfoDO extends BaseDO {

    /**
     * 拼团ID
     */
    @TableId
    private Long id;

    /**
     * 发起人用户ID
     */
    private Long creatorId;

    /**
     * 奖品ID
     */
    private Long prizeId;

    /**
     * 奖品名称
     */
    private String prizeName;

    /**
     * 奖品图片
     */
    private String prizeImage;

    /**
     * 奖品价值
     */
    private BigDecimal prizeValue;

    /**
     * 目标人数
     */
    private Integer targetSize;

    /**
     * 当前参与人数
     */
    private Integer currentSize;

    /**
     * 参团费用
     */
    private BigDecimal entryFee;

    /**
     * 状态：0-待拼团，1-拼团中，2-已开奖，3-已退款
     * 
     * 枚举 {@link cn.iocoder.yudao.module.tv.enums.group.GroupStatusEnum}
     */
    private Integer status;

    /**
     * 中奖用户ID（开奖后更新）
     */
    private Long winnerId;

    /**
     * 随机种子（开奖时生成，方便公示验证）
     */
    private String randomSeed;

    /**
     * 开奖时间
     */
    private LocalDateTime drawTime;

    /**
     * 截止时间
     */
    private LocalDateTime expireTime;
}
