package cn.iocoder.yudao.module.tv.enums.report;

import cn.iocoder.yudao.framework.common.core.IntArrayValuable;
import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Arrays;

/**
 * 举报类型枚举
 *
 * <AUTHOR>
 */
@Getter
@AllArgsConstructor
public enum ReportTypeEnum implements IntArrayValuable {

    PORNOGRAPHY(1, "色情低俗"),
    ILLEGAL(2, "违法违规"),
    FALSE_INFO(3, "虚假信息"),
    MALICIOUS_MARKETING(4, "恶意营销"),
    COPYRIGHT_INFRINGEMENT(5, "侵权盗版"),
    OTHER(6, "其他");

    public static final int[] ARRAYS = Arrays.stream(values()).mapToInt(ReportTypeEnum::getType).toArray();

    /**
     * 类型
     */
    private final Integer type;
    /**
     * 名字
     */
    private final String name;

    @Override
    public int[] array() {
        return ARRAYS;
    }

    public static ReportTypeEnum valueOf(Integer type) {
        return Arrays.stream(values()).filter(item -> item.getType().equals(type)).findFirst().orElse(null);
    }

}
