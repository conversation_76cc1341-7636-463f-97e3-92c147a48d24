package cn.iocoder.yudao.module.tv.dal.mysql.like;

import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.mybatis.core.mapper.BaseMapperX;
import cn.iocoder.yudao.framework.mybatis.core.query.LambdaQueryWrapperX;
import cn.iocoder.yudao.module.tv.controller.app.like.vo.AppLikeVideoPageReqVO;
import cn.iocoder.yudao.module.tv.dal.dataobject.like.LikeDO;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
 * 点赞 Mapper
 */
@Mapper
public interface LikeMapper extends BaseMapperX<LikeDO> {

    /**
     * 根据用户编号和视频编号查询点赞
     *
     * @param userId 用户编号
     * @param videoId 视频编号
     * @return 点赞
     */
    default LikeDO selectByUserIdAndVideoId(Long userId, Long videoId) {
        return selectOne(new LambdaQueryWrapperX<LikeDO>()
                .eq(LikeDO::getUserId, userId)
                .eq(LikeDO::getVideoId, videoId));
    }

    /**
     * 根据用户编号和视频编号删除点赞
     *
     * @param userId 用户编号
     * @param videoId 视频编号
     * @return 影响行数
     */
    default int deleteByUserIdAndVideoId(Long userId, Long videoId) {
        return delete(new LambdaQueryWrapperX<LikeDO>()
                .eq(LikeDO::getUserId, userId)
                .eq(LikeDO::getVideoId, videoId));
    }

    /**
     * 根据用户编号查询点赞分页
     *
     * @param userId 用户编号
     * @param pageReqVO 分页请求
     * @return 点赞分页
     */
    default PageResult<LikeDO> selectPageByUserId(Long userId, AppLikeVideoPageReqVO pageReqVO) {
        return selectPage(pageReqVO, new LambdaQueryWrapperX<LikeDO>()
                .eq(LikeDO::getUserId, userId)
                .orderByDesc(LikeDO::getCreateTime));
    }

    /**
     * 根据用户编号查询点赞列表
     *
     * @param userId 用户编号
     * @return 点赞列表
     */
    default List<LikeDO> selectListByUserId(Long userId) {
        return selectList(new LambdaQueryWrapperX<LikeDO>()
                .eq(LikeDO::getUserId, userId)
                .orderByDesc(LikeDO::getCreateTime));
    }
}