package cn.iocoder.yudao.module.tv.controller;

import cn.hutool.core.util.RandomUtil;
import cn.hutool.core.util.StrUtil;
import cn.iocoder.yudao.framework.common.pojo.CommonResult;
import cn.iocoder.yudao.module.system.api.application.ApplicationApi;
import cn.iocoder.yudao.module.tv.controller.app.linking.vo.AppDeepLinkingCreateReqVO;
import org.springframework.data.redis.core.BoundHashOperations;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.annotation.security.PermitAll;
import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;

import java.io.IOException;
import java.util.Optional;

import static cn.iocoder.yudao.framework.common.exception.enums.GlobalErrorCodeConstants.NOT_IMPLEMENTED;
import static cn.iocoder.yudao.framework.common.pojo.CommonResult.success;
import static cn.iocoder.yudao.framework.web.core.util.WebFrameworkUtils.getLoginUserId;

/**
 * 默认 Controller，解决部分 module 未开启时的 404 提示。
 * 例如说，/bpm/** 路径，工作流
 *
 * <AUTHOR>
 */
@RestController
public class DeepLinkingController {
    @Resource
    private ApplicationApi applicationApi;
    @Resource
    private StringRedisTemplate stringRedisTemplate;



    // 浏览器访问网页时通常 Accept: text/html，axios/fetch 通常 Accept: application/json。
    @GetMapping(value = "/{linking}", produces = "text/html")
    @PermitAll
    public void linking(@PathVariable("linking") String linking, HttpServletResponse response) throws IOException {
        String deepLinking = linking;
        BoundHashOperations<String, Object, Object> hashOperations = stringRedisTemplate.boundHashOps(deepLinking);
        String redirectUrl = StrUtil.toStringOrNull(hashOperations.get("redirectUrl"));
        String appCode = StrUtil.toStringOrNull(hashOperations.get("appCode"));
        String inviter = StrUtil.toStringOrNull(hashOperations.get("inviter"));
        if(StrUtil.isAllNotBlank(redirectUrl,appCode,inviter)){
            // 1. 记录邀请人的内容。
            applicationApi.recordInvitationCode(appCode,Long.valueOf(inviter));
            // 2. 重定向到其他对应的页面去啦。
            response.sendRedirect(redirectUrl);
        }else{
            response.setStatus(404);
        }
    }

}
