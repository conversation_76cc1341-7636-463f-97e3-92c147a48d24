package cn.iocoder.yudao.module.tv.service.category;

import java.util.*;
import javax.validation.*;
import cn.iocoder.yudao.module.tv.controller.admin.category.vo.*;
import cn.iocoder.yudao.module.tv.dal.dataobject.category.CategoryDO;
import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.common.pojo.PageParam;

/**
 * 视频分类 Service 接口
 *
 * <AUTHOR>
 */
public interface CategoryService {

    /**
     * 创建视频分类
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    Long createCategory(@Valid CategorySaveReqVO createReqVO);

    /**
     * 更新视频分类
     *
     * @param updateReqVO 更新信息
     */
    void updateCategory(@Valid CategorySaveReqVO updateReqVO);

    /**
     * 删除视频分类
     *
     * @param id 编号
     */
    void deleteCategory(Long id);

    /**
     * 获得视频分类
     *
     * @param id 编号
     * @return 视频分类
     */
    CategoryDO getCategory(Long id);

    /**
     * 获得视频分类分页
     *
     * @param pageReqVO 分页查询
     * @return 视频分类分页
     */
    PageResult<CategoryDO> getCategoryPage(CategoryPageReqVO pageReqVO);

    /**
     * 获得启用状态的分类列表
     *
     * @param status 状态
     * @return 分类列表
     */
    List<CategoryDO> getCategoryListByStatus(Integer status);

}