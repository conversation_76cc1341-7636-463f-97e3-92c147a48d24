package cn.iocoder.yudao.module.tv.service.report.antiabuse;

import cn.iocoder.yudao.module.tv.dal.dataobject.report.ReportDO;

/**
 * 举报反滥用服务接口
 *
 * <AUTHOR>
 */
public interface ReportAntiAbuseService {

    /**
     * 检查用户是否存在恶意举报行为
     *
     * @param userId 用户ID
     * @return 是否恶意举报
     */
    boolean isMaliciousReporter(Long userId);

    /**
     * 处理恶意举报用户
     *
     * @param userId 用户ID
     * @param reportCount 举报次数
     * @param reason 处理原因
     */
    void handleMaliciousReporter(Long userId, Long reportCount, String reason);

    /**
     * 记录举报行为（用于分析）
     *
     * @param report 举报信息
     */
    void recordReportBehavior(ReportDO report);

    /**
     * 检查举报是否被驳回过多（可能是恶意举报）
     *
     * @param userId 用户ID
     * @return 是否被驳回过多
     */
    boolean hasExcessiveRejections(Long userId);

}
