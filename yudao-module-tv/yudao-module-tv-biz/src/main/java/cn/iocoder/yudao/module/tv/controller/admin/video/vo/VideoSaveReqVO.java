package cn.iocoder.yudao.module.tv.controller.admin.video.vo;

import cn.iocoder.yudao.framework.common.validation.InEnum;
import cn.iocoder.yudao.module.tv.enums.video.VideoStatusEnum;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.util.List;

@Schema(description = "管理后台 - 视频保存 Request VO")
@Data
public class VideoSaveReqVO {

    @Schema(description = "编号", example = "1024")
    private Long id;

    @Schema(description = "配置编号", example = "1024")
    private Long configId;

    @Schema(description = "标题", required = true, example = "视频标题")
    @NotEmpty(message = "标题不能为空")
    private String title;

    @Schema(description = "描述", example = "视频描述")
    private String description;

    @Schema(description = "封面图片", required = true, example = "https://www.iocoder.cn/cover.jpg")
    @NotEmpty(message = "封面图片不能为空")
    private String coverUrl;

    @Schema(description = "视频类型：0-单个视频，1-多个视频，2-图文混合", required = true, example = "0")
    @NotNull(message = "视频类型不能为空")
    private Integer type;

    @Schema(description = "主视频URL", example = "https://www.iocoder.cn/video.mp4")
    private String videoUrl;

    @Schema(description = "视频时长（秒）", example = "120")
    private Integer duration;

    @Schema(description = "分类编号", required = true, example = "1024")
    @NotNull(message = "分类编号不能为空")
    private Long categoryId;

    @Schema(description = "标签列表", example = "[\"标签1\", \"标签2\"]")
    private String tags;

    @Schema(description = "排序", example = "1024")
    private Integer sort;

    @Schema(description = "状态：0-草稿，1-发布，2-下架", required = true, example = "1")
    @NotNull(message = "状态不能为空")
    @InEnum(VideoStatusEnum.class)
    private Integer status;

    @Schema(description = "媒体资源列表")
    private List<VideoMediaSaveReqVO> mediaList;
} 