package cn.iocoder.yudao.module.tv.controller.admin.report;

import cn.iocoder.yudao.framework.apilog.core.annotation.ApiAccessLog;
import cn.iocoder.yudao.framework.common.pojo.CommonResult;
import cn.iocoder.yudao.framework.common.pojo.PageParam;
import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.common.util.object.BeanUtils;
import cn.iocoder.yudao.framework.excel.core.util.ExcelUtils;
import cn.iocoder.yudao.module.tv.controller.admin.report.vo.*;
import cn.iocoder.yudao.module.tv.controller.admin.report.vo.ReportPageReqVO;
import cn.iocoder.yudao.module.tv.controller.admin.report.vo.ReportRespVO;
import cn.iocoder.yudao.module.tv.controller.admin.report.vo.ReportSaveReqVO;
import cn.iocoder.yudao.module.tv.dal.dataobject.report.ReportDO;
import cn.iocoder.yudao.module.tv.service.report.ReportService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import java.io.IOException;
import java.util.List;

import static cn.iocoder.yudao.framework.apilog.core.enums.OperateTypeEnum.EXPORT;
import static cn.iocoder.yudao.framework.common.pojo.CommonResult.success;
import static cn.iocoder.yudao.framework.security.core.util.SecurityFrameworkUtils.getLoginUserId;

@Tag(name = "管理后台 - 举报反馈")
@RestController
@RequestMapping("/tv/report")
@Validated
public class ReportController {

    @Resource
    private ReportService reportService;

    @PostMapping("/create")
    @Operation(summary = "创建举报反馈")
    @PreAuthorize("@ss.hasPermission('tv:report:create')")
    public CommonResult<Long> createReport(@Valid @RequestBody ReportSaveReqVO createReqVO) {
        return success(reportService.createReport(createReqVO));
    }

    @PutMapping("/update")
    @Operation(summary = "更新举报反馈")
    @PreAuthorize("@ss.hasPermission('tv:report:update')")
    public CommonResult<Boolean> updateReport(@Valid @RequestBody ReportSaveReqVO updateReqVO) {
        reportService.updateReport(updateReqVO);
        return success(true);
    }

    @PutMapping("/handle")
    @Operation(summary = "处理举报反馈")
    @PreAuthorize("@ss.hasPermission('tv:report:handle')")
    public CommonResult<Boolean> handleReport(@RequestParam("id") Long id,
                                              @RequestParam("handleResult") String handleResult,
                                              @RequestParam("isViolation") Boolean isViolation) {
        reportService.handleReport(id, getLoginUserId(), handleResult, isViolation);
        return success(true);
    }

    @DeleteMapping("/delete")
    @Operation(summary = "删除举报反馈")
    @Parameter(name = "id", description = "编号", required = true)
    @PreAuthorize("@ss.hasPermission('tv:report:delete')")
    public CommonResult<Boolean> deleteReport(@RequestParam("id") Long id) {
        reportService.deleteReport(id);
        return success(true);
    }

    @GetMapping("/get")
    @Operation(summary = "获得举报反馈")
    @Parameter(name = "id", description = "编号", required = true, example = "1024")
    @PreAuthorize("@ss.hasPermission('tv:report:query')")
    public CommonResult<ReportRespVO> getReport(@RequestParam("id") Long id) {
        ReportDO report = reportService.getReport(id);
        return success(BeanUtils.toBean(report, ReportRespVO.class));
    }

    @GetMapping("/page")
    @Operation(summary = "获得举报反馈分页")
    @PreAuthorize("@ss.hasPermission('tv:report:query')")
    public CommonResult<PageResult<ReportRespVO>> getReportPage(@Valid ReportPageReqVO pageReqVO) {
        PageResult<ReportRespVO> pageResult = reportService.getReportPage(pageReqVO);
        return success(pageResult);
    }

    @GetMapping("/export-excel")
    @Operation(summary = "导出举报反馈 Excel")
    @PreAuthorize("@ss.hasPermission('tv:report:export')")
    @ApiAccessLog(operateType = EXPORT)
    public void exportReportExcel(@Valid ReportPageReqVO pageReqVO,
                                  HttpServletResponse response) throws IOException {
        pageReqVO.setPageSize(PageParam.PAGE_SIZE_NONE);
        List<ReportRespVO> list = reportService.getReportPage(pageReqVO).getList();
        // 导出 Excel
        ExcelUtils.write(response, "举报反馈.xls", "数据", ReportRespVO.class, list);
    }

    @GetMapping("/detail")
    @Operation(summary = "获得举报详情")
    @Parameter(name = "id", description = "编号", required = true, example = "1024")
    @PreAuthorize("@ss.hasPermission('tv:report:query')")
    public CommonResult<ReportDetailRespVO> getReportDetail(@RequestParam("id") Long id) {
        ReportDetailRespVO detail = reportService.getReportDetail(id);
        return success(detail);
    }

    @GetMapping("/statistics")
    @Operation(summary = "获得举报统计")
    @PreAuthorize("@ss.hasPermission('tv:report:query')")
    public CommonResult<ReportStatisticsRespVO> getReportStatistics(@Valid ReportStatisticsReqVO reqVO) {
        ReportStatisticsRespVO statistics = reportService.getReportStatistics(reqVO);
        return success(statistics);
    }

    @GetMapping("/pending-page")
    @Operation(summary = "获得待办举报分页")
    @PreAuthorize("@ss.hasPermission('tv:report:query')")
    public CommonResult<PageResult<ReportRespVO>> getPendingReportPage(@Valid ReportPageReqVO pageReqVO) {
        PageResult<ReportRespVO> pageResult = reportService.getPendingReportPage(pageReqVO);
        return success(pageResult);
    }

    @PutMapping("/audit")
    @Operation(summary = "审核举报")
    @PreAuthorize("@ss.hasPermission('tv:report:audit')")
    public CommonResult<Boolean> auditReport(@Valid @RequestBody ReportAuditReqVO auditReqVO) {
        reportService.auditReport(auditReqVO, getLoginUserId());
        return success(true);
    }

    @PutMapping("/batch-audit")
    @Operation(summary = "批量审核举报")
    @PreAuthorize("@ss.hasPermission('tv:report:audit')")
    public CommonResult<Boolean> batchAuditReports(@RequestParam("ids") List<Long> ids,
                                                    @RequestParam("action") String action,
                                                    @RequestParam("handleResult") String handleResult) {
        reportService.batchAuditReports(ids, action, handleResult, getLoginUserId());
        return success(true);
    }

}
