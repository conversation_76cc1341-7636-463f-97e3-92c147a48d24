package cn.iocoder.yudao.module.tv.service.privilege;

import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.common.util.object.BeanUtils;
import cn.iocoder.yudao.module.tv.controller.admin.privilege.vo.PrivilegePageReqVO;
import cn.iocoder.yudao.module.tv.controller.admin.privilege.vo.PrivilegeSaveReqVO;
import cn.iocoder.yudao.module.tv.controller.app.privilege.vo.AppPrivilegeRespVO;
import cn.iocoder.yudao.module.tv.convert.privilege.PrivilegeConvert;
import cn.iocoder.yudao.module.tv.dal.dataobject.privilege.PrivilegeDO;
import cn.iocoder.yudao.module.tv.dal.dataobject.privilege.UserPrivilegeDO;
import cn.iocoder.yudao.module.tv.dal.mysql.privilege.PrivilegeMapper;
import cn.iocoder.yudao.module.tv.dal.mysql.privilege.UserPrivilegeMapper;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.validation.annotation.Validated;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.time.temporal.ChronoUnit;

import static cn.iocoder.yudao.framework.common.exception.util.ServiceExceptionUtil.exception;
import static cn.iocoder.yudao.module.tv.enums.ErrorCodeConstants.PRIVILEGE_NOT_EXISTS;

/**
 * 特权 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class PrivilegeServiceImpl implements PrivilegeService {

    @Resource
    private PrivilegeMapper privilegeMapper;

    @Resource
    private UserPrivilegeMapper userPrivilegeMapper;

    @Override
    public Long createPrivilege(PrivilegeSaveReqVO createReqVO) {
        // 创建特权
        PrivilegeDO privilege = PrivilegeConvert.INSTANCE.convert(createReqVO);
        privilegeMapper.insert(privilege);
        return privilege.getId();
    }

    @Override
    public void updatePrivilege(PrivilegeSaveReqVO updateReqVO) {
        // 校验存在
        validatePrivilegeExists(updateReqVO.getId());
        // 更新特权
        PrivilegeDO updateObj = PrivilegeConvert.INSTANCE.convert(updateReqVO);
        privilegeMapper.updateById(updateObj);
    }

    @Override
    public void deletePrivilege(Long id) {
        // 校验存在
        validatePrivilegeExists(id);
        // 删除特权
        privilegeMapper.deleteById(id);
    }

    @Override
    public PrivilegeDO getPrivilege(Long id) {
        return privilegeMapper.selectById(id);
    }

    @Override
    public PageResult<PrivilegeDO> getPrivilegePage(PrivilegePageReqVO pageReqVO) {
        return privilegeMapper.selectPage(pageReqVO);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Long openPrivilege(Long userId, Long privilegeId) {
        // 校验特权是否存在
        PrivilegeDO privilege = validatePrivilegeExists(privilegeId);
        // 查询用户所有未过期特权，取endTime最大值
        UserPrivilegeDO lastPrivilege = userPrivilegeMapper.selectOne(
                new LambdaQueryWrapper<UserPrivilegeDO>()
                        .eq(UserPrivilegeDO::getUserId, userId)
                        .eq(UserPrivilegeDO::getStatus, 1)
                        .gt(UserPrivilegeDO::getEndTime, LocalDateTime.now())
                        .orderByDesc(UserPrivilegeDO::getEndTime)
                        .last("LIMIT 1"));
        LocalDateTime startTime = LocalDateTime.now();
        if (lastPrivilege != null && lastPrivilege.getEndTime().isAfter(startTime)) {
            startTime = lastPrivilege.getEndTime();
        }
        LocalDateTime endTime = startTime.plusDays(privilege.getDuration());
        // 插入新记录
        UserPrivilegeDO newUserPrivilege = new UserPrivilegeDO();
        newUserPrivilege.setUserId(userId);
        newUserPrivilege.setPrivilegeId(privilegeId);
        newUserPrivilege.setStartTime(startTime);
        newUserPrivilege.setEndTime(endTime);
        newUserPrivilege.setStatus(1);
        userPrivilegeMapper.insert(newUserPrivilege);
        return newUserPrivilege.getId();
    }

    @Override
    public boolean hasPrivilege(Long userId) {
        return userPrivilegeMapper.selectCount(new LambdaQueryWrapper<UserPrivilegeDO>()
                .eq(UserPrivilegeDO::getUserId, userId)
                .eq(UserPrivilegeDO::getStatus, 1)
                .gt(UserPrivilegeDO::getEndTime, LocalDateTime.now())) > 0;
    }

    @Override
    public AppPrivilegeRespVO getUserPrivilege(Long userId) {
        // 获取用户当前有效的特权
        UserPrivilegeDO userPrivilege = userPrivilegeMapper.selectOne(new LambdaQueryWrapper<UserPrivilegeDO>()
                .eq(UserPrivilegeDO::getUserId, userId)
                .eq(UserPrivilegeDO::getStatus, 1)
                .gt(UserPrivilegeDO::getEndTime, LocalDateTime.now())
                .orderByDesc(UserPrivilegeDO::getEndTime)
                .last("LIMIT 1"));

        if (userPrivilege == null) {
            return null;
        }

        // 获取特权信息
        PrivilegeDO privilege = privilegeMapper.selectById(userPrivilege.getPrivilegeId());
        if (privilege == null) {
            return null;
        }

        // 转换为 VO
        AppPrivilegeRespVO respVO = BeanUtils.toBean(privilege,AppPrivilegeRespVO.class);
        respVO.setHasPrivilege(true);
        respVO.setRemainingDays((int) ChronoUnit.DAYS.between(LocalDateTime.now(), userPrivilege.getEndTime()));
        return respVO;
    }

    private PrivilegeDO validatePrivilegeExists(Long id) {
        PrivilegeDO privilege = privilegeMapper.selectById(id);
        if (privilege == null) {
            throw exception(PRIVILEGE_NOT_EXISTS);
        }
        return privilege;
    }

} 