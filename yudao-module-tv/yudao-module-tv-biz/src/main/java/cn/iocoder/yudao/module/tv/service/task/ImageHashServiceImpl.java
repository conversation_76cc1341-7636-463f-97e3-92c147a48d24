package cn.iocoder.yudao.module.tv.service.task;

import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.imageio.ImageIO;
import java.awt.*;
import java.awt.image.BufferedImage;
import java.io.ByteArrayInputStream;
import java.io.IOException;
import java.util.Base64;

/**
 * 图片哈希服务实现类
 * 使用感知哈希算法（pHash）计算图片指纹
 *
 * <AUTHOR>
 */
@Service
@Slf4j
public class ImageHashServiceImpl implements ImageHashService {

    private static final int HASH_SIZE = 8; // 哈希大小，生成64位哈希
    private static final int SMALL_SIZE = 32; // 缩放后的图片大小

    @Override
    public String calculateImageHash(String base64Image) {
        try {
            // 1. 解码base64图片
            byte[] imageBytes = Base64.getDecoder().decode(base64Image.replaceAll("^data:image/[^;]*;base64,", ""));
            BufferedImage image = ImageIO.read(new ByteArrayInputStream(imageBytes));
            
            if (image == null) {
                throw new IllegalArgumentException("无法解析图片数据");
            }

            // 2. 缩放图片到32x32
            BufferedImage smallImage = resizeImage(image, SMALL_SIZE, SMALL_SIZE);

            // 3. 转换为灰度图
            BufferedImage grayImage = convertToGrayscale(smallImage);

            // 4. 计算DCT（离散余弦变换）
            double[][] dctMatrix = calculateDCT(grayImage);

            // 5. 提取左上角8x8区域的低频信息
            double[][] lowFreq = extractLowFrequency(dctMatrix, HASH_SIZE);

            // 6. 计算平均值
            double average = calculateAverage(lowFreq);

            // 7. 生成哈希值
            return generateHash(lowFreq, average);

        } catch (Exception e) {
            log.error("[calculateImageHash][计算图片哈希失败]", e);
            throw new RuntimeException("计算图片哈希失败", e);
        }
    }

    @Override
    public int calculateHammingDistance(String hash1, String hash2) {
        if (hash1.length() != hash2.length()) {
            throw new IllegalArgumentException("哈希值长度不一致");
        }

        int distance = 0;
        for (int i = 0; i < hash1.length(); i++) {
            if (hash1.charAt(i) != hash2.charAt(i)) {
                distance++;
            }
        }
        return distance;
    }

    @Override
    public boolean isSimilar(String hash1, String hash2, int threshold) {
        int distance = calculateHammingDistance(hash1, hash2);
        return distance <= threshold;
    }

    /**
     * 缩放图片
     */
    private BufferedImage resizeImage(BufferedImage originalImage, int width, int height) {
        BufferedImage resizedImage = new BufferedImage(width, height, BufferedImage.TYPE_INT_RGB);
        Graphics2D g = resizedImage.createGraphics();
        g.setRenderingHint(RenderingHints.KEY_INTERPOLATION, RenderingHints.VALUE_INTERPOLATION_BILINEAR);
        g.drawImage(originalImage, 0, 0, width, height, null);
        g.dispose();
        return resizedImage;
    }

    /**
     * 转换为灰度图
     */
    private BufferedImage convertToGrayscale(BufferedImage colorImage) {
        BufferedImage grayImage = new BufferedImage(colorImage.getWidth(), colorImage.getHeight(), BufferedImage.TYPE_BYTE_GRAY);
        Graphics2D g = grayImage.createGraphics();
        g.drawImage(colorImage, 0, 0, null);
        g.dispose();
        return grayImage;
    }

    /**
     * 计算DCT（简化版本）
     */
    private double[][] calculateDCT(BufferedImage image) {
        int width = image.getWidth();
        int height = image.getHeight();
        double[][] matrix = new double[height][width];

        // 获取像素值
        for (int y = 0; y < height; y++) {
            for (int x = 0; x < width; x++) {
                matrix[y][x] = image.getRGB(x, y) & 0xFF; // 获取灰度值
            }
        }

        // 简化的DCT计算（实际应用中可以使用更精确的DCT算法）
        return matrix;
    }

    /**
     * 提取低频信息
     */
    private double[][] extractLowFrequency(double[][] dctMatrix, int size) {
        double[][] lowFreq = new double[size][size];
        for (int y = 0; y < size; y++) {
            for (int x = 0; x < size; x++) {
                lowFreq[y][x] = dctMatrix[y][x];
            }
        }
        return lowFreq;
    }

    /**
     * 计算平均值
     */
    private double calculateAverage(double[][] matrix) {
        double sum = 0;
        int count = 0;
        for (double[] row : matrix) {
            for (double value : row) {
                sum += value;
                count++;
            }
        }
        return sum / count;
    }

    /**
     * 生成哈希值
     */
    private String generateHash(double[][] matrix, double average) {
        StringBuilder hash = new StringBuilder();
        for (double[] row : matrix) {
            for (double value : row) {
                hash.append(value > average ? '1' : '0');
            }
        }
        return hash.toString();
    }

}
