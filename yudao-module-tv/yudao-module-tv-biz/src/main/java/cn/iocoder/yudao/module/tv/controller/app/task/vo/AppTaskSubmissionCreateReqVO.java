package cn.iocoder.yudao.module.tv.controller.app.task.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;

import javax.validation.constraints.*;

@Schema(description = "用户 App - 任务提交创建 Request VO")
@Data
public class AppTaskSubmissionCreateReqVO {

    @Schema(description = "任务ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "1024")
    @NotNull(message = "任务ID不能为空")
    private Long taskId;

    @Schema(description = "截图base64数据", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotEmpty(message = "截图不能为空")
    private String screenshotBase64;

    @Schema(description = "提交说明", example = "已分享到微信群并获得多个点赞")
    private String description;

}
