package cn.iocoder.yudao.module.tv.controller.app.task;

import cn.iocoder.yudao.framework.common.pojo.CommonResult;
import cn.iocoder.yudao.framework.common.pojo.PageResult;

import cn.iocoder.yudao.module.tv.controller.app.task.vo.AppTaskRespVO;
import cn.iocoder.yudao.module.tv.controller.app.task.vo.AppTaskSubmissionCreateReqVO;
import cn.iocoder.yudao.module.tv.controller.app.task.vo.AppTaskSubmissionPageReqVO;
import cn.iocoder.yudao.module.tv.controller.app.task.vo.AppTaskSubmissionRespVO;
import cn.iocoder.yudao.module.tv.service.task.TaskService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.List;

import static cn.iocoder.yudao.framework.common.pojo.CommonResult.success;
import static cn.iocoder.yudao.framework.security.core.util.SecurityFrameworkUtils.getLoginUserId;

@Tag(name = "用户 App - 任务系统")
@RestController
@RequestMapping("/tv/task")
@Validated
public class AppTaskController {

    @Resource
    private TaskService taskService;

    @GetMapping("/list")
    @Operation(summary = "获得可用任务列表")

    public CommonResult<List<AppTaskRespVO>> getTaskList() {
        List<AppTaskRespVO> list = taskService.getUserAvailableTasks(getLoginUserId());
        return success(list);
    }

    @PostMapping("/submit")
    @Operation(summary = "提交任务")

    public CommonResult<Long> submitTask(@Valid @RequestBody AppTaskSubmissionCreateReqVO createReqVO) {
        return success(taskService.submitTask(getLoginUserId(), createReqVO));
    }

    @GetMapping("/submission/page")
    @Operation(summary = "获得我的任务提交分页")
    public CommonResult<PageResult<AppTaskSubmissionRespVO>> getTaskSubmissionPage(@Valid AppTaskSubmissionPageReqVO pageReqVO) {
        PageResult<AppTaskSubmissionRespVO> pageResult = taskService.getUserTaskSubmissionPage(getLoginUserId(), pageReqVO);
        return success(pageResult);
    }

}
