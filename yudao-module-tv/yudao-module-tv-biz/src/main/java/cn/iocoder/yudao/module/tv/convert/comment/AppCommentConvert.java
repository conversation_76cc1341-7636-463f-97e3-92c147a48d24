package cn.iocoder.yudao.module.tv.convert.comment;

import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.module.tv.controller.app.comment.vo.AppCommentRespVO;
import cn.iocoder.yudao.module.tv.dal.dataobject.comment.CommentDO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

@Mapper
public interface AppCommentConvert {

    AppCommentConvert INSTANCE = Mappers.getMapper(AppCommentConvert.class);

    AppCommentRespVO convert(CommentDO bean);

    PageResult<AppCommentRespVO> convertPage(PageResult<CommentDO> page);
} 