package cn.iocoder.yudao.module.tv.convert.privilege;

import cn.iocoder.yudao.module.tv.controller.admin.privilege.vo.PrivilegeBaseVO;
import cn.iocoder.yudao.module.tv.controller.admin.privilege.vo.PrivilegeSaveReqVO;
import cn.iocoder.yudao.module.tv.dal.dataobject.privilege.PrivilegeDO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

/**
 * 特权 Convert
 *
 * <AUTHOR>
 */
@Mapper
public interface PrivilegeConvert {

    PrivilegeConvert INSTANCE = Mappers.getMapper(PrivilegeConvert.class);

    PrivilegeDO convert(PrivilegeSaveReqVO bean);

    PrivilegeBaseVO convert(PrivilegeDO bean);

} 