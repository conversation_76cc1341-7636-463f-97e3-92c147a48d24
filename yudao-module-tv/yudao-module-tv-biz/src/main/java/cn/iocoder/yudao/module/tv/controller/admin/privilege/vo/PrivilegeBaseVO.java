package cn.iocoder.yudao.module.tv.controller.admin.privilege.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.math.BigDecimal;

@Schema(description = "管理后台 - 特权 Base VO")
@Data
public class PrivilegeBaseVO {

    @Schema(description = "编号", example = "1")
    private Long id;

    @Schema(description = "特权名称", example = "VIP会员")
    private String name;

    @Schema(description = "特权描述", example = "开通后可以免费观看所有视频")
    private String description;

    @Schema(description = "价格", example = "99.00")
    private BigDecimal price;

    @Schema(description = "有效期（天）", example = "30")
    private Integer duration;

    @Schema(description = "状态", example = "1")
    private Integer status;

} 