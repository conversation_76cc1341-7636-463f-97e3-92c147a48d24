package cn.iocoder.yudao.module.tv.controller.app.history.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

import javax.validation.constraints.NotNull;

@Schema(description = "用户 APP - 观看历史创建 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class AppHistoryCreateReqVO extends AppHistoryBaseVO {

    @Schema(description = "用户编号", required = true, example = "1024")
    @NotNull(message = "用户编号不能为空")
    private Long userId;

    @Schema(description = "视频编号", required = true, example = "2048")
    @NotNull(message = "视频编号不能为空")
    private Long videoId;

    @Schema(description = "观看进度", required = true, example = "100")
    @NotNull(message = "观看进度不能为空")
    private Integer progress;

    @Schema(description = "是否看完", required = true, example = "true")
    @NotNull(message = "是否看完不能为空")
    private Boolean finished;
} 