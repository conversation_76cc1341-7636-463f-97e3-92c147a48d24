package cn.iocoder.yudao.module.tv.service.content.impl;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.StrUtil;
import cn.iocoder.yudao.framework.common.util.number.MoneyUtils;
import cn.iocoder.yudao.framework.mybatis.core.query.LambdaQueryWrapperX;
import cn.iocoder.yudao.module.tv.controller.admin.content.vo.ContentImportReqVO;
import cn.iocoder.yudao.module.tv.controller.admin.content.vo.ContentImportResultVO;
import cn.iocoder.yudao.module.tv.service.ProcessService;
import cn.iocoder.yudao.module.tv.service.content.ContentImportService;
import cn.iocoder.yudao.module.tv.service.content.FileScanService;
import cn.iocoder.yudao.module.tv.service.content.model.CollectionInfo;
import cn.iocoder.yudao.module.tv.service.content.model.GroupInfo;
import cn.iocoder.yudao.module.tv.service.content.model.MediaFileInfo;
import cn.iocoder.yudao.module.tv.dal.dataobject.content.ContentDO;
import cn.iocoder.yudao.module.tv.dal.dataobject.content.ContentCollectionItemDO;
import cn.iocoder.yudao.module.tv.dal.dataobject.content.ContentMediaDO;
import cn.iocoder.yudao.module.tv.dal.mysql.content.ContentMapper;
import cn.iocoder.yudao.module.tv.dal.mysql.content.ContentCollectionItemMapper;
import cn.iocoder.yudao.module.tv.dal.mysql.content.ContentMediaMapper;
import cn.iocoder.yudao.module.tv.service.dto.VideoProcessResult;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.io.File;
import java.math.BigDecimal;
import java.nio.file.Path;
import java.time.LocalDateTime;
import java.util.*;

/**
 * 内容导入服务实现类
 */
@Slf4j
@Service
public class ContentImportServiceImpl implements ContentImportService {

    @Resource
    private FileScanService fileScanService;
    @Resource
    private ProcessService processService;
    @Resource
    private ContentMapper contentMapper;
    @Resource
    private ContentCollectionItemMapper collectionItemMapper;
    @Resource
    private ContentMediaMapper mediaMapper;


    @Override
    @Transactional(rollbackFor = Exception.class)
    public ContentImportResultVO importContent(ContentImportReqVO reqVO) {
        ContentImportResultVO result = new ContentImportResultVO();
        result.setStartTime(LocalDateTime.now());
        result.setSuccess(false);
        result.setErrors(new ArrayList<>());

        try {
            // 扫描并导入
            ContentImportResultVO scanResult = scanAndImport(reqVO.getDownloadRoot(),reqVO.getSearcher());
            result.setCollectionCount(scanResult.getCollectionCount());
            result.setGroupCount(scanResult.getGroupCount());
            result.setMediaCount(scanResult.getMediaCount());
            result.setSuccess(true);
        } catch (Exception e) {
            log.error("导入内容失败", e);
            result.getErrors().add("导入失败：" + e.getMessage());
        } finally {
            result.setEndTime(LocalDateTime.now());
            result.setDuration(result.getEndTime().toInstant(java.time.ZoneOffset.UTC).toEpochMilli() - 
                             result.getStartTime().toInstant(java.time.ZoneOffset.UTC).toEpochMilli());
        }

        return result;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public ContentImportResultVO scanAndImport(String downloadRoot,String searcher) {
        ContentImportResultVO result = new ContentImportResultVO();
        result.setStartTime(LocalDateTime.now());
        result.setSuccess(false);
        result.setErrors(new ArrayList<>());

        try {
            // 1. 扫描所有合集
            List<CollectionInfo> collections = fileScanService.scanCollections(downloadRoot);
            int collectionCount = 0;
            int groupCount = 0;
            int mediaCount = 0;

            // 2. 处理每个合集
            col: for (CollectionInfo collection : collections) {
                Long collectionContentId = null;
                collection.setCoverPath(processImage(collection.getCoverPath()));
                // 去重判断
                ContentDO contentExists = isContentExists(decorateUrl(collection.getCoverPath(), searcher));
                if (contentExists != null) {
                    log.info("合集 {} 已存在，跳过导入", collection.getCollectionId());
                    collectionContentId = contentExists.getId();
                }else{
                    // 导入合集
                    collectionContentId = importCollection(collection, searcher);
                    collectionCount++;
                }



                // 处理合集下的组
                for (GroupInfo group : collection.getGroups()) {
                    Long groupContentId = importGroup(group,searcher);
                    // 判断是否导入过啦。
                    if(groupContentId != null){
                        groupCount++;

                        // 建立关联关系
                        createCollectionItemRelation(collectionContentId, groupContentId, group.getSort());

                        // 导入媒体文件
                        mediaCount += importMediaFiles(groupContentId, group.getMediaFiles(), searcher);

                        // 更新翻页的排序时间。 保证新更新的合集在前面。
                        contentMapper.updatePublishTime(collectionContentId,LocalDateTime.now());
                        // 防止太多的数据导致的卡死
                        if(mediaCount > 100){
                            break col;
                        }
                    }

                }

            }

            result.setCollectionCount(collectionCount);
            result.setGroupCount(groupCount);
            result.setMediaCount(mediaCount);
            result.setSuccess(true);

        } catch (Exception e) {
            log.error("扫描导入失败", e);
            result.getErrors().add("扫描导入失败：" + e.getMessage());
        } finally {
            result.setEndTime(LocalDateTime.now());
            result.setDuration(result.getEndTime().toInstant(java.time.ZoneOffset.UTC).toEpochMilli() - 
                             result.getStartTime().toInstant(java.time.ZoneOffset.UTC).toEpochMilli());
        }

        return result;
    }

    public ContentDO isContentExists(String url) {
        // 检查是否存在以该groupId为标题的合集内容
        return contentMapper.selectOne(new LambdaQueryWrapperX<ContentDO>()
                .eq(ContentDO::getCoverUrl, url));
    }

    /**
     * 导入合集
     */
    private Long importCollection(CollectionInfo collection,String searcher) {
        ContentDO content = new ContentDO();
        String description = readFileContent(collection.getDescriptionPath());
        content.setTitle(StrUtil.maxLength(description,96));
        content.setDescription(description);
        content.setCoverUrl(decorateUrl(collection.getCoverPath(),searcher));
        content.setType(4); // 合集类型
        content.setStatus(1); // 发布状态
        content.setCategoryId(1L); // 默认分类
        content.setPublishTime(LocalDateTime.now());
        content.setIsPaid(0);
        content.setCreator("system");
        content.setCreateTime(LocalDateTime.now());
        content.setUpdater("system");
        content.setUpdateTime(LocalDateTime.now());
        content.setDeleted(false);
        content.setTenantId(1L);

        contentMapper.insert(content);
        return content.getId();
    }
    public String decorateUrl(Path path,String searcher) {
        return decorateUrl(StrUtil.toStringOrNull(path),searcher);
    }
    public String decorateUrl(String str,String searcher) {
        return StrUtil.replace( str, searcher, "");
    }
    /**
     * 导入组
     */
    private Long importGroup(GroupInfo group,String searcher) {
        ContentDO content = new ContentDO();
        String description = readFileContent(group.getDescriptionPath());
        // msg.txt 没有就用文件夹的名字。
        if(StrUtil.isBlank(description)){
            // 这段代码用 Optional 如何编写呢?
            description = Optional.ofNullable(group.getPaidFolderPath())
                    .map(f -> f.getFileName().toString())
                    .orElse(null); // 或者 orElse("默认值")，根据你的需求
        }
        // 直接跳过啦。
        if(StrUtil.isBlank(description)) return null;

        content.setTitle(StrUtil.maxLength(description,96));
        content.setDescription(description);
        content.setType(5); // 图片视频混合类型
        content.setStatus(1); // 发布状态
        content.setCategoryId(1L); // 默认分类
        content.setPublishTime(LocalDateTime.now());
        int price = MoneyUtils.calculateRatePrice(CollectionUtil.size(group.getMediaFiles()) + 10, 2d);
        content.setPrice(new BigDecimal(price) ); // 默认价格
        content.setIsPaid(price > 0 ? 1 : 0); // 付费内容
        content.setCreator("system");
        content.setCreateTime(LocalDateTime.now());
        content.setUpdater("system");
        content.setUpdateTime(LocalDateTime.now());
        content.setDeleted(false);
        content.setTenantId(1L);

        // 设置封面（preview_sort=1的文件）
        Path coverUrl = group.getMediaFiles().stream()
                .filter( x -> x.getIsPreview() && x.getType() == 0 )
                .min(Comparator.comparing(MediaFileInfo::getPreviewSort))
                .map(MediaFileInfo::getFilePath)
                .orElse(null);
        // 如果这里还是没有封面的话，就从付费里面找一张。
        if(coverUrl == null){
            coverUrl = group.getMediaFiles().stream()
                    .filter( x -> !x.getIsPreview() && x.getType() == 0 )
                    .min(Comparator.comparing(MediaFileInfo::getSort))
                    .map(MediaFileInfo::getFilePath)
                    .orElse(null);
        }
        // 优化图片，获取优化后图片URL
        String decorateUrl = processImage(coverUrl,searcher);
        content.setCoverUrl(decorateUrl);
        if (isContentExists(decorateUrl) == null) {
            contentMapper.insert(content);
            return content.getId();
        }else{
            return null;
        }
    }

    /**
     * 创建合集与组的关联关系
     */
    private void createCollectionItemRelation(Long collectionContentId, Long groupContentId, Integer sort) {
        ContentCollectionItemDO item = new ContentCollectionItemDO();
        item.setContentId(collectionContentId);
        item.setItemContentId(groupContentId);
        item.setSort(sort != null ? sort : 0);
        item.setCreator("system");
        item.setCreateTime(LocalDateTime.now());
        item.setUpdater("system");
        item.setUpdateTime(LocalDateTime.now());
        item.setDeleted(false);

        collectionItemMapper.insert(item);
    }

    /**
     * 导入媒体文件
     */
    private int importMediaFiles(Long groupContentId, List<MediaFileInfo> mediaFiles,String searcher) {
        int count = 0;
        // 先将图片排在前面，视频排在后面
        List<MediaFileInfo> sortedMediaFiles = new ArrayList<>(mediaFiles);
        sortedMediaFiles.sort(Comparator.comparingInt(MediaFileInfo::getType));
        for (MediaFileInfo mediaFile : sortedMediaFiles) {
            String filePath = decorateUrl(mediaFile.getFilePath(),searcher);
            // 文件路径去重
            if (mediaMapper.selectCount(new LambdaQueryWrapperX<ContentMediaDO>()
                    .eq(ContentMediaDO::getUrl, filePath)) > 0) {
                log.info("媒体文件 {} 已存在，跳过导入", filePath);
                continue;
            }
            ContentMediaDO media = new ContentMediaDO();
            media.setContentId(groupContentId);
            Integer type = mediaFile.getType();
            media.setType(type);
            if(type == 0){
                // 优化图片，获取优化后图片URL
                media.setUrl(processImage(mediaFile.getFilePath(),searcher));
            } else if(type == 1){
                // 对于视频的额外处理。
                VideoProcessResult processResult = processService.processVideo(mediaFile.getFilePath().toFile());
                media.setDuration(processResult.getDuration());
                String firstFrameImageUrl = processResult.getFirstFrameImageUrl();
                media.setCoverUrl(decorateUrl(firstFrameImageUrl,searcher));
                media.setUrl(filePath);
            }
            media.setSort(mediaFile.getSort());
            media.setIsPreview(mediaFile.getIsPreview());
            media.setPreviewSort(mediaFile.getPreviewSort());
            media.setCreator("system");
            media.setCreateTime(LocalDateTime.now());
            media.setUpdater("system");
            media.setUpdateTime(LocalDateTime.now());
            media.setDeleted(false);
            media.setTenantId(1L);
            mediaMapper.insert(media);
            count++;
        }
        return count;
    }
    /**
     * 优化图片，获取优化后图片URL
     * @param path
     * @param searcher
     * @return
     */
    public String processImage(Path path,String searcher){
        if(path != null){
            File optimizedImageUrl = processService.optimizedImage(path.toFile());
            if (optimizedImageUrl != null) {
                return decorateUrl(optimizedImageUrl.getAbsolutePath(), searcher);
            }
        }
        return null;
    }
    /**
     * 优化图片，获取优化后图片Path
     * @param path
     * @return
     */
    public Path processImage(Path path){
        if(path != null){
            File optimizedImageUrl = processService.optimizedImage(path.toFile());
            if (optimizedImageUrl != null) {
                return optimizedImageUrl.toPath();
            }
        }
        return null;
    }
    /**
     * 读取文件内容
     */
    private String readFileContent(java.nio.file.Path filePath) {
        if (filePath == null || !java.nio.file.Files.exists(filePath)) {
            return "";
        }
        try {
            return new String(java.nio.file.Files.readAllBytes(filePath));
        } catch (Exception e) {
            log.warn("读取文件内容失败：{}", filePath, e);
            return "";
        }
    }

}
/**
 1. 封面必须是图片。 不能是视频。
 2. msg.txt 没有就用文件夹的名字。
 3. 爬虫报错的问题，解决。


 /app-api/tv/content/page?pageNo=1&pageSize=10&type=4  获得不同明星合集。

 /app-api/tv/content/get-content-page?pageNo=1&pageSize=30&type=4&contentId=2017  某个明星所有的压缩包分页

 /app-api/tv/content/detail?id=2018  查看是否已经解锁啦。

 /app-api/tv/content/preview-content-page?pageNo=1&pageSize=3&type=5&contentId=2018 没有解锁就调用这个预览

 /app-api/tv/content/get-content-page?pageNo=1&pageSize=3&type=5&contentId=2018 没有解锁就调用这个预览

 */
