package cn.iocoder.yudao.module.tv.dal.mysql.group;

import cn.iocoder.yudao.framework.mybatis.core.mapper.BaseMapperX;
import cn.iocoder.yudao.framework.mybatis.core.query.LambdaQueryWrapperX;
import cn.iocoder.yudao.module.tv.dal.dataobject.group.UserLotteryStatDO;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
 * 用户抽奖统计 Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface UserLotteryStatMapper extends BaseMapperX<UserLotteryStatDO> {

    default UserLotteryStatDO selectByUserId(Long userId) {
        return selectOne(new LambdaQueryWrapperX<UserLotteryStatDO>()
                .eq(UserLotteryStatDO::getUserId, userId));
    }

    default List<UserLotteryStatDO> selectListByUserIds(List<Long> userIds) {
        return selectList(new LambdaQueryWrapperX<UserLotteryStatDO>()
                .in(UserLotteryStatDO::getUserId, userIds));
    }

    default List<UserLotteryStatDO> selectListByFailCountGte(Integer failCount) {
        return selectList(new LambdaQueryWrapperX<UserLotteryStatDO>()
                .ge(UserLotteryStatDO::getFailCount, failCount)
                .orderByDesc(UserLotteryStatDO::getFailCount)
                .orderByAsc(UserLotteryStatDO::getLastJoinTime));
    }

    default List<UserLotteryStatDO> selectTopWinners(Integer limit) {
        return selectList(new LambdaQueryWrapperX<UserLotteryStatDO>()
                .orderByDesc(UserLotteryStatDO::getTotalWin)
                .orderByDesc(UserLotteryStatDO::getTotalJoin)
                .last("LIMIT " + limit));
    }

    default Long countByFailCountGte(Integer failCount) {
        return selectCount(new LambdaQueryWrapperX<UserLotteryStatDO>()
                .ge(UserLotteryStatDO::getFailCount, failCount));
    }
}
