package cn.iocoder.yudao.module.tv.job.video;

import java.io.File;

/**
 * 视频信息对象
 * 包含视频文件、封面文件和时长信息
 * 
 * <AUTHOR>
 */
public class VideoInfo {
    
    /**
     * 视频文件（可能是MP4或M3U8文件）
     */
    private File videoFile;
    
    /**
     * 封面文件
     */
    private File coverFile;
    
    /**
     * 视频时长（秒）
     */
    private int duration;
    
    public VideoInfo() {
    }
    
    public VideoInfo(File videoFile, File coverFile, int duration) {
        this.videoFile = videoFile;
        this.coverFile = coverFile;
        this.duration = duration;
    }
    
    public File getVideoFile() {
        return videoFile;
    }
    
    public void setVideoFile(File videoFile) {
        this.videoFile = videoFile;
    }
    
    public File getCoverFile() {
        return coverFile;
    }
    
    public void setCoverFile(File coverFile) {
        this.coverFile = coverFile;
    }
    
    public int getDuration() {
        return duration;
    }
    
    public void setDuration(int duration) {
        this.duration = duration;
    }
    
    @Override
    public String toString() {
        return "VideoInfo{" +
                "videoFile=" + (videoFile != null ? videoFile.getName() : "null") +
                ", coverFile=" + (coverFile != null ? coverFile.getName() : "null") +
                ", duration=" + duration +
                '}';
    }
}
