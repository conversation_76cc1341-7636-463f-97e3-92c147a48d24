package cn.iocoder.yudao.module.tv.service.report.handler;

import cn.iocoder.yudao.module.tv.dal.dataobject.report.ReportDO;

/**
 * 举报处理器接口
 * 
 * 用于业务解耦，不同业务模块可以实现自己的举报处理逻辑
 *
 * <AUTHOR>
 */
public interface ReportHandler {

    /**
     * 获取支持的业务类型
     *
     * @return 业务类型
     */
    String getBusinessType();

    /**
     * 举报创建后的处理逻辑
     * 
     * @param report 举报信息
     */
    default void afterReportCreate(ReportDO report) {
        // 默认空实现，子类可以重写
    }

    /**
     * 举报处理后的业务逻辑
     * 
     * @param report 举报信息
     * @param isViolation 是否违规（true-违规需要处理，false-不违规驳回）
     */
    default void afterReportHandle(ReportDO report, boolean isViolation) {
        // 默认空实现，子类可以重写
    }

    /**
     * 检查是否需要自动处理
     * 
     * @param businessId 业务ID
     * @param reportCount 当前举报次数
     * @return 是否需要自动处理
     */
    default boolean shouldAutoHandle(Long businessId, Long reportCount) {
        // 默认超过3次举报就自动处理
        return reportCount >= 3;
    }

    /**
     * 自动处理逻辑
     * 
     * @param businessId 业务ID
     * @param reportCount 举报次数
     */
    default void autoHandle(Long businessId, Long reportCount) {
        // 默认空实现，子类可以重写
    }

    /**
     * 获取业务对象的详细信息（用于管理后台展示）
     * 
     * @param businessId 业务ID
     * @return 业务对象信息
     */
    default Object getBusinessDetail(Long businessId) {
        return null;
    }

}
