package cn.iocoder.yudao.module.tv.enums.message;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Arrays;

/**
 * 消息类型枚举
 *
 * <AUTHOR>
 */
@Getter
@AllArgsConstructor
public enum MessageTypeEnum {

    CONTENT_UPLOAD("CONTENT_UPLOAD", "内容上传处理"),
    CONTENT_COMPRESS("CONTENT_COMPRESS", "内容压缩处理"),
    CONTENT_IMPORT("CONTENT_IMPORT", "内容导入处理");

    /**
     * 类型
     */
    private final String type;
    /**
     * 名字
     */
    private final String name;

    public static MessageTypeEnum valueOfType(String type) {
        return Arrays.stream(values()).filter(item -> item.getType().equals(type)).findFirst().orElse(null);
    }

}
