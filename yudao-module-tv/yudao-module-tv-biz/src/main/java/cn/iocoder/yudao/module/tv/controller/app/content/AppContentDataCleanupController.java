package cn.iocoder.yudao.module.tv.controller.app.content;

import cn.iocoder.yudao.framework.common.pojo.CommonResult;
import cn.iocoder.yudao.module.tv.service.content.ContentDataCleanupService;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

import static cn.iocoder.yudao.framework.common.pojo.CommonResult.success;

/**
 * 管理后台 - 内容数据清理
 *
 * <AUTHOR>
 */
@Tag(name = "管理后台 - 内容数据清理")
@RestController
@RequestMapping("/tv/content-cleanup")
@Validated
@Slf4j
public class AppContentDataCleanupController {

    @Resource
    private ContentDataCleanupService contentDataCleanupService;

    @PostMapping("/execute")
    public CommonResult<ContentDataCleanupService.ContentCleanupResult> executeCleanup() {
        log.info("[executeCleanup][手动触发内容数据清理]");
        
        ContentDataCleanupService.ContentCleanupResult result = contentDataCleanupService.executeCleanup();
        
        return success(result);
    }

    @PostMapping("/check")
    public CommonResult<ContentDataCheckResult> checkDataIssues() {
        log.info("[checkDataIssues][检查内容数据问题]");
        
        ContentDataCheckResult result = contentDataCleanupService.checkDataIssues();
        
        return success(result);
    }

    /**
     * 数据检查结果
     */
    public static class ContentDataCheckResult {
        private int garbageContentCount;
        private int missingCoverContentCount;
        private int invalidPreviewContentCount;
        private String summary;

        // Getters and Setters
        public int getGarbageContentCount() {
            return garbageContentCount;
        }

        public void setGarbageContentCount(int garbageContentCount) {
            this.garbageContentCount = garbageContentCount;
        }

        public int getMissingCoverContentCount() {
            return missingCoverContentCount;
        }

        public void setMissingCoverContentCount(int missingCoverContentCount) {
            this.missingCoverContentCount = missingCoverContentCount;
        }

        public int getInvalidPreviewContentCount() {
            return invalidPreviewContentCount;
        }

        public void setInvalidPreviewContentCount(int invalidPreviewContentCount) {
            this.invalidPreviewContentCount = invalidPreviewContentCount;
        }

        public String getSummary() {
            return summary;
        }

        public void setSummary(String summary) {
            this.summary = summary;
        }

        @Override
        public String toString() {
            return "ContentDataCheckResult{" +
                    "garbageContentCount=" + garbageContentCount +
                    ", missingCoverContentCount=" + missingCoverContentCount +
                    ", invalidPreviewContentCount=" + invalidPreviewContentCount +
                    ", summary='" + summary + '\'' +
                    '}';
        }
    }

}
