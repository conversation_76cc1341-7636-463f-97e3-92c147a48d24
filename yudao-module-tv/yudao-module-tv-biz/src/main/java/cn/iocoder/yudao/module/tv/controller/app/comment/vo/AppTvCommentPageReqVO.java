package cn.iocoder.yudao.module.tv.controller.app.comment.vo;

import cn.iocoder.yudao.framework.common.pojo.PageParam;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

import javax.validation.constraints.NotNull;

@Schema(description = "用户 APP - 评论分页 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class AppTvCommentPageReqVO extends PageParam {

    @Schema(description = "视频编号", required = true, example = "1024")
    @NotNull(message = "视频编号不能为空")
    private Long videoId;

    @Schema(description = "父评论编号", example = "2048")
    private Long parentId;
} 