package cn.iocoder.yudao.module.tv.job.video;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.NumberUtil;
import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.common.util.object.BeanUtils;
import cn.iocoder.yudao.framework.quartz.core.handler.JobHandler;
import cn.iocoder.yudao.framework.tenant.core.job.TenantJob;
import cn.iocoder.yudao.framework.tenant.core.util.TenantUtils;
import cn.iocoder.yudao.module.system.api.tenant.TenantApi;
import cn.iocoder.yudao.module.tv.controller.admin.video.vo.VideoPageReqVO;
import cn.iocoder.yudao.module.tv.dal.dataobject.video.VideoDO;
import cn.iocoder.yudao.module.tv.dal.mysql.video.VideoMapper;
import cn.iocoder.yudao.module.tv.enums.video.VideoPaidEnum;
import cn.iocoder.yudao.module.tv.enums.video.VideoStatusEnum;
import cn.iocoder.yudao.module.tv.service.video.VideoService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;

/**
 * 视频付费状态更新 Job
 * 
 * 每天定时将所有租户下点赞数和收藏数相加大于100的视频设置为付费视频
 * 每大于100积分就加一分，最大积分值为15
 *
 *
 *
 该功能会每天定时检查所有租户下的视频，将点赞数和收藏数相加大于100的视频自动设置为付费视频，并根据点赞数和收藏数的总和动态计算所需积分。

 ## 计算规则

 1. 当视频的点赞数和收藏数之和大于等于100时，视频将被设置为付费视频
 2. 每超过100点赞+收藏数，所需积分增加1分
 3. 最大积分值为15分
 4. 如果视频的点赞数和收藏数之和低于100，则视频将被设置为免费视频

 */
@Component
@Slf4j
public class VideoPaidUpdateJob implements JobHandler {

    /**
     * 最大积分值
     */
    private static final int MAX_PRICE = 15;
    
    /**
     * 每增加多少点赞+收藏数，增加1积分
     */
    private static final int POINTS_PER_PRICE = 100;

    @Resource
    private VideoService videoService;
    
    @Resource
    private VideoMapper videoMapper;
    
    @Resource
    private TenantApi tenantApi;

    @Override
    @TenantJob
    public String execute(String param) {
        // 1. 查询所有已发布的视频
        VideoPageReqVO pageReqVO = new VideoPageReqVO();
        pageReqVO.setStatus(VideoStatusEnum.RELEASE.getStatus());
        pageReqVO.setPageSize(500); // 每次处理500条
        
        int pageNo = 1;
        int totalUpdated = 0;
        
        // 分页查询所有视频
        while (true) {
            pageReqVO.setPageNo(pageNo);
            PageResult<VideoDO> pageResult = videoService.getVideoPage(pageReqVO);
            List<VideoDO> videos = pageResult.getList();
            if (CollUtil.isEmpty(videos)) {
                break;
            }
            
            // 处理当前页的视频
            int updated = processVideos(videos);
            totalUpdated += updated;
            
            // 如果已经是最后一页，则结束循环
            if (videos.size() < pageReqVO.getPageSize()) {
                break;
            }
            
            // 继续下一页
            pageNo++;
        }
        return totalUpdated+"条视频被设置成付费";
    }
    
    /**
     * 处理一批视频
     * 
     * @param videos 视频列表
     * @return 更新的视频数量
     */
    private int processVideos(List<VideoDO> videos) {
        if (CollUtil.isEmpty(videos)) {
            return 0;
        }
        
        List<VideoDO> toUpdateVideos = new ArrayList<>();
        
        // 遍历视频，计算需要更新的视频
        for (VideoDO video : videos) {
            // 计算点赞数和收藏数的总和。
            int totalCount = 0;
            // 时间超过10分钟就算付费视频啦。
            if(video.getDuration() > 30 ){
                totalCount = 100;
            }
            //
            totalCount += video.getLikeCount() + video.getFavoriteCount() + video.getPlayCount()/50;
            // 如果总和大于100，设置为付费视频
            if (totalCount >= POINTS_PER_PRICE) {
                // 计算价格：每超过100点赞+收藏数，加1积分，最大15积分
                int price = Math.min(MAX_PRICE, totalCount / POINTS_PER_PRICE);

                // 如果视频状态或价格有变化，则需要更新
                boolean needUpdate = !VideoPaidEnum.PAID.getPaid().equals(video.getIsPaid()) || 
                        ! Integer.valueOf(price).equals(video.getPrice());

                if (needUpdate) {
                    VideoDO updateVideo = new VideoDO();
                    updateVideo.setId(video.getId());
                    updateVideo.setIsPaid(VideoPaidEnum.PAID.getPaid());
                    updateVideo.setPrice(price);
                    toUpdateVideos.add(updateVideo);
                }
            } else if (VideoPaidEnum.PAID.getPaid().equals(video.getIsPaid())) {
                // 如果总和不足100，但当前是付费视频，则改为免费
                VideoDO updateVideo = new VideoDO();
                updateVideo.setId(video.getId());
                updateVideo.setIsPaid(VideoPaidEnum.FREE.getPaid());
                updateVideo.setPrice(0);
                toUpdateVideos.add(updateVideo);
            }
        }
        
        // 批量更新视频
        if (CollUtil.isNotEmpty(toUpdateVideos)) {
            for (VideoDO video : toUpdateVideos) {
                videoService.updateVideo(video);
            }
            log.info("[processVideos][批量更新了 {} 个视频的付费状态]", toUpdateVideos.size());
        }
        
        return toUpdateVideos.size();
    }
}
