package cn.iocoder.yudao.module.tv.service.content;

import cn.hutool.core.collection.CollUtil;
import cn.iocoder.yudao.module.tv.controller.app.content.AppContentDataCleanupController;
import cn.iocoder.yudao.module.tv.dal.dataobject.content.ContentCollectionItemDO;
import cn.iocoder.yudao.module.tv.dal.dataobject.content.ContentDO;
import cn.iocoder.yudao.module.tv.dal.dataobject.content.ContentMediaDO;
import cn.iocoder.yudao.module.tv.dal.mysql.content.ContentCollectionItemMapper;
import cn.iocoder.yudao.module.tv.dal.mysql.content.ContentMapper;
import cn.iocoder.yudao.module.tv.dal.mysql.content.ContentMediaMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 内容数据清理服务
 * 用于清理和修复tv_content和tv_content_media表的历史数据问题
 *
 * <AUTHOR>
 */
@Service
@Slf4j
public class ContentDataCleanupService {

    @Resource
    private ContentMapper contentMapper;

    @Resource
    private ContentMediaMapper contentMediaMapper;

    @Resource
    private ContentCollectionItemMapper contentCollectionItemMapper;

    /**
     * 执行数据清理和修复
     */
    @Transactional(rollbackFor = Exception.class)
    public ContentCleanupResult executeCleanup() {
        log.info("[executeCleanup][开始执行内容数据清理和修复]");
        
        ContentCleanupResult result = new ContentCleanupResult();
        
        try {
            // 1. 清理垃圾数据（媒体数量少于4的内容）
            int deletedGarbageCount = cleanupGarbageContent();
            result.setDeletedGarbageContentCount(deletedGarbageCount);
            
            // 2. 修复封面URL
            int fixedCoverCount = fixContentCoverUrl();
            result.setFixedCoverUrlCount(fixedCoverCount);
            
            // 3. 修复试看内容配置
            int fixedPreviewCount = fixPreviewContent();
            result.setFixedPreviewContentCount(fixedPreviewCount);
            
            result.setSuccess(true);
            result.setMessage("数据清理和修复完成");
            
            log.info("[executeCleanup][数据清理和修复完成] result: {}", result);
            
        } catch (Exception e) {
            result.setSuccess(false);
            result.setMessage("数据清理和修复失败: " + e.getMessage());
            log.error("[executeCleanup][数据清理和修复失败]", e);
            throw e;
        }
        
        return result;
    }

    /**
     * 检查数据问题（不执行修复）
     */
    public AppContentDataCleanupController.ContentDataCheckResult checkDataIssues() {
        log.info("[checkDataIssues][开始检查内容数据问题]");

        AppContentDataCleanupController.ContentDataCheckResult result = new AppContentDataCleanupController.ContentDataCheckResult();

        try {
            // 1. 检查垃圾数据
            int garbageCount = checkGarbageContent();
            result.setGarbageContentCount(garbageCount);

            // 2. 检查缺失封面的内容
            int missingCoverCount = checkMissingCoverContent();
            result.setMissingCoverContentCount(missingCoverCount);

            // 3. 检查试看配置问题
            int invalidPreviewCount = checkInvalidPreviewContent();
            result.setInvalidPreviewContentCount(invalidPreviewCount);

            // 生成摘要
            String summary = String.format(
                "发现数据问题 - 垃圾内容: %d个, 缺失封面: %d个, 试看配置异常: %d个",
                garbageCount, missingCoverCount, invalidPreviewCount
            );
            result.setSummary(summary);

            log.info("[checkDataIssues][数据问题检查完成] result: {}", result);

        } catch (Exception e) {
            result.setSummary("数据问题检查失败: " + e.getMessage());
            log.error("[checkDataIssues][数据问题检查失败]", e);
        }

        return result;
    }

    /**
     * 检查垃圾数据数量
     */
    private int checkGarbageContent() {
        List<ContentDO> type5Contents = contentMapper.selectList(
                new cn.iocoder.yudao.framework.mybatis.core.query.LambdaQueryWrapperX<ContentDO>()
                        .eq(ContentDO::getType, 5)
        );

        if (CollUtil.isEmpty(type5Contents)) {
            return 0;
        }

        int garbageCount = 0;
        for (ContentDO content : type5Contents) {
            Long mediaCount = contentMediaMapper.selectCount(
                    new cn.iocoder.yudao.framework.mybatis.core.query.LambdaQueryWrapperX<ContentMediaDO>()
                            .eq(ContentMediaDO::getContentId, content.getId())
            );
            if (mediaCount < 4) {
                garbageCount++;
            }
        }

        return garbageCount;
    }

    /**
     * 检查缺失封面的内容数量
     */
    private int checkMissingCoverContent() {
        Long count = contentMapper.selectCount(
                new cn.iocoder.yudao.framework.mybatis.core.query.LambdaQueryWrapperX<ContentDO>()
                        .eq(ContentDO::getType, 5)
                        .and(wrapper -> wrapper.isNull(ContentDO::getCoverUrl)
                                .or().eq(ContentDO::getCoverUrl, ""))
        );
        return count.intValue();
    }

    /**
     * 检查试看配置异常的内容数量
     */
    private int checkInvalidPreviewContent() {
        List<ContentDO> type5Contents = contentMapper.selectList(
                new cn.iocoder.yudao.framework.mybatis.core.query.LambdaQueryWrapperX<ContentDO>()
                        .eq(ContentDO::getType, 5)
        );

        if (CollUtil.isEmpty(type5Contents)) {
            return 0;
        }

        int invalidCount = 0;
        for (ContentDO content : type5Contents) {
            if (checkSingleContentPreviewIssue(content.getId())) {
                invalidCount++;
            }
        }

        return invalidCount;
    }

    /**
     * 检查单个内容的试看配置是否有问题
     */
    private boolean checkSingleContentPreviewIssue(Long contentId) {
        List<ContentMediaDO> allMedias = contentMediaMapper.selectListByContentId(contentId);

        if (CollUtil.isEmpty(allMedias)) {
            return false;
        }

        long previewCount = allMedias.stream().filter(media -> Boolean.TRUE.equals(media.getIsPreview())).count();
        long paidCount = allMedias.stream().filter(media -> Boolean.FALSE.equals(media.getIsPreview())).count();

        // 检查是否有问题
        return previewCount == 0 || previewCount > 6 || paidCount <= 1 || paidCount <= previewCount;
    }

    /**
     * 清理垃圾数据：删除媒体数量少于4的type=5内容
     */
    private int cleanupGarbageContent() {
        log.info("[cleanupGarbageContent][开始清理垃圾数据]");
        
        // 查询所有type=5的内容
        List<ContentDO> type5Contents = contentMapper.selectList(
                new cn.iocoder.yudao.framework.mybatis.core.query.LambdaQueryWrapperX<ContentDO>()
                        .eq(ContentDO::getType, 5)
        );
        
        if (CollUtil.isEmpty(type5Contents)) {
            log.info("[cleanupGarbageContent][没有找到type=5的内容]");
            return 0;
        }
        
        List<Long> contentIds = type5Contents.stream()
                .map(ContentDO::getId)
                .collect(Collectors.toList());
        
        // 统计每个content_id的媒体数量
        Map<Long, Long> mediaCountMap = new HashMap<>();
        for (Long contentId : contentIds) {
            Long count = contentMediaMapper.selectCount(
                    new cn.iocoder.yudao.framework.mybatis.core.query.LambdaQueryWrapperX<ContentMediaDO>()
                            .eq(ContentMediaDO::getContentId, contentId)
            );
            mediaCountMap.put(contentId, count);
        }
        
        // 找出媒体数量少于4的内容ID
        List<Long> garbageContentIds = mediaCountMap.entrySet().stream()
                .filter(entry -> entry.getValue() < 4)
                .map(Map.Entry::getKey)
                .collect(Collectors.toList());
        
        if (CollUtil.isEmpty(garbageContentIds)) {
            log.info("[cleanupGarbageContent][没有找到需要清理的垃圾数据]");
            return 0;
        }
        
        log.info("[cleanupGarbageContent][找到{}个垃圾内容需要清理] contentIds: {}", 
                garbageContentIds.size(), garbageContentIds);

        int deletedCount = 0;
        for (Long contentId : garbageContentIds) {
            // 删除媒体记录
            contentMediaMapper.deleteByContentId(contentId);
            // 删除媒体记录
            contentCollectionItemMapper.delete(ContentCollectionItemDO::getItemContentId,contentId);
            // 删除内容记录
            contentMapper.deleteById(contentId);
            deletedCount++;
        }

        log.info("[cleanupGarbageContent][垃圾数据清理完成] deletedCount: {}", deletedCount);
        return deletedCount;
    }

    /**
     * 修复内容封面URL
     */
    private int fixContentCoverUrl() {
        log.info("[fixContentCoverUrl][开始修复内容封面URL]");
        
        // 查询所有type=5且cover_url为空的内容
        List<ContentDO> contentsWithoutCover = contentMapper.selectList(
                new cn.iocoder.yudao.framework.mybatis.core.query.LambdaQueryWrapperX<ContentDO>()
                        .eq(ContentDO::getType, 5)
                        .and(wrapper -> wrapper.isNull(ContentDO::getCoverUrl)
                                .or().eq(ContentDO::getCoverUrl, ""))
        );
        
        if (CollUtil.isEmpty(contentsWithoutCover)) {
            log.info("[fixContentCoverUrl][没有找到需要修复封面的内容]");
            return 0;
        }
        
        int fixedCount = 0;
        
        for (ContentDO content : contentsWithoutCover) {
            String coverUrl = findSuitableCoverUrl(content.getId());
            if (coverUrl != null) {
                ContentDO updateContent = new ContentDO();
                updateContent.setId(content.getId());
                updateContent.setCoverUrl(coverUrl);
                contentMapper.updateById(updateContent);
                fixedCount++;
                
                log.debug("[fixContentCoverUrl][修复封面URL] contentId: {}, coverUrl: {}", 
                        content.getId(), coverUrl);
            }
        }
        
        log.info("[fixContentCoverUrl][封面URL修复完成] fixedCount: {}", fixedCount);
        return fixedCount;
    }

    /**
     * 为内容查找合适的封面URL
     */
    private String findSuitableCoverUrl(Long contentId) {
        // 1. 优先从type=0的图片中获取
        List<ContentMediaDO> imageMedias = contentMediaMapper.selectList(
                new cn.iocoder.yudao.framework.mybatis.core.query.LambdaQueryWrapperX<ContentMediaDO>()
                        .eq(ContentMediaDO::getContentId, contentId)
                        .eq(ContentMediaDO::getType, 0) // 图片
                        .orderByAsc(ContentMediaDO::getSort)
                        .last("LIMIT 1")
        );
        
        if (CollUtil.isNotEmpty(imageMedias)) {
            return imageMedias.get(0).getUrl();
        }
        
        // 2. 如果没有图片，从type=1的视频封面中获取
        List<ContentMediaDO> videoMedias = contentMediaMapper.selectList(
                new cn.iocoder.yudao.framework.mybatis.core.query.LambdaQueryWrapperX<ContentMediaDO>()
                        .eq(ContentMediaDO::getContentId, contentId)
                        .eq(ContentMediaDO::getType, 1) // 视频
                        .isNotNull(ContentMediaDO::getCoverUrl)
                        .orderByAsc(ContentMediaDO::getSort)
                        .last("LIMIT 1")
        );
        
        if (CollUtil.isNotEmpty(videoMedias)) {
            return videoMedias.get(0).getCoverUrl();
        }
        
        return null;
    }

    /**
     * 修复试看内容配置
     */
    private int fixPreviewContent() {
        log.info("[fixPreviewContent][开始修复试看内容配置]");
        
        // 查询所有type=5的内容
        List<ContentDO> type5Contents = contentMapper.selectList(
                new cn.iocoder.yudao.framework.mybatis.core.query.LambdaQueryWrapperX<ContentDO>()
                        .eq(ContentDO::getType, 5)
        );
        
        if (CollUtil.isEmpty(type5Contents)) {
            log.info("[fixPreviewContent][没有找到type=5的内容]");
            return 0;
        }
        
        int fixedCount = 0;
        
        for (ContentDO content : type5Contents) {
            if (fixSingleContentPreview(content.getId())) {
                fixedCount++;
            }
        }
        
        log.info("[fixPreviewContent][试看内容配置修复完成] fixedCount: {}", fixedCount);
        return fixedCount;
    }

    /**
     * 修复单个内容的试看配置
     */
    private boolean fixSingleContentPreview(Long contentId) {
        // 查询该内容的所有媒体
        List<ContentMediaDO> allMedias = contentMediaMapper.selectListByContentId(contentId);

        if (CollUtil.isEmpty(allMedias)) {
            return false;
        }

        // 统计当前试看和付费内容数量
        long previewCount = allMedias.stream().filter(media -> Boolean.TRUE.equals(media.getIsPreview())).count();
        long paidCount = allMedias.stream().filter(media -> Boolean.FALSE.equals(media.getIsPreview())).count();

        boolean needFix = false;

        // 检查是否需要修复
        if (previewCount == 0) {
            // 没有试看内容，需要设置
            needFix = true;
        } else if (previewCount > 6) {
            // 试看内容超过6个，需要调整
            needFix = true;
        } else if (paidCount <= 1) {
            // 付费内容少于等于1个，需要调整
            needFix = true;
        } else if (paidCount <= previewCount) {
            // 付费内容不比免费内容多，需要调整
            needFix = true;
        }

        if (!needFix) {
            return false;
        }

        log.info("[fixSingleContentPreview][修复内容试看配置] contentId: {}, 当前试看: {}, 当前付费: {}",
                contentId, previewCount, paidCount);

        // 重新分配试看和付费内容
        redistributePreviewContent(contentId, allMedias);

        return true;
    }

    /**
     * 重新分配试看和付费内容
     */
    private void redistributePreviewContent(Long contentId, List<ContentMediaDO> allMedias) {
        int totalCount = allMedias.size();

        // 计算合理的试看和付费内容数量
        // 试看内容：总数的1/3，但不超过6个，至少1个
        int previewCount = Math.max(1, Math.min(6, totalCount / 3));
        // 付费内容：剩余的，但要确保比试看内容多
        int paidCount = totalCount - previewCount;

        // 如果付费内容不比试看内容多，调整比例
        if (paidCount <= previewCount && totalCount > 2) {
            previewCount = Math.max(1, (totalCount - 1) / 2);
            paidCount = totalCount - previewCount;
        }

        log.info("[redistributePreviewContent][重新分配] contentId: {}, 总数: {}, 试看: {}, 付费: {}",
                contentId, totalCount, previewCount, paidCount);

        // 按排序值排序，前面的设为试看，后面的设为付费
        allMedias.sort(Comparator.comparing(ContentMediaDO::getSort, Comparator.nullsLast(Integer::compareTo)));

        // 更新媒体的试看状态
        for (int i = 0; i < allMedias.size(); i++) {
            ContentMediaDO media = allMedias.get(i);
            boolean shouldBePreview = i < previewCount;

            if (!Objects.equals(media.getIsPreview(), shouldBePreview)) {
                ContentMediaDO updateMedia = new ContentMediaDO();
                updateMedia.setId(media.getId());
                updateMedia.setIsPreview(shouldBePreview);

                // 设置试看排序
                if (shouldBePreview) {
                    updateMedia.setPreviewSort(i + 1);
                } else {
                    updateMedia.setPreviewSort(null);
                }

                contentMediaMapper.updateById(updateMedia);

                log.debug("[redistributePreviewContent][更新媒体] mediaId: {}, isPreview: {}, previewSort: {}",
                        media.getId(), shouldBePreview, updateMedia.getPreviewSort());
            }
        }
    }

    /**
     * 数据清理结果
     */
    public static class ContentCleanupResult {
        private boolean success;
        private String message;
        private int deletedGarbageContentCount;
        private int fixedCoverUrlCount;
        private int fixedPreviewContentCount;

        // Getters and Setters
        public boolean isSuccess() {
            return success;
        }

        public void setSuccess(boolean success) {
            this.success = success;
        }

        public String getMessage() {
            return message;
        }

        public void setMessage(String message) {
            this.message = message;
        }

        public int getDeletedGarbageContentCount() {
            return deletedGarbageContentCount;
        }

        public void setDeletedGarbageContentCount(int deletedGarbageContentCount) {
            this.deletedGarbageContentCount = deletedGarbageContentCount;
        }

        public int getFixedCoverUrlCount() {
            return fixedCoverUrlCount;
        }

        public void setFixedCoverUrlCount(int fixedCoverUrlCount) {
            this.fixedCoverUrlCount = fixedCoverUrlCount;
        }

        public int getFixedPreviewContentCount() {
            return fixedPreviewContentCount;
        }

        public void setFixedPreviewContentCount(int fixedPreviewContentCount) {
            this.fixedPreviewContentCount = fixedPreviewContentCount;
        }

        @Override
        public String toString() {
            return "ContentCleanupResult{" +
                    "success=" + success +
                    ", message='" + message + '\'' +
                    ", deletedGarbageContentCount=" + deletedGarbageContentCount +
                    ", fixedCoverUrlCount=" + fixedCoverUrlCount +
                    ", fixedPreviewContentCount=" + fixedPreviewContentCount +
                    '}';
        }
    }

}
