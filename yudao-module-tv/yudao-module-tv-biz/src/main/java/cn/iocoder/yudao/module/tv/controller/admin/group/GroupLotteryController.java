package cn.iocoder.yudao.module.tv.controller.admin.group;

import cn.iocoder.yudao.framework.common.pojo.CommonResult;
import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.module.tv.controller.admin.group.vo.GroupInfoPageReqVO;
import cn.iocoder.yudao.module.tv.controller.admin.group.vo.GroupOrderPageReqVO;
import cn.iocoder.yudao.module.tv.controller.admin.group.vo.GroupResultPageReqVO;
import cn.iocoder.yudao.module.tv.dal.dataobject.group.GroupInfoDO;
import cn.iocoder.yudao.module.tv.dal.dataobject.group.GroupOrderDO;
import cn.iocoder.yudao.module.tv.dal.dataobject.group.GroupResultDO;
import cn.iocoder.yudao.module.tv.service.group.GroupLotteryService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;

import static cn.iocoder.yudao.framework.common.pojo.CommonResult.success;

@Tag(name = "管理后台 - 拼团抽奖")
@RestController
@RequestMapping("/tv/group-lottery")
@Validated
@Slf4j
public class GroupLotteryController {

    @Resource
    private GroupLotteryService groupLotteryService;

    @GetMapping("/group-info/page")
    @Operation(summary = "获得拼团信息分页")
    @PreAuthorize("@ss.hasPermission('tv:group:query')")
    public CommonResult<PageResult<GroupInfoDO>> getGroupInfoPage(@Valid GroupInfoPageReqVO pageReqVO) {
        PageResult<GroupInfoDO> pageResult = groupLotteryService.getGroupInfoPage(pageReqVO);
        return success(pageResult);
    }

    @GetMapping("/group-info/get")
    @Operation(summary = "获得拼团信息")
    @Parameter(name = "id", description = "编号", required = true, example = "1024")
    @PreAuthorize("@ss.hasPermission('tv:group:query')")
    public CommonResult<GroupInfoDO> getGroupInfo(@RequestParam("id") Long id) {
        GroupInfoDO groupInfo = groupLotteryService.getGroupInfo(id);
        return success(groupInfo);
    }

    @GetMapping("/group-order/page")
    @Operation(summary = "获得拼团参与记录分页")
    @PreAuthorize("@ss.hasPermission('tv:group:query')")
    public CommonResult<PageResult<GroupOrderDO>> getGroupOrderPage(@Valid GroupOrderPageReqVO pageReqVO) {
        PageResult<GroupOrderDO> pageResult = groupLotteryService.getGroupOrderPage(pageReqVO);
        return success(pageResult);
    }

    @GetMapping("/group-result/page")
    @Operation(summary = "获得开奖记录分页")
    @PreAuthorize("@ss.hasPermission('tv:group:query')")
    public CommonResult<PageResult<GroupResultDO>> getGroupResultPage(@Valid GroupResultPageReqVO pageReqVO) {
        PageResult<GroupResultDO> pageResult = groupLotteryService.getGroupResultPage(pageReqVO);
        return success(pageResult);
    }

    @GetMapping("/group-result/get")
    @Operation(summary = "获得开奖记录")
    @Parameter(name = "id", description = "编号", required = true, example = "1024")
    @PreAuthorize("@ss.hasPermission('tv:group:query')")
    public CommonResult<GroupResultDO> getGroupResult(@RequestParam("id") Long id) {
        GroupResultDO groupResult = groupLotteryService.getGroupResult(id);
        return success(groupResult);
    }

    @PostMapping("/draw")
    @Operation(summary = "手动开奖")
    @Parameter(name = "groupId", description = "拼团ID", required = true, example = "1024")
    @PreAuthorize("@ss.hasPermission('tv:group:draw')")
    public CommonResult<Long> drawLottery(@RequestParam("groupId") Long groupId) {
        Long resultId = groupLotteryService.drawLottery(groupId);
        return success(resultId);
    }

    @PostMapping("/handle-expired")
    @Operation(summary = "处理超时拼团")
    @PreAuthorize("@ss.hasPermission('tv:group:manage')")
    public CommonResult<Integer> handleExpiredGroups() {
        int count = groupLotteryService.handleExpiredGroups();
        return success(count);
    }

    @PostMapping("/handle-full")
    @Operation(summary = "处理满员拼团")
    @PreAuthorize("@ss.hasPermission('tv:group:manage')")
    public CommonResult<Integer> handleFullGroups() {
        int count = groupLotteryService.handleFullGroups();
        return success(count);
    }
}
