package cn.iocoder.yudao.module.tv.controller.app.group.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotNull;
import javax.validation.constraints.Positive;
import java.math.BigDecimal;

@Schema(description = "用户 App - 发起拼团 Request VO")
@Data
public class AppGroupCreateReqVO {

    @Schema(description = "奖品ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "1024")
    @NotNull(message = "奖品ID不能为空")
    private Long prizeId;

    @Schema(description = "订单ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "1024")
    private String orderNo;

    @Schema(description = "目标人数", requiredMode = Schema.RequiredMode.REQUIRED, example = "10")
    @NotNull(message = "目标人数不能为空")
    @Positive(message = "目标人数必须大于0")
    private Integer targetSize;

    @Schema(description = "参团费用", requiredMode = Schema.RequiredMode.REQUIRED, example = "9.9")
    @NotNull(message = "参团费用不能为空")
    @Positive(message = "参团费用必须大于0")
    private BigDecimal entryFee;

    @Schema(description = "拼团时长（小时）", requiredMode = Schema.RequiredMode.REQUIRED, example = "24")
    @NotNull(message = "拼团时长不能为空")
    @Positive(message = "拼团时长必须大于0")
    private Integer durationHours;
}
