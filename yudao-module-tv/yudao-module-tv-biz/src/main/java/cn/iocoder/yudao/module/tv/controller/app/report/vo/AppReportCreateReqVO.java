package cn.iocoder.yudao.module.tv.controller.app.report.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;

import javax.validation.constraints.*;
import java.util.List;

@Schema(description = "用户 App - 举报反馈创建 Request VO")
@Data
public class AppReportCreateReqVO {

    @Schema(description = "业务类型", requiredMode = Schema.RequiredMode.REQUIRED, example = "content")
    @NotEmpty(message = "业务类型不能为空")
    private String businessType;

    @Schema(description = "业务ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "1024")
    @NotNull(message = "业务ID不能为空")
    private Long businessId;

    @Schema(description = "举报类型", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
    @NotNull(message = "举报类型不能为空")
    private Integer reportType;

    @Schema(description = "举报原因描述", example = "内容涉及色情低俗")
    private String reportReason;

    @Schema(description = "举报截图", example = "[\"http://example.com/image1.jpg\"]")
    private List<String> reportImages;

}
