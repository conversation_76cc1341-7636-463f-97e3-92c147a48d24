package cn.iocoder.yudao.module.tv.service.content;

import javax.validation.*;
import cn.iocoder.yudao.module.tv.controller.admin.content.vo.*;
import cn.iocoder.yudao.module.tv.controller.app.content.vo.ContentTypePageReqVO;
import cn.iocoder.yudao.module.tv.controller.app.content.vo.AppContentSearchReqVO;
import cn.iocoder.yudao.module.tv.controller.app.favorite.vo.AppFavoriteVideoPageReqVO;
import cn.iocoder.yudao.module.tv.controller.app.history.vo.AppHistoryVideoPageReqVO;
import cn.iocoder.yudao.module.tv.controller.app.like.vo.AppLikeVideoPageReqVO;
import cn.iocoder.yudao.module.tv.dal.dataobject.content.ContentDO;
import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.common.pojo.PageParam;
import cn.iocoder.yudao.module.tv.controller.app.content.vo.AppContentDetailRespVO;

import java.util.List;

/**
 * 内容主体 Service 接口
 *
 * <AUTHOR>
 */
public interface ContentService {

    /**
     * 创建内容主体
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    Long createContent(@Valid ContentSaveReqVO createReqVO);

    /**
     * 更新内容主体
     *
     * @param updateReqVO 更新信息
     */
    void updateContent(@Valid ContentSaveReqVO updateReqVO);

    /**
     * 删除内容主体
     *
     * @param id 编号
     */
    void deleteContent(Long id);

    /**
     * 获得内容主体
     *
     * @param id 编号
     * @return 内容主体
     */
    ContentDO getContent(Long id);

    /**
     * 获得内容主体分页
     *
     * @param pageReqVO 分页查询
     * @return 内容主体分页
     */
    PageResult<ContentDO> getContentPage(ContentPageReqVO pageReqVO);

    PageResult<ContentRespVO> selectContentPage(ContentPageReqVO pageReqVO);

    AppContentDetailRespVO getDetailRespVO(Long userId, Long id);

    AppContentDetailRespVO getContentDetailRespVO(Long userId, Long id);

    boolean unlockContent(Long userId, Long id);

    PageResult<ContentRespVO> getUserUnlockedContentPage(Long userId, PageParam pageReqVO);

    PageResult<ContentRespVO> getLikeContentPage(Long loginUserId, AppLikeVideoPageReqVO pageVO);

    PageResult<ContentRespVO> getFavoriteContentPage(Long loginUserId, AppFavoriteVideoPageReqVO pageVO);

    PageResult<?> detailContentItemPage(Long userId, @Valid ContentTypePageReqVO pageReqVO);

    PageResult<?> previewContentItemPage(@Valid ContentTypePageReqVO pageReqVO);

    /**
     * 搜索内容分页
     *
     * @param searchReqVO 搜索条件
     * @return 内容分页结果
     */
    PageResult<ContentRespVO> searchContentPage(AppContentSearchReqVO searchReqVO);

    //
    List<ContentRespVO> selectBatchIds(List<Long> videoIds);

    void incrementPlayCount(Long videoId);

    int relatedCollection(Long collectionContentId, Long groupContentId);
}