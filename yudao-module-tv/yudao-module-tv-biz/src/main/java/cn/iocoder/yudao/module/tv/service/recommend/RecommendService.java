package cn.iocoder.yudao.module.tv.service.recommend;

import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.module.tv.dal.dataobject.video.VideoDO;

public interface RecommendService {
    PageResult<VideoDO> getRecommendVideos(Long userId, Integer count);

    PageResult<VideoDO> getCollaborativeFilteringRecommendations(Long userId, Integer count);

    PageResult<VideoDO> getContentBasedRecommendations(Long userId, Integer count);

    PageResult<VideoDO> getHotRecommendations(Integer count);

    PageResult<VideoDO> getInterestBasedRecommendations(Long userId, Integer count);
}
