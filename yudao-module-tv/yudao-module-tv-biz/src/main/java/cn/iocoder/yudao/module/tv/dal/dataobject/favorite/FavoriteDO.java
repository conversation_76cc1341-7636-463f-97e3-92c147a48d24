package cn.iocoder.yudao.module.tv.dal.dataobject.favorite;

import cn.iocoder.yudao.framework.mybatis.core.dataobject.BaseDO;
import cn.iocoder.yudao.framework.mybatis.core.dataobject.PhysicDo;
import com.baomidou.mybatisplus.annotation.KeySequence;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 收藏 DO
 */
@TableName("tv_favorite")
@KeySequence("tv_favorite_seq")
@Data
@EqualsAndHashCode(callSuper = true)
public class FavoriteDO extends PhysicDo {

    /**
     * 收藏编号
     */
    @TableId
    private Long id;

    /**
     * 用户编号
     */
    private Long userId;

    /**
     * 视频编号
     */
    private Long videoId;
} 