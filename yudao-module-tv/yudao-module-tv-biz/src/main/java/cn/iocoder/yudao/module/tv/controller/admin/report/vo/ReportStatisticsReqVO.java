package cn.iocoder.yudao.module.tv.controller.admin.report.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;
import org.springframework.format.annotation.DateTimeFormat;

import java.time.LocalDateTime;

import static cn.iocoder.yudao.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;

@Schema(description = "管理后台 - 举报统计 Request VO")
@Data
public class ReportStatisticsReqVO {

    @Schema(description = "业务类型", example = "content")
    private String businessType;

    @Schema(description = "举报类型", example = "1")
    private Integer reportType;

    @Schema(description = "统计时间范围")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime[] timeRange;

    @Schema(description = "统计维度：day-按天，week-按周，month-按月", example = "day")
    private String dimension = "day";

}
