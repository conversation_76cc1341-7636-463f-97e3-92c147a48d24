package cn.iocoder.yudao.module.tv.dal.mysql.group;

import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.mybatis.core.mapper.BaseMapperX;
import cn.iocoder.yudao.framework.mybatis.core.query.LambdaQueryWrapperX;
import cn.iocoder.yudao.module.tv.controller.admin.group.vo.GroupInfoPageReqVO;
import cn.iocoder.yudao.module.tv.dal.dataobject.group.GroupInfoDO;
import org.apache.ibatis.annotations.Mapper;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 拼团信息 Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface GroupInfoMapper extends BaseMapperX<GroupInfoDO> {

    default PageResult<GroupInfoDO> selectPage(GroupInfoPageReqVO reqVO) {
        return selectPage(reqVO, new LambdaQueryWrapperX<GroupInfoDO>()
                .eqIfPresent(GroupInfoDO::getCreatorId, reqVO.getCreatorId())
                .eqIfPresent(GroupInfoDO::getPrizeId, reqVO.getPrizeId())
                .eqIfPresent(GroupInfoDO::getStatus, reqVO.getStatus())
                .betweenIfPresent(GroupInfoDO::getCreateTime, reqVO.getCreateTime())
                .orderByDesc(GroupInfoDO::getId));
    }

    default List<GroupInfoDO> selectListByCreatorId(Long creatorId) {
        return selectList(new LambdaQueryWrapperX<GroupInfoDO>()
                .eq(GroupInfoDO::getCreatorId, creatorId)
                .orderByDesc(GroupInfoDO::getCreateTime));
    }

    default List<GroupInfoDO> selectListByStatus(Integer status) {
        return selectList(new LambdaQueryWrapperX<GroupInfoDO>()
                .eq(GroupInfoDO::getStatus, status)
                .orderByAsc(GroupInfoDO::getExpireTime));
    }

    default List<GroupInfoDO> selectExpiredGroups() {
        return selectList(new LambdaQueryWrapperX<GroupInfoDO>()
                .in(GroupInfoDO::getStatus, 0, 1) // 待拼团或拼团中
                .lt(GroupInfoDO::getExpireTime, LocalDateTime.now())
                .orderByAsc(GroupInfoDO::getExpireTime));
    }

    default List<GroupInfoDO> selectFullGroups() {
        return selectList(new LambdaQueryWrapperX<GroupInfoDO>()
                .eq(GroupInfoDO::getStatus, 1) // 拼团中
                .apply("current_size >= target_size")
                .orderByAsc(GroupInfoDO::getCreateTime));
    }

    default List<GroupInfoDO> selectListByPrizeId(Long prizeId) {
        return selectList(new LambdaQueryWrapperX<GroupInfoDO>()
                .eq(GroupInfoDO::getPrizeId, prizeId)
                .orderByDesc(GroupInfoDO::getCreateTime));
    }

    default Long countByCreatorIdAndStatus(Long creatorId, Integer status) {
        return selectCount(new LambdaQueryWrapperX<GroupInfoDO>()
                .eq(GroupInfoDO::getCreatorId, creatorId)
                .eq(GroupInfoDO::getStatus, status));
    }

    default Long countByPrizeIdAndStatus(Long prizeId, Integer status) {
        return selectCount(new LambdaQueryWrapperX<GroupInfoDO>()
                .eq(GroupInfoDO::getPrizeId, prizeId)
                .eq(GroupInfoDO::getStatus, status));
    }
}
