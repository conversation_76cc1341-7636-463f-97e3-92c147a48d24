package cn.iocoder.yudao.module.tv.controller.app.group;

import cn.iocoder.yudao.framework.common.pojo.CommonResult;
import cn.iocoder.yudao.module.tv.controller.app.group.vo.AppGroupCreateReqVO;
import cn.iocoder.yudao.module.tv.controller.app.group.vo.AppGroupDetailRespVO;
import cn.iocoder.yudao.module.tv.controller.app.group.vo.AppGroupJoinReqVO;
import cn.iocoder.yudao.module.tv.dal.dataobject.prize.PrizeDO;
import cn.iocoder.yudao.module.tv.service.group.GroupLotteryService;
import cn.iocoder.yudao.module.tv.service.prize.PrizeService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.List;

import static cn.iocoder.yudao.framework.common.pojo.CommonResult.success;
import static cn.iocoder.yudao.framework.security.core.util.SecurityFrameworkUtils.getLoginUserId;

@Tag(name = "用户 App - 拼团抽奖")
@RestController
@RequestMapping("/tv/app-group-lottery")
@Validated
@Slf4j
public class AppGroupLotteryController {

    @Resource
    private GroupLotteryService groupLotteryService;
    @Resource
    private PrizeService prizeService;

    @PostMapping("/create")
    @Operation(summary = "发起拼团")
    public CommonResult<Long> createGroup(@Valid @RequestBody AppGroupCreateReqVO createReqVO) {
        Long groupId = groupLotteryService.createGroup(getLoginUserId(), createReqVO);
        return success(groupId);
    }

    @PostMapping("/join")
    @Operation(summary = "参与拼团")
    public CommonResult<Long> joinGroup(@Valid @RequestBody AppGroupJoinReqVO joinReqVO) {
        Long orderId = groupLotteryService.joinGroup(getLoginUserId(), joinReqVO);
        return success(orderId);
    }

    @GetMapping("/detail")
    @Operation(summary = "获取拼团详情")
    @Parameter(name = "groupId", description = "拼团ID", required = true, example = "1024")
    public CommonResult<AppGroupDetailRespVO> getGroupDetail(@RequestParam("groupId") Long groupId) {
        Long userId = getLoginUserId(); // 可能为null，用于判断是否已参与
        AppGroupDetailRespVO detail = groupLotteryService.getGroupDetail(userId, groupId);
        return success(detail);
    }

    @GetMapping("/my-groups")
    @Operation(summary = "我的拼团列表")
    public CommonResult<List<AppGroupDetailRespVO>> getMyGroups() {
        List<AppGroupDetailRespVO> groups = groupLotteryService.getUserGroups(getLoginUserId());
        return success(groups);
    }

    @GetMapping("/hot-groups")
    @Operation(summary = "热门拼团列表")
    @Parameter(name = "limit", description = "数量限制", example = "10")
    public CommonResult<List<AppGroupDetailRespVO>> getHotGroups(@RequestParam(value = "limit", defaultValue = "10") Integer limit) {
        List<AppGroupDetailRespVO> groups = groupLotteryService.getHotGroups(limit);
        return success(groups);
    }

    @GetMapping("/available-prizes")
    @Operation(summary = "获取可用奖品列表")
    public CommonResult<List<PrizeDO>> getAvailablePrizes() {
        List<PrizeDO> prizes = prizeService.getAvailablePrizes();
        return success(prizes);
    }

    @GetMapping("/prize/detail")
    @Operation(summary = "获取奖品详情")
    @Parameter(name = "prizeId", description = "奖品ID", required = true, example = "1024")
    public CommonResult<PrizeDO> getPrizeDetail(@RequestParam("prizeId") Long prizeId) {
        PrizeDO prize = prizeService.getPrize(prizeId);
        return success(prize);
    }
}
