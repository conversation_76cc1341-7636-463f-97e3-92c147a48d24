package cn.iocoder.yudao.module.tv.service.content.strategy.impl;

import cn.hutool.core.codec.Base64;
import cn.hutool.core.io.FileUtil;
import cn.hutool.core.io.IoUtil;
import cn.hutool.core.util.CharsetUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.core.util.ZipUtil;
import cn.hutool.crypto.SecureUtil;
import cn.iocoder.yudao.framework.common.util.io.FileUtils;
import cn.iocoder.yudao.module.infra.api.deduplication.DeduplicationFileApi;
import cn.iocoder.yudao.module.infra.api.deduplication.dto.DeduplicationFileUploadReqDTO;
import cn.iocoder.yudao.module.infra.api.deduplication.dto.DeduplicationFileRespDTO;
import cn.iocoder.yudao.module.tv.dal.dataobject.content.ContentMediaDO;
import cn.iocoder.yudao.module.tv.dal.mysql.content.ContentMediaMapper;
import cn.iocoder.yudao.module.tv.service.ProcessService;
import cn.iocoder.yudao.module.tv.service.content.strategy.ContentUploadStrategy;
import cn.iocoder.yudao.module.tv.util.FFmpegVideoUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.io.*;

import java.nio.charset.Charset;
import java.nio.charset.StandardCharsets;
import java.nio.file.Files;
import java.util.*;
import java.util.zip.ZipEntry;
import java.util.zip.ZipInputStream;

/**
 * 图片和视频混合上传策略实现
 * 处理 type=5 的内容（图片和视频混合）
 * 支持ZIP压缩包上传
 *
 * <AUTHOR>
 */
@Component
@Slf4j
public class MediaUploadStrategy implements ContentUploadStrategy {

    @Resource
    private DeduplicationFileApi deduplicationFileApi;

    @Resource
    private ContentMediaMapper contentMediaMapper;
    @Resource
    private ProcessService processService;

    // 支持的图片格式
    private static final Set<String> IMAGE_EXTENSIONS = new HashSet<>(Arrays.asList("jpg", "jpeg", "png", "gif", "bmp", "webp"));
    
    // 支持的视频格式
    private static final Set<String> VIDEO_EXTENSIONS = new HashSet<>(Arrays.asList("mp4", "avi", "mov", "wmv", "flv", "mkv"));

    @Override
    public Integer getSupportedType() {
        return 5; // 图片和视频混合
    }

    @Override
    public ContentUploadResult processContentFile(Long contentId, Long configId,
                                                InputStream contentInputStream,
                                                String fileName,
                                                Long fileSize,
                                                Map<String, Object> extParams,
                                                Boolean syncCompress,
                                                String compressConfig) {
        try {
            log.info("[MediaUploadStrategy][开始处理混合媒体上传] contentId: {}, fileName: {}, fileSize: {}", 
                contentId, fileName, fileSize);

            List<ContentMediaDO> mediaList = new ArrayList<>();
            
            if (isZipFile(fileName)) {
                // 处理ZIP压缩包
                mediaList = processZipFile(contentId,configId, contentInputStream, syncCompress, compressConfig);
            } else {
                // 处理单个文件
                ContentMediaDO media = processSingleFile(contentId,configId, contentInputStream, fileName,
                    fileSize, syncCompress, compressConfig);
                if (media != null) {
                    mediaList.add(media);
                }
            }

            if (mediaList.isEmpty()) {
                return new ContentUploadResult(false, "没有找到有效的媒体文件");
            }

            // 删除旧的媒体记录
//            contentMediaMapper.deleteByContentId(contentId);

            // 批量插入新的媒体记录
            contentMediaMapper.insertBatch(mediaList);

            // 返回处理结果
            Map<String, Object> resultData = new HashMap<>();
            resultData.put("mediaCount", mediaList.size());
            resultData.put("imageCount", mediaList.stream().mapToInt(m -> ObjectUtil.equal(0,m.getType()) ? 1 : 0).sum());
            resultData.put("videoCount", mediaList.stream().mapToInt(m -> ObjectUtil.equal(1,m.getType())  ? 1 : 0).sum());

            log.info("[MediaUploadStrategy][混合媒体上传成功] contentId: {}, 总文件数: {}", 
                contentId, mediaList.size());

            return new ContentUploadResult(true, "混合媒体上传成功", resultData);

        } catch (Exception e) {
            log.error("[MediaUploadStrategy][处理混合媒体上传失败] contentId: {}, fileName: {}", 
                contentId, fileName, e);
            return new ContentUploadResult(false, "处理混合媒体上传失败: " + e.getMessage());
        }
    }

    /**
     * 处理ZIP压缩包
     */
    private List<ContentMediaDO> processZipFile(Long contentId,Long configId, InputStream zipInputStream,
                                              Boolean syncCompress, String compressConfig) throws IOException {
        List<ContentMediaDO> mediaList = new ArrayList<>();
        File zipFile = FileUtil.writeFromStream(zipInputStream, FileUtil.createTempFile());
        // 标记 JVM 退出时，自动删除
        zipFile.deleteOnExit();

        // 1. 创建临时目录（Hutool unzip 会自动创建目录，但最好自己指定，方便清理）
        File tempDir = new File(zipFile.getParent(), "unzip_" + System.currentTimeMillis());
        if (!tempDir.exists()) {
            tempDir.mkdirs();
        }

        // 2. 解压（指定 UTF-8 编码，必要时可改 GBK）
        File unzipDir = null;
        // 尝试不同编码（UTF-8 优先，其次 GBK）
        List<Charset> charsets = Arrays.asList(CharsetUtil.CHARSET_UTF_8, CharsetUtil.CHARSET_GBK);
        for (Charset charset : charsets) {
            try{
                unzipDir = ZipUtil.unzip(zipFile, tempDir, charset);
            } catch (Exception e) {
                log.warn("[MediaUploadStrategy][跳过不支持的编码格式] charset: {} . error:{} ", charset , e.getMessage());
            }

        }


        try {
            // 3. 遍历解压后的文件
            Collection<File> files = FileUtil.loopFiles(unzipDir);
            int sortOrder = 1;
            for (File file : files) {
                String entryName = FileUtil.subPath(unzipDir.toString(), file); // 相对路径
                if (shouldSkip(entryName)) {
                    continue;
                }

                String extension = getFileExtension(entryName);
                if (!isValidMediaFile(extension)) {
                    log.debug("[MediaUploadStrategy][跳过不支持的文件] fileName: {}", entryName);
                    continue;
                }

                // 读取文件内容

                // 调用处理方法
                try (InputStream bais = Files.newInputStream(file.toPath())) {
                    ContentMediaDO media = processSingleFile(contentId, configId, bais,
                            file.getName(), file.length(), syncCompress, compressConfig);
                    if (media != null) {
                        media.setSort(sortOrder++);
                        media.setIsPreview(sortOrder < 8);
                        mediaList.add(media);
                    }
                }
            }
        } finally {
            // 4. 删除临时目录，避免磁盘堆积
            FileUtil.del(unzipDir);
        }

        return mediaList;
    }

    /** 是否跳过隐藏/系统文件 */
    private boolean shouldSkip(String entryName) {
        return entryName.contains("__MACOSX/")
                || entryName.contains("/._")
                || entryName.startsWith("._");
    }


    /**
     * 处理单个文件
     */
    private ContentMediaDO processSingleFile(Long contentId,Long configId, InputStream fileInputStream,
                                           String fileName, Long fileSize,
                                           Boolean syncCompress, String compressConfig) {
        try {
            String extension = getFileExtension(fileName);
            if (!isValidMediaFile(extension)) {
                log.warn("[MediaUploadStrategy][不支持的文件格式] fileName: {}", fileName);
                return null;
            }

            ContentMediaDO.ContentMediaDOBuilder contentMediaDOBuilder = ContentMediaDO.builder().contentId(contentId);

            // 上传文件到去重文件系统
            DeduplicationFileUploadReqDTO uploadReq = new DeduplicationFileUploadReqDTO();
            uploadReq.setName(fileName);
            // md5避免中文乱码
            uploadReq.setPath(StrUtil.format("pk/{}/{}/{}.{}",configId,contentId, Base64.encode(fileName),extension));
            uploadReq.setInputStream(fileInputStream);
            uploadReq.setContentLength(fileSize);
            uploadReq.setSyncCompress(syncCompress);
            uploadReq.setConfigId(configId);
            
            // 根据文件类型设置压缩类型和MIME类型
            if (IMAGE_EXTENSIONS.contains(extension)) {
                uploadReq.setType(getImageMimeType(extension));
                uploadReq.setCompressType("IMAGE_AVIF");
            } else if (VIDEO_EXTENSIONS.contains(extension)) {
                uploadReq.setType(getVideoMimeType(extension));
                uploadReq.setCompressType("MP4_COMPRESS");
                uploadReq.setCompressConfig(compressConfig);
                File videoFile = uploadReq.getTempFile();
                // 1. 获取视频时长
                int duration = FFmpegVideoUtils.getDuration(videoFile);
                contentMediaDOBuilder.duration(duration);
                // 获得第一帧封面图片。
                File coverImage = processService.extractCoverImage(videoFile);
                if(coverImage != null){
                    // 上传
                    DeduplicationFileUploadReqDTO uploadCoverReq = new DeduplicationFileUploadReqDTO();
                    uploadCoverReq.setTempFile(coverImage);
                    uploadCoverReq.setName(coverImage.getName());
                    uploadCoverReq.setConfigId(configId);
                    DeduplicationFileRespDTO uploadCoverResult = deduplicationFileApi.uploadFile(uploadCoverReq);
                    // 设置对应的值
                    contentMediaDOBuilder.coverUrl(uploadCoverResult.getUrl());
                }
            }

            DeduplicationFileRespDTO uploadResult = deduplicationFileApi.uploadFile(uploadReq);

            if (uploadResult == null || uploadResult.getUrl() == null) {
                log.error("[MediaUploadStrategy][文件上传失败] fileName: {}", fileName);
                return null;
            }

            // 创建媒体记录
            return contentMediaDOBuilder
                .url(uploadResult.getUrl())
                .type(IMAGE_EXTENSIONS.contains(extension) ? 0 : 1)
                .build();

        } catch (Exception e) {
            log.error("[MediaUploadStrategy][处理单个文件失败] fileName: {}", fileName, e);
            return null;
        }
    }

    /**
     * 判断是否为ZIP文件
     */
    private boolean isZipFile(String fileName) {
        return fileName != null && fileName.toLowerCase().endsWith(".zip");
    }

    /**
     * 获取文件扩展名
     */
    private String getFileExtension(String fileName) {
        if (fileName == null || !fileName.contains(".")) {
            return "";
        }
        return fileName.substring(fileName.lastIndexOf('.') + 1).toLowerCase();
    }

    /**
     * 判断是否为有效的媒体文件
     */
    private boolean isValidMediaFile(String extension) {
        return IMAGE_EXTENSIONS.contains(extension) || VIDEO_EXTENSIONS.contains(extension);
    }

    /**
     * 获取图片MIME类型
     */
    private String getImageMimeType(String extension) {
        switch (extension) {
            case "jpg":
            case "jpeg":
                return "image/jpeg";
            case "png":
                return "image/png";
            case "gif":
                return "image/gif";
            case "bmp":
                return "image/bmp";
            case "webp":
                return "image/webp";
            default:
                return "image/jpeg";
        }
    }

    /**
     * 获取视频MIME类型
     */
    private String getVideoMimeType(String extension) {
        switch (extension) {
            case "mp4":
                return "video/mp4";
            case "avi":
                return "video/avi";
            case "mov":
                return "video/quicktime";
            case "wmv":
                return "video/x-ms-wmv";
            case "flv":
                return "video/x-flv";
            case "mkv":
                return "video/x-matroska";
            default:
                return "video/mp4";
        }
    }

}
