package cn.iocoder.yudao.module.tv.controller.app.category.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Schema(description = "用户 APP - 视频分类 Response VO")
@Data
public class AppCategoryRespVO {

    @Schema(description = "分类编号", required = true, example = "1024")
    private Long id;

    @Schema(description = "分类名称", required = true, example = "动作片")
    private String name;

    @Schema(description = "父分类编号", required = true, example = "0")
    private Long parentId;

    @Schema(description = "显示顺序", required = true, example = "1")
    private Integer sort;

}
