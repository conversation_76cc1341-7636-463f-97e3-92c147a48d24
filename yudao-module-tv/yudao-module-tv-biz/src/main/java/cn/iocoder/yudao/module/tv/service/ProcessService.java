package cn.iocoder.yudao.module.tv.service;

import java.io.File;
import cn.iocoder.yudao.module.tv.service.dto.VideoProcessResult;

public interface ProcessService {
    /**
     * 聚合处理视频：生成首帧图、获取时长、优化MP4流式播放
     * @param videoFile 视频文件
     * @return 处理结果
     */
    VideoProcessResult processVideo(File videoFile);

    /**
     * 优化图片
     * @param imageFile
     * @return
     */
    File optimizedImage(File imageFile);
    /**
     * 提取视频第一帧作为封面图
     *
     * @param videoFile 视频文件
     * @return 封面图文件，失败返回null
     */
    File extractCoverImage(File videoFile);
}