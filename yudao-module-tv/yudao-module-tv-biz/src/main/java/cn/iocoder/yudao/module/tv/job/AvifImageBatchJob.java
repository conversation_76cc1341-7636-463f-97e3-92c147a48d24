package cn.iocoder.yudao.module.tv.job;

import cn.hutool.core.thread.ThreadUtil;
import cn.hutool.core.util.StrUtil;
import cn.iocoder.yudao.framework.quartz.core.handler.JobHandler;
import cn.iocoder.yudao.framework.tenant.core.job.TenantJob;
import cn.iocoder.yudao.framework.tenant.core.util.TenantUtils;
import cn.iocoder.yudao.module.tv.dal.dataobject.content.ContentMediaDO;
import cn.iocoder.yudao.module.tv.dal.mysql.content.ContentMediaMapper;
import cn.iocoder.yudao.module.tv.service.ProcessService;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import lombok.extern.slf4j.Slf4j;
import org.apache.poi.ss.formula.functions.T;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.ApplicationArguments;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.io.File;
import java.util.List;
import org.springframework.boot.ApplicationRunner;
/**
 * 数据库里面很多不是avif的图片。 我需要再写一个定时器，将这些数据慢慢的处理成avif。
 * 如果后续的内容都是avif图片的话，就不用这样的操作处理啦。
 */
@Slf4j
@Component
public class AvifImageBatchJob implements ApplicationRunner {

    @Resource
    private ContentMediaMapper mediaMapper;
    @Resource
    private ProcessService processService;
    @Value("${yudao.tv.job.avif.base:/}")
    private String bashPath;
//    @Override
@TenantJob
    public String execute(String bash)  {
        int size = batchOptimizeImagesToAvif(bash);
        return "处理"+size + "个";
    }
    // 每小时执行一次
    public int batchOptimizeImagesToAvif(String bash) {
        int i = 0;
        // 查询一批不是avif的图片（type=0，url不以.avif结尾）
        List<ContentMediaDO> images = mediaMapper.selectList(
                new LambdaQueryWrapper<ContentMediaDO>()
                        .eq(ContentMediaDO::getType, 0)
                        .notLikeLeft(ContentMediaDO::getUrl, ".avif")
                        .last("limit 100")
        );
        if (images == null || images.isEmpty()) {
            log.info("[AvifImageBatchJob] 没有需要优化的图片");
            return i;
        }
        for (ContentMediaDO media : images) {
            try {
                File originFile = new File(getAbsolutePath(bash,media.getUrl()));
                File avifFile = processService.optimizedImage(originFile);
                if (avifFile != null && avifFile.exists()) {
                    // 更新数据库
                    media.setUrl(avifFile.getAbsolutePath());
                    mediaMapper.updateById(media);
                    log.info("[AvifImageBatchJob] 优化图片成功: {} -> {}", originFile.getName(), avifFile.getName());
                    i++;
                }else{
                    Long id = media.getId();
                    mediaMapper.deleteById(id);
                    log.info("[AvifImageBatchJob] 原始文件 id=[{}]异常: {} -> {}", id, originFile.getAbsolutePath(), avifFile);
                }
            } catch (Exception e) {
                log.warn("[AvifImageBatchJob] 优化图片失败: {}", media.getUrl(), e);
            }
        }
        return i++;
    }


    // 获取文件绝对路径
    private String getAbsolutePath(String base_dir,String relativePath) {
        return base_dir + StrUtil.removePrefix(relativePath,"/");
    }

    @Override
    @TenantJob
    public void run(ApplicationArguments args) throws Exception {
//        ThreadUtil.execute(()->execute(bashPath));
    }
}