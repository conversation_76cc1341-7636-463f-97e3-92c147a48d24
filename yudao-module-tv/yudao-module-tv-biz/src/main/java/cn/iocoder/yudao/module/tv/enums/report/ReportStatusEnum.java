package cn.iocoder.yudao.module.tv.enums.report;

import cn.iocoder.yudao.framework.common.core.IntArrayValuable;
import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Arrays;

/**
 * 举报处理状态枚举
 *
 * <AUTHOR>
 */
@Getter
@AllArgsConstructor
public enum ReportStatusEnum implements IntArrayValuable {

    PENDING(0, "待处理"),
    PROCESSING(1, "处理中"),
    PROCESSED(2, "已处理"),
    REJECTED(3, "已驳回"),
    PENDING_CONFIRM(4, "待二次确认");

    public static final int[] ARRAYS = Arrays.stream(values()).mapToInt(ReportStatusEnum::getStatus).toArray();

    /**
     * 状态
     */
    private final Integer status;
    /**
     * 名字
     */
    private final String name;

    @Override
    public int[] array() {
        return ARRAYS;
    }

    public static ReportStatusEnum valueOf(Integer status) {
        return Arrays.stream(values()).filter(item -> item.getStatus().equals(status)).findFirst().orElse(null);
    }

}
