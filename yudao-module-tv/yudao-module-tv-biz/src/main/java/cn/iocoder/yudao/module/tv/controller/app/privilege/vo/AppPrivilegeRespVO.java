package cn.iocoder.yudao.module.tv.controller.app.privilege.vo;

import cn.iocoder.yudao.module.tv.controller.admin.privilege.vo.PrivilegeBaseVO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

@Schema(description = "用户 APP - 特权 Response VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class AppPrivilegeRespVO extends PrivilegeBaseVO {

    @Schema(description = "是否已开通", example = "true")
    private Boolean hasPrivilege;

    @Schema(description = "剩余天数", example = "15")
    private Integer remainingDays;

} 