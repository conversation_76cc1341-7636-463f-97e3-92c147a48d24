package cn.iocoder.yudao.module.tv.service.message.impl;

import cn.hutool.core.util.IdUtil;
import cn.hutool.json.JSONUtil;
import cn.iocoder.yudao.module.tv.dal.dataobject.message.LocalMessageDO;
import cn.iocoder.yudao.module.tv.dal.mysql.message.LocalMessageMapper;
import cn.iocoder.yudao.module.tv.enums.message.MessageStatusEnum;
import cn.iocoder.yudao.module.tv.service.message.LocalMessageService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 本地消息服务实现类
 *
 * <AUTHOR>
 */
@Service
@Slf4j
public class LocalMessageServiceImpl implements LocalMessageService {

    @Resource
    private LocalMessageMapper localMessageMapper;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public String sendMessage(String topic, String messageType, Object payload, String businessId, String businessType) {
        return sendMessage(topic, messageType, payload, businessId, businessType, 3);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public String sendMessage(String topic, String messageType, Object payload, String businessId, String businessType, int maxRetryCount) {
        String messageId = IdUtil.fastSimpleUUID();
        
        LocalMessageDO message = LocalMessageDO.builder()
                .messageId(messageId)
                .topic(topic)
                .messageType(messageType)
                .payload(JSONUtil.toJsonStr(payload))
                .status(MessageStatusEnum.PENDING.getStatus())
                .retryCount(0)
                .maxRetryCount(maxRetryCount)
                .businessId(businessId)
                .businessType(businessType)
                .build();

        localMessageMapper.insert(message);
        
        log.info("[sendMessage][保存本地消息成功] messageId: {}, topic: {}, businessId: {}", 
                messageId, topic, businessId);
        
        return messageId;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean markMessageSending(String messageId) {
        LocalMessageDO message = localMessageMapper.selectByMessageId(messageId);
        if (message == null) {
            log.warn("[markMessageSending][消息不存在] messageId: {}", messageId);
            return false;
        }

        if (!MessageStatusEnum.PENDING.getStatus().equals(message.getStatus()) && 
            !MessageStatusEnum.FAILED.getStatus().equals(message.getStatus())) {
            log.warn("[markMessageSending][消息状态不正确] messageId: {}, status: {}", messageId, message.getStatus());
            return false;
        }

        LocalMessageDO updateMessage = new LocalMessageDO();
        updateMessage.setId(message.getId());
        updateMessage.setStatus(MessageStatusEnum.SENDING.getStatus());
        updateMessage.setRetryCount(message.getRetryCount() + 1);
        
        int result = localMessageMapper.updateById(updateMessage);
        
        log.debug("[markMessageSending][标记消息发送中] messageId: {}, result: {}", messageId, result);
        return result > 0;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean markMessageSuccess(String messageId) {
        LocalMessageDO message = localMessageMapper.selectByMessageId(messageId);
        if (message == null) {
            log.warn("[markMessageSuccess][消息不存在] messageId: {}", messageId);
            return false;
        }

        LocalMessageDO updateMessage = new LocalMessageDO();
        updateMessage.setId(message.getId());
        updateMessage.setStatus(MessageStatusEnum.SUCCESS.getStatus());
        updateMessage.setErrorMessage(null);
        
        int result = localMessageMapper.updateById(updateMessage);
        
        log.info("[markMessageSuccess][标记消息成功] messageId: {}, result: {}", messageId, result);
        return result > 0;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean markMessageFailed(String messageId, String errorMessage) {
        LocalMessageDO message = localMessageMapper.selectByMessageId(messageId);
        if (message == null) {
            log.warn("[markMessageFailed][消息不存在] messageId: {}", messageId);
            return false;
        }

        LocalMessageDO updateMessage = new LocalMessageDO();
        updateMessage.setId(message.getId());
        updateMessage.setStatus(MessageStatusEnum.FAILED.getStatus());
        updateMessage.setErrorMessage(errorMessage);
        
        // 计算下次重试时间（指数退避）
        int retryCount = message.getRetryCount();
        if (retryCount < message.getMaxRetryCount()) {
            long delaySeconds = Math.min(300, (long) Math.pow(2, retryCount) * 30); // 最大延迟5分钟
            updateMessage.setNextRetryTime(LocalDateTime.now().plusSeconds(delaySeconds));
        }
        
        int result = localMessageMapper.updateById(updateMessage);
        
        log.warn("[markMessageFailed][标记消息失败] messageId: {}, retryCount: {}, nextRetryTime: {}, error: {}", 
                messageId, retryCount, updateMessage.getNextRetryTime(), errorMessage);
        return result > 0;
    }

    @Override
    public List<LocalMessageDO> getPendingMessages(int limit) {
        return localMessageMapper.selectPendingMessages(limit);
    }

    @Override
    public List<LocalMessageDO> getRetryMessages(int limit) {
        return localMessageMapper.selectRetryMessages(limit);
    }

    @Override
    public LocalMessageDO getMessage(String messageId) {
        return localMessageMapper.selectByMessageId(messageId);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public int cleanExpiredSuccessMessages(int days) {
        int count = localMessageMapper.deleteExpiredSuccessMessages(days);
        log.info("[cleanExpiredSuccessMessages][清理过期成功消息] days: {}, count: {}", days, count);
        return count;
    }

}
