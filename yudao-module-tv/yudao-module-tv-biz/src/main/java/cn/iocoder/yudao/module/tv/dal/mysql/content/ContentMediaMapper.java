package cn.iocoder.yudao.module.tv.dal.mysql.content;

import cn.iocoder.yudao.framework.common.pojo.PageParam;
import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.mybatis.core.mapper.BaseMapperX;
import cn.iocoder.yudao.framework.mybatis.core.query.LambdaQueryWrapperX;
import cn.iocoder.yudao.module.tv.dal.dataobject.category.CategoryDO;
import cn.iocoder.yudao.module.tv.dal.dataobject.content.ContentMediaDO;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Select;
import java.util.List;

@Mapper
public interface ContentMediaMapper extends BaseMapperX<ContentMediaDO> {
    /**
     * 根据内容ID查询图片和视频混合资源列表
     */
    default List<ContentMediaDO> selectListByContentId(Long contentId) {
        return selectList(new LambdaQueryWrapperX<ContentMediaDO>()
                .eq(ContentMediaDO::getContentId, contentId)
                .orderByAsc(ContentMediaDO::getSort));
    }
    /**
     * 根据内容ID查询图片和视频混合资源列表
     */
    default List<ContentMediaDO> selectListByContentIds(List<Long> contentIds) {
        return selectList(new LambdaQueryWrapperX<ContentMediaDO>()
                .in(ContentMediaDO::getContentId, contentIds)
                .orderByAsc(ContentMediaDO::getSort));
    }


    default PageResult<ContentMediaDO> selectPage(PageParam reqVO, Long contentId) {
        return selectPage(reqVO, new LambdaQueryWrapperX<ContentMediaDO>()
                .eq(ContentMediaDO::getContentId, contentId)
                .orderByDesc(ContentMediaDO::getSort));
    }





    /**
     * 根据内容ID查询图片和视频混合资源列表
     */
    default List<ContentMediaDO> selectListByContentIdOfpreview(Long contentId) {
        return selectList(new LambdaQueryWrapperX<ContentMediaDO>()
                .eq(ContentMediaDO::getContentId, contentId)
                .eq(ContentMediaDO::getIsPreview, true)
                .orderByAsc(ContentMediaDO::getPreviewSort));
    }
    /**
     * 根据内容ID查询图片和视频混合资源列表
     */
    default List<ContentMediaDO> selectListByContentIdsOfpreview(List<Long> contentIds) {
        return selectList(new LambdaQueryWrapperX<ContentMediaDO>()
                .in(ContentMediaDO::getContentId, contentIds)
                .eq(ContentMediaDO::getIsPreview, true)
                .orderByAsc(ContentMediaDO::getPreviewSort));
    }
    default PageResult<ContentMediaDO> selectPageOfpreview(PageParam reqVO, Long contentId) {
        return selectPage(reqVO, new LambdaQueryWrapperX<ContentMediaDO>()
                .eq(ContentMediaDO::getContentId, contentId)
                .eq(ContentMediaDO::getIsPreview, true)
                .orderByDesc(ContentMediaDO::getPreviewSort));
    }

    /**
     * 根据内容ID删除媒体记录
     *
     * @param contentId 内容ID
     * @return 删除数量
     */
    default int deleteByContentId(Long contentId) {
        return delete(ContentMediaDO::getContentId, contentId);
    }
}