package cn.iocoder.yudao.module.tv.enums.prize;

import cn.iocoder.yudao.framework.common.core.IntArrayValuable;
import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Arrays;

/**
 * 奖品类型枚举
 *
 * <AUTHOR>
 */
@Getter
@AllArgsConstructor
public enum PrizeTypeEnum implements IntArrayValuable {

    PHYSICAL(1, "实物"),
    POINTS(2, "虚拟（积分）"),
    BALANCE(3, "虚拟（余额）"),
    COUPON(4, "优惠券");

    public static final int[] ARRAYS = Arrays.stream(values()).mapToInt(PrizeTypeEnum::getType).toArray();

    /**
     * 类型
     */
    private final Integer type;
    /**
     * 名字
     */
    private final String name;

    @Override
    public int[] array() {
        return ARRAYS;
    }

    public static PrizeTypeEnum valueOf(Integer type) {
        return Arrays.stream(values()).filter(item -> item.getType().equals(type)).findFirst().orElse(null);
    }

    /**
     * 判断是否为虚拟奖品
     */
    public static boolean isVirtual(Integer type) {
        return POINTS.getType().equals(type) || BALANCE.getType().equals(type) || COUPON.getType().equals(type);
    }

    /**
     * 判断是否为实物奖品
     */
    public static boolean isPhysical(Integer type) {
        return PHYSICAL.getType().equals(type);
    }
}
