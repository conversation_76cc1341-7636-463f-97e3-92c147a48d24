package cn.iocoder.yudao.module.tv.controller.app.video.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

import java.math.BigDecimal;
import java.time.LocalDateTime;

@Schema(description = "用户 APP - 视频详情 Response VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class AppVideoDetailRespVO extends AppVideoRespVO {

    @Schema(description = "是否已点赞", required = true, example = "true")
    private Boolean liked;

    @Schema(description = "是否已收藏", required = true, example = "true")
    private Boolean favorite;

    @Schema(description = "是否已解锁", required = true, example = "false")
    private Boolean isUnlocked;

    @Schema(description = "是否可以播放", required = true, example = "true")
    private Boolean canPlay;

    @Schema(description = "是否可以完整播放", required = true, example = "false")
    private Boolean canPlayFull;

    @Schema(description = "试看时长（秒）", example = "60")
    private Integer trialSeconds;

    @Schema(description = "实际价格（积分）", example = "80")
    private Integer actualPrice;
}