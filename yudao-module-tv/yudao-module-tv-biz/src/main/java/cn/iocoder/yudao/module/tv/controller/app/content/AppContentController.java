package cn.iocoder.yudao.module.tv.controller.app.content;

import cn.iocoder.yudao.framework.apilog.core.annotation.ApiAccessLog;
import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.common.pojo.CommonResult;
import cn.iocoder.yudao.module.tv.controller.admin.content.vo.ContentPageReqVO;
import cn.iocoder.yudao.module.tv.controller.admin.content.vo.ContentRespVO;
import cn.iocoder.yudao.module.tv.controller.admin.content.vo.ContentUploadReqVO;
import cn.iocoder.yudao.module.tv.controller.app.content.vo.AppCollectionItemRelationReqVO;
import cn.iocoder.yudao.module.tv.controller.app.content.vo.ContentTypePageReqVO;
import cn.iocoder.yudao.module.tv.controller.app.content.vo.AppContentSearchReqVO;
import cn.iocoder.yudao.module.tv.controller.app.favorite.vo.AppFavoriteVideoPageReqVO;
import cn.iocoder.yudao.module.tv.controller.app.like.vo.AppLikeVideoPageReqVO;
import cn.iocoder.yudao.module.tv.service.ProcessService;
import cn.iocoder.yudao.module.tv.service.content.ContentService;
import cn.iocoder.yudao.module.tv.controller.app.content.vo.AppContentDetailRespVO;
import cn.iocoder.yudao.module.tv.service.content.ContentUploadService;
import cn.iocoder.yudao.module.tv.service.dto.VideoProcessResult;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;
import org.springframework.validation.annotation.Validated;
import javax.annotation.Resource;
import javax.annotation.security.PermitAll;
import javax.validation.Valid;

import java.io.File;
import java.io.IOException;
import java.util.Map;

import static cn.iocoder.yudao.framework.apilog.core.enums.OperateTypeEnum.CREATE;
import static cn.iocoder.yudao.framework.common.pojo.CommonResult.success;
import static cn.iocoder.yudao.framework.web.core.util.WebFrameworkUtils.getLoginUserId;

@RestController
@RequestMapping("/tv/content")
@Validated
public class AppContentController {

    @Resource
    private ContentService contentService;
    @Resource
    private ContentUploadService contentUploadService;
    /**
     * 分页查询内容列表（移动端）
     */
    @GetMapping("/page")
    @Operation(summary = "获得内容主体分页")
    @PermitAll
    public CommonResult<PageResult<ContentRespVO>> getContentPage(@Valid ContentPageReqVO pageReqVO) {
        pageReqVO.setStatus(1); // 必须是发布的内容。
        PageResult<ContentRespVO> pageResult = contentService.selectContentPage(pageReqVO);
        return success(pageResult);
    }

    @GetMapping("/detail")
    @Operation(summary = "获得详情")
    @Parameter(name = "id", description = "编号", required = true, example = "1024")
    public CommonResult<AppContentDetailRespVO> getVideoDetail(@RequestParam("id") Long id) {
        // 获取视频详情，包含播放权限信息
        return success(contentService.getDetailRespVO(getLoginUserId(), id));
    }

    @GetMapping("/content-detail")
    @Operation(summary = "获得带有内容的详情")
    @Parameter(name = "id", description = "编号", required = true, example = "1024")
    public CommonResult<AppContentDetailRespVO> getContentDetail(@RequestParam("id") Long id) {
        // 获取视频详情，包含播放的内容url，一口气返回，而且校验权限。
        return success(contentService.getContentDetailRespVO(getLoginUserId(), id));
    }

    @GetMapping("/favorite-page")
    @Operation(summary = "获得收藏的内容分页")
    public CommonResult<PageResult<ContentRespVO>> getFavoriteContentPage(@Valid AppFavoriteVideoPageReqVO pageVO) {
        return success(contentService.getFavoriteContentPage(getLoginUserId(), pageVO));
    }
    @GetMapping("/like-page")
    @Operation(summary = "获得喜欢的内容分页")
    public CommonResult<PageResult<ContentRespVO>> getLikeContentPage(@Valid AppLikeVideoPageReqVO pageVO) {
        return success(contentService.getLikeContentPage(getLoginUserId(), pageVO));
    }
    @GetMapping("/unlocked-page")
    @Operation(summary = "获得已解锁的内容分页")
    public CommonResult<PageResult<ContentRespVO>> getUserUnlockedContentPage(@Valid AppLikeVideoPageReqVO pageVO) {
        return success(contentService.getUserUnlockedContentPage(getLoginUserId(), pageVO));
    }
    @PostMapping("/unlock")
    @Operation(summary = "解锁内容")
    @Parameter(name = "contentId", description = "内容编号", required = true, example = "1024")
    public CommonResult<Boolean> unlockContent(@RequestParam("contentId") Long contentId) {
        return success(contentService.unlockContent(getLoginUserId(), contentId));
    }

    @GetMapping("/preview-content-page")
    @Operation(summary = "预览内容部分载体分页")
    @PermitAll
    public CommonResult<PageResult<?>> previewContentItemPage( ContentTypePageReqVO pageReqVO) {
        // 避免后端校验，权限逻辑
        return success(contentService.previewContentItemPage(pageReqVO));
    }

    @GetMapping("/get-content-page")
    @Operation(summary = "获得内容全部载体分页")
    public CommonResult<PageResult<?>> detailContentItemPage( ContentTypePageReqVO pageReqVO) {
        // 校验权限逻辑,权限不足返回预览的内容。
        return success(contentService.detailContentItemPage(getLoginUserId(),pageReqVO));
    }

    @GetMapping("/search")
    @Operation(summary = "搜索内容")
    @PermitAll
    public CommonResult<PageResult<ContentRespVO>> searchContent(@Valid AppContentSearchReqVO searchReqVO) {
        searchReqVO.setStatus(1); // 必须是发布的内容。
        PageResult<ContentRespVO> pageResult = contentService.searchContentPage(searchReqVO);
        return success(pageResult);
    }

    @PostMapping("/relatedCollection")
    @Operation(summary = "关联合集")
    @PermitAll
    public CommonResult<Integer> relatedCollection(@Valid AppCollectionItemRelationReqVO collectionItemRelationReqVO) {
        Integer pageResult = contentService.relatedCollection(collectionItemRelationReqVO.getCollectionContentId(),collectionItemRelationReqVO.getItemContentId());
        return success(pageResult);
    }

    @PostMapping("/upload")
    @Operation(summary = "统一内容上传接口")
    public CommonResult<Map<String, Object>> uploadContent(
            @Valid ContentUploadReqVO uploadReq) throws IOException {
        Map<String, Object> result = contentUploadService.uploadContent(uploadReq);
        return success(result);
    }
}