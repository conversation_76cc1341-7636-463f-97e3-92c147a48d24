package cn.iocoder.yudao.module.tv.controller.app.video;

import cn.iocoder.yudao.framework.common.pojo.CommonResult;
import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.module.tv.controller.admin.video.vo.VideoPageReqVO;
import cn.iocoder.yudao.module.tv.controller.app.video.vo.AppVideoDetailRespVO;
import cn.iocoder.yudao.module.tv.controller.app.video.vo.AppVideoPageReqVO;
import cn.iocoder.yudao.module.tv.controller.app.video.vo.AppVideoRespVO;

import cn.iocoder.yudao.module.tv.convert.video.AppVideoConvert;
import cn.iocoder.yudao.module.tv.dal.dataobject.video.VideoDO;
import cn.iocoder.yudao.module.tv.service.history.HistoryService;
import cn.iocoder.yudao.module.tv.service.video.VideoService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;

import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.List;



import static cn.iocoder.yudao.framework.common.pojo.CommonResult.success;
import static cn.iocoder.yudao.framework.web.core.util.WebFrameworkUtils.getLoginUserId;

@Tag(name = "用户 APP - 视频")
@RestController
@RequestMapping("/tv/video")
@Validated
public class AppVideoController {

    @Resource
    private VideoService videoService;

    @Resource
    private HistoryService historyService;

    @GetMapping("/get")
    @Operation(summary = "获得视频")
    @Parameter(name = "id", description = "编号", required = true, example = "1024")
    public CommonResult<AppVideoRespVO> getVideo(@RequestParam("id") Long id) {
        VideoDO video = videoService.getVideo(id);
        return success(AppVideoConvert.INSTANCE.convert(video));
    }

    @GetMapping("/detail")
    @Operation(summary = "获得视频详情")
    @Parameter(name = "id", description = "编号", required = true, example = "1024")
    public CommonResult<AppVideoDetailRespVO> getVideoDetail(@RequestParam("id") Long id) {
        // 获取视频详情，包含播放权限信息
        return success(videoService.getVideoDetail(getLoginUserId(), id));
    }



    @PostMapping("/unlock")
    @Operation(summary = "解锁视频")
    @Parameter(name = "videoId", description = "视频编号", required = true, example = "1024")
    public CommonResult<Boolean> unlockVideo(@RequestParam("videoId") Long videoId) {
        return success(videoService.unlockVideo(getLoginUserId(), videoId));
    }



    @GetMapping("/list-by-category")
    @Operation(summary = "获得分类视频列表")
    @Parameter(name = "categoryId", description = "分类编号", required = true, example = "1024")
    public CommonResult<List<AppVideoRespVO>> getVideoListByCategory(@RequestParam("categoryId") Long categoryId) {
        List<VideoDO> list = videoService.getVideoListByCategory(categoryId);
        return success(AppVideoConvert.INSTANCE.convertList(list));
    }

    @GetMapping("/page-by-category")
    @Operation(summary = "获得分类视频分页")
    public CommonResult<PageResult<AppVideoRespVO>> getVideoPageByCategory(@Valid AppVideoPageReqVO pageVO) {
        PageResult<VideoDO> pageResult = videoService.getVideoPageByCategory(pageVO);
        return success(AppVideoConvert.INSTANCE.convertPage(pageResult));
    }

    @GetMapping("/list-by-tag")
    @Operation(summary = "获得标签视频列表")
    @Parameter(name = "tag", description = "标签", required = true, example = "动作")
    public CommonResult<List<AppVideoRespVO>> getVideoListByTag(@RequestParam("tag") String tag) {
        List<VideoDO> list = videoService.getVideoListByTag(tag);
        return success(AppVideoConvert.INSTANCE.convertList(list));
    }

    @GetMapping("/page-by-tag")
    @Operation(summary = "获得标签视频分页")
    public CommonResult<PageResult<AppVideoRespVO>> getVideoPageByTag(@Valid AppVideoPageReqVO pageVO) {
        PageResult<VideoDO> pageResult = videoService.getVideoPageByTag(pageVO);
        return success(AppVideoConvert.INSTANCE.convertPage(pageResult));
    }

    @GetMapping("/page")
    @Operation(summary = "获得视频分页")
    public CommonResult<PageResult<AppVideoRespVO>> getVideoPage(@Valid VideoPageReqVO pageVO) {
        pageVO.setStatus(1);
        PageResult<VideoDO> pageResult = videoService.getVideoPage(pageVO);
        return success(AppVideoConvert.INSTANCE.convertPage(pageResult));
    }

    @GetMapping("/recommend")
    @Operation(summary = "获得推荐视频")
    @Parameter(name = "userId", description = "用户编号", required = true, example = "1024")
    @Parameter(name = "count", description = "推荐数量", required = true, example = "10")
    public CommonResult<PageResult<AppVideoRespVO>> getRecommendVideos(@RequestParam("userId") Long userId,
                                                                     @RequestParam(value = "count", defaultValue = "10") Integer count) {
        PageResult<VideoDO> pageResult = videoService.getRecommendVideos(userId, count);
        return success(AppVideoConvert.INSTANCE.convertPage(pageResult));
    }

//    @PostMapping("/play")
//    @Operation(summary = "播放视频")
//    @Parameter(name = "userId", description = "用户编号", required = true, example = "1024")
//    @Parameter(name = "videoId", description = "视频编号", required = true, example = "2048")
//    public CommonResult<AppVideoRespVO> playVideo(@RequestParam("userId") Long userId,
//                                                @RequestParam("videoId") Long videoId) {
//        // 获取视频信息
//        VideoDO video = videoService.getVideo(videoId);
//        // 创建或更新观看历史
//        historyService.updateHistory(userId, videoId, 0, false);
//        return success(AppVideoConvert.INSTANCE.convert(video));
//    }
//
//    @PostMapping("/update-progress")
//    @Operation(summary = "更新观看进度")
//    @Parameter(name = "userId", description = "用户编号", required = true, example = "1024")
//    @Parameter(name = "videoId", description = "视频编号", required = true, example = "2048")
//    @Parameter(name = "progress", description = "观看进度（秒）", required = true, example = "120")
//    @Parameter(name = "finished", description = "是否看完", required = true, example = "false")
//    public CommonResult<Boolean> updateProgress(@RequestParam("userId") Long userId,
//                                              @RequestParam("videoId") Long videoId,
//                                              @RequestParam("progress") Integer progress,
//                                              @RequestParam("finished") Boolean finished) {
//        historyService.updateHistory(userId, videoId, progress, finished);
//        return success(true);
//    }

    @GetMapping("/unlocked-list")
    @Operation(summary = "获取已解锁视频列表")
    public CommonResult<PageResult<AppVideoRespVO>> getUnlockedVideoList(@Valid VideoPageReqVO pageVO) {
        // 获取当前用户ID
        Long userId = getLoginUserId();
        // 获取用户已解锁的视频分页
        PageResult<VideoDO> pageResult = videoService.getUserUnlockedVideoPage(userId, pageVO);
        // 转换为响应VO
        return success(AppVideoConvert.INSTANCE.convertPage(pageResult));
    }
}