package cn.iocoder.yudao.module.tv.dal.mysql.privilege;

import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.mybatis.core.mapper.BaseMapperX;
import cn.iocoder.yudao.framework.mybatis.core.query.LambdaQueryWrapperX;
import cn.iocoder.yudao.module.tv.controller.admin.privilege.vo.PrivilegePageReqVO;
import cn.iocoder.yudao.module.tv.dal.dataobject.privilege.PrivilegeDO;
import org.apache.ibatis.annotations.Mapper;

/**
 * 特权 Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface PrivilegeMapper extends BaseMapperX<PrivilegeDO> {

    default PageResult<PrivilegeDO> selectPage(PrivilegePageReqVO reqVO) {
        return selectPage(reqVO, new LambdaQueryWrapperX<PrivilegeDO>()
                .likeIfPresent(PrivilegeDO::getName, reqVO.getName())
                .eqIfPresent(PrivilegeDO::getStatus, reqVO.getStatus())
                .orderByDesc(PrivilegeDO::getId));
    }

} 