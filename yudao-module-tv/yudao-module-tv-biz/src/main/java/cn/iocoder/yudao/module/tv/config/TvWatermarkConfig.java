package cn.iocoder.yudao.module.tv.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

/**
 * TV模块水印配置
 * <per>
 * tv:
 *   watermark:
 *     # 是否启用水印
 *     enabled: true
 *
 *     # 水印图片路径（绝对路径）
 *     logo-path: "/path/to/your/logo.png"
 *
 *     # 水印位置：top-left, top-right, bottom-left, bottom-right, center
 *     # 默认右上角
 *     position: "top-right"
 *
 *     # 水印透明度（0.0-1.0，0.0完全透明，1.0完全不透明）
 *     # 默认0.5
 *     opacity: 0.5
 *
 *     # 水印大小比例（相对于原图/视频的比例）
 *     # 默认0.1即10%
 *     scale: 0.1
 * </per>
 * <AUTHOR>
 */
@Component
@ConfigurationProperties(prefix = "tv.watermark")
@Data
public class TvWatermarkConfig {

    /**
     * 是否启用水印
     */
    private Boolean enabled = false;

    /**
     * 水印图片路径
     */
    private String logoPath;

    /**
     * 水印位置：top-left, top-right, bottom-left, bottom-right, center
     */
    private String position = "top-right"; // 1

    /**
     * 水印透明度（0.0-1.0）
     */
    private Double opacity = 0.5;

    /**
     * 水印大小比例（相对于原图的比例）
     */
    private Double scale = 0.1;

    /**
     * 需要覆盖的原logo位置（9宫格：1-9 或 top-left等）
     */
    private String coverPosition;

    /**
     * 覆盖区域大小，格式"width:height"，如"200:100"
     */
    private String coverSize;

}
