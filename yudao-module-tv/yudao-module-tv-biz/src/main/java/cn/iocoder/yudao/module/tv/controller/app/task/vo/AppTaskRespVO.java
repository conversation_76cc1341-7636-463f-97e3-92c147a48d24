package cn.iocoder.yudao.module.tv.controller.app.task.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;

import java.time.LocalDateTime;

@Schema(description = "用户 App - 任务信息 Response VO")
@Data
public class AppTaskRespVO {

    @Schema(description = "任务ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "1024")
    private Long id;

    @Schema(description = "任务名称", requiredMode = Schema.RequiredMode.REQUIRED, example = "分享网站到微信群")
    private String name;

    @Schema(description = "任务描述", example = "分享网站到微信群/微博并截图上传")
    private String description;

    @Schema(description = "任务类型", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
    private Integer type;

    @Schema(description = "任务类型名称", example = "分享任务")
    private String typeName;

    @Schema(description = "奖励积分", requiredMode = Schema.RequiredMode.REQUIRED, example = "20")
    private Integer rewardPoints;

    @Schema(description = "每日完成次数限制", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
    private Integer dailyLimit;

    @Schema(description = "总完成次数限制", example = "100")
    private Integer totalLimit;

    @Schema(description = "任务状态", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
    private Integer status;

    @Schema(description = "任务状态名称", example = "启用")
    private String statusName;

    @Schema(description = "任务开始时间")
    private LocalDateTime startTime;

    @Schema(description = "任务结束时间")
    private LocalDateTime endTime;

    @Schema(description = "排序值", example = "0")
    private Integer sort;

    // 用户相关信息
    @Schema(description = "用户今日已完成次数", example = "0")
    private Integer todayCompletedCount;

    @Schema(description = "用户总完成次数", example = "5")
    private Integer totalCompletedCount;

    @Schema(description = "是否可以完成", example = "true")
    private Boolean canComplete;

    @Schema(description = "不能完成的原因", example = "今日完成次数已达上限")
    private String cannotCompleteReason;

}
