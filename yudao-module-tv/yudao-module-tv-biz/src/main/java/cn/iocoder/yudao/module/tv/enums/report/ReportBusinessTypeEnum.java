package cn.iocoder.yudao.module.tv.enums.report;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Arrays;

/**
 * 举报业务类型枚举
 *
 * <AUTHOR>
 */
@Getter
@AllArgsConstructor
public enum ReportBusinessTypeEnum {

    CONTENT("content", "内容举报"),
    USER("user", "用户举报"),
    COMMENT("comment", "评论举报");

    /**
     * 业务类型
     */
    private final String type;
    /**
     * 名字
     */
    private final String name;

    public static ReportBusinessTypeEnum valueOfType(String type) {
        return Arrays.stream(values()).filter(item -> item.getType().equals(type)).findFirst().orElse(null);
    }

}
