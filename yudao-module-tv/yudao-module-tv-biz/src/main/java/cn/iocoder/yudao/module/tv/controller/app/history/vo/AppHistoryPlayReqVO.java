package cn.iocoder.yudao.module.tv.controller.app.history.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotNull;

@Schema(description = "用户 APP - 播放视频历史 Request VO")
@Data
public class AppHistoryPlayReqVO {

    @Schema(description = "视频编号", required = true, example = "1024")
    @NotNull(message = "视频编号不能为空")
    private Long videoId;

    @Schema(description = "观看进度（秒）", required = true, example = "120")
    @NotNull(message = "观看进度不能为空")
    private Integer progress;

    @Schema(description = "是否看完", required = true, example = "false")
    @NotNull(message = "是否看完不能为空")
    private Boolean finished;

}
