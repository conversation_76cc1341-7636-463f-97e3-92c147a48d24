package cn.iocoder.yudao.module.tv.service.prize;

import cn.iocoder.yudao.framework.common.exception.util.ServiceExceptionUtil;
import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.module.tv.controller.admin.prize.vo.PrizePageReqVO;
import cn.iocoder.yudao.module.tv.dal.dataobject.prize.PrizeDO;
import cn.iocoder.yudao.module.tv.dal.mysql.prize.PrizeMapper;
import cn.iocoder.yudao.module.tv.enums.prize.PrizeStatusEnum;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.List;

import static cn.iocoder.yudao.module.tv.enums.ErrorCodeConstants.*;

/**
 * 奖品 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Slf4j
public class PrizeServiceImpl implements PrizeService {

    @Resource
    private PrizeMapper prizeMapper;

    @Override
    public PrizeDO getPrize(Long id) {
        return prizeMapper.selectById(id);
    }

    @Override
    public List<PrizeDO> getPrizeList() {
        return prizeMapper.selectList();
    }

    @Override
    public List<PrizeDO> getAvailablePrizes() {
        return prizeMapper.selectAvailablePrizes();
    }

    @Override
    public PageResult<PrizeDO> getPrizePage(PrizePageReqVO pageReqVO) {
        return prizeMapper.selectPage(pageReqVO);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Long createPrize(PrizeDO prize) {
        prizeMapper.insert(prize);
        return prize.getId();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updatePrize(PrizeDO prize) {
        // 校验存在
        validatePrizeExists(prize.getId());
        // 更新
        prizeMapper.updateById(prize);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void deletePrize(Long id) {
        // 校验存在
        validatePrizeExists(id);
        // 删除
        prizeMapper.deleteById(id);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void decreaseStock(Long id, Integer count) {
        PrizeDO prize = validatePrizeExists(id);
        if (prize.getStock() != null && prize.getStock() != -1) {
            if (prize.getStock() < count) {
                throw ServiceExceptionUtil.exception(PRIZE_STOCK_NOT_ENOUGH);
            }
            prize.setStock(prize.getStock() - count);
            prizeMapper.updateById(prize);
        }
    }

    private PrizeDO validatePrizeExists(Long id) {
        PrizeDO prize = prizeMapper.selectById(id);
        if (prize == null) {
            throw ServiceExceptionUtil.exception(PRIZE_NOT_EXISTS);
        }
        return prize;
    }
}
