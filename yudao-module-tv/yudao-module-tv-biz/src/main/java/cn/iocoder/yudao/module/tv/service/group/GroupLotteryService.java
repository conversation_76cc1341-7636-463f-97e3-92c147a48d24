package cn.iocoder.yudao.module.tv.service.group;

import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.module.tv.controller.admin.group.vo.GroupInfoPageReqVO;
import cn.iocoder.yudao.module.tv.controller.admin.group.vo.GroupOrderPageReqVO;
import cn.iocoder.yudao.module.tv.controller.admin.group.vo.GroupResultPageReqVO;
import cn.iocoder.yudao.module.tv.controller.app.group.vo.AppGroupCreateReqVO;
import cn.iocoder.yudao.module.tv.controller.app.group.vo.AppGroupDetailRespVO;
import cn.iocoder.yudao.module.tv.controller.app.group.vo.AppGroupJoinReqVO;
import cn.iocoder.yudao.module.tv.dal.dataobject.group.GroupInfoDO;
import cn.iocoder.yudao.module.tv.dal.dataobject.group.GroupOrderDO;
import cn.iocoder.yudao.module.tv.dal.dataobject.group.GroupResultDO;

import javax.validation.Valid;
import java.util.List;

/**
 * 拼团抽奖 Service 接口
 *
 * <AUTHOR>
 */
public interface GroupLotteryService {

    // ==================== 拼团管理 ====================

    /**
     * 创建拼团
     *
     * @param userId 用户ID
     * @param createReqVO 创建信息
     * @return 拼团ID
     */
    Long createGroup(Long userId, @Valid AppGroupCreateReqVO createReqVO);

    /**
     * 参与拼团
     *
     * @param userId 用户ID
     * @param joinReqVO 参与信息
     * @return 参与记录ID
     */
    Long joinGroup(Long userId, @Valid AppGroupJoinReqVO joinReqVO);

    /**
     * 获取拼团详情
     *
     * @param userId 用户ID（用于判断是否已参与）
     * @param groupId 拼团ID
     * @return 拼团详情
     */
    AppGroupDetailRespVO getGroupDetail(Long userId, Long groupId);

    /**
     * 获取用户参与的拼团列表
     *
     * @param userId 用户ID
     * @return 拼团列表
     */
    List<AppGroupDetailRespVO> getUserGroups(Long userId);

    /**
     * 获取热门拼团列表
     *
     * @param limit 数量限制
     * @return 拼团列表
     */
    List<AppGroupDetailRespVO> getHotGroups(Integer limit);

    // ==================== 开奖相关 ====================

    /**
     * 执行开奖（满员时自动调用）
     *
     * @param groupId 拼团ID
     * @return 开奖结果ID
     */
    Long drawLottery(Long groupId);

    /**
     * 处理超时拼团（退款）
     *
     * @param groupId 拼团ID
     */
    void handleExpiredGroup(Long groupId);

    /**
     * 批量处理超时拼团
     *
     * @return 处理数量
     */
    int handleExpiredGroups();

    /**
     * 批量处理满员拼团开奖
     *
     * @return 处理数量
     */
    int handleFullGroups();

    // ==================== 管理后台 ====================

    /**
     * 获得拼团信息分页
     *
     * @param pageReqVO 分页查询
     * @return 拼团信息分页
     */
    PageResult<GroupInfoDO> getGroupInfoPage(GroupInfoPageReqVO pageReqVO);

    /**
     * 获得拼团参与记录分页
     *
     * @param pageReqVO 分页查询
     * @return 参与记录分页
     */
    PageResult<GroupOrderDO> getGroupOrderPage(GroupOrderPageReqVO pageReqVO);

    /**
     * 获得开奖记录分页
     *
     * @param pageReqVO 分页查询
     * @return 开奖记录分页
     */
    PageResult<GroupResultDO> getGroupResultPage(GroupResultPageReqVO pageReqVO);

    /**
     * 获得拼团信息
     *
     * @param id 编号
     * @return 拼团信息
     */
    GroupInfoDO getGroupInfo(Long id);

    /**
     * 获得开奖记录
     *
     * @param id 编号
     * @return 开奖记录
     */
    GroupResultDO getGroupResult(Long id);

    // ==================== 统计相关 ====================

    /**
     * 更新用户抽奖统计（参与时调用）
     *
     * @param userId 用户ID
     */
    void updateUserLotteryStat(Long userId);

    /**
     * 更新用户中奖统计（中奖时调用）
     *
     * @param userId 用户ID
     */
    void updateUserWinStat(Long userId);
}
