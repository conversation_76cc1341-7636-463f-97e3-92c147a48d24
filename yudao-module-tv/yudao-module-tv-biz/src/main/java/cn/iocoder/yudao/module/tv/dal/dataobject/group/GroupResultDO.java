package cn.iocoder.yudao.module.tv.dal.dataobject.group;

import cn.iocoder.yudao.framework.mybatis.core.dataobject.BaseDO;
import com.baomidou.mybatisplus.annotation.KeySequence;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.ToString;

/**
 * 拼团开奖记录 DO
 *
 * <AUTHOR>
 */
@TableName("tv_group_result")
@KeySequence("tv_group_result_seq")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class GroupResultDO extends BaseDO {

    /**
     * 记录ID
     */
    @TableId
    private Long id;

    /**
     * 拼团ID
     */
    private Long groupId;

    /**
     * 中奖用户ID
     */
    private Long winnerId;

    /**
     * 奖品ID
     */
    private Long prizeId;

    /**
     * 奖品名称
     */
    private String prizeName;

    /**
     * 随机种子
     */
    private String randomSeed;

    /**
     * 算法类型：1-权重随机，2-保底必中
     * 
     * 枚举 {@link cn.iocoder.yudao.module.tv.enums.group.DrawAlgorithmEnum}
     */
    private Integer algorithmType;

    /**
     * 开奖详情（JSON格式，包含参与用户权重等信息）
     */
    private String drawDetails;
}
