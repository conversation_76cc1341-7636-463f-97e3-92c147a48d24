package cn.iocoder.yudao.module.tv.controller.app.favorite.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotNull;

@Schema(description = "用户 APP - 收藏创建 Request VO")
@Data
public class AppFavoriteCreateReqVO {

    @Schema(description = "视频编号", required = true, example = "2048")
    @NotNull(message = "视频编号不能为空")
    private Long videoId;
} 