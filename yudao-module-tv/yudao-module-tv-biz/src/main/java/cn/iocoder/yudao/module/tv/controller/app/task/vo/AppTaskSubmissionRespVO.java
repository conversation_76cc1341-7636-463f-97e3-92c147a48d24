package cn.iocoder.yudao.module.tv.controller.app.task.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;

import java.time.LocalDateTime;

@Schema(description = "用户 App - 任务提交记录 Response VO")
@Data
public class AppTaskSubmissionRespVO {

    @Schema(description = "提交ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "1024")
    private Long id;

    @Schema(description = "任务ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "1024")
    private Long taskId;

    @Schema(description = "任务名称", example = "分享网站到微信群")
    private String taskName;

    @Schema(description = "截图base64数据")
    private String screenshotBase64;

    @Schema(description = "提交说明", example = "已分享到微信群并获得多个点赞")
    private String description;

    @Schema(description = "审核状态", requiredMode = Schema.RequiredMode.REQUIRED, example = "0")
    private Integer status;

    @Schema(description = "审核状态名称", example = "待审核")
    private String statusName;

    @Schema(description = "审核结果说明", example = "截图清晰，分享内容符合要求")
    private String auditResult;

    @Schema(description = "审核时间")
    private LocalDateTime auditTime;

    @Schema(description = "实际奖励积分", example = "20")
    private Integer rewardPoints;

    @Schema(description = "提交时间", requiredMode = Schema.RequiredMode.REQUIRED)
    private LocalDateTime createTime;

}
