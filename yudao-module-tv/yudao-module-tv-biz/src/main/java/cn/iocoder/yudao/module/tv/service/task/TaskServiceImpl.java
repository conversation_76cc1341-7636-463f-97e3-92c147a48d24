package cn.iocoder.yudao.module.tv.service.task;

import cn.hutool.core.util.StrUtil;
import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.common.util.object.BeanUtils;
import cn.iocoder.yudao.module.member.api.point.MemberPointApi;
import cn.iocoder.yudao.module.member.enums.point.MemberPointBizTypeEnum;
import cn.iocoder.yudao.module.tv.controller.admin.task.vo.TaskSaveReqVO;
import cn.iocoder.yudao.module.tv.controller.admin.task.vo.TaskSubmissionAuditReqVO;
import cn.iocoder.yudao.module.tv.controller.app.task.vo.AppTaskRespVO;
import cn.iocoder.yudao.module.tv.controller.app.task.vo.AppTaskSubmissionCreateReqVO;
import cn.iocoder.yudao.module.tv.controller.app.task.vo.AppTaskSubmissionPageReqVO;
import cn.iocoder.yudao.module.tv.controller.app.task.vo.AppTaskSubmissionRespVO;
import cn.iocoder.yudao.module.tv.dal.dataobject.task.TaskDO;
import cn.iocoder.yudao.module.tv.dal.dataobject.task.TaskSubmissionDO;
import cn.iocoder.yudao.module.tv.dal.dataobject.task.UserTaskDO;
import cn.iocoder.yudao.module.tv.dal.mysql.task.TaskMapper;
import cn.iocoder.yudao.module.tv.dal.mysql.task.TaskSubmissionMapper;
import cn.iocoder.yudao.module.tv.dal.mysql.task.UserTaskMapper;
import cn.iocoder.yudao.module.tv.enums.task.TaskStatusEnum;
import cn.iocoder.yudao.module.tv.enums.task.TaskSubmissionStatusEnum;
import cn.iocoder.yudao.module.tv.enums.task.TaskTypeEnum;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.validation.annotation.Validated;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.List;

import static cn.iocoder.yudao.framework.common.exception.util.ServiceExceptionUtil.exception;
import static cn.iocoder.yudao.module.tv.enums.ErrorCodeConstants.*;

/**
 * 任务系统 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
@Slf4j
public class TaskServiceImpl implements TaskService {

    @Resource
    private TaskMapper taskMapper;

    @Resource
    private UserTaskMapper userTaskMapper;

    @Resource
    private TaskSubmissionMapper taskSubmissionMapper;

    @Resource
    private ImageHashService imageHashService;

    @Resource
    private MemberPointApi memberPointApi;

    @Override
    public Long createTask(TaskSaveReqVO createReqVO) {
        // 插入
        TaskDO task = BeanUtils.toBean(createReqVO, TaskDO.class);
        taskMapper.insert(task);
        return task.getId();
    }

    @Override
    public void updateTask(TaskSaveReqVO updateReqVO) {
        // 校验存在
        validateTaskExists(updateReqVO.getId());
        // 更新
        TaskDO updateObj = BeanUtils.toBean(updateReqVO, TaskDO.class);
        taskMapper.updateById(updateObj);
    }

    @Override
    public void deleteTask(Long id) {
        // 校验存在
        validateTaskExists(id);
        // 删除
        taskMapper.deleteById(id);
    }

    @Override
    public TaskDO getTask(Long id) {
        return taskMapper.selectById(id);
    }

    @Override
    public List<TaskDO> getTaskList() {
        return taskMapper.selectList();
    }

    @Override
    public List<AppTaskRespVO> getUserAvailableTasks(Long userId) {
        // 1. 获取启用的任务列表
        List<TaskDO> tasks = taskMapper.selectEnabledTasks();
        
        // 2. 转换为响应VO并设置用户相关信息
        List<AppTaskRespVO> respList = BeanUtils.toBean(tasks, AppTaskRespVO.class);
        
        // 3. 设置任务类型名称和用户完成情况
        LocalDateTime startOfDay = LocalDateTime.now().withHour(0).withMinute(0).withSecond(0).withNano(0);
        LocalDateTime endOfDay = startOfDay.plusDays(1).minusNanos(1);
        
        respList.forEach(resp -> {
            // 设置任务类型名称
            TaskTypeEnum taskType = TaskTypeEnum.valueOf(resp.getType());
            if (taskType != null) {
                resp.setTypeName(taskType.getName());
            }
            
            // 设置任务状态名称
            TaskStatusEnum taskStatus = TaskStatusEnum.valueOf(resp.getStatus());
            if (taskStatus != null) {
                resp.setStatusName(taskStatus.getName());
            }
            
            // 获取用户任务记录
            UserTaskDO userTask = userTaskMapper.selectByUserIdAndTaskId(userId, resp.getId());
            if (userTask != null) {
                resp.setTotalCompletedCount(userTask.getCompletedCount());
            } else {
                resp.setTotalCompletedCount(0);
            }
            
            // 统计今日完成次数
            Integer todayCount = taskSubmissionMapper.countTodayApproved(userId, resp.getId(), startOfDay, endOfDay);
            resp.setTodayCompletedCount(todayCount);
            
            // 判断是否可以完成
            boolean canComplete = true;
            String reason = null;
            
            // 检查今日完成次数限制
            if (todayCount >= resp.getDailyLimit()) {
                canComplete = false;
                reason = "今日完成次数已达上限";
            }
            
            // 检查总完成次数限制
            if (resp.getTotalLimit() != null && resp.getTotalCompletedCount() >= resp.getTotalLimit()) {
                canComplete = false;
                reason = "总完成次数已达上限";
            }
            
            resp.setCanComplete(canComplete);
            resp.setCannotCompleteReason(reason);
        });
        
        return respList;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Long submitTask(Long userId, AppTaskSubmissionCreateReqVO createReqVO) {
        // 1. 校验任务存在且可用
        TaskDO task = validateTaskExists(createReqVO.getTaskId());
        if (!TaskStatusEnum.ENABLED.getStatus().equals(task.getStatus())) {
            throw exception(TASK_NOT_ENABLED);
        }

        // 2. 检查用户是否可以完成任务
        LocalDateTime startOfDay = LocalDateTime.now().withHour(0).withMinute(0).withSecond(0).withNano(0);
        LocalDateTime endOfDay = startOfDay.plusDays(1).minusNanos(1);

        Integer todayCount = taskSubmissionMapper.countTodayApproved(userId, createReqVO.getTaskId(), startOfDay, endOfDay);
        if (todayCount >= task.getDailyLimit()) {
            throw exception(TASK_DAILY_LIMIT_EXCEEDED);
        }

        // 3. 计算图片哈希值
        String screenshotHash = imageHashService.calculateImageHash(createReqVO.getScreenshotBase64());

        // 4. 检查是否有相似的截图（防止重复提交）
        List<TaskSubmissionDO> similarSubmissions = taskSubmissionMapper.selectSimilarScreenshots(screenshotHash, 5);
        for (TaskSubmissionDO similar : similarSubmissions) {
            if (imageHashService.isSimilar(screenshotHash, similar.getScreenshotHash())) {
                throw exception(TASK_DUPLICATE_SCREENSHOT);
            }
        }

        // 5. 创建提交记录
        TaskSubmissionDO submission = TaskSubmissionDO.builder()
                .userId(userId)
                .taskId(createReqVO.getTaskId())
                .screenshotBase64(createReqVO.getScreenshotBase64())
                .screenshotHash(screenshotHash)
                .description(createReqVO.getDescription())
                .status(TaskSubmissionStatusEnum.PENDING.getStatus())
                .build();

        taskSubmissionMapper.insert(submission);

        log.info("[submitTask][用户提交任务] userId={}, taskId={}, submissionId={}",
                userId, createReqVO.getTaskId(), submission.getId());

        return submission.getId();
    }

    @Override
    public PageResult<AppTaskSubmissionRespVO> getUserTaskSubmissionPage(Long userId, AppTaskSubmissionPageReqVO pageReqVO) {
        PageResult<TaskSubmissionDO> pageResult = taskSubmissionMapper.selectUserSubmissionPage(userId, pageReqVO);

        // 转换为响应VO
        List<AppTaskSubmissionRespVO> respList = BeanUtils.toBean(pageResult.getList(), AppTaskSubmissionRespVO.class);

        // 设置任务名称和状态名称
        respList.forEach(resp -> {
            // 获取任务信息
            TaskDO task = taskMapper.selectById(resp.getTaskId());
            if (task != null) {
                resp.setTaskName(task.getName());
            }

            // 设置状态名称
            TaskSubmissionStatusEnum status = TaskSubmissionStatusEnum.valueOf(resp.getStatus());
            if (status != null) {
                resp.setStatusName(status.getName());
            }
        });

        return new PageResult<>(respList, pageResult.getTotal());
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void auditTaskSubmission(TaskSubmissionAuditReqVO auditReqVO, Long auditorId) {
        // 1. 校验提交记录存在
        TaskSubmissionDO submission = validateTaskSubmissionExists(auditReqVO.getId());

        // 2. 校验状态
        if (!TaskSubmissionStatusEnum.PENDING.getStatus().equals(submission.getStatus())) {
            throw exception(TASK_SUBMISSION_NOT_EXISTS);
        }

        // 3. 更新审核状态
        TaskSubmissionDO updateObj = new TaskSubmissionDO();
        updateObj.setId(auditReqVO.getId());
        updateObj.setAuditResult(auditReqVO.getAuditResult());
        updateObj.setAuditUserId(auditorId);
        updateObj.setAuditTime(LocalDateTime.now());

        if ("approve".equals(auditReqVO.getAction())) {
            // 审核通过
            updateObj.setStatus(TaskSubmissionStatusEnum.APPROVED.getStatus());

            // 设置奖励积分
            TaskDO task = taskMapper.selectById(submission.getTaskId());
            Integer rewardPoints = auditReqVO.getRewardPoints() != null ?
                    auditReqVO.getRewardPoints() : task.getRewardPoints();
            updateObj.setRewardPoints(rewardPoints);

            taskSubmissionMapper.updateById(updateObj);

            // 发放积分奖励
            grantPointReward(submission.getUserId(), rewardPoints, task.getName());

            // 更新用户任务记录
            updateUserTaskRecord(submission.getUserId(), submission.getTaskId(), rewardPoints);

            log.info("[auditTaskSubmission][任务提交审核通过] submissionId={}, userId={}, rewardPoints={}",
                    auditReqVO.getId(), submission.getUserId(), rewardPoints);
        } else {
            // 审核拒绝
            updateObj.setStatus(TaskSubmissionStatusEnum.REJECTED.getStatus());
            taskSubmissionMapper.updateById(updateObj);

            log.info("[auditTaskSubmission][任务提交审核拒绝] submissionId={}, userId={}, reason={}",
                    auditReqVO.getId(), submission.getUserId(), auditReqVO.getAuditResult());
        }
    }

    @Override
    public TaskSubmissionDO getTaskSubmission(Long id) {
        return taskSubmissionMapper.selectById(id);
    }

    @Override
    public List<TaskSubmissionDO> getPendingTaskSubmissions() {
        return taskSubmissionMapper.selectPendingSubmissions();
    }

    /**
     * 校验任务是否存在
     */
    private TaskDO validateTaskExists(Long id) {
        TaskDO task = taskMapper.selectById(id);
        if (task == null) {
            throw exception(TASK_NOT_EXISTS);
        }
        return task;
    }

    /**
     * 校验任务提交记录是否存在
     */
    private TaskSubmissionDO validateTaskSubmissionExists(Long id) {
        TaskSubmissionDO submission = taskSubmissionMapper.selectById(id);
        if (submission == null) {
            throw exception(TASK_SUBMISSION_NOT_EXISTS);
        }
        return submission;
    }

    /**
     * 发放积分奖励
     */
    private void grantPointReward(Long userId, Integer points, String taskName) {
        try {
            // 需要先添加任务奖励的业务类型到MemberPointBizTypeEnum中
            // 这里暂时使用其他类型，实际使用时需要添加TASK_REWARD类型
            memberPointApi.addPoint(userId, points, MemberPointBizTypeEnum.SIGN.getType(),
                    "task_" + System.currentTimeMillis());

            log.info("[grantPointReward][发放积分奖励成功] userId={}, points={}, taskName={}",
                    userId, points, taskName);
        } catch (Exception e) {
            log.error("[grantPointReward][发放积分奖励失败] userId={}, points={}, taskName={}",
                    userId, points, taskName, e);
            // 这里可以考虑是否要抛出异常，或者记录到失败队列中重试
        }
    }

    /**
     * 更新用户任务记录
     */
    private void updateUserTaskRecord(Long userId, Long taskId, Integer rewardPoints) {
        UserTaskDO userTask = userTaskMapper.selectByUserIdAndTaskId(userId, taskId);
        if (userTask == null) {
            // 创建新记录
            userTask = UserTaskDO.builder()
                    .userId(userId)
                    .taskId(taskId)
                    .completedCount(1)
                    .lastCompletedTime(LocalDateTime.now())
                    .totalRewardPoints(rewardPoints)
                    .build();
            userTaskMapper.insert(userTask);
        } else {
            // 更新记录
            UserTaskDO updateObj = new UserTaskDO();
            updateObj.setId(userTask.getId());
            updateObj.setCompletedCount(userTask.getCompletedCount() + 1);
            updateObj.setLastCompletedTime(LocalDateTime.now());
            updateObj.setTotalRewardPoints(userTask.getTotalRewardPoints() + rewardPoints);
            userTaskMapper.updateById(updateObj);
        }
    }

}
