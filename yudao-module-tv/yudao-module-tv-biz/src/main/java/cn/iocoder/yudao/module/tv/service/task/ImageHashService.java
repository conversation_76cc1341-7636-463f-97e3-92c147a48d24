package cn.iocoder.yudao.module.tv.service.task;

/**
 * 图片哈希服务接口
 *
 * <AUTHOR>
 */
public interface ImageHashService {

    /**
     * 计算图片的感知哈希值
     *
     * @param base64Image base64编码的图片数据
     * @return 图片哈希值
     */
    String calculateImageHash(String base64Image);

    /**
     * 计算两个哈希值的汉明距离（相似度）
     *
     * @param hash1 哈希值1
     * @param hash2 哈希值2
     * @return 汉明距离，值越小表示越相似
     */
    int calculateHammingDistance(String hash1, String hash2);

    /**
     * 判断两个图片是否相似
     *
     * @param hash1 哈希值1
     * @param hash2 哈希值2
     * @param threshold 相似度阈值，默认5
     * @return 是否相似
     */
    boolean isSimilar(String hash1, String hash2, int threshold);

    /**
     * 判断两个图片是否相似（使用默认阈值5）
     *
     * @param hash1 哈希值1
     * @param hash2 哈希值2
     * @return 是否相似
     */
    default boolean isSimilar(String hash1, String hash2) {
        return isSimilar(hash1, hash2, 5);
    }

}
