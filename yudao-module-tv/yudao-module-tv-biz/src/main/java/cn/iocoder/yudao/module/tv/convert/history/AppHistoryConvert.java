package cn.iocoder.yudao.module.tv.convert.history;

import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.module.tv.controller.admin.content.vo.ContentRespVO;
import cn.iocoder.yudao.module.tv.controller.app.history.vo.AppHistoryRespVO;
import cn.iocoder.yudao.module.tv.controller.app.history.vo.AppHistoryVideoRespVO;
import cn.iocoder.yudao.module.tv.controller.app.video.vo.AppVideoRespVO;
import cn.iocoder.yudao.module.tv.dal.dataobject.history.HistoryDO;
import cn.iocoder.yudao.module.tv.dal.dataobject.video.VideoDO;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.factory.Mappers;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

@Mapper
public interface AppHistoryConvert {

    AppHistoryConvert INSTANCE = Mappers.getMapper(AppHistoryConvert.class);

    AppHistoryRespVO convert(HistoryDO bean);

    PageResult<AppHistoryRespVO> convertPage(PageResult<HistoryDO> page);

    /**
     * 将 VideoDO 和 HistoryDO 的信息组合成 AppHistoryVideoRespVO 对象
     *
     * @param video 视频对象
     * @param history 历史记录对象
     * @return 历史观看视频响应对象
     */
    @Mapping(source = "video.id", target = "id")
    @Mapping(source = "video.title", target = "title")
    @Mapping(source = "video.description", target = "description")
    @Mapping(source = "video.coverUrl", target = "coverUrl")
    @Mapping(source = "video.categoryId", target = "categoryId")
    @Mapping(source = "video.playCount", target = "playCount")
    @Mapping(source = "video.likeCount", target = "likeCount")
    @Mapping(source = "video.favoriteCount", target = "favoriteCount")
    @Mapping(source = "video.commentCount", target = "commentCount")
    @Mapping(source = "video.createTime", target = "createTime")
    @Mapping(source = "history.progress", target = "progress")
    @Mapping(source = "history.finished", target = "finished")
    @Mapping(source = "history.updater", target = "updater")
    AppHistoryVideoRespVO convert(ContentRespVO video, HistoryDO history);

    /**
     * 将视频列表和历史记录列表转换为 AppHistoryVideoRespVO 列表
     *
     * @param videoList 视频列表
     * @param historyMap 历史记录映射，以视频ID为键
     * @return 历史观看视频响应列表
     */
    default List<AppHistoryVideoRespVO> convertList(List<ContentRespVO> videoList, Map<Long, HistoryDO> historyMap) {
        List<AppHistoryVideoRespVO> resultList = new ArrayList<>(videoList.size());
        for (ContentRespVO video : videoList) {
            HistoryDO history = historyMap.get(video.getId());
            if (history != null) {
                resultList.add(convert(video, history));
            }
        }
        return resultList;
    }
}