package cn.iocoder.yudao.module.tv.controller.admin.report.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;

import java.time.LocalDateTime;

@Schema(description = "管理后台 - 举报反馈 Response VO")
@Data
@ExcelIgnoreUnannotated
public class ReportRespVO {

    @Schema(description = "主键", requiredMode = Schema.RequiredMode.REQUIRED, example = "1024")
    @ExcelProperty("编号")
    private Long id;

    @Schema(description = "业务类型", requiredMode = Schema.RequiredMode.REQUIRED, example = "content")
    @ExcelProperty("业务类型")
    private String businessType;

    @Schema(description = "业务ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "1024")
    @ExcelProperty("业务ID")
    private Long businessId;

    @Schema(description = "举报类型", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
    @ExcelProperty("举报类型")
    private Integer reportType;

    @Schema(description = "举报类型名称", example = "色情低俗")
    @ExcelProperty("举报类型名称")
    private String reportTypeName;

    @Schema(description = "举报原因描述", example = "内容涉及色情低俗")
    @ExcelProperty("举报原因")
    private String reportReason;

    @Schema(description = "举报截图", example = "[\"http://example.com/image1.jpg\"]")
    private Object reportImages; // 可以是String(JSON)或List<String>

    @Schema(description = "举报人ID", example = "1024")
    @ExcelProperty("举报人ID")
    private Long reporterId;

    @Schema(description = "举报人IP地址", example = "***********")
    @ExcelProperty("举报人IP")
    private String reporterIp;

    @Schema(description = "处理状态", example = "0")
    @ExcelProperty("处理状态")
    private Integer status;

    @Schema(description = "处理状态名称", example = "待处理")
    @ExcelProperty("处理状态名称")
    private String statusName;

    @Schema(description = "处理结果说明", example = "经核实，该内容确实违规，已下架处理")
    @ExcelProperty("处理结果")
    private String handleResult;

    @Schema(description = "处理人ID", example = "1024")
    @ExcelProperty("处理人ID")
    private Long handleUserId;

    @Schema(description = "处理时间")
    @ExcelProperty("处理时间")
    private LocalDateTime handleTime;

    @Schema(description = "创建时间", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("创建时间")
    private LocalDateTime createTime;

}
