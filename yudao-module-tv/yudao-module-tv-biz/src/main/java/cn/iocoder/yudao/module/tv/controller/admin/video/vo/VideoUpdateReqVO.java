package cn.iocoder.yudao.module.tv.controller.admin.video.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;

@Schema(description = "管理后台 - 视频更新 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class VideoUpdateReqVO extends VideoCreateReqVO {

    @Schema(description = "视频编号", required = true, example = "1024")
    @NotNull(message = "视频编号不能为空")
    private Long id;
} 