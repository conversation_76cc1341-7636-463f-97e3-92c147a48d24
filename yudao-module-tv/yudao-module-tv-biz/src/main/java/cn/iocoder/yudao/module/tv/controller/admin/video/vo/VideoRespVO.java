package cn.iocoder.yudao.module.tv.controller.admin.video.vo;

import cn.iocoder.yudao.framework.excel.core.annotations.DictFormat;
import cn.iocoder.yudao.framework.excel.core.convert.DictConvert;
import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;

import static cn.iocoder.yudao.module.tv.enums.DictTypeConstants.*;

@Schema(description = "管理后台 - 视频 Response VO")
@Data
@ExcelIgnoreUnannotated
public class VideoRespVO {

    @Schema(description = "编号", requiredMode = Schema.RequiredMode.REQUIRED, example = "3591")
    @ExcelProperty("编号")
    private Long id;

    @Schema(description = "标题", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("标题")
    private String title;

    @Schema(description = "描述", example = "你猜")
    @ExcelProperty("描述")
    private String description;

    @Schema(description = "封面图片", requiredMode = Schema.RequiredMode.REQUIRED, example = "https://www.iocoder.cn")
    @ExcelProperty("封面图片")
    private String coverUrl;

    @Schema(description = "视频类型", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
    @ExcelProperty(value = "视频类型", converter = DictConvert.class)
    @DictFormat(TV_VIDEO_TYPE) 
    private Integer type;

    @Schema(description = "主视频", example = "https://www.iocoder.cn")
    @ExcelProperty("主视频")
    private String videoUrl;

    @Schema(description = "时长-秒")
    @ExcelProperty("时长-秒")
    private Integer duration;

    @Schema(description = "播放次数", requiredMode = Schema.RequiredMode.REQUIRED, example = "26852")
    @ExcelProperty("播放次数")
    private Integer playCount;

    @Schema(description = "点赞次数", requiredMode = Schema.RequiredMode.REQUIRED, example = "4118")
    @ExcelProperty("点赞次数")
    private Integer likeCount;

    @Schema(description = "收藏次数", requiredMode = Schema.RequiredMode.REQUIRED, example = "12423")
    @ExcelProperty("收藏次数")
    private Integer favoriteCount;

    @Schema(description = "评论次数", requiredMode = Schema.RequiredMode.REQUIRED, example = "1758")
    @ExcelProperty("评论次数")
    private Integer commentCount;

    @Schema(description = "状态", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
    @ExcelProperty(value = "状态", converter = DictConvert.class)
    @DictFormat(TV_VIDEO_STATUS) 
    private Integer status;

    @Schema(description = "分类编号", requiredMode = Schema.RequiredMode.REQUIRED, example = "3358")
    @ExcelProperty("分类编号")
    private Long categoryId;

    @Schema(description = "标签列表")
    @ExcelProperty("标签列表")
    private String tags;

    @Schema(description = "排序")
    @ExcelProperty("排序")
    private Integer sort;

    @Schema(description = "付费视频", requiredMode = Schema.RequiredMode.REQUIRED, example = "18766")
    @ExcelProperty(value = "付费视频", converter = DictConvert.class)
    @DictFormat(TV_VIDEO_PAID) 
    private Integer isPaid;

    @Schema(description = "视频价格", requiredMode = Schema.RequiredMode.REQUIRED, example = "13535")
    @ExcelProperty("视频价格")
    private BigDecimal price;

    @Schema(description = "折扣价格", example = "388")
    @ExcelProperty("折扣价格")
    private BigDecimal discountPrice;

    @Schema(description = "折扣开始时间")
    @ExcelProperty("折扣开始时间")
    private LocalDateTime discountStartTime;

    @Schema(description = "折扣结束时间")
    @ExcelProperty("折扣结束时间")
    private LocalDateTime discountEndTime;

    @Schema(description = "创建时间", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("创建时间")
    private LocalDateTime createTime;

}