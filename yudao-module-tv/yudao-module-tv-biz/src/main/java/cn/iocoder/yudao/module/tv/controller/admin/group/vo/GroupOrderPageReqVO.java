package cn.iocoder.yudao.module.tv.controller.admin.group.vo;

import cn.iocoder.yudao.framework.common.pojo.PageParam;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import org.springframework.format.annotation.DateTimeFormat;

import java.time.LocalDateTime;

import static cn.iocoder.yudao.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;

@Schema(description = "管理后台 - 拼团参与记录分页 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class GroupOrderPageReqVO extends PageParam {

    @Schema(description = "拼团ID", example = "1024")
    private Long groupId;

    @Schema(description = "用户ID", example = "2048")
    private Long userId;

    @Schema(description = "支付订单号", example = "ORDER123456")
    private String orderNo;

    @Schema(description = "参与时间")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime[] joinTime;
}
