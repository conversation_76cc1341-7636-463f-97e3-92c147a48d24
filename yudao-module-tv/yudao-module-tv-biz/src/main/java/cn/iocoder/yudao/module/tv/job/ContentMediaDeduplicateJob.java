package cn.iocoder.yudao.module.tv.job;

import cn.hutool.core.util.StrUtil;
import cn.iocoder.yudao.framework.quartz.core.handler.JobHandler;
import cn.iocoder.yudao.framework.tenant.core.job.TenantJob;
import cn.iocoder.yudao.module.tv.dal.dataobject.content.ContentDO;
import cn.iocoder.yudao.module.tv.dal.dataobject.content.ContentMediaDO;
import cn.iocoder.yudao.module.tv.dal.mysql.content.ContentMapper;
import cn.iocoder.yudao.module.tv.dal.mysql.content.ContentMediaMapper;
import cn.iocoder.yudao.module.tv.util.FFmpegVideoUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.io.File;
import java.io.FileInputStream;
import java.security.MessageDigest;
import java.util.*;
import java.util.stream.Collectors;

/**
 * tv_content_media内容去重定时任务
 */
@Slf4j
@Component
public class ContentMediaDeduplicateJob implements JobHandler {

    @Resource
    private ContentMediaMapper contentMediaMapper;
    @Resource
    private ContentMapper contentMapper;


    @Override
    @TenantJob
    public String execute(String param) throws Exception {
        String base_dir = param;
        log.info("开始执行tv_content_media内容去重定时任务");
        int totalDeleted = 0;
        int fileDeleted = 0;
        try {
            Map<String, List<ContentMediaDO>> groupMap = new HashMap<>();

            List<ContentDO> contentDOList = contentMapper.selectList(ContentDO::getType, 5);
            col: for (ContentDO contentDO : contentDOList) {
                Long contentDOId = contentDO.getId();
                List<ContentMediaDO> allMedia = contentMediaMapper.selectList(ContentMediaDO::getContentId,contentDOId);
                // 1. 计算每个文件的hash
                Map<Long, String> idToHash = new HashMap<>();
                for (ContentMediaDO media : allMedia) {
                    String absPath = getAbsolutePath(base_dir,media.getUrl());
                    String hash = calcFileHash(absPath);
                    if(StrUtil.isNotBlank(hash))    idToHash.put(media.getId(), hash);
                }
                // 2. 按 contentId + hash 分组
                for (ContentMediaDO media : allMedia) {
                    String key = idToHash.get(media.getId());
                    groupMap.computeIfAbsent(key, k -> new ArrayList<>()).add(media);
                }
                // 3. 对每组重复，保留预览，删除多余
                for (Map.Entry<String, List<ContentMediaDO>> entry : groupMap.entrySet()) {
                    String hash = entry.getKey();
                    List<ContentMediaDO> group = entry.getValue();
                    if (group.size() > 1) {
                        // 优先保留 isPreview = true
                        ContentMediaDO keep = group.stream()
                                .filter(ContentMediaDO::getIsPreview)
                                .findFirst()
                                .orElse(group.get(0));
                        log.info("[ContentMediaDeduplicateKeep] hash =[{}] , url = [{}]",hash ,keep.getUrl() );
                        for (ContentMediaDO m : group) {
                            Long id = m.getId();
                            if (!Objects.equals(id, keep.getId())) {
                                if (keep.getIsPreview()) {
                                    log.info("[ContentMediaDeduplicateSkip] hash =[{}] , url = [{}]", hash ,m.getUrl());
                                    continue;
                                }

                                // 4. 删除数据库记录
                                String absolutePath = getAbsolutePath(base_dir, m.getUrl());
                                log.info("[ContentMediaDeduplicateDelete] id =[{}] , url = [{}]", id ,absolutePath);
                                FFmpegVideoUtils.markFileAsProcessed(new File(absolutePath));
                                contentMediaMapper.deleteById(id);
                                totalDeleted++;
                                fileDeleted++;
                                if(totalDeleted > 1000){
                                    break col;
                                }
                                //                            // 5. 检查是否还有其他引用 ， 这一步不要是多余的。

//                                boolean stillReferenced = allMedia.stream()
//                                        .anyMatch(other -> !Objects.equals(other.getId(), m.getId())
//                                                && Objects.equals(other.getUrl(), m.getUrl()));
//                                if (!stillReferenced) {
//                                    if (deleteFile(getAbsolutePath(base_dir,m.getUrl()))) {
//                                        fileDeleted++;
//                                    }
//                                }
                            }
                        }
                    }
                }
            }


            return "tv_content_media去重完成，删除记录数：" + totalDeleted + "，删除文件数：" + fileDeleted;
        } catch (Exception e) {
            log.error("tv_content_media去重定时任务执行失败", e);
            return "tv_content_media去重定时任务执行失败：" + e.getMessage();
        }
    }

    // 计算文件MD5
    private String calcFileHash(String filePath) {
        try (FileInputStream fis = new FileInputStream(filePath)) {
            MessageDigest md = MessageDigest.getInstance("SHA-256");
            byte[] buffer = new byte[4096];
            int len;
            while ((len = fis.read(buffer)) != -1) {
                md.update(buffer, 0, len);
            }
            byte[] digest = md.digest();
            StringBuilder sb = new StringBuilder();
            for (byte b : digest) sb.append(String.format("%02x", b));
            return sb.toString();
        } catch (Exception e) {
            // 文件不存在或读取失败，返回特殊hash
//            return "FILE_NOT_FOUND";
            return null;
        }
    }

    // 获取文件绝对路径
    private String getAbsolutePath(String base_dir,String relativePath) {
        return base_dir + StrUtil.removePrefix(relativePath,"/");
    }

    // 删除物理文件
    private boolean deleteFile(String filePath) {
        File file = new File(filePath);
        if (file.exists()) {
            return file.delete();
        }
        return false;
    }
} 