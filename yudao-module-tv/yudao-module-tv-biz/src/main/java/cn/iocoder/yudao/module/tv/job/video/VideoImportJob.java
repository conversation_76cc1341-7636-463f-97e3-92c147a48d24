package cn.iocoder.yudao.module.tv.job.video;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.io.FileUtil;
import cn.hutool.core.util.ArrayUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.iocoder.yudao.framework.quartz.core.handler.JobHandler;
import cn.iocoder.yudao.framework.tenant.core.util.TenantUtils;
import cn.iocoder.yudao.module.infra.api.file.FileApi;
import cn.iocoder.yudao.module.infra.api.file.dto.FileConfigRespDTO;
import cn.iocoder.yudao.module.tv.controller.admin.video.vo.VideoSaveReqVO;
import cn.iocoder.yudao.module.tv.dal.dataobject.video.VideoDO;
import cn.iocoder.yudao.module.tv.dal.mysql.video.VideoMapper;
import cn.iocoder.yudao.module.tv.enums.video.VideoStatusEnum;
import cn.iocoder.yudao.module.tv.service.category.CategoryService;
import cn.iocoder.yudao.module.tv.service.video.VideoService;
import cn.iocoder.yudao.module.tv.util.FFmpegVideoUtils;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.io.File;
import java.util.ArrayList;
import java.util.List;

/**
 * 视频导入 Job
 *
 * 扫描 目录，将视频文件导入到系统中
 *
 * @
 */
@Component
@Slf4j
public class VideoImportJob implements JobHandler {
    /**
     * 失败提示
     */
    private static final String FAIL_RES = "job参数有问题";
    /**
     * 视频类型 - 单个视频
     */
    private static final Integer VIDEO_TYPE_SINGLE = 0;

    /**
     * 描述文件名
     */
    private static final String DESC_FILE_NAME = "msg.text";

    @Resource
    private VideoService videoService;

    @Resource
    private CategoryService categoryService;

    @Resource
    private VideoMapper videoMapper;

    @Resource
    private FileApi fileApi;

    /**
     * 任务上下文，封装任务执行所需的参数
     * 每次执行任务时创建新的实例，避免并发冲突
     */
    @Data
    private static class JobContext {
        private Long fileId;
        private String basePath;
        private String domain;
        private Long categoryId;
        private Long durationRelease;

        public JobContext(Long fileId, String basePath, String domain, Long categoryId, Long durationRelease) {
            this.fileId = fileId;
            this.basePath = basePath;
            this.domain = domain;
            this.categoryId = categoryId;
            this.durationRelease = durationRelease;
        }

        /**
         * 装饰url的路径情况，用于更好的入库
         */
        public String decorateUrl(File targetFile) {
            return StrUtil.replace(targetFile.getAbsolutePath(), basePath, domain);
        }
    }

    /**
     *
     * @param param 参数：  租户id:文件配置id:分类id:相对路径:
     * @return
     */
    @Override
    public synchronized String execute(String param) { // 添加锁，防止
        long[] longs = StrUtil.splitToLong(param, ":");
        Long tenantId = ArrayUtil.get(longs, 0);
        Long fileId = ArrayUtil.get(longs, 1);
        Long categoryId = ArrayUtil.get(longs, 2);
        Long relative = ArrayUtil.get(longs, 3); // 为0就是相对路径。
        Long durationRelease = ArrayUtil.get(longs, 4); // 设置发布类型的视频时长
        if (ObjectUtil.hasNull(tenantId, fileId, categoryId, durationRelease)) {
            return FAIL_RES;
        }

        // 获取文件配置
        FileConfigRespDTO fileConfig = fileApi.getFileConfig(fileId);
        if (fileConfig == null) {
            return FAIL_RES;
        }

        // 创建任务上下文，每次执行任务时创建新的实例，避免并发冲突
        String basePath = fileConfig.getBasePath();
        String domain = relative == 0 ? "" : fileConfig.getDomain();
        JobContext context = new JobContext(fileId,basePath, domain, categoryId, durationRelease);

        // 开始执行，将上下文作为参数传递
        return TenantUtils.execute(tenantId, () -> executeInterl(context));
    }

    /**
     * 执行导入任务
     *
     * @param context 任务上下文
     * @return 执行结果
     */
    public String executeInterl(JobContext context) {
        log.info("[execute][开始执行视频导入任务]");

        try {
            // 分类校验，得在这里校验获得参数。
            if (categoryService.getCategory(context.getCategoryId()) == null) {
                return FAIL_RES;
            }
            // 检查基础目录是否存在
            if (!FileUtil.exist(context.getBasePath())) {
                log.error("[execute][基础目录({})不存在]", context.getBasePath());
                return "基础目录不存在";
            }

            // 获取所有频道目录
            File baseDir = new File(context.getBasePath());
            if (!baseDir.exists() || !baseDir.isDirectory()) {
                log.info("[execute][基础目录({})]不存在或不是目录", context.getBasePath());
                return "基础目录不存在或不是目录";
            }

            List<File> channelDirs = new ArrayList<>();
            File[] files = baseDir.listFiles();
            if (files != null) {
                for (File file : files) {
                    if (file.isDirectory()) {
                        channelDirs.add(file);
                    }
                }
            }

            if (CollUtil.isEmpty(channelDirs)) {
                log.info("[execute][没有找到频道目录]");
                return "没有找到频道目录";
            }

            // 使用计数器跟踪导入数量
            int totalImported = 0;

            try {
                // 处理每个频道目录
                for (File channelDir : channelDirs) {
                    String channelName = channelDir.getName();
                    log.info("[execute][开始处理频道({})]", channelName);

                    // 获取所有日期目录
                    List<File> dateDirs = new ArrayList<>();
                    File[] dateFiles = channelDir.listFiles();
                    if (dateFiles != null) {
                        for (File file : dateFiles) {
                            if (file.isDirectory()) {
                                dateDirs.add(file);
                            }
                        }
                    }

                    if (CollUtil.isEmpty(dateDirs)) {
                        log.info("[execute][频道({})下没有找到日期目录]", channelName);
                        continue;
                    }

                    // 处理每个日期目录
                    for (File dateDir : dateDirs) {
                        String dateName = dateDir.getName();
                        log.info("[execute][开始处理日期({})]", dateName);

                        // 获取所有媒体组目录
                        List<File> mediaDirs = new ArrayList<>();
                        File[] mediaFiles = dateDir.listFiles();
                        if (mediaFiles != null) {
                            for (File file : mediaFiles) {
                                if (file.isDirectory()) {
                                    mediaDirs.add(file);
                                }
                            }
                        }

                        if (CollUtil.isEmpty(mediaDirs)) {
                            log.info("[execute][日期({})下没有找到媒体组目录]", dateName);
                            continue;
                        }

                        // 处理每个媒体组目录
                        for (File mediaDir : mediaDirs) {
                            final String mediaGroupName = mediaDir.getName();
                            final File mediaDirFinal = mediaDir;
                            final String channelNameFinal = channelName;

                            // 直接同步处理媒体组
                            try {
                                log.info("[execute][开始处理媒体组({})]", mediaGroupName);
                                int imported = processMediaGroup(context, mediaDirFinal, context.getCategoryId(), channelNameFinal);
                                totalImported += imported;
                            } catch (OutOfMemoryError e) {
                                // 特别处理OOM错误
                                log.error("[execute][处理媒体组({})内存溢出，跳过此媒体组]", mediaGroupName, e);
                                System.gc(); // 尝试回收内存
                            } catch (Exception e) {
                                log.error("[execute][处理媒体组({})异常]", mediaGroupName, e);
                            }
                        }
                    }
                }

                // 所有媒体组已同步处理完成
            } finally {
                // 无需关闭线程池，因为已经改为同步执行
            }

            log.info("[execute][视频导入任务执行完成，共导入 {} 个视频]", totalImported);
            return String.format("导入视频 %d 个", totalImported);
        } catch (Throwable e) {
            // 捕获所有异常，包括Error
            log.error("[execute][视频导入任务执行异常]", e);
            return "导入任务执行异常: " + e.getMessage();
        }
    }


    /**
     * 处理媒体组目录
     *
     * @param context 任务上下文
     * @param mediaDir 媒体组目录
     * @param categoryId 分类ID
     * @param channelName 频道名称
     * @return 导入的视频数量
     */
    private int processMediaGroup(JobContext context, File mediaDir, Long categoryId, String channelName) {
        // 读取描述文件
        File descFile = new File(mediaDir, DESC_FILE_NAME);
        String description = "";
        if (descFile.exists()) {
            description = FileUtil.readUtf8String(descFile);
        }

        // 获取所有图片文件和视频信息
        List<File> imageFiles = new ArrayList<>();
        List<VideoInfo> videoInfos = new ArrayList<>();

        File[] files = mediaDir.listFiles();
        if (files != null) {
            for (File file : files) {
                if (file.isFile()) {
                    String fileName = file.getName().toLowerCase();
                    if (fileName.endsWith(".jpg")) {
                        // 检查图片文件是否完整
                        if (FFmpegVideoUtils.isImageFileComplete(file)) {
                            imageFiles.add(file);
                        } else {
                            log.warn("[processMediaGroup][图片文件({})不完整，删除结果({})]", file.getName() , FileUtil.del(file));
                        }
                    } else if (fileName.endsWith(".mp4")) {
                        if(StrUtil.isBlank(description)){
                            // 将MP4文件标记为已处理（设置为1字节）, 舍弃不要这个mp4文件啦。
                            log.info("[processMediaGroup][MP4({}) 描述为空,舍弃不要]", file.getName());
                            FFmpegVideoUtils.markFileAsProcessed(file);
                        }
                        // 1. 先判断MP4是否已经处理过（文件大小为1字节且存在对应的M3U8）
                        if (FFmpegVideoUtils.isFileProcessed(file)) {
                            // 检查是否存在对应的M3U8文件
                            String baseName = file.getName().substring(0, file.getName().lastIndexOf('.'));
                            File m3u8Dir = new File(file.getParent(), baseName);
                            File m3u8File = new File(m3u8Dir, "index.m3u8");

                            if (m3u8File.exists() && m3u8File.length() > 0) {
                                log.info("[processMediaGroup][MP4({})已处理过，直接使用M3U8文件: {}]", file.getName(), m3u8File.getName());

                                // 检查M3U8视频是否已存在数据库中
                                String m3u8VideoUrl = context.decorateUrl(m3u8File);
                                if (isVideoPathExists(m3u8VideoUrl)) {
                                    log.info("[processMediaGroup][M3U8视频({})已存在数据库，跳过]", m3u8File.getName());
                                    continue;
                                }

                                // 查找对应的封面文件和获取时长
                                String m3u8BaseName = m3u8File.getParentFile().getName();
                                String m3u8ParentDir = m3u8File.getParentFile().getParent();
                                File coverFile = new File(m3u8ParentDir, m3u8BaseName + ".jpg");
                                if (!coverFile.exists() || coverFile.length() == 0) {
                                    coverFile = null; // 封面文件不存在
                                }
                                int duration = getDurationFromProcessedVideo(file, m3u8File);

                                // 创建VideoInfo对象并添加到列表中
                                VideoInfo videoInfo = new VideoInfo(m3u8File, coverFile, duration);
                                videoInfos.add(videoInfo);
                                continue;
                            } else {
                                log.warn("[processMediaGroup][MP4({})已标记为处理过，但M3U8文件不存在，重新处理]", file.getName());
                                // 重置MP4文件，重新处理
                                // 这里不重置，让后续逻辑重新处理
                            }
                        }

                        // 2. 检查视频文件是否完整可播放
                        if (!FFmpegVideoUtils.isVideoFilePlayable(file)) {
                            log.warn("[processMediaGroup][视频文件({})不完整或不可播放，删除结果({})]", file.getName(), FileUtil.del(file));
                            continue;
                        }

                        // 3. 提前获取视频时长
                        int duration = FFmpegVideoUtils.getDuration(file);
                        if (duration <= 0) {
                            log.warn("[processMediaGroup][视频({})时长获取失败，跳过处理]", file.getName());
                            continue;
                        }
                        log.info("[processMediaGroup][视频({})时长: {}秒]", file.getName(), duration);

                        // 4. 提取第一帧作为封面图
                        File coverImageFile = extractCoverImage(file);
                        if (coverImageFile == null) {
                            log.warn("[processMediaGroup][视频({})封面提取失败]", file.getName());
                        } else {
                            log.info("[processMediaGroup][视频({})封面提取成功: {}]", file.getName(), coverImageFile.getName());
                        }

                        // 5. 将MP4转换为M3U8格式
                        log.info("[processMediaGroup][开始转换MP4为M3U8: {}]", file.getName());
                        File m3u8File = FFmpegVideoUtils.convertMp4ToM3u8(file);

                        if (m3u8File != null) {
                            log.info("[processMediaGroup][MP4转M3U8成功: {} -> {}]", file.getName(), m3u8File.getName());

                            // 6. 转换成功后，将MP4文件标记为已处理（设置为1字节）
                            boolean marked = FFmpegVideoUtils.markFileAsProcessed(file);
                            if (marked) {
                                log.info("[processMediaGroup][MP4文件({})已标记为已处理]", file.getName());
                            } else {
                                log.warn("[processMediaGroup][MP4文件({})标记失败]", file.getName());
                            }

                            // 检查M3U8视频是否已存在数据库中
                            String m3u8VideoUrl = context.decorateUrl(m3u8File);
                            if (isVideoPathExists(m3u8VideoUrl)) {
                                log.info("[processMediaGroup][M3U8视频({})已存在数据库，跳过]", m3u8File.getName());
                                continue;
                            }

                            // 创建VideoInfo对象并添加到列表中
                            VideoInfo videoInfo = new VideoInfo(m3u8File, coverImageFile, duration);
                            videoInfos.add(videoInfo);
                        } else {
                            log.warn("[processMediaGroup][MP4转M3U8失败，使用原MP4文件: {}]", file.getName());

                            // 如果转换失败，检查原MP4是否已存在数据库中
                            String videoUrl = context.decorateUrl(file);
                            if (isVideoPathExists(videoUrl)) {
                                log.info("[processMediaGroup][原MP4视频({})已存在数据库，跳过]", file.getName());
                                continue;
                            }

                            // 转换失败时仍使用原MP4文件
                            VideoInfo videoInfo = new VideoInfo(file, coverImageFile, duration);
                            videoInfos.add(videoInfo);
                        }
                    }
                }
            }
        }

        if (CollUtil.isEmpty(videoInfos)) {
            log.info("[processMediaGroup][媒体组({})下没有找到视频文件]", mediaDir.getName());
            return 0;
        }

        // 处理每个视频信息
        int importedCount = 0;
        for (VideoInfo videoInfo : videoInfos) {
            try {
                File videoFile = videoInfo.getVideoFile();
                File coverFile = videoInfo.getCoverFile();
                int duration = videoInfo.getDuration();

                String videoUrl = context.decorateUrl(videoFile);

                // 导入视频（现在可能是M3U8文件），使用已获取的时长和封面
                importVideo(context, videoUrl, videoFile, coverFile, duration, importedCount + description, categoryId, channelName);
                importedCount++;
            } catch (Exception e) {
                log.error("[processMediaGroup][处理视频({})异常]", videoInfo.getVideoFile().getName(), e);
            }
        }

        return importedCount;
    }

    /**
     * 检查视频是否已存在
     *
     * @param videoFileName 视频文件名
     * @return 是否已存在
     */
    private boolean isVideoExists(String videoFileName) {
        // 根据视频文件名查询是否已存在记录
        // 这里使用简单的标题匹配，实际可能需要更复杂的逻辑
        return videoMapper.selectCount(VideoDO::getTitle, videoFileName) > 0;
    }

    /**
     * 检查视频是否已存在
     *
     * @param videoUrl 视频文件名
     * @return 是否已存在
     */
    private boolean isVideoPathExists(String videoUrl) {
        // 根据视频文件名查询是否已存在记录
        // 这里使用简单的标题匹配，实际可能需要更复杂的逻辑
        return videoMapper.selectCount(VideoDO::getVideoUrl, videoUrl) > 0;
    }

    /**
     * 从已处理的视频获取时长
     *
     * @param originalMp4File 原始MP4文件（1字节）
     * @param m3u8File M3U8文件
     * @return 视频时长（秒）
     */
    private int getDurationFromProcessedVideo(File originalMp4File, File m3u8File) {
        try {
            // 尝试从M3U8文件获取时长
            int duration = FFmpegVideoUtils.getDuration(m3u8File);
            if (duration > 0) {
                log.debug("[getDurationFromProcessedVideo][从M3U8文件获取时长: {}秒]", duration);
                return duration;
            }

            // 如果M3U8获取失败，尝试从原始MP4文件名推断（如果有缓存信息）
            // 这里可以扩展为从其他地方获取时长信息
            log.warn("[getDurationFromProcessedVideo][无法获取视频时长，使用默认值0秒]");
            return 0; // 默认值

        } catch (Exception e) {
            log.error("[getDurationFromProcessedVideo][获取视频时长异常]", e);
            return 0; // 默认值
        }
    }



    /**
     * 提取视频第一帧作为封面图
     *
     * @param videoFile 视频文件
     * @return 封面图文件，失败返回null
     */
    private File extractCoverImage(File videoFile) {
        try {
            log.info("[extractCoverImage][开始提取视频封面: {}]", videoFile.getName());

            // 生成封面文件名
            String videoFileName = videoFile.getName();
            String baseName = videoFileName.substring(0, videoFileName.lastIndexOf('.'));
            String coverFileName = baseName + ".jpg";
            File coverFile = new File(videoFile.getParent(), coverFileName);

            // 如果封面文件已存在，直接返回
            if (coverFile.exists() && coverFile.length() > 0) {
                log.info("[extractCoverImage][封面文件已存在: {}]", coverFile.getName());
                return coverFile;
            }

            // 使用FFmpeg提取第一帧
            File extractedFrame = FFmpegVideoUtils.extractFirstFrame(videoFile, null);
            if (extractedFrame != null) {
                try {
                    // 复制到目标位置
                    FileUtil.copyFile(extractedFrame, coverFile);
                    log.info("[extractCoverImage][封面提取成功: {} -> {}]", videoFile.getName(), coverFile.getName());
                    return coverFile;
                } finally {
                    // 删除临时文件
                    if (extractedFrame.exists()) {
                        extractedFrame.delete();
                    }
                }
            } else {
                log.warn("[extractCoverImage][封面提取失败: {}]", videoFile.getName());
                return null;
            }

        } catch (Exception e) {
            log.error("[extractCoverImage][封面提取异常: {}]", videoFile.getName(), e);
            return null;
        }
    }

    /**
     * 导入视频
     *
     * @param context 任务上下文
     * @param videoUrl 视频url
     * @param videoFile 视频文件（可能是M3U8文件）
     * @param coverFile 封面文件
     * @param duration 视频时长（秒）
     * @param description 描述
     * @param categoryId 分类ID
     * @param channelName 频道名称
     */
    private void importVideo(JobContext context, String videoUrl, File videoFile, File coverFile, int duration, String description, Long categoryId, String channelName) {
        try {
            // 1. 检查视频文件是否有效
            if (!videoFile.exists() || videoFile.length() == 0) {
                log.error("[importVideo][视频文件({})不存在或大小为0]", videoFile.getName());
                return;
            }

            // 判断是否是M3U8文件
            boolean isM3u8 = videoFile.getName().toLowerCase().endsWith(".m3u8");

            // 2. 处理封面文件
            String coverUrl = null;
            if (coverFile != null && coverFile.exists() && coverFile.length() > 0) {
                // 使用已提取的封面文件
                coverUrl = context.decorateUrl(coverFile);
                log.info("[importVideo][使用已提取的封面文件: {}]", coverFile.getName());
            } else {
                log.warn("[importVideo][视频({})没有找到封面文件]", videoFile.getName());
            }

            // 3. 使用传入的视频时长
            log.info("[importVideo][视频({}) 时长为 {} 秒，视频类型: {}]", videoFile.getName(), duration, isM3u8 ? "M3U8" : "MP4");

            // 4. 创建视频记录
            VideoSaveReqVO createReqVO = new VideoSaveReqVO();
            createReqVO.setConfigId(context.getFileId());
            createReqVO.setTitle(StrUtil.maxLength(description, 100/4));
            createReqVO.setDescription(description);
            createReqVO.setCoverUrl(coverUrl);
            createReqVO.setType(VIDEO_TYPE_SINGLE);
            createReqVO.setVideoUrl(videoUrl);
            createReqVO.setDuration(duration);
            createReqVO.setCategoryId(categoryId);
            createReqVO.setTags(channelName);
            if (duration > context.getDurationRelease()) {
                createReqVO.setStatus(VideoStatusEnum.RELEASE.getStatus());
            } else {
                createReqVO.setStatus(VideoStatusEnum.DRAFT.getStatus());
            }

            // 5. 保存视频
            videoService.createVideo(createReqVO);

            log.info("[importVideo][视频({})导入成功]", videoFile.getName());
        } catch (OutOfMemoryError e) {
            // 特别处理OOM错误
            log.error("[importVideo][视频({})导入过程中内存溢出]", videoFile.getName(), e);
            System.gc(); // 尝试回收内存
        } catch (Exception e) {
            log.error("[importVideo][视频({})导入失败]", videoFile.getName(), e);
        }
    }
}
