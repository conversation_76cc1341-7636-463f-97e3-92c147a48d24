package cn.iocoder.yudao.module.tv.dal.dataobject.task;

import cn.iocoder.yudao.framework.tenant.core.db.TenantBaseDO;
import com.baomidou.mybatisplus.annotation.*;
import lombok.*;

import java.time.LocalDateTime;

/**
 * 任务定义 DO
 *
 * <AUTHOR>
 */
@TableName("tv_task")
@KeySequence("tv_task_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class TaskDO extends TenantBaseDO {

    /**
     * 主键
     */
    @TableId
    private Long id;

    /**
     * 任务名称
     */
    private String name;

    /**
     * 任务描述
     */
    private String description;

    /**
     * 任务类型：1-分享任务，2-短视频任务
     */
    private Integer type;

    /**
     * 奖励积分
     */
    private Integer rewardPoints;

    /**
     * 每日完成次数限制
     */
    private Integer dailyLimit;

    /**
     * 总完成次数限制，NULL表示无限制
     */
    private Integer totalLimit;

    /**
     * 任务状态：0-禁用，1-启用
     */
    private Integer status;

    /**
     * 任务开始时间
     */
    private LocalDateTime startTime;

    /**
     * 任务结束时间
     */
    private LocalDateTime endTime;

    /**
     * 排序值，越小越靠前
     */
    private Integer sort;

}
