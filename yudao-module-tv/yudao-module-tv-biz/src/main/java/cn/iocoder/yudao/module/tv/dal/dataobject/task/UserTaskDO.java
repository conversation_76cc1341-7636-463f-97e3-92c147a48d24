package cn.iocoder.yudao.module.tv.dal.dataobject.task;

import cn.iocoder.yudao.framework.tenant.core.db.TenantBaseDO;
import com.baomidou.mybatisplus.annotation.*;
import lombok.*;

import java.time.LocalDateTime;

/**
 * 用户任务记录 DO
 *
 * <AUTHOR>
 */
@TableName("tv_user_task")
@KeySequence("tv_user_task_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class UserTaskDO extends TenantBaseDO {

    /**
     * 主键
     */
    @TableId
    private Long id;

    /**
     * 用户ID
     */
    private Long userId;

    /**
     * 任务ID
     */
    private Long taskId;

    /**
     * 已完成次数
     */
    private Integer completedCount;

    /**
     * 最后完成时间
     */
    private LocalDateTime lastCompletedTime;

    /**
     * 总获得积分
     */
    private Integer totalRewardPoints;

}
