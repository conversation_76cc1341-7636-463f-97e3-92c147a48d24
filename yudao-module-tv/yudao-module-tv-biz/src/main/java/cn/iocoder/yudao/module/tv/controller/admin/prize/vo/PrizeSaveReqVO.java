package cn.iocoder.yudao.module.tv.controller.admin.prize.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.math.BigDecimal;

@Schema(description = "管理后台 - 奖品新增/修改 Request VO")
@Data
public class PrizeSaveReqVO {

    @Schema(description = "奖品ID", example = "1024")
    private Long id;

    @Schema(description = "奖品名称", requiredMode = Schema.RequiredMode.REQUIRED, example = "iPhone 15")
    @NotBlank(message = "奖品名称不能为空")
    private String name;

    @Schema(description = "奖品描述", example = "最新款iPhone手机")
    private String description;

    @Schema(description = "奖品图片", example = "https://example.com/prize.jpg")
    private String imageUrl;

    @Schema(description = "奖品类型", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
    @NotNull(message = "奖品类型不能为空")
    private Integer type;

    @Schema(description = "奖品价值", example = "6999.00")
    private BigDecimal value;

    @Schema(description = "库存数量，-1表示无限制", example = "100")
    private Integer stock;

    @Schema(description = "状态", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
    @NotNull(message = "状态不能为空")
    private Integer status;

    @Schema(description = "排序", example = "1")
    private Integer sort;
}
