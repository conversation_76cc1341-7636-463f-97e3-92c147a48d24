package cn.iocoder.yudao.module.tv.dal.dataobject.group;

import cn.iocoder.yudao.framework.mybatis.core.dataobject.BaseDO;
import com.baomidou.mybatisplus.annotation.KeySequence;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.ToString;

import java.time.LocalDateTime;

/**
 * 用户抽奖统计 DO
 *
 * <AUTHOR>
 */
@TableName("tv_user_lottery_stat")
@KeySequence("tv_user_lottery_stat_seq")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class UserLotteryStatDO extends BaseDO {

    /**
     * 主键ID
     */
    @TableId
    private Long id;

    /**
     * 用户ID
     */
    private Long userId;

    /**
     * 累计参与次数
     */
    private Integer totalJoin;

    /**
     * 累计中奖次数
     */
    private Integer totalWin;

    /**
     * 连续未中奖次数
     */
    private Integer failCount;

    /**
     * 当前权重
     */
    private Integer weight;

    /**
     * 上次参与时间
     */
    private LocalDateTime lastJoinTime;

    /**
     * 上次中奖时间
     */
    private LocalDateTime lastWinTime;
}
