package cn.iocoder.yudao.module.tv.controller.app.content.vo;

import cn.iocoder.yudao.framework.common.pojo.PageParam;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import org.springframework.format.annotation.DateTimeFormat;

import java.math.BigDecimal;
import java.time.LocalDateTime;

import static cn.iocoder.yudao.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;

@Schema(description = "用户 APP - 内容搜索 Request VO")
@Data
@ToString(callSuper = true)
public class AppCollectionItemRelationReqVO  {

    @Schema(description = "集合内容id", example = "1")
    private Long collectionContentId;
    @Schema(description = "子集内容id", example = "2")
    private Long itemContentId;

}
