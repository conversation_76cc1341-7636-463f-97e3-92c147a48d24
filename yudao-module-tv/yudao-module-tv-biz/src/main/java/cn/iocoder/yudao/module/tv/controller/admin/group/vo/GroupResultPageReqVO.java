package cn.iocoder.yudao.module.tv.controller.admin.group.vo;

import cn.iocoder.yudao.framework.common.pojo.PageParam;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import org.springframework.format.annotation.DateTimeFormat;

import java.time.LocalDateTime;

import static cn.iocoder.yudao.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;

@Schema(description = "管理后台 - 拼团开奖记录分页 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class GroupResultPageReqVO extends PageParam {

    @Schema(description = "拼团ID", example = "1024")
    private Long groupId;

    @Schema(description = "中奖用户ID", example = "2048")
    private Long winnerId;

    @Schema(description = "奖品ID", example = "3072")
    private Long prizeId;

    @Schema(description = "算法类型", example = "1")
    private Integer algorithmType;

    @Schema(description = "创建时间")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime[] createTime;
}
