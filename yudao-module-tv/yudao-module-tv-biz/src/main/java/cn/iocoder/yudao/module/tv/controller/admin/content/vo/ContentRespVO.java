package cn.iocoder.yudao.module.tv.controller.admin.content.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;
import java.util.*;
import java.math.BigDecimal;
import org.springframework.format.annotation.DateTimeFormat;
import java.time.LocalDateTime;
import com.alibaba.excel.annotation.*;

@Schema(description = "管理后台 - 内容主体 Response VO")
@Data
@ExcelIgnoreUnannotated
public class ContentRespVO {

    @Schema(description = "编号", requiredMode = Schema.RequiredMode.REQUIRED, example = "15635")
    @ExcelProperty("编号")
    private Long id;

    @Schema(description = "标题", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("标题")
    private String title;

    @Schema(description = "描述", example = "随便")
    @ExcelProperty("描述")
    private String description;

    @Schema(description = "封面图片", example = "随便")
    @ExcelProperty("封面图片")
    private String coverUrl;

    @Schema(description = "视频类型：0-单个视频，1-多个视频，2-图文混合,3-图文,4-合集", requiredMode = Schema.RequiredMode.REQUIRED, example = "2")
    @ExcelProperty("视频类型：0-单个视频，1-多个视频，2-图文混合,3-图文,4-合集")
    private Integer type;

    @Schema(description = "播放次数", requiredMode = Schema.RequiredMode.REQUIRED, example = "31655")
    @ExcelProperty("播放次数")
    private Integer playCount;

    @Schema(description = "点赞次数", requiredMode = Schema.RequiredMode.REQUIRED, example = "21904")
    @ExcelProperty("点赞次数")
    private Integer likeCount;

    @Schema(description = "收藏次数", requiredMode = Schema.RequiredMode.REQUIRED, example = "17998")
    @ExcelProperty("收藏次数")
    private Integer favoriteCount;

    @Schema(description = "评论次数", requiredMode = Schema.RequiredMode.REQUIRED, example = "12263")
    @ExcelProperty("评论次数")
    private Integer commentCount;

    @Schema(description = "推荐系统权重因子")
    @ExcelProperty("推荐系统权重因子")
    private Integer recommendWeight;

    @Schema(description = "内容质量评分（平台内部使用）")
    @ExcelProperty("内容质量评分（平台内部使用）")
    private Integer qualityScore;

    @Schema(description = "状态：0-草稿，1-发布，2-下架", requiredMode = Schema.RequiredMode.REQUIRED, example = "2")
    @ExcelProperty("状态：0-草稿，1-发布，2-下架")
    private Integer status;

    @Schema(description = "分类编号", requiredMode = Schema.RequiredMode.REQUIRED, example = "3708")
    @ExcelProperty("分类编号")
    private Long categoryId;

    @Schema(description = "发布时间")
    @ExcelProperty("发布时间")
    private LocalDateTime publishTime;

    @Schema(description = "是否付费视频：0-免费，1-付费", requiredMode = Schema.RequiredMode.REQUIRED, example = "1458")
    @ExcelProperty("是否付费视频：0-免费，1-付费")
    private Integer isPaid;

    @Schema(description = "视频价格", requiredMode = Schema.RequiredMode.REQUIRED, example = "19267")
    @ExcelProperty("视频价格")
    private BigDecimal price;

    @Schema(description = "折扣价格", example = "9426")
    @ExcelProperty("折扣价格")
    private BigDecimal discountPrice;

    @Schema(description = "折扣开始时间")
    @ExcelProperty("折扣开始时间")
    private LocalDateTime discountStartTime;

    @Schema(description = "折扣结束时间")
    @ExcelProperty("折扣结束时间")
    private LocalDateTime discountEndTime;

    @Schema(description = "创建时间", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("创建时间")
    private LocalDateTime createTime;

    @Schema(description = "修改者")
    private String updater;
}