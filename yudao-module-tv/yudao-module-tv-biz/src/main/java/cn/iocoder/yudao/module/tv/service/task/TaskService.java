package cn.iocoder.yudao.module.tv.service.task;

import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.module.tv.controller.admin.task.vo.TaskSaveReqVO;
import cn.iocoder.yudao.module.tv.controller.admin.task.vo.TaskSubmissionAuditReqVO;
import cn.iocoder.yudao.module.tv.controller.app.task.vo.AppTaskRespVO;
import cn.iocoder.yudao.module.tv.controller.app.task.vo.AppTaskSubmissionCreateReqVO;
import cn.iocoder.yudao.module.tv.controller.app.task.vo.AppTaskSubmissionPageReqVO;
import cn.iocoder.yudao.module.tv.controller.app.task.vo.AppTaskSubmissionRespVO;
import cn.iocoder.yudao.module.tv.dal.dataobject.task.TaskDO;
import cn.iocoder.yudao.module.tv.dal.dataobject.task.TaskSubmissionDO;

import javax.validation.Valid;
import java.util.List;

/**
 * 任务系统 Service 接口
 *
 * <AUTHOR>
 */
public interface TaskService {

    /**
     * 创建任务
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    Long createTask(@Valid TaskSaveReqVO createReqVO);

    /**
     * 更新任务
     *
     * @param updateReqVO 更新信息
     */
    void updateTask(@Valid TaskSaveReqVO updateReqVO);

    /**
     * 删除任务
     *
     * @param id 编号
     */
    void deleteTask(Long id);

    /**
     * 获得任务
     *
     * @param id 编号
     * @return 任务
     */
    TaskDO getTask(Long id);

    /**
     * 获得任务列表
     *
     * @return 任务列表
     */
    List<TaskDO> getTaskList();

    /**
     * 获得用户可用的任务列表
     *
     * @param userId 用户ID
     * @return 任务列表
     */
    List<AppTaskRespVO> getUserAvailableTasks(Long userId);

    /**
     * 提交任务
     *
     * @param userId 用户ID
     * @param createReqVO 提交信息
     * @return 提交记录ID
     */
    Long submitTask(Long userId, @Valid AppTaskSubmissionCreateReqVO createReqVO);

    /**
     * 获得用户的任务提交分页
     *
     * @param userId 用户ID
     * @param pageReqVO 分页查询
     * @return 任务提交分页
     */
    PageResult<AppTaskSubmissionRespVO> getUserTaskSubmissionPage(Long userId, AppTaskSubmissionPageReqVO pageReqVO);

    /**
     * 审核任务提交
     *
     * @param auditReqVO 审核信息
     * @param auditorId 审核人ID
     */
    void auditTaskSubmission(@Valid TaskSubmissionAuditReqVO auditReqVO, Long auditorId);

    /**
     * 获得任务提交记录
     *
     * @param id 编号
     * @return 任务提交记录
     */
    TaskSubmissionDO getTaskSubmission(Long id);

    /**
     * 获得待审核的任务提交列表
     *
     * @return 待审核列表
     */
    List<TaskSubmissionDO> getPendingTaskSubmissions();

}
