package cn.iocoder.yudao.module.tv.controller.admin.prize;

import cn.iocoder.yudao.framework.common.pojo.CommonResult;
import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.module.tv.controller.admin.prize.vo.PrizePageReqVO;
import cn.iocoder.yudao.module.tv.controller.admin.prize.vo.PrizeSaveReqVO;
import cn.iocoder.yudao.module.tv.dal.dataobject.prize.PrizeDO;
import cn.iocoder.yudao.module.tv.service.prize.PrizeService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.List;

import static cn.iocoder.yudao.framework.common.pojo.CommonResult.success;

@Tag(name = "管理后台 - 奖品管理")
@RestController
@RequestMapping("/tv/prize")
@Validated
@Slf4j
public class PrizeController {

    @Resource
    private PrizeService prizeService;

    @PostMapping("/create")
    @Operation(summary = "创建奖品")
    @PreAuthorize("@ss.hasPermission('tv:prize:create')")
    public CommonResult<Long> createPrize(@Valid @RequestBody PrizeSaveReqVO createReqVO) {
        PrizeDO prize = convertToPrizeDO(createReqVO);
        return success(prizeService.createPrize(prize));
    }

    @PutMapping("/update")
    @Operation(summary = "更新奖品")
    @PreAuthorize("@ss.hasPermission('tv:prize:update')")
    public CommonResult<Boolean> updatePrize(@Valid @RequestBody PrizeSaveReqVO updateReqVO) {
        PrizeDO prize = convertToPrizeDO(updateReqVO);
        prizeService.updatePrize(prize);
        return success(true);
    }

    @DeleteMapping("/delete")
    @Operation(summary = "删除奖品")
    @Parameter(name = "id", description = "编号", required = true)
    @PreAuthorize("@ss.hasPermission('tv:prize:delete')")
    public CommonResult<Boolean> deletePrize(@RequestParam("id") Long id) {
        prizeService.deletePrize(id);
        return success(true);
    }

    @GetMapping("/get")
    @Operation(summary = "获得奖品")
    @Parameter(name = "id", description = "编号", required = true, example = "1024")
    @PreAuthorize("@ss.hasPermission('tv:prize:query')")
    public CommonResult<PrizeDO> getPrize(@RequestParam("id") Long id) {
        PrizeDO prize = prizeService.getPrize(id);
        return success(prize);
    }

    @GetMapping("/list")
    @Operation(summary = "获得奖品列表")
    @PreAuthorize("@ss.hasPermission('tv:prize:query')")
    public CommonResult<List<PrizeDO>> getPrizeList() {
        List<PrizeDO> list = prizeService.getPrizeList();
        return success(list);
    }

    @GetMapping("/available-list")
    @Operation(summary = "获得可用奖品列表")
    @PreAuthorize("@ss.hasPermission('tv:prize:query')")
    public CommonResult<List<PrizeDO>> getAvailablePrizes() {
        List<PrizeDO> list = prizeService.getAvailablePrizes();
        return success(list);
    }

    @GetMapping("/page")
    @Operation(summary = "获得奖品分页")
    @PreAuthorize("@ss.hasPermission('tv:prize:query')")
    public CommonResult<PageResult<PrizeDO>> getPrizePage(@Valid PrizePageReqVO pageReqVO) {
        PageResult<PrizeDO> pageResult = prizeService.getPrizePage(pageReqVO);
        return success(pageResult);
    }

    private PrizeDO convertToPrizeDO(PrizeSaveReqVO reqVO) {
        return PrizeDO.builder()
                .id(reqVO.getId())
                .name(reqVO.getName())
                .description(reqVO.getDescription())
                .imageUrl(reqVO.getImageUrl())
                .type(reqVO.getType())
                .value(reqVO.getValue())
                .stock(reqVO.getStock())
                .status(reqVO.getStatus())
                .sort(reqVO.getSort())
                .build();
    }
}
