package cn.iocoder.yudao.module.tv.dal.mysql.report;

import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.mybatis.core.query.LambdaQueryWrapperX;
import cn.iocoder.yudao.framework.mybatis.core.mapper.BaseMapperX;
import cn.iocoder.yudao.module.tv.controller.admin.report.vo.ReportPageReqVO;
import cn.iocoder.yudao.module.tv.controller.app.report.vo.AppReportPageReqVO;
import cn.iocoder.yudao.module.tv.dal.dataobject.report.ReportDO;
import org.apache.ibatis.annotations.Mapper;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 举报反馈 Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface ReportMapper extends BaseMapperX<ReportDO> {

    default PageResult<ReportDO> selectPage(ReportPageReqVO reqVO) {
        return selectPage(reqVO, new LambdaQueryWrapperX<ReportDO>()
                .eqIfPresent(ReportDO::getBusinessType, reqVO.getBusinessType())
                .eqIfPresent(ReportDO::getBusinessId, reqVO.getBusinessId())
                .eqIfPresent(ReportDO::getReportType, reqVO.getReportType())
                .eqIfPresent(ReportDO::getReporterId, reqVO.getReporterId())
                .eqIfPresent(ReportDO::getStatus, reqVO.getStatus())
                .eqIfPresent(ReportDO::getHandleUserId, reqVO.getHandleUserId())
                .betweenIfPresent(ReportDO::getCreateTime, reqVO.getCreateTime())
                .betweenIfPresent(ReportDO::getHandleTime, reqVO.getHandleTime())
                .orderByDesc(ReportDO::getId));
    }

    /**
     * 根据业务类型和业务ID查询举报列表
     *
     * @param businessType 业务类型
     * @param businessId 业务ID
     * @return 举报列表
     */
    default List<ReportDO> selectListByBusiness(String businessType, Long businessId) {
        return selectList(new LambdaQueryWrapperX<ReportDO>()
                .eq(ReportDO::getBusinessType, businessType)
                .eq(ReportDO::getBusinessId, businessId)
                .orderByDesc(ReportDO::getCreateTime));
    }

    /**
     * 统计某个业务的举报次数
     *
     * @param businessType 业务类型
     * @param businessId 业务ID
     * @return 举报次数
     */
    default Long countByBusiness(String businessType, Long businessId) {
        return selectCount(new LambdaQueryWrapperX<ReportDO>()
                .eq(ReportDO::getBusinessType, businessType)
                .eq(ReportDO::getBusinessId, businessId));
    }

    /**
     * 统计某个业务的有效举报次数（已处理且处理结果为违规）
     *
     * @param businessType 业务类型
     * @param businessId 业务ID
     * @return 有效举报次数
     */
    default Long countValidReportsByBusiness(String businessType, Long businessId) {
        return selectCount(new LambdaQueryWrapperX<ReportDO>()
                .eq(ReportDO::getBusinessType, businessType)
                .eq(ReportDO::getBusinessId, businessId)
                .eq(ReportDO::getStatus, 2) // 已处理
                .isNotNull(ReportDO::getHandleResult)
                .notLike(ReportDO::getHandleResult, "驳回")); // 简单判断，实际可以更精确
    }

    /**
     * 根据用户ID分页查询举报列表
     *
     * @param userId 用户ID
     * @param pageReqVO 分页查询条件
     * @return 举报分页结果
     */
    default PageResult<ReportDO> selectUserReportPage(Long userId, AppReportPageReqVO pageReqVO) {
        return selectPage(pageReqVO, new LambdaQueryWrapperX<ReportDO>()
                .eq(ReportDO::getReporterId, userId)
                .eqIfPresent(ReportDO::getBusinessType, pageReqVO.getBusinessType())
                .eqIfPresent(ReportDO::getReportType, pageReqVO.getReportType())
                .eqIfPresent(ReportDO::getStatus, pageReqVO.getStatus())
                .betweenIfPresent(ReportDO::getCreateTime, pageReqVO.getCreateTime())
                .orderByDesc(ReportDO::getCreateTime));
    }

    /**
     * 统计用户今日举报次数
     *
     * @param userId 用户ID
     * @param startTime 开始时间（今日0点）
     * @param endTime 结束时间（今日23:59:59）
     * @return 今日举报次数
     */
    default Long countUserTodayReports(Long userId, LocalDateTime startTime, LocalDateTime endTime) {
        return selectCount(new LambdaQueryWrapperX<ReportDO>()
                .eq(ReportDO::getReporterId, userId)
                .between(ReportDO::getCreateTime, startTime, endTime));
    }

    /**
     * 检查用户是否已举报过某个业务对象
     *
     * @param userId 用户ID
     * @param businessType 业务类型
     * @param businessId 业务ID
     * @return 是否已举报
     */
    default boolean existsUserReport(Long userId, String businessType, Long businessId) {
        return selectCount(new LambdaQueryWrapperX<ReportDO>()
                .eq(ReportDO::getReporterId, userId)
                .eq(ReportDO::getBusinessType, businessType)
                .eq(ReportDO::getBusinessId, businessId)) > 0;
    }

    /**
     * 统计用户最近一段时间的举报次数
     *
     * @param userId 用户ID
     * @param startTime 开始时间
     * @return 举报次数
     */
    default Long countUserRecentReports(Long userId, LocalDateTime startTime) {
        return selectCount(new LambdaQueryWrapperX<ReportDO>()
                .eq(ReportDO::getReporterId, userId)
                .ge(ReportDO::getCreateTime, startTime));
    }

}
