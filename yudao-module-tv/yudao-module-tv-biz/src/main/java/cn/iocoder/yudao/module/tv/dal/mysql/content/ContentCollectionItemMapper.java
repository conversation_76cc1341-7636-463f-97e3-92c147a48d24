package cn.iocoder.yudao.module.tv.dal.mysql.content;

import cn.iocoder.yudao.framework.common.pojo.PageParam;
import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.mybatis.core.mapper.BaseMapperX;
import cn.iocoder.yudao.framework.mybatis.core.query.LambdaQueryWrapperX;
import cn.iocoder.yudao.module.tv.dal.dataobject.content.ContentCollectionItemDO;
import cn.iocoder.yudao.module.tv.dal.dataobject.content.ContentMediaDO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

@Mapper
public interface ContentCollectionItemMapper extends BaseMapperX<ContentCollectionItemDO> {
    default PageResult<ContentCollectionItemDO> selectPage(PageParam reqVO, Long contentId) {
        return selectPage(reqVO, new LambdaQueryWrapperX<ContentCollectionItemDO>()
                .eq(ContentCollectionItemDO::getContentId, contentId)
                .orderByDesc(ContentCollectionItemDO::getSort));
    }

    /**
     * 根据合集ID获取最大排序号
     *
     * @param collectionId 合集ID
     * @return 最大排序号
     */
    @Select("SELECT MAX(sort) FROM tv_content_collection_item WHERE item_content_id = #{collectionId} AND deleted = 0")
    Integer selectMaxSortOrderByCollectionId(@Param("collectionId") Long collectionId);

    /**
     * 根据内容ID查询合集项目列表
     *
     * @param contentId 内容ID
     * @return 合集项目列表
     */
    default List<ContentCollectionItemDO> selectByContentId(Long contentId) {
        return selectList(new LambdaQueryWrapperX<ContentCollectionItemDO>()
            .eq(ContentCollectionItemDO::getContentId, contentId)
            .orderByAsc(ContentCollectionItemDO::getSort));
    }
}