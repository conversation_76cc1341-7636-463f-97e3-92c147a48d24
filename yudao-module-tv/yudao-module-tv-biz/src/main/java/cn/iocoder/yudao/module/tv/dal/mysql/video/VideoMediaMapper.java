package cn.iocoder.yudao.module.tv.dal.mysql.video;

import cn.iocoder.yudao.framework.mybatis.core.mapper.BaseMapperX;
import cn.iocoder.yudao.module.tv.dal.dataobject.video.VideoMediaDO;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
 * 视频媒体资源 Mapper
 */
@Mapper
public interface VideoMediaMapper extends BaseMapperX<VideoMediaDO> {

    /**
     * 获取视频的媒体资源列表
     *
     * @param videoId 视频编号
     * @return 媒体资源列表
     */
    default List<VideoMediaDO> selectListByVideoId(Long videoId) {
        return selectList(VideoMediaDO::getVideoId, videoId);
    }

    /**
     * 删除视频的媒体资源
     *
     * @param videoId 视频编号
     */
    default void deleteByVideoId(Long videoId) {
        delete(VideoMediaDO::getVideoId, videoId);
    }
} 