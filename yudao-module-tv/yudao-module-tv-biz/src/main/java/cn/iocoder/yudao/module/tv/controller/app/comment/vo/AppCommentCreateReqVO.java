package cn.iocoder.yudao.module.tv.controller.app.comment.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;

@Schema(description = "用户 APP - 评论创建 Request VO")
@Data
public class AppCommentCreateReqVO {

    @Schema(description = "用户编号", required = true, example = "1024")
    @NotNull(message = "用户编号不能为空")
    private Long userId;

    @Schema(description = "视频编号", required = true, example = "2048")
    @NotNull(message = "视频编号不能为空")
    private Long videoId;

    @Schema(description = "父评论编号", example = "3072")
    private Long parentId;

    @Schema(description = "评论内容", required = true, example = "真不错！")
    @NotEmpty(message = "评论内容不能为空")
    private String content;
} 