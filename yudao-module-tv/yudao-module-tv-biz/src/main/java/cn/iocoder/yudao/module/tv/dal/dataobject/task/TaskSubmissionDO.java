package cn.iocoder.yudao.module.tv.dal.dataobject.task;

import cn.iocoder.yudao.framework.tenant.core.db.TenantBaseDO;
import com.baomidou.mybatisplus.annotation.*;
import lombok.*;

import java.time.LocalDateTime;

/**
 * 任务提交记录 DO
 *
 * <AUTHOR>
 */
@TableName("tv_task_submission")
@KeySequence("tv_task_submission_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class TaskSubmissionDO extends TenantBaseDO {

    /**
     * 主键
     */
    @TableId
    private Long id;

    /**
     * 用户ID
     */
    private Long userId;

    /**
     * 任务ID
     */
    private Long taskId;

    /**
     * 截图base64数据
     */
    private String screenshotBase64;

    /**
     * 截图哈希值
     */
    private String screenshotHash;

    /**
     * 提交说明
     */
    private String description;

    /**
     * 审核状态：0-待审核，1-审核通过，2-审核拒绝
     */
    private Integer status;

    /**
     * 审核结果说明
     */
    private String auditResult;

    /**
     * 审核人ID
     */
    private Long auditUserId;

    /**
     * 审核时间
     */
    private LocalDateTime auditTime;

    /**
     * 实际奖励积分
     */
    private Integer rewardPoints;

}
