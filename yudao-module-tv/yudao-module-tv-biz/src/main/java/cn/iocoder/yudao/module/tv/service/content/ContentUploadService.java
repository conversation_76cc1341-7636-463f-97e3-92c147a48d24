package cn.iocoder.yudao.module.tv.service.content;

import cn.hutool.core.util.ObjectUtil;
import cn.hutool.system.SystemUtil;
import cn.iocoder.yudao.framework.common.util.object.BeanUtils;
import cn.iocoder.yudao.module.infra.api.deduplication.DeduplicationFileApi;
import cn.iocoder.yudao.module.infra.api.deduplication.dto.DeduplicationFileUploadReqDTO;
import cn.iocoder.yudao.module.infra.api.deduplication.dto.DeduplicationFileRespDTO;
import cn.iocoder.yudao.module.tv.config.TvWatermarkConfig;
import cn.iocoder.yudao.module.tv.controller.admin.content.vo.ContentUploadReqVO;
import cn.iocoder.yudao.module.tv.dal.dataobject.content.ContentDO;
import cn.iocoder.yudao.module.tv.dal.mysql.content.ContentMapper;
import cn.iocoder.yudao.module.tv.service.content.strategy.ContentUploadStrategy;
import cn.iocoder.yudao.module.tv.service.message.LocalMessageService;
import cn.iocoder.yudao.module.tv.service.message.dto.ContentUploadMessageDTO;
import cn.iocoder.yudao.module.tv.enums.message.MessageTypeEnum;
import cn.hutool.core.io.FileUtil;
import cn.hutool.core.util.IdUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import java.io.IOException;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static cn.iocoder.yudao.framework.common.exception.util.ServiceExceptionUtil.exception;
import static cn.iocoder.yudao.module.infra.enums.ErrorCodeConstants.DATA_SOURCE_CONFIG_NOT_EXISTS;
import static cn.iocoder.yudao.module.tv.enums.ErrorCodeConstants.CONTENT_ITEM_UPLOAD;
import static cn.iocoder.yudao.module.tv.enums.ErrorCodeConstants.CONTENT_UPLOAD;

/**
 * TV内容上传服务
 *
 * <AUTHOR>
 */
@Service
@Slf4j
public class ContentUploadService {

    @Resource
    private DeduplicationFileApi deduplicationFileApi;

    @Resource
    private ContentMapper contentMapper;

    @Resource
    private List<ContentUploadStrategy> uploadStrategies;

    @Resource
    private LocalMessageService localMessageService;

    @Resource
    private TvWatermarkConfig watermarkConfig;

    // 策略映射表
    private final Map<Integer, ContentUploadStrategy> strategyMap = new HashMap<>();

    @PostConstruct
    public void initStrategies() {
        for (ContentUploadStrategy strategy : uploadStrategies) {
            strategyMap.put(strategy.getSupportedType(), strategy);
            log.info("[ContentUploadService][注册上传策略] type: {}, strategy: {}", 
                strategy.getSupportedType(), strategy.getClass().getSimpleName());
        }
    }

    /**
     * 统一的内容上传接口
     */
    @Transactional(rollbackFor = Exception.class)
    public Map<String, Object> uploadContent(ContentUploadReqVO uploadReq) throws IOException {

            log.info("[ContentUploadService][开始处理内容上传] title: {}, type: {}",
                uploadReq.getTitle(), uploadReq.getType());

            // 1. 上传封面文件
            String coverUrl = uploadCoverFile(uploadReq);
            if (coverUrl == null) {
                throw exception(CONTENT_UPLOAD);
            }
            //  返回成功结果
            Map<String, Object> resultData = new HashMap<>();
            resultData.put("coverUrl", coverUrl);
            // 2. 更新内容记录的封面URL
            ContentDO content = BeanUtils.toBean(uploadReq, ContentDO.class);
            

            // 3. 根据类型选择策略处理内容文件
            ContentUploadStrategy strategy = strategyMap.get(uploadReq.getType());
            MultipartFile contentFile = uploadReq.getContentFile();
            if( ObjectUtil.isAllNotEmpty(strategy,contentFile) ){

                // 设置内容状态为处理中
                content.setStatus(3); // 3-处理中
                content.setCoverUrl(coverUrl);
                contentMapper.insert(content);
                Long contentId = content.getId();
                resultData.put("contentId", contentId);

                // 保存文件到临时目录
                String tempFilePath = saveTempFile(contentFile);

                // 创建内容上传消息
                ContentUploadMessageDTO uploadMessage = ContentUploadMessageDTO.builder()
                        .contentId(contentId)
                        .configId(uploadReq.getConfigId())
                        .tempFilePath(tempFilePath)
                        .fileName(contentFile.getOriginalFilename())
                        .fileSize(contentFile.getSize())
                        .extParams(uploadReq.getExtParams())
                        .syncCompress(uploadReq.getSyncCompress())
                        .compressConfig(uploadReq.getCompressConfig())
                        .contentType(uploadReq.getType())
                        .build();

                // 发送到本地消息表，实现事务一致性
                String messageId = localMessageService.sendMessage(
                        "content.upload", // 主题
                        MessageTypeEnum.CONTENT_UPLOAD.getType(), // 消息类型
                        uploadMessage, // 消息内容
                        contentId.toString(), // 业务ID
                        "CONTENT_UPLOAD" // 业务类型
                );

                resultData.put("messageId", messageId);
                resultData.put("status", "PROCESSING"); // 处理中状态

                log.info("[uploadContent][内容上传消息已发送] contentId: {}, messageId: {}, tempFile: {}",
                        contentId, messageId, tempFilePath);
            }else {
                content.setStatus(1);
                content.setCoverUrl(coverUrl);
                contentMapper.insert(content);
                Long contentId = content.getId();
                resultData.put("contentId", contentId);
            }


            log.info("[ContentUploadService][内容上传成功] contentId: {}, type: {}", 
                uploadReq.getTitle(), uploadReq.getType());
            return resultData;

    }

    /**
     * 保存文件到临时目录
     *
     * @param file 上传的文件
     * @return 临时文件路径
     */
    private String saveTempFile(MultipartFile file) throws IOException {

        // 创建临时目录
        String tempDir =  null;
        // 设置水印参数 解决： 重复加水印导致视频文件损坏的bug需要保留原文件。
        if (watermarkConfig.getEnabled() && watermarkConfig.getLogoPath() != null) {
            tempDir = SystemUtil.getUserInfo().getCurrentDir() + "/content-upload/";  // 没有设置水印就不保留原始文件。
        }else{
            // 没有设置水印就不保留原始文件。
            tempDir = System.getProperty("java.io.tmpdir") + "/content-upload/";
        }

        FileUtil.mkdir(tempDir);

        // 生成唯一文件名
        String originalFilename = file.getOriginalFilename();
        String extension = FileUtil.extName(originalFilename);
        String tempFileName = IdUtil.fastSimpleUUID() + "." + extension;
        String tempFilePath = tempDir + tempFileName;

        // 保存文件
        file.transferTo(new java.io.File(tempFilePath));

        log.info("[saveTempFile][保存临时文件成功] originalName: {}, tempPath: {}",
                originalFilename, tempFilePath);

        return tempFilePath;
    }

    /**
     * 上传封面文件
     */
    private String uploadCoverFile(ContentUploadReqVO uploadReq) throws IOException {
        try {
            DeduplicationFileUploadReqDTO coverUploadReq = new DeduplicationFileUploadReqDTO();
            coverUploadReq.setName(uploadReq.getCoverFile().getOriginalFilename());
            coverUploadReq.setInputStream(uploadReq.getCoverFile().getInputStream());
            coverUploadReq.setContentLength(uploadReq.getCoverFile().getSize());
            coverUploadReq.setType(uploadReq.getCoverFile().getContentType());
            coverUploadReq.setSyncCompress(true); // 封面图片默认启用压缩
            coverUploadReq.setCompressType("IMAGE_AVIF"); // 封面图片转AVIF格式
            coverUploadReq.setConfigId(uploadReq.getConfigId());
            // 设置水印参数
            if (watermarkConfig.getEnabled() && watermarkConfig.getLogoPath() != null) {
                coverUploadReq.setWatermarkPath(watermarkConfig.getLogoPath());
                coverUploadReq.setWatermarkPosition(watermarkConfig.getPosition());
                coverUploadReq.setWatermarkOpacity(watermarkConfig.getOpacity());
                coverUploadReq.setWatermarkScale(watermarkConfig.getScale());
                coverUploadReq.setCoverPosition(watermarkConfig.getCoverPosition());
                coverUploadReq.setCoverSize(watermarkConfig.getCoverSize());
            }
            DeduplicationFileRespDTO uploadResult = deduplicationFileApi.uploadFile(coverUploadReq);
            
            if (uploadResult != null && uploadResult.getUrl() != null) {
                log.info("[ContentUploadService][封面上传成功] contentId: {}, coverUrl: {}, compressed: {}", 
                    uploadReq.getTitle(), uploadResult.getUrl(), uploadResult.getIsCompressed());
                return uploadResult.getUrl();
            } else {
                log.error("[ContentUploadService][封面上传失败] contentId: {}", uploadReq.getTitle());
                return null;
            }

        } catch (Exception e) {
            log.error("[ContentUploadService][封面上传异常] contentId: {}", uploadReq.getTitle(), e);
            return null;
        }
    }


}
