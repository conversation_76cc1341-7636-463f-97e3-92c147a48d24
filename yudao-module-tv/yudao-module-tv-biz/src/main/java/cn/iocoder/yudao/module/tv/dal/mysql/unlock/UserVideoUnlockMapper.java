package cn.iocoder.yudao.module.tv.dal.mysql.unlock;

import cn.iocoder.yudao.framework.common.pojo.PageParam;
import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.mybatis.core.mapper.BaseMapperX;
import cn.iocoder.yudao.framework.mybatis.core.query.LambdaQueryWrapperX;
import cn.iocoder.yudao.module.tv.controller.admin.video.vo.VideoPageReqVO;
import cn.iocoder.yudao.module.tv.dal.dataobject.unlock.UserVideoUnlockDO;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
 * 用户视频解锁记录 Mapper
 */
@Mapper
public interface UserVideoUnlockMapper extends BaseMapperX<UserVideoUnlockDO> {

    /**
     * 根据用户编号和视频编号查询解锁记录
     *
     * @param userId 用户编号
     * @param videoId 视频编号
     * @return 解锁记录
     */
    default UserVideoUnlockDO selectByUserIdAndVideoId(Long userId, Long videoId) {
        return selectOne(new LambdaQueryWrapperX<UserVideoUnlockDO>()
                .eq(UserVideoUnlockDO::getUserId, userId)
                .eq(UserVideoUnlockDO::getVideoId, videoId));
    }

    /**
     * 根据用户编号查询解锁记录列表
     *
     * @param userId 用户编号
     * @return 解锁记录列表
     */
    default List<UserVideoUnlockDO> selectListByUserId(Long userId) {
        return selectList(new LambdaQueryWrapperX<UserVideoUnlockDO>()
                .eq(UserVideoUnlockDO::getUserId, userId)
                .orderByDesc(UserVideoUnlockDO::getUnlockTime));
    }

    /**
     * 根据用户编号查询解锁记录分页
     *
     * @param userId 用户编号
     * @param pageReqVO 分页参数
     * @return 解锁记录分页
     */
    default PageResult<UserVideoUnlockDO> selectPageByUserId(Long userId, PageParam pageReqVO) {
        return selectPage(pageReqVO, new LambdaQueryWrapperX<UserVideoUnlockDO>()
                .eq(UserVideoUnlockDO::getUserId, userId)
                .orderByDesc(UserVideoUnlockDO::getUnlockTime));
    }

}
