package cn.iocoder.yudao.module.tv.controller.app.report;

import cn.iocoder.yudao.framework.common.pojo.CommonResult;
import cn.iocoder.yudao.framework.web.core.util.WebFrameworkUtils;
import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.module.tv.controller.app.report.vo.AppReportCreateReqVO;
import cn.iocoder.yudao.module.tv.controller.app.report.vo.AppReportPageReqVO;
import cn.iocoder.yudao.module.tv.controller.app.report.vo.AppReportRespVO;
import cn.iocoder.yudao.module.tv.service.report.ReportService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;


import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.validation.Valid;

import static cn.iocoder.yudao.framework.common.pojo.CommonResult.success;
import static cn.iocoder.yudao.framework.common.util.servlet.ServletUtils.getClientIP;
import static cn.iocoder.yudao.framework.security.core.util.SecurityFrameworkUtils.getLoginUserId;

@Tag(name = "用户 App - 举报反馈")
@RestController
@RequestMapping("/tv/report")
@Validated
public class AppReportController {

    @Resource
    private ReportService reportService;

    @PostMapping("/create")
    @Operation(summary = "创建举报反馈")
    public CommonResult<Long> createReport(@Valid @RequestBody AppReportCreateReqVO createReqVO) {
        String userIp = getClientIP();
        return success(reportService.createAppReport(getLoginUserId(), createReqVO, userIp));
    }

    @GetMapping("/my-page")
    @Operation(summary = "获得我的举报分页")
    public CommonResult<PageResult<AppReportRespVO>> getMyReportPage(@Valid AppReportPageReqVO pageReqVO) {
        PageResult<AppReportRespVO> pageResult = reportService.getUserReportPage(getLoginUserId(), pageReqVO);
        return success(pageResult);
    }

    @GetMapping("/can-report")
    @Operation(summary = "检查是否可以举报")
    public CommonResult<Boolean> canReport(@RequestParam("businessType") String businessType,
                                           @RequestParam("businessId") Long businessId) {
        boolean canReport = reportService.canUserReport(getLoginUserId(), businessType, businessId);
        return success(canReport);
    }

    @GetMapping("/today-count")
    @Operation(summary = "获取今日举报次数")
    public CommonResult<Long> getTodayReportCount() {
        Long count = reportService.countUserTodayReports(getLoginUserId());
        return success(count);
    }

}
