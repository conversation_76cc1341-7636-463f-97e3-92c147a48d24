package cn.iocoder.yudao.module.tv.service.report;

import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.module.tv.controller.admin.report.vo.ReportAuditReqVO;
import cn.iocoder.yudao.module.tv.controller.admin.report.vo.ReportDetailRespVO;
import cn.iocoder.yudao.module.tv.controller.admin.report.vo.ReportPageReqVO;
import cn.iocoder.yudao.module.tv.controller.admin.report.vo.ReportRespVO;
import cn.iocoder.yudao.module.tv.controller.admin.report.vo.ReportSaveReqVO;
import cn.iocoder.yudao.module.tv.controller.admin.report.vo.ReportStatisticsReqVO;
import cn.iocoder.yudao.module.tv.controller.admin.report.vo.ReportStatisticsRespVO;
import cn.iocoder.yudao.module.tv.controller.app.report.vo.AppReportCreateReqVO;
import cn.iocoder.yudao.module.tv.controller.app.report.vo.AppReportPageReqVO;
import cn.iocoder.yudao.module.tv.controller.app.report.vo.AppReportRespVO;
import cn.iocoder.yudao.module.tv.dal.dataobject.report.ReportDO;

import javax.validation.Valid;
import java.util.List;

/**
 * 举报反馈 Service 接口
 *
 * <AUTHOR>
 */
public interface ReportService {

    /**
     * 创建举报反馈
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    Long createReport(@Valid ReportSaveReqVO createReqVO);

    /**
     * APP端创建举报反馈
     *
     * @param userId 用户ID
     * @param createReqVO 创建信息
     * @param userIp 用户IP
     * @return 编号
     */
    Long createAppReport(Long userId, @Valid AppReportCreateReqVO createReqVO, String userIp);

    /**
     * 更新举报反馈
     *
     * @param updateReqVO 更新信息
     */
    void updateReport(@Valid ReportSaveReqVO updateReqVO);

    /**
     * 处理举报反馈
     *
     * @param id 举报ID
     * @param handleUserId 处理人ID
     * @param handleResult 处理结果
     * @param isViolation 是否违规
     */
    void handleReport(Long id, Long handleUserId, String handleResult, boolean isViolation);

    /**
     * 删除举报反馈
     *
     * @param id 编号
     */
    void deleteReport(Long id);

    /**
     * 获得举报反馈
     *
     * @param id 编号
     * @return 举报反馈
     */
    ReportDO getReport(Long id);

    /**
     * 获得举报反馈分页
     *
     * @param pageReqVO 分页查询
     * @return 举报反馈分页
     */
    PageResult<ReportRespVO> getReportPage(ReportPageReqVO pageReqVO);

    /**
     * 根据业务类型和业务ID查询举报列表
     *
     * @param businessType 业务类型
     * @param businessId 业务ID
     * @return 举报列表
     */
    List<ReportDO> getReportListByBusiness(String businessType, Long businessId);

    /**
     * 统计某个业务的举报次数
     *
     * @param businessType 业务类型
     * @param businessId 业务ID
     * @return 举报次数
     */
    Long countReportsByBusiness(String businessType, Long businessId);

    /**
     * 获得用户的举报分页
     *
     * @param userId 用户ID
     * @param pageReqVO 分页查询
     * @return 举报分页
     */
    PageResult<AppReportRespVO> getUserReportPage(Long userId, AppReportPageReqVO pageReqVO);

    /**
     * 检查用户是否可以举报（反滥用检查）
     *
     * @param userId 用户ID
     * @param businessType 业务类型
     * @param businessId 业务ID
     * @return 是否可以举报
     */
    boolean canUserReport(Long userId, String businessType, Long businessId);

    /**
     * 统计用户今日举报次数
     *
     * @param userId 用户ID
     * @return 今日举报次数
     */
    Long countUserTodayReports(Long userId);

    /**
     * 获取举报详情（包含举报人信息、被举报对象详情、历史举报记录）
     *
     * @param id 举报ID
     * @return 举报详情
     */
    ReportDetailRespVO getReportDetail(Long id);

    /**
     * 获取举报统计数据
     *
     * @param reqVO 统计查询条件
     * @return 统计结果
     */
    ReportStatisticsRespVO getReportStatistics(ReportStatisticsReqVO reqVO);

    /**
     * 获取待办举报队列（待处理的举报）
     *
     * @param pageReqVO 分页查询条件
     * @return 待办举报分页
     */
    PageResult<ReportRespVO> getPendingReportPage(ReportPageReqVO pageReqVO);

    /**
     * 审核举报（采纳、驳回、二次确认）
     *
     * @param auditReqVO 审核请求
     * @param auditorId 审核人ID
     */
    void auditReport(ReportAuditReqVO auditReqVO, Long auditorId);

    /**
     * 批量审核举报
     *
     * @param ids 举报ID列表
     * @param action 审核动作
     * @param handleResult 处理结果
     * @param auditorId 审核人ID
     */
    void batchAuditReports(List<Long> ids, String action, String handleResult, Long auditorId);

}
