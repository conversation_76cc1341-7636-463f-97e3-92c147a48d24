package cn.iocoder.yudao.module.tv.controller.app.group.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotNull;

@Schema(description = "用户 App - 参与拼团 Request VO")
@Data
public class AppGroupJoinReqVO {

    @Schema(description = "拼团ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "1024")
    @NotNull(message = "拼团ID不能为空")
    private Long groupId;

    @Schema(description = "订单ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "1024")
    private String orderNo;
}
