package cn.iocoder.yudao.module.tv.controller.app.like.vo;

import cn.iocoder.yudao.framework.common.pojo.PageParam;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

@Schema(description = "用户 APP - 喜欢的视频分页 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class AppLikeVideoPageReqVO extends PageParam {

    @Schema(description = "视频标题", example = "示例视频")
    private String title;

    @Schema(description = "视频分类编号", example = "1024")
    private Long categoryId;

    @Schema(description = "视频标签", example = "动作")
    private String tag;

}
