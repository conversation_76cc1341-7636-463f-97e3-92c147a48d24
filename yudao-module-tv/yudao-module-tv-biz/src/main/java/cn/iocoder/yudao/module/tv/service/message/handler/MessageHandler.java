package cn.iocoder.yudao.module.tv.service.message.handler;

import cn.iocoder.yudao.module.tv.dal.dataobject.message.LocalMessageDO;

/**
 * 消息处理器接口
 *
 * <AUTHOR>
 */
public interface MessageHandler {

    /**
     * 获取支持的消息类型
     *
     * @return 消息类型
     */
    String getSupportedMessageType();

    /**
     * 处理消息
     *
     * @param message 消息
     * @return 处理结果
     */
    MessageHandleResult handleMessage(LocalMessageDO message);

    /**
     * 消息处理结果
     */
    class MessageHandleResult {
        private final boolean success;
        private final String message;
        private final Object data;

        public MessageHandleResult(boolean success, String message) {
            this(success, message, null);
        }

        public MessageHandleResult(boolean success, String message, Object data) {
            this.success = success;
            this.message = message;
            this.data = data;
        }

        public boolean isSuccess() {
            return success;
        }

        public String getMessage() {
            return message;
        }

        public Object getData() {
            return data;
        }

        public static MessageHandleResult success() {
            return new MessageHandleResult(true, "处理成功");
        }

        public static MessageHandleResult success(String message) {
            return new MessageHandleResult(true, message);
        }

        public static MessageHandleResult success(String message, Object data) {
            return new MessageHandleResult(true, message, data);
        }

        public static MessageHandleResult failure(String message) {
            return new MessageHandleResult(false, message);
        }

        public static MessageHandleResult failure(String message, Object data) {
            return new MessageHandleResult(false, message, data);
        }
    }

}
