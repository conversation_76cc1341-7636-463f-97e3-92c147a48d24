package cn.iocoder.yudao.module.tv.dal.mysql.comment;

import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.mybatis.core.mapper.BaseMapperX;
import cn.iocoder.yudao.framework.mybatis.core.query.LambdaQueryWrapperX;
import cn.iocoder.yudao.module.tv.controller.app.comment.vo.AppTvCommentPageReqVO;
import cn.iocoder.yudao.module.tv.dal.dataobject.comment.CommentDO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Update;

import java.util.List;

@Mapper
public interface CommentMapper extends BaseMapperX<CommentDO> {

    default PageResult<CommentDO> selectPage(AppTvCommentPageReqVO pageReqVO) {
        return selectPage(pageReqVO, new LambdaQueryWrapperX<CommentDO>()
                .eq(CommentDO::getVideoId, pageReqVO.getVideoId())
                .eqIfPresent(CommentDO::getParentId, pageReqVO.getParentId())
                .orderByDesc(CommentDO::getCreateTime));
    }

    default List<CommentDO> selectListByVideoId(Long videoId) {
        return selectList(new LambdaQueryWrapperX<CommentDO>()
                .eq(CommentDO::getVideoId, videoId)
                .orderByDesc(CommentDO::getCreateTime));
    }

    default List<CommentDO> selectListByParentId(Long parentId) {
        return selectList(new LambdaQueryWrapperX<CommentDO>()
                .eq(CommentDO::getParentId, parentId)
                .orderByDesc(CommentDO::getCreateTime));
    }

    @Update("UPDATE tv_comment SET like_count = like_count + 1 WHERE id = #{id}")
    void incrementLikeCount(@Param("id") Long id);

    @Update("UPDATE tv_comment SET like_count = like_count - 1 WHERE id = #{id} AND like_count > 0")
    void decrementLikeCount(@Param("id") Long id);

    @Update("UPDATE tv_comment SET reply_count = reply_count + 1 WHERE id = #{id}")
    void incrementReplyCount(@Param("id") Long id);

    @Update("UPDATE tv_comment SET reply_count = reply_count - 1 WHERE id = #{id} AND reply_count > 0")
    void decrementReplyCount(@Param("id") Long id);
} 