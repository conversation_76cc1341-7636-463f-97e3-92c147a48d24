package cn.iocoder.yudao.module.tv.service.like;

import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.module.tv.controller.admin.content.vo.ContentRespVO;
import cn.iocoder.yudao.module.tv.controller.app.like.vo.AppLikeVideoPageReqVO;
import cn.iocoder.yudao.module.tv.controller.app.video.vo.AppVideoRespVO;
import cn.iocoder.yudao.module.tv.convert.video.AppVideoConvert;
import cn.iocoder.yudao.module.tv.dal.dataobject.like.LikeDO;
import cn.iocoder.yudao.module.tv.dal.dataobject.video.VideoDO;
import cn.iocoder.yudao.module.tv.dal.mysql.content.ContentMapper;
import cn.iocoder.yudao.module.tv.dal.mysql.like.LikeMapper;
import cn.iocoder.yudao.module.tv.service.content.ContentService;
import cn.iocoder.yudao.module.tv.service.video.VideoService;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;

/**
 * 点赞 Service 实现类
 */
@Service
public class LikeServiceImpl implements LikeService {

    @Resource
    private LikeMapper likeMapper;

    @Resource
    private ContentMapper contentMapper;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Long createLike(Long userId, Long videoId) {
        // 创建点赞
        LikeDO like = new LikeDO();
        like.setUserId(userId);
        like.setVideoId(videoId);
        likeMapper.insert(like);
        // 更新视频点赞数
        contentMapper.incrementLikeCount(videoId);
        return like.getId();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void deleteLike(Long userId, Long videoId) {
        // 删除点赞
        likeMapper.deleteByUserIdAndVideoId(userId, videoId);
        // 更新视频点赞数
        contentMapper.decrementLikeCount(videoId);
    }

    @Override
    public boolean negation(Long userId, Long videoId) {
        boolean liked = isLiked(userId, videoId);
        if (liked) {
            deleteLike(userId, videoId);
        }else{
            createLike(userId, videoId);
        }
        return !liked;
    }

    @Override
    public boolean isLiked(Long userId, Long videoId) {
        return likeMapper.selectByUserIdAndVideoId(userId, videoId) != null;
    }

    @Override
    public PageResult<LikeDO> getLikeDOPageResult(Long loginUserId, AppLikeVideoPageReqVO pageVO) {
        PageResult<LikeDO> likePageResult = likeMapper.selectPageByUserId(loginUserId, pageVO);
        return likePageResult;
    }

    @Override
    public LikeDO getLike(Long userId, Long videoId) {
        return likeMapper.selectByUserIdAndVideoId(userId, videoId);
    }
}

