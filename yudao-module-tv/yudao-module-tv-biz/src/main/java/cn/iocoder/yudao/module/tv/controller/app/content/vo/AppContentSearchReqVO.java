package cn.iocoder.yudao.module.tv.controller.app.content.vo;

import cn.iocoder.yudao.framework.common.pojo.PageParam;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import org.springframework.format.annotation.DateTimeFormat;

import java.math.BigDecimal;
import java.time.LocalDateTime;

import static cn.iocoder.yudao.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;

@Schema(description = "用户 APP - 内容搜索 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class AppContentSearchReqVO extends PageParam {

    @Schema(description = "关键词搜索（标题、描述）", example = "搞笑视频")
    private String keyword;

    @Schema(description = "视频类型：0-单个视频，1-多个视频，2-图集,3-图文,4-合集,5-图片和视频混合", example = "0")
    private Integer type;

    @Schema(description = "分类编号", example = "1")
    private Long categoryId;

    @Schema(description = "状态：0-草稿，1-发布，2-下架", example = "1")
    private Integer status;

    @Schema(description = "是否付费视频：0-免费，1-付费", example = "0")
    private Integer isPaid;

    @Schema(description = "价格范围-最小值", example = "0.00")
    private BigDecimal minPrice;

    @Schema(description = "价格范围-最大值", example = "99.99")
    private BigDecimal maxPrice;

    @Schema(description = "发布时间范围-开始时间")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime publishTimeStart;

    @Schema(description = "发布时间范围-结束时间")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime publishTimeEnd;

    @Schema(description = "最小播放次数", example = "100")
    private Integer minPlayCount;

    @Schema(description = "最小点赞次数", example = "10")
    private Integer minLikeCount;

    @Schema(description = "最小收藏次数", example = "5")
    private Integer minFavoriteCount;

    @Schema(description = "排序字段：createTime-创建时间，publishTime-发布时间，playCount-播放次数，likeCount-点赞次数，favoriteCount-收藏次数，price-价格，recommendWeight-推荐权重", example = "createTime")
    private String sortField;

    @Schema(description = "排序方向：asc-升序，desc-降序", example = "desc")
    private String sortOrder;

    @Schema(description = "创建者", example = "254")
    private String creator;
    public AppContentSearchReqVO() {
        // 设置默认值
        this.status = 1; // 默认只查询已发布的内容
        this.sortField = "createTime"; // 默认按创建时间排序
        this.sortOrder = "desc"; // 默认降序
    }
}
