package cn.iocoder.yudao.module.tv.controller.app.privilege;

import cn.iocoder.yudao.framework.common.pojo.CommonResult;
import cn.iocoder.yudao.framework.tenant.core.aop.TenantIgnore;
import cn.iocoder.yudao.module.tv.controller.app.privilege.vo.AppPrivilegeRespVO;
import cn.iocoder.yudao.module.tv.service.privilege.PrivilegeService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.annotation.security.PermitAll;

import static cn.iocoder.yudao.framework.common.pojo.CommonResult.success;
import static cn.iocoder.yudao.framework.security.core.util.SecurityFrameworkUtils.getLoginUserId;

@Tag(name = "用户 APP - 特权")
@RestController
@RequestMapping("/tv/app/privilege")
@Validated
public class AppPrivilegeController {

    @Resource
    private PrivilegeService privilegeService;

    @GetMapping("/get")
    @Operation(summary = "获得用户特权信息")
    public CommonResult<AppPrivilegeRespVO> getUserPrivilege() {
        return success(privilegeService.getUserPrivilege(getLoginUserId()));
    }

    @GetMapping("/open")
    @Operation(summary = "开通特权")
    @Parameter(name = "userId", description = "用户id", required = true)
    @Parameter(name = "privilegeId", description = "特权编号", required = true)
    @PermitAll
    public CommonResult<Long> openPrivilege(@RequestParam("userId") Long userId,@RequestParam("privilegeId") Long privilegeId) {
        return success(privilegeService.openPrivilege(userId, privilegeId));
    }

} 