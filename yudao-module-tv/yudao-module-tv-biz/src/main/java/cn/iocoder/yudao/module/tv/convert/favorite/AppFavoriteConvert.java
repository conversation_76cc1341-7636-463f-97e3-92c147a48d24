package cn.iocoder.yudao.module.tv.convert.favorite;

import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.module.tv.controller.app.favorite.vo.AppFavoriteRespVO;
import cn.iocoder.yudao.module.tv.dal.dataobject.favorite.FavoriteDO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

@Mapper
public interface AppFavoriteConvert {

    AppFavoriteConvert INSTANCE = Mappers.getMapper(AppFavoriteConvert.class);

    AppFavoriteRespVO convert(FavoriteDO bean);

    PageResult<AppFavoriteRespVO> convertPage(PageResult<FavoriteDO> page);
} 