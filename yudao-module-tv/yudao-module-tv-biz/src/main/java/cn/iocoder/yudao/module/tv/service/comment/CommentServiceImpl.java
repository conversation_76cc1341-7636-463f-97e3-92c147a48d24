package cn.iocoder.yudao.module.tv.service.comment;

import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.module.tv.controller.app.comment.vo.AppTvCommentPageReqVO;
import cn.iocoder.yudao.module.tv.dal.dataobject.comment.CommentDO;
import cn.iocoder.yudao.module.tv.dal.mysql.comment.CommentMapper;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.List;

/**
 * 评论 Service 实现类
 */
@Service
public class CommentServiceImpl implements CommentService {

    @Resource
    private CommentMapper commentMapper;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Long createComment(Long userId, Long videoId, Long parentId, String content) {
        // 创建评论
        CommentDO comment = new CommentDO();
        comment.setUserId(userId);
        comment.setVideoId(videoId);
        comment.setParentId(parentId);
        comment.setContent(content);
        comment.setLikeCount(0);
        comment.setReplyCount(0);
        commentMapper.insert(comment);
        // 如果是回复评论，增加父评论的回复数
        if (parentId != null) {
            commentMapper.incrementReplyCount(parentId);
        }
        return comment.getId();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void deleteComment(Long id) {
        // 获取评论信息
        CommentDO comment = commentMapper.selectById(id);
        if (comment == null) {
            return;
        }
        // 删除评论
        commentMapper.deleteById(id);
        // 如果是回复评论，减少父评论的回复数
        if (comment.getParentId() != null) {
            commentMapper.decrementReplyCount(comment.getParentId());
        }
    }

    @Override
    public CommentDO getComment(Long id) {
        return commentMapper.selectById(id);
    }

    @Override
    public PageResult<CommentDO> getCommentPage(AppTvCommentPageReqVO pageReqVO) {
        return commentMapper.selectPage(pageReqVO);
    }

    @Override
    public List<CommentDO> getCommentListByVideoId(Long videoId) {
        return commentMapper.selectListByVideoId(videoId);
    }

    @Override
    public List<CommentDO> getCommentListByParentId(Long parentId) {
        return commentMapper.selectListByParentId(parentId);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void incrementLikeCount(Long id) {
        commentMapper.incrementLikeCount(id);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void decrementLikeCount(Long id) {
        commentMapper.decrementLikeCount(id);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void incrementReplyCount(Long id) {
        commentMapper.incrementReplyCount(id);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void decrementReplyCount(Long id) {
        commentMapper.decrementReplyCount(id);
    }
} 