package cn.iocoder.yudao.module.tv.controller.admin.task.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;

import javax.validation.constraints.*;
import java.time.LocalDateTime;

@Schema(description = "管理后台 - 任务新增/修改 Request VO")
@Data
public class TaskSaveReqVO {

    @Schema(description = "任务ID", example = "1024")
    private Long id;

    @Schema(description = "任务名称", requiredMode = Schema.RequiredMode.REQUIRED, example = "分享网站到微信群")
    @NotEmpty(message = "任务名称不能为空")
    private String name;

    @Schema(description = "任务描述", example = "分享网站到微信群/微博并截图上传")
    private String description;

    @Schema(description = "任务类型", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
    @NotNull(message = "任务类型不能为空")
    private Integer type;

    @Schema(description = "奖励积分", requiredMode = Schema.RequiredMode.REQUIRED, example = "20")
    @NotNull(message = "奖励积分不能为空")
    @Min(value = 1, message = "奖励积分必须大于0")
    private Integer rewardPoints;

    @Schema(description = "每日完成次数限制", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
    @NotNull(message = "每日完成次数限制不能为空")
    @Min(value = 1, message = "每日完成次数限制必须大于0")
    private Integer dailyLimit;

    @Schema(description = "总完成次数限制", example = "100")
    private Integer totalLimit;

    @Schema(description = "任务状态", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
    @NotNull(message = "任务状态不能为空")
    private Integer status;

    @Schema(description = "任务开始时间")
    private LocalDateTime startTime;

    @Schema(description = "任务结束时间")
    private LocalDateTime endTime;

    @Schema(description = "排序值", example = "0")
    private Integer sort;

}
