package cn.iocoder.yudao.module.tv.dal.mysql.task;

import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.mybatis.core.query.LambdaQueryWrapperX;
import cn.iocoder.yudao.framework.mybatis.core.mapper.BaseMapperX;
import cn.iocoder.yudao.module.tv.controller.app.task.vo.AppTaskSubmissionPageReqVO;
import cn.iocoder.yudao.module.tv.dal.dataobject.task.TaskSubmissionDO;
import cn.iocoder.yudao.module.tv.enums.task.TaskSubmissionStatusEnum;
import org.apache.ibatis.annotations.Mapper;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 任务提交记录 Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface TaskSubmissionMapper extends BaseMapperX<TaskSubmissionDO> {

    /**
     * 分页查询用户的任务提交记录
     *
     * @param userId 用户ID
     * @param pageReqVO 分页查询条件
     * @return 分页结果
     */
    default PageResult<TaskSubmissionDO> selectUserSubmissionPage(Long userId, AppTaskSubmissionPageReqVO pageReqVO) {
        return selectPage(pageReqVO, new LambdaQueryWrapperX<TaskSubmissionDO>()
                .eq(TaskSubmissionDO::getUserId, userId)
                .eqIfPresent(TaskSubmissionDO::getTaskId, pageReqVO.getTaskId())
                .eqIfPresent(TaskSubmissionDO::getStatus, pageReqVO.getStatus())
                .betweenIfPresent(TaskSubmissionDO::getCreateTime, pageReqVO.getCreateTime())
                .orderByDesc(TaskSubmissionDO::getCreateTime));
    }

    /**
     * 查询相似的截图哈希值
     *
     * @param screenshotHash 截图哈希值
     * @param threshold 相似度阈值
     * @return 相似的提交记录
     */
    default List<TaskSubmissionDO> selectSimilarScreenshots(String screenshotHash, int threshold) {
        // 这里简化处理，实际应该使用更复杂的相似度查询
        // 可以考虑使用数据库的全文搜索或者专门的相似度查询
        return selectList(new LambdaQueryWrapperX<TaskSubmissionDO>()
                .eq(TaskSubmissionDO::getScreenshotHash, screenshotHash)
                .last("LIMIT 10"));
    }

    /**
     * 统计用户今日通过审核的任务提交次数
     *
     * @param userId 用户ID
     * @param taskId 任务ID
     * @param startOfDay 今日开始时间
     * @param endOfDay 今日结束时间
     * @return 今日通过次数
     */
    default Integer countTodayApproved(Long userId, Long taskId, LocalDateTime startOfDay, LocalDateTime endOfDay) {
        return Math.toIntExact(selectCount(new LambdaQueryWrapperX<TaskSubmissionDO>()
                .eq(TaskSubmissionDO::getUserId, userId)
                .eq(TaskSubmissionDO::getTaskId, taskId)
                .eq(TaskSubmissionDO::getStatus, TaskSubmissionStatusEnum.APPROVED.getStatus())
                .between(TaskSubmissionDO::getAuditTime, startOfDay, endOfDay)));
    }

    /**
     * 查询待审核的提交记录
     *
     * @return 待审核记录列表
     */
    default List<TaskSubmissionDO> selectPendingSubmissions() {
        return selectList(new LambdaQueryWrapperX<TaskSubmissionDO>()
                .eq(TaskSubmissionDO::getStatus, TaskSubmissionStatusEnum.PENDING.getStatus())
                .orderByAsc(TaskSubmissionDO::getCreateTime));
    }

    /**
     * 统计用户的任务提交次数
     *
     * @param userId 用户ID
     * @param taskId 任务ID
     * @return 提交次数
     */
    default Long countUserSubmissions(Long userId, Long taskId) {
        return selectCount(new LambdaQueryWrapperX<TaskSubmissionDO>()
                .eq(TaskSubmissionDO::getUserId, userId)
                .eq(TaskSubmissionDO::getTaskId, taskId));
    }

}
