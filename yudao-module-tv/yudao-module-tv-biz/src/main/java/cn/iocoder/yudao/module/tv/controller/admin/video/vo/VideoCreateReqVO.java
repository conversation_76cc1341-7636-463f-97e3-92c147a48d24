package cn.iocoder.yudao.module.tv.controller.admin.video.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;

@Schema(description = "管理后台 - 视频创建 Request VO")
@Data
public class VideoCreateReqVO {

    @Schema(description = "视频标题", required = true, example = "示例视频")
    @NotEmpty(message = "视频标题不能为空")
    private String title;

    @Schema(description = "视频描述", example = "这是一个示例视频")
    private String description;

    @Schema(description = "视频封面URL", example = "https://www.example.com/cover.jpg")
    private String coverUrl;

    @Schema(description = "视频URL", required = true, example = "https://www.example.com/video.mp4")
    @NotEmpty(message = "视频URL不能为空")
    private String videoUrl;

    @Schema(description = "视频时长（秒）", required = true, example = "3600")
    @NotNull(message = "视频时长不能为空")
    private Integer duration;

    @Schema(description = "视频标签", example = "动作,冒险")
    private String tags;

    @Schema(description = "视频分类ID", required = true, example = "1024")
    @NotNull(message = "视频分类不能为空")
    private Long categoryId;

    @Schema(description = "状态", required = true, example = "1")
    @NotNull(message = "状态不能为空")
    private Integer status;
} 