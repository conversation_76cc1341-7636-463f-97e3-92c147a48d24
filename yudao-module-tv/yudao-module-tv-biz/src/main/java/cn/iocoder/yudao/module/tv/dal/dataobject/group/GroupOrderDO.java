package cn.iocoder.yudao.module.tv.dal.dataobject.group;

import cn.iocoder.yudao.framework.mybatis.core.dataobject.BaseDO;
import com.baomidou.mybatisplus.annotation.KeySequence;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.ToString;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 拼团参与记录 DO
 *
 * <AUTHOR>
 */
@TableName("tv_group_order")
@KeySequence("tv_group_order_seq")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class GroupOrderDO extends BaseDO {

    /**
     * 参与记录ID
     */
    @TableId
    private Long id;

    /**
     * 拼团ID
     */
    private Long groupId;

    /**
     * 用户ID
     */
    private Long userId;

    /**
     * 支付订单号
     */
    private String orderNo;

    /**
     * 支付金额
     */
    private BigDecimal amount;

    /**
     * 用户在团内的序号
     */
    private Integer seqNo;

    /**
     * 参与时间
     */
    private LocalDateTime joinTime;
}
