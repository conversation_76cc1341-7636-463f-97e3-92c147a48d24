package cn.iocoder.yudao.module.tv.dal.dataobject.content;

import cn.iocoder.yudao.framework.tenant.core.db.TenantBaseDO;
import lombok.*;
import java.util.*;
import java.time.LocalDateTime;
import java.math.BigDecimal;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.time.LocalDateTime;
import java.time.LocalDateTime;
import java.time.LocalDateTime;
import com.baomidou.mybatisplus.annotation.*;
import cn.iocoder.yudao.framework.mybatis.core.dataobject.BaseDO;

/**
 * 内容主体 DO
 *
 * <AUTHOR>
 */
@TableName("tv_content")
@KeySequence("tv_content_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ContentDO extends TenantBaseDO {

    /**
     * 编号
     */
    @TableId
    private Long id;
    /**
     * 标题
     */
    private String title;
    /**
     * 描述
     */
    private String description;
    /**
     * 封面图片
     */
    private String coverUrl;
    /**
     * 视频类型：0-单个视频，1-多个视频，2-图集,3-图文,4-合集,5-图片和视频混合
     */
    private Integer type;
    /**
     * 播放次数
     */
    private Integer playCount;
    /**
     * 点赞次数
     */
    private Integer likeCount;
    /**
     * 收藏次数
     */
    private Integer favoriteCount;
    /**
     * 评论次数
     */
    private Integer commentCount;
    /**
     * 推荐系统权重因子
     */
    private Integer recommendWeight;
    /**
     * 内容质量评分（平台内部使用）
     */
    private Integer qualityScore;
    /**
     * 状态：0-草稿，1-发布，2-下架，3-处理中
     */
    private Integer status;
    /**
     * 分类编号
     */
    private Long categoryId;
    /**
     * 发布时间
     */
    private LocalDateTime publishTime;
    /**
     * 是否付费视频：0-免费，1-付费
     */
    private Integer isPaid;
    /**
     * 视频价格
     */
    private BigDecimal price;
    /**
     * 折扣价格
     */
    private BigDecimal discountPrice;
    /**
     * 折扣开始时间
     */
    private LocalDateTime discountStartTime;
    /**
     * 折扣结束时间
     */
    private LocalDateTime discountEndTime;

}