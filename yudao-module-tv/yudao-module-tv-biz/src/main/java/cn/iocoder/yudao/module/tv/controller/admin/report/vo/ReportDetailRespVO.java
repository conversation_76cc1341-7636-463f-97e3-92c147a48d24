package cn.iocoder.yudao.module.tv.controller.admin.report.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;

import java.time.LocalDateTime;
import java.util.List;

@Schema(description = "管理后台 - 举报详情 Response VO")
@Data
public class ReportDetailRespVO {

    @Schema(description = "举报信息")
    private ReportRespVO report;

    @Schema(description = "举报人信息")
    private ReporterInfo reporter;

    @Schema(description = "被举报对象详情")
    private Object businessDetail;

    @Schema(description = "历史举报记录")
    private List<ReportRespVO> historyReports;

    @Schema(description = "举报人信息")
    @Data
    public static class ReporterInfo {
        @Schema(description = "用户ID", example = "1024")
        private Long userId;
        
        @Schema(description = "用户昵称", example = "张三")
        private String nickname;
        
        @Schema(description = "用户头像", example = "http://example.com/avatar.jpg")
        private String avatar;
        
        @Schema(description = "注册时间")
        private LocalDateTime createTime;
        
        @Schema(description = "最近举报次数", example = "5")
        private Long recentReportCount;
        
        @Schema(description = "历史举报次数", example = "20")
        private Long totalReportCount;
    }

}
