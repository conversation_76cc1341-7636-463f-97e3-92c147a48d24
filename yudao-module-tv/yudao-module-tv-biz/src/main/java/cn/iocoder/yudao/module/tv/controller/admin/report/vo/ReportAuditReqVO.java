package cn.iocoder.yudao.module.tv.controller.admin.report.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;

import javax.validation.constraints.*;

@Schema(description = "管理后台 - 举报审核 Request VO")
@Data
public class ReportAuditReqVO {

    @Schema(description = "举报ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "1024")
    @NotNull(message = "举报ID不能为空")
    private Long id;

    @Schema(description = "审核动作：adopt-采纳，reject-驳回，confirm-二次确认", requiredMode = Schema.RequiredMode.REQUIRED, example = "adopt")
    @NotEmpty(message = "审核动作不能为空")
    private String action;

    @Schema(description = "处理结果说明", requiredMode = Schema.RequiredMode.REQUIRED, example = "经核实，该内容确实违规，已下架处理")
    @NotEmpty(message = "处理结果说明不能为空")
    private String handleResult;

    @Schema(description = "是否需要二次确认", example = "false")
    private Boolean needConfirm = false;

}
