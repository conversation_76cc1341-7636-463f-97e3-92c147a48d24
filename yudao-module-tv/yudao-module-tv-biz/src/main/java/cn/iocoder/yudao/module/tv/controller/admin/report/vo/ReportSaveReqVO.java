package cn.iocoder.yudao.module.tv.controller.admin.report.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;

import javax.validation.constraints.*;
import java.time.LocalDateTime;
import java.util.List;

@Schema(description = "管理后台 - 举报反馈新增/修改 Request VO")
@Data
public class ReportSaveReqVO {

    @Schema(description = "主键", requiredMode = Schema.RequiredMode.REQUIRED, example = "1024")
    private Long id;

    @Schema(description = "业务类型", requiredMode = Schema.RequiredMode.REQUIRED, example = "content")
    @NotEmpty(message = "业务类型不能为空")
    private String businessType;

    @Schema(description = "业务ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "1024")
    @NotNull(message = "业务ID不能为空")
    private Long businessId;

    @Schema(description = "举报类型", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
    @NotNull(message = "举报类型不能为空")
    private Integer reportType;

    @Schema(description = "举报原因描述", example = "内容涉及色情低俗")
    private String reportReason;

    @Schema(description = "举报截图", example = "[\"http://example.com/image1.jpg\"]")
    private List<String> reportImages;

    @Schema(description = "举报人ID", example = "1024")
    private Long reporterId;

    @Schema(description = "举报人IP地址", example = "***********")
    private String reporterIp;

    @Schema(description = "处理状态", example = "0")
    private Integer status;

    @Schema(description = "处理结果说明", example = "经核实，该内容确实违规，已下架处理")
    private String handleResult;

    @Schema(description = "处理人ID", example = "1024")
    private Long handleUserId;

    @Schema(description = "处理时间")
    private LocalDateTime handleTime;

}
