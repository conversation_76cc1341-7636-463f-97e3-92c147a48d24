package cn.iocoder.yudao.module.tv.enums.group;

import cn.iocoder.yudao.framework.common.core.IntArrayValuable;
import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Arrays;

/**
 * 开奖算法类型枚举
 *
 * <AUTHOR>
 */
@Getter
@AllArgsConstructor
public enum DrawAlgorithmEnum implements IntArrayValuable {

    WEIGHTED_RANDOM(1, "权重随机"),
    GUARANTEED_WIN(2, "保底必中");

    public static final int[] ARRAYS = Arrays.stream(values()).mapToInt(DrawAlgorithmEnum::getType).toArray();

    /**
     * 类型
     */
    private final Integer type;
    /**
     * 名字
     */
    private final String name;

    @Override
    public int[] array() {
        return ARRAYS;
    }

    public static DrawAlgorithmEnum valueOf(Integer type) {
        return Arrays.stream(values()).filter(item -> item.getType().equals(type)).findFirst().orElse(null);
    }
}
