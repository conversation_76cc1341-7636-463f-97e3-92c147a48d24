package cn.iocoder.yudao.module.tv.service.unlock;

import cn.iocoder.yudao.framework.common.pojo.PageParam;
import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.module.tv.controller.admin.video.vo.VideoPageReqVO;
import cn.iocoder.yudao.module.tv.dal.dataobject.unlock.UserVideoUnlockDO;
import cn.iocoder.yudao.module.tv.dal.dataobject.video.VideoDO;

import java.util.List;
import java.util.Set;

/**
 * 视频解锁 Service 接口
 */
public interface VideoUnlockService {

    /**
     * 判断用户是否已解锁视频
     *
     * @param userId 用户编号
     * @param videoId 视频编号
     * @return 是否已解锁
     */
    boolean isVideoUnlocked(Long userId, Long videoId);

    /**
     * 解锁视频
     *
     * @param userId 用户编号
     * @param videoId 视频编号
     * @param points 消费积分数
     * @return 解锁记录编号
     */
    Long unlockVideo(Long userId, Long videoId, Integer points);

    /**
     * 获取用户已解锁的视频ID列表
     *
     * @param userId 用户编号
     * @return 视频ID列表
     */
    Set<Long> getUserUnlockedVideoIds(Long userId);

    PageResult<UserVideoUnlockDO> selectPageByUserId(Long userId, PageParam pageReqVO);

    /**
     * 获取用户解锁记录
     *
     * @param userId 用户编号
     * @param videoId 视频编号
     * @return 解锁记录
     */
    UserVideoUnlockDO getUserVideoUnlock(Long userId, Long videoId);

}
