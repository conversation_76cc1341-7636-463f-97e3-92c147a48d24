package cn.iocoder.yudao.module.tv.service.report.handler;

import cn.iocoder.yudao.module.tv.dal.dataobject.content.ContentDO;
import cn.iocoder.yudao.module.tv.dal.dataobject.report.ReportDO;
import cn.iocoder.yudao.module.tv.dal.mysql.content.ContentMapper;
import cn.iocoder.yudao.module.tv.enums.report.ReportBusinessTypeEnum;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * 内容举报处理器
 *
 * <AUTHOR>
 */
@Component
@Slf4j
public class ContentReportHandler implements ReportHandler {

    @Resource
    private ContentMapper contentMapper;

    @Override
    public String getBusinessType() {
        return ReportBusinessTypeEnum.CONTENT.getType();
    }

    @Override
    public void afterReportCreate(ReportDO report) {
        log.info("[afterReportCreate][内容({})被举报，举报类型：{}]", 
                report.getBusinessId(), report.getReportType());
        
        // 检查是否需要自动处理
        // 这里可以根据举报次数决定是否自动下架
        // 暂时不在创建时自动处理，等管理员审核
    }

    @Override
    public void afterReportHandle(ReportDO report, boolean isViolation) {
        if (isViolation) {
            // 违规内容，执行下架操作
            offlineContent(report.getBusinessId());
            log.info("[afterReportHandle][内容({})违规，已自动下架]", report.getBusinessId());
        } else {
            log.info("[afterReportHandle][内容({})举报被驳回，无需处理]", report.getBusinessId());
        }
    }

    @Override
    public boolean shouldAutoHandle(Long businessId, Long reportCount) {
        // 内容被举报超过5次自动下架
        return reportCount >= 3;
    }

    @Override
    public void autoHandle(Long businessId, Long reportCount) {
        // 自动下架内容
        offlineContent(businessId);
        log.warn("[autoHandle][内容({})被举报{}次，已自动下架]", businessId, reportCount);
    }

    @Override
    public Object getBusinessDetail(Long businessId) {
        // 返回内容详情，用于管理后台展示
        return contentMapper.selectById(businessId);
    }

    /**
     * 下架内容
     *
     * @param contentId 内容ID
     */
    private void offlineContent(Long contentId) {
        ContentDO content = contentMapper.selectById(contentId);
        if (content != null && content.getStatus() == 1) { // 只有发布状态的内容才能下架
            ContentDO updateObj = new ContentDO();
            updateObj.setId(contentId);
            updateObj.setStatus(2); // 设置为下架状态
            contentMapper.updateById(updateObj);
            log.info("[offlineContent][内容({})已下架]", contentId);
        }
    }

}
