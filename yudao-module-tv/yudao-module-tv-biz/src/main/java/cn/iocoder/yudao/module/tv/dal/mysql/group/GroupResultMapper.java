package cn.iocoder.yudao.module.tv.dal.mysql.group;

import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.mybatis.core.mapper.BaseMapperX;
import cn.iocoder.yudao.framework.mybatis.core.query.LambdaQueryWrapperX;
import cn.iocoder.yudao.module.tv.controller.admin.group.vo.GroupResultPageReqVO;
import cn.iocoder.yudao.module.tv.dal.dataobject.group.GroupResultDO;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
 * 拼团开奖记录 Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface GroupResultMapper extends BaseMapperX<GroupResultDO> {

    default PageResult<GroupResultDO> selectPage(GroupResultPageReqVO reqVO) {
        return selectPage(reqVO, new LambdaQueryWrapperX<GroupResultDO>()
                .eqIfPresent(GroupResultDO::getGroupId, reqVO.getGroupId())
                .eqIfPresent(GroupResultDO::getWinnerId, reqVO.getWinnerId())
                .eqIfPresent(GroupResultDO::getPrizeId, reqVO.getPrizeId())
                .eqIfPresent(GroupResultDO::getAlgorithmType, reqVO.getAlgorithmType())
                .betweenIfPresent(GroupResultDO::getCreateTime, reqVO.getCreateTime())
                .orderByDesc(GroupResultDO::getId));
    }

    default GroupResultDO selectByGroupId(Long groupId) {
        return selectOne(new LambdaQueryWrapperX<GroupResultDO>()
                .eq(GroupResultDO::getGroupId, groupId));
    }

    default List<GroupResultDO> selectListByWinnerId(Long winnerId) {
        return selectList(new LambdaQueryWrapperX<GroupResultDO>()
                .eq(GroupResultDO::getWinnerId, winnerId)
                .orderByDesc(GroupResultDO::getCreateTime));
    }

    default List<GroupResultDO> selectListByPrizeId(Long prizeId) {
        return selectList(new LambdaQueryWrapperX<GroupResultDO>()
                .eq(GroupResultDO::getPrizeId, prizeId)
                .orderByDesc(GroupResultDO::getCreateTime));
    }

    default List<GroupResultDO> selectListByAlgorithmType(Integer algorithmType) {
        return selectList(new LambdaQueryWrapperX<GroupResultDO>()
                .eq(GroupResultDO::getAlgorithmType, algorithmType)
                .orderByDesc(GroupResultDO::getCreateTime));
    }

    default Long countByWinnerId(Long winnerId) {
        return selectCount(new LambdaQueryWrapperX<GroupResultDO>()
                .eq(GroupResultDO::getWinnerId, winnerId));
    }

    default Long countByPrizeId(Long prizeId) {
        return selectCount(new LambdaQueryWrapperX<GroupResultDO>()
                .eq(GroupResultDO::getPrizeId, prizeId));
    }
}
