package cn.iocoder.yudao.module.tv.controller.admin.video.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.ToString;

import javax.validation.constraints.NotNull;

@Schema(description = "管理后台 - 视频更新 Request VO")
@Data
@ToString(callSuper = true)
public class VideoUpdateStatusReqVO  {

    @Schema(description = "状态", required = true, example = "1")
    @NotNull(message = "状态不能为空")
    private Integer status;

    @Schema(description = "视频编号", required = true, example = "1024")
    @NotNull(message = "视频编号不能为空")
    private Long id;
} 