package cn.iocoder.yudao.module.tv.controller.app.comment.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDateTime;

@Schema(description = "用户 APP - 评论 Response VO")
@Data
public class AppCommentRespVO {

    @Schema(description = "评论编号", required = true, example = "1024")
    private Long id;

    @Schema(description = "用户编号", required = true, example = "2048")
    private Long userId;

    @Schema(description = "视频编号", required = true, example = "3072")
    private Long videoId;

    @Schema(description = "父评论编号", example = "4096")
    private Long parentId;

    @Schema(description = "评论内容", required = true, example = "真不错！")
    private String content;

    @Schema(description = "点赞数", required = true, example = "10")
    private Integer likeCount;

    @Schema(description = "回复数", required = true, example = "5")
    private Integer replyCount;

    @Schema(description = "创建时间", required = true)
    private LocalDateTime createTime;
} 