package cn.iocoder.yudao.module.tv.controller.admin.report.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

@Schema(description = "管理后台 - 举报统计 Response VO")
@Data
public class ReportStatisticsRespVO {

    @Schema(description = "总举报数", example = "100")
    private Long totalCount;

    @Schema(description = "待处理数", example = "20")
    private Long pendingCount;

    @Schema(description = "已处理数", example = "70")
    private Long processedCount;

    @Schema(description = "已驳回数", example = "10")
    private Long rejectedCount;

    @Schema(description = "按业务类型统计")
    private Map<String, Long> businessTypeStats;

    @Schema(description = "按举报类型统计")
    private Map<String, Long> reportTypeStats;

    @Schema(description = "按时间统计")
    private List<TimeStatItem> timeStats;

    @Schema(description = "时间统计项")
    @Data
    public static class TimeStatItem {
        @Schema(description = "时间", example = "2023-12-01")
        private String time;
        
        @Schema(description = "举报数量", example = "10")
        private Long count;
    }

}
