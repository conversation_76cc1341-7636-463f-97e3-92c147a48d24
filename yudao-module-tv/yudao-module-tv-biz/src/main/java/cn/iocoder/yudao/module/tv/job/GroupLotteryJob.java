package cn.iocoder.yudao.module.tv.job;

import cn.iocoder.yudao.framework.quartz.core.handler.JobHandler;
import cn.iocoder.yudao.framework.tenant.core.job.TenantJob;
import cn.iocoder.yudao.module.tv.service.group.GroupLotteryService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * 拼团抽奖定时任务
 *
 * <AUTHOR>
 */
@Component
@Slf4j
public class GroupLotteryJob implements JobHandler {

    @Resource
    private GroupLotteryService groupLotteryService;

    @Override
    @TenantJob
    public String execute(String param) {
        log.info("[execute][开始执行拼团抽奖定时任务]");
        
        int expiredCount = 0;
        int fullCount = 0;
        
        try {
            // 1. 处理超时拼团（退款）
            expiredCount = groupLotteryService.handleExpiredGroups();
            log.info("[execute][处理超时拼团完成] count={}", expiredCount);
            
            // 2. 处理满员拼团（开奖）
            fullCount = groupLotteryService.handleFullGroups();
            log.info("[execute][处理满员拼团完成] count={}", fullCount);
            
        } catch (Exception e) {
            log.error("[execute][拼团抽奖定时任务执行失败]", e);
            return "执行失败：" + e.getMessage();
        }
        
        String result = String.format("执行成功：处理超时拼团 %d 个，处理满员拼团 %d 个", expiredCount, fullCount);
        log.info("[execute][拼团抽奖定时任务执行完成] {}", result);
        return result;
    }
}
