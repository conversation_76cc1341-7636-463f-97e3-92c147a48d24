package cn.iocoder.yudao.module.tv.dal.mysql.message;

import cn.iocoder.yudao.framework.mybatis.core.query.LambdaQueryWrapperX;
import cn.iocoder.yudao.framework.mybatis.core.mapper.BaseMapperX;
import cn.iocoder.yudao.module.tv.dal.dataobject.message.LocalMessageDO;
import cn.iocoder.yudao.module.tv.enums.message.MessageStatusEnum;
import org.apache.ibatis.annotations.Mapper;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 本地消息表 Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface LocalMessageMapper extends BaseMapperX<LocalMessageDO> {

    /**
     * 查询待发送的消息
     *
     * @param limit 限制数量
     * @return 待发送消息列表
     */
    default List<LocalMessageDO> selectPendingMessages(int limit) {
        return selectList(new LambdaQueryWrapperX<LocalMessageDO>()
                .eq(LocalMessageDO::getStatus, MessageStatusEnum.PENDING.getStatus())
                .and(wrapper -> wrapper
                        .isNull(LocalMessageDO::getNextRetryTime)
                        .or()
                        .le(LocalMessageDO::getNextRetryTime, LocalDateTime.now()))
                .orderByAsc(LocalMessageDO::getCreateTime)
                .last("LIMIT " + limit));
    }

    /**
     * 查询失败需要重试的消息
     *
     * @param limit 限制数量
     * @return 需要重试的消息列表
     */
    default List<LocalMessageDO> selectRetryMessages(int limit) {
        return selectList(new LambdaQueryWrapperX<LocalMessageDO>()
                .eq(LocalMessageDO::getStatus, MessageStatusEnum.FAILED.getStatus())
                .apply("retry_count < max_retry_count") // 字段比较
                .le(LocalMessageDO::getNextRetryTime, LocalDateTime.now())
                .orderByAsc(LocalMessageDO::getNextRetryTime)
                .last("LIMIT " + limit));
    }

    /**
     * 根据消息ID查询消息
     *
     * @param messageId 消息ID
     * @return 消息
     */
    default LocalMessageDO selectByMessageId(String messageId) {
        return selectOne(new LambdaQueryWrapperX<LocalMessageDO>()
                .eq(LocalMessageDO::getMessageId, messageId));
    }

    /**
     * 查询指定主题的消息
     *
     * @param topic 主题
     * @param status 状态
     * @param limit 限制数量
     * @return 消息列表
     */
    default List<LocalMessageDO> selectByTopicAndStatus(String topic, Integer status, int limit) {
        return selectList(new LambdaQueryWrapperX<LocalMessageDO>()
                .eq(LocalMessageDO::getTopic, topic)
                .eq(LocalMessageDO::getStatus, status)
                .orderByAsc(LocalMessageDO::getCreateTime)
                .last("LIMIT " + limit));
    }

    /**
     * 清理过期的成功消息（超过指定天数）
     *
     * @param days 天数
     * @return 清理数量
     */
    default int deleteExpiredSuccessMessages(int days) {
        LocalDateTime expireTime = LocalDateTime.now().minusDays(days);
        return delete(new LambdaQueryWrapperX<LocalMessageDO>()
                .eq(LocalMessageDO::getStatus, MessageStatusEnum.SUCCESS.getStatus())
                .lt(LocalMessageDO::getUpdateTime, expireTime));
    }

}
