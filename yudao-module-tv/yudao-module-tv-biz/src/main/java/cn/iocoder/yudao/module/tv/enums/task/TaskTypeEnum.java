package cn.iocoder.yudao.module.tv.enums.task;

import cn.iocoder.yudao.framework.common.core.IntArrayValuable;
import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Arrays;

/**
 * 任务类型枚举
 *
 * <AUTHOR>
 */
@Getter
@AllArgsConstructor
public enum TaskTypeEnum implements IntArrayValuable {

    SHARE(1, "分享任务", "分享网站到微信群/微博并截图上传", 20),
    SHORT_VIDEO(2, "短视频任务", "发布关于网站使用体验的短视频截图", 40);

    public static final int[] ARRAYS = Arrays.stream(values()).mapToInt(TaskTypeEnum::getType).toArray();

    /**
     * 类型
     */
    private final Integer type;
    /**
     * 名字
     */
    private final String name;
    /**
     * 描述
     */
    private final String description;
    /**
     * 默认奖励积分
     */
    private final Integer defaultRewardPoints;

    @Override
    public int[] array() {
        return ARRAYS;
    }

    public static TaskTypeEnum valueOf(Integer type) {
        return Arrays.stream(values()).filter(item -> item.getType().equals(type)).findFirst().orElse(null);
    }

}
