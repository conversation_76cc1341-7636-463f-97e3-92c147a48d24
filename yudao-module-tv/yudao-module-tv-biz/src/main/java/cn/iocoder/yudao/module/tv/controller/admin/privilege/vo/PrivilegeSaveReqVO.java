package cn.iocoder.yudao.module.tv.controller.admin.privilege.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.math.BigDecimal;

@Schema(description = "管理后台 - 特权创建/更新 Request VO")
@Data
public class PrivilegeSaveReqVO {

    @Schema(description = "编号", example = "1")
    private Long id;

    @Schema(description = "特权名称", required = true, example = "VIP会员")
    @NotEmpty(message = "特权名称不能为空")
    private String name;

    @Schema(description = "特权描述", example = "开通后可以免费观看所有视频")
    private String description;

    @Schema(description = "价格", required = true, example = "99.00")
    @NotNull(message = "价格不能为空")
    private BigDecimal price;

    @Schema(description = "有效期（天）", required = true, example = "30")
    @NotNull(message = "有效期不能为空")
    private Integer duration;

    @Schema(description = "状态", required = true, example = "1")
    @NotNull(message = "状态不能为空")
    private Integer status;

} 