package cn.iocoder.yudao.module.tv.service.prize;

import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.module.tv.controller.admin.prize.vo.PrizePageReqVO;
import cn.iocoder.yudao.module.tv.dal.dataobject.prize.PrizeDO;

import java.util.List;

/**
 * 奖品 Service 接口
 *
 * <AUTHOR>
 */
public interface PrizeService {

    /**
     * 获得奖品
     *
     * @param id 编号
     * @return 奖品
     */
    PrizeDO getPrize(Long id);

    /**
     * 获得奖品列表
     *
     * @return 奖品列表
     */
    List<PrizeDO> getPrizeList();

    /**
     * 获得可用奖品列表
     *
     * @return 可用奖品列表
     */
    List<PrizeDO> getAvailablePrizes();

    /**
     * 获得奖品分页
     *
     * @param pageReqVO 分页查询
     * @return 奖品分页
     */
    PageResult<PrizeDO> getPrizePage(PrizePageReqVO pageReqVO);

    /**
     * 创建奖品
     *
     * @param prize 奖品信息
     * @return 编号
     */
    Long createPrize(PrizeDO prize);

    /**
     * 更新奖品
     *
     * @param prize 奖品信息
     */
    void updatePrize(PrizeDO prize);

    /**
     * 删除奖品
     *
     * @param id 编号
     */
    void deletePrize(Long id);

    /**
     * 减少奖品库存
     *
     * @param id 奖品ID
     * @param count 减少数量
     */
    void decreaseStock(Long id, Integer count);
}
