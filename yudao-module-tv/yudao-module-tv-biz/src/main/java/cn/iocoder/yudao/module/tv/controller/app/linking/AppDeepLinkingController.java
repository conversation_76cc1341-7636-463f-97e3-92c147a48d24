package cn.iocoder.yudao.module.tv.controller.app.linking;

import cn.hutool.core.util.RandomUtil;
import cn.hutool.core.util.StrUtil;
import cn.iocoder.yudao.framework.common.pojo.CommonResult;
import cn.iocoder.yudao.module.tv.controller.app.like.vo.AppLikeCreateReqVO;
import cn.iocoder.yudao.module.tv.controller.app.linking.vo.AppDeepLinkingCreateReqVO;
import cn.iocoder.yudao.module.tv.controller.app.linking.vo.AppDeepLinkingCreateRespVO;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.data.redis.core.BoundHashOperations;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.validation.Valid;

import java.time.Duration;
import java.util.Optional;

import static cn.iocoder.yudao.framework.common.pojo.CommonResult.success;
import static cn.iocoder.yudao.framework.web.core.util.WebFrameworkUtils.getLoginUserId;

/**
 * 这种叫：
 *
 * Deep Link（深度链接）
 *
 * 广义：指从外部入口（比如短链接、APP唤起链接）直达应用或网站的某个具体页面，而不是首页。
 *
 * 在 Web 上表现为：短链自动重定向到你网站的某个落地页（Landing Page）。
 *
 * 在 App 里：点链接直接打开 App 内的某个内容页。
 *
 * URL Redirection（URL 重定向 / 短链跳转）
 *
 * 技术层面专业叫法，就是 短链跳转/302 重定向。
 *
 * 短链接服务会查到目标地址，然后用 302/307 重定向带用户进入真正的页面。
 *
 * 所以：
 * 👉 从业务角度，叫 Deep Linking（深度链接）；
 * 👉 从实现角度，叫 短链重定向（URL Redirection）。
 */
@Tag(name = "用户 APP - 深度链接")
@RestController
@RequestMapping("/tv/deep-linking")
@Validated
public class AppDeepLinkingController {
    @Resource
    private StringRedisTemplate stringRedisTemplate;
    // 需要自己添加前缀的匹配内容。
    @PostMapping("/create")
    public CommonResult<AppDeepLinkingCreateRespVO> createLike(@Valid @RequestBody AppDeepLinkingCreateReqVO createReqVO, HttpServletRequest request) {

        String deepLinking = Optional.ofNullable(createReqVO.getLinking()).orElse( RandomUtil.randomString(8) );
        //
        BoundHashOperations<String, Object, Object> hashOperations = stringRedisTemplate.boundHashOps(deepLinking);
        hashOperations.put("appCode",createReqVO.getAppCode());
        hashOperations.put("redirectUrl",createReqVO.getRedirectUrl());
        hashOperations.put("inviter",StrUtil.toStringOrNull(getLoginUserId()));
        //
        hashOperations.expire(Duration.ofMinutes(createReqVO.getExpire()));
        //
        AppDeepLinkingCreateRespVO deepLinkingCreateRespVO = new AppDeepLinkingCreateRespVO();
        deepLinkingCreateRespVO.setLinking(deepLinking);
        deepLinkingCreateRespVO.setDeepLinkingUrl(getDomain(request) + "/" + deepLinking);
        return success(deepLinkingCreateRespVO);
    }

    /**
     * 浏览器设置的url
     * @param request
     * @return
     */
    public String getDomain(HttpServletRequest request) {
        String scheme = request.getScheme();          // http / https
        // 浏览器或 axios 调用时，HTTP 请求头里一定会有 Host :
        /*
        GET /tv/deep-linking/create HTTP/1.1
        Host: api.xxx.com
         */
        String host = request.getHeader("Host");      // api.xxx.com
        return scheme + "://" + host;                 // https://api.xxx.com
    }
}
