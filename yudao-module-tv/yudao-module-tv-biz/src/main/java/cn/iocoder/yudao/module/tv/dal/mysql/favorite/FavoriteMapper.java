package cn.iocoder.yudao.module.tv.dal.mysql.favorite;

import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.mybatis.core.mapper.BaseMapperX;
import cn.iocoder.yudao.framework.mybatis.core.query.LambdaQueryWrapperX;
import cn.iocoder.yudao.module.tv.controller.app.favorite.vo.AppFavoritePageReqVO;
import cn.iocoder.yudao.module.tv.controller.app.favorite.vo.AppFavoriteVideoPageReqVO;
import cn.iocoder.yudao.module.tv.dal.dataobject.favorite.FavoriteDO;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

@Mapper
public interface FavoriteMapper extends BaseMapperX<FavoriteDO> {

    default FavoriteDO selectByUserIdAndVideoId(Long userId, Long videoId) {
        return selectOne(new LambdaQueryWrapperX<FavoriteDO>()
                .eq(FavoriteDO::getUserId, userId)
                .eq(FavoriteDO::getVideoId, videoId));
    }

    default int deleteByUserIdAndVideoId(Long userId, Long videoId) {
        return delete(new LambdaQueryWrapperX<FavoriteDO>()
                .eq(FavoriteDO::getUserId, userId)
                .eq(FavoriteDO::getVideoId, videoId));
    }

    default List<FavoriteDO> selectListByUserId(Long userId) {
        return selectList(new LambdaQueryWrapperX<FavoriteDO>()
                .eq(FavoriteDO::getUserId, userId)
                .orderByDesc(FavoriteDO::getCreateTime));
    }

    default PageResult<FavoriteDO> selectPage(AppFavoritePageReqVO pageReqVO) {
        return selectPage(pageReqVO, new LambdaQueryWrapperX<FavoriteDO>()
                .eq(FavoriteDO::getUserId, pageReqVO.getUserId())
                .orderByDesc(FavoriteDO::getCreateTime));
    }

    /**
     * 根据用户编号和分页参数查询收藏分页
     *
     * @param userId 用户编号
     * @param pageReqVO 分页参数
     * @return 收藏分页
     */
    default PageResult<FavoriteDO> selectPageByUserId(Long userId, AppFavoriteVideoPageReqVO pageReqVO) {
        return selectPage(pageReqVO, new LambdaQueryWrapperX<FavoriteDO>()
                .eq(FavoriteDO::getUserId, userId)
                .orderByDesc(FavoriteDO::getCreateTime));
    }
}

