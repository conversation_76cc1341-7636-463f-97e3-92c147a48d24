package cn.iocoder.yudao.module.tv.enums.prize;

import cn.iocoder.yudao.framework.common.core.IntArrayValuable;
import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Arrays;

/**
 * 奖品状态枚举
 *
 * <AUTHOR>
 */
@Getter
@AllArgsConstructor
public enum PrizeStatusEnum implements IntArrayValuable {

    DISABLED(0, "禁用"),
    ENABLED(1, "启用");

    public static final int[] ARRAYS = Arrays.stream(values()).mapToInt(PrizeStatusEnum::getStatus).toArray();

    /**
     * 状态
     */
    private final Integer status;
    /**
     * 名字
     */
    private final String name;

    @Override
    public int[] array() {
        return ARRAYS;
    }

    public static PrizeStatusEnum valueOf(Integer status) {
        return Arrays.stream(values()).filter(item -> item.getStatus().equals(status)).findFirst().orElse(null);
    }
}
