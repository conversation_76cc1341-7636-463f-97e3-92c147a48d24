package cn.iocoder.yudao.module.tv.enums.task;

import cn.iocoder.yudao.framework.common.core.IntArrayValuable;
import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Arrays;

/**
 * 任务状态枚举
 *
 * <AUTHOR>
 */
@Getter
@AllArgsConstructor
public enum TaskStatusEnum implements IntArrayValuable {

    DISABLED(0, "禁用"),
    ENABLED(1, "启用");

    public static final int[] ARRAYS = Arrays.stream(values()).mapToInt(TaskStatusEnum::getStatus).toArray();

    /**
     * 状态
     */
    private final Integer status;
    /**
     * 名字
     */
    private final String name;

    @Override
    public int[] array() {
        return ARRAYS;
    }

    public static TaskStatusEnum valueOf(Integer status) {
        return Arrays.stream(values()).filter(item -> item.getStatus().equals(status)).findFirst().orElse(null);
    }

}
