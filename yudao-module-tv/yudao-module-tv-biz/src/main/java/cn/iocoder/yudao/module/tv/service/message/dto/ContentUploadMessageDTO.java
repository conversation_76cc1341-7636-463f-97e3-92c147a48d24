package cn.iocoder.yudao.module.tv.service.message.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Map;

/**
 * 内容上传消息DTO
 *
 * <AUTHOR>
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ContentUploadMessageDTO {

    /**
     * 内容ID
     */
    private Long contentId;

    /**
     * 配置ID
     */
    private Long configId;

    /**
     * 临时文件路径
     */
    private String tempFilePath;

    /**
     * 原始文件名
     */
    private String fileName;

    /**
     * 文件大小
     */
    private Long fileSize;

    /**
     * 扩展参数
     */
    private Map<String, Object> extParams;

    /**
     * 是否同步压缩
     */
    private Boolean syncCompress;

    /**
     * 压缩配置
     */
    private String compressConfig;

    /**
     * 内容类型
     */
    private Integer contentType;

}
