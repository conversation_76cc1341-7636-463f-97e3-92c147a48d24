package cn.iocoder.yudao.module.tv.controller.admin.video.vo;

import cn.iocoder.yudao.framework.common.pojo.PageParam;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

@Schema(description = "管理后台 - 视频分页 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class VideoPageReqVO extends PageParam {

    @Schema(description = "标题", example = "视频标题")
    private String title;

    @Schema(description = "分类编号", example = "1024")
    private Long categoryId;

    @Schema(description = "状态", example = "1")
    private Integer status;

    @Schema(description = "视频类型：0-单个视频，1-多个视频，2-图文混合", example = "0")
    private Integer type;
} 