package cn.iocoder.yudao.module.tv.service.content.strategy.impl;

import cn.iocoder.yudao.module.infra.api.deduplication.DeduplicationFileApi;
import cn.iocoder.yudao.module.infra.api.deduplication.dto.DeduplicationFileUploadReqDTO;
import cn.iocoder.yudao.module.infra.api.deduplication.dto.DeduplicationFileRespDTO;
import cn.iocoder.yudao.module.tv.dal.dataobject.content.ContentVideoDO;
import cn.iocoder.yudao.module.tv.dal.mysql.content.ContentVideoMapper;
import cn.iocoder.yudao.module.tv.service.content.strategy.ContentUploadStrategy;
import cn.iocoder.yudao.module.tv.config.TvWatermarkConfig;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.io.InputStream;
import java.util.HashMap;
import java.util.Map;

/**
 * 单个视频上传策略实现
 * 处理 type=0 的内容
 *
 * <AUTHOR>
 */
@Component
@Slf4j
public class SingleVideoUploadStrategy implements ContentUploadStrategy {

    @Resource
    private DeduplicationFileApi deduplicationFileApi;

    @Resource
    private ContentVideoMapper contentVideoMapper;

    @Resource
    private TvWatermarkConfig watermarkConfig;

    @Override
    public Integer getSupportedType() {
        return 0; // 单个视频
    }

    @Override
    public ContentUploadResult processContentFile(Long contentId, Long configId,
                                                InputStream contentInputStream,
                                                String fileName,
                                                Long fileSize,
                                                Map<String, Object> extParams,
                                                Boolean syncCompress,
                                                String compressConfig) {
        try {
            log.info("[SingleVideoUploadStrategy][开始处理单个视频上传] contentId: {}, fileName: {}, fileSize: {}", 
                contentId, fileName, fileSize);

            // 1. 上传视频文件到去重文件系统
            DeduplicationFileUploadReqDTO uploadReq = new DeduplicationFileUploadReqDTO();
            uploadReq.setName(fileName);
            uploadReq.setInputStream(contentInputStream);
            uploadReq.setContentLength(fileSize);
            uploadReq.setType(getVideoMimeType(fileName));
            uploadReq.setSyncCompress(syncCompress);
            uploadReq.setCompressType("MP4_COMPRESS"); // 视频压缩
            uploadReq.setCompressConfig(compressConfig);

            // 设置水印参数
            if (watermarkConfig.getEnabled() && watermarkConfig.getLogoPath() != null) {
                uploadReq.setWatermarkPath(watermarkConfig.getLogoPath());
                uploadReq.setWatermarkPosition(watermarkConfig.getPosition());
                uploadReq.setWatermarkOpacity(watermarkConfig.getOpacity());
                uploadReq.setWatermarkScale(watermarkConfig.getScale());
            }
            uploadReq.setConfigId(configId);

            DeduplicationFileRespDTO uploadResult = deduplicationFileApi.uploadFile(uploadReq);
            
            if (uploadResult == null || uploadResult.getUrl() == null) {
                return new ContentUploadResult(false, "视频文件上传失败");
            }

            // 2. 检查是否已存在视频记录
            ContentVideoDO existingVideo = contentVideoMapper.selectByContentId(contentId);
            
            if (existingVideo != null) {
                // 更新现有记录
                existingVideo.setVideoUrl(uploadResult.getUrl());
                existingVideo.setDuration(extractDurationFromParams(extParams));
                existingVideo.setResolution(extractResolutionFromParams(extParams));
                contentVideoMapper.updateById(existingVideo);
                
                log.info("[SingleVideoUploadStrategy][更新视频记录成功] contentId: {}, videoId: {}", 
                    contentId, existingVideo.getId());
            } else {
                // 创建新记录
                ContentVideoDO contentVideo = ContentVideoDO.builder()
                    .contentId(contentId)
                    .videoUrl(uploadResult.getUrl())
                    .duration(extractDurationFromParams(extParams))
                    .resolution(extractResolutionFromParams(extParams))
                    .build();
                contentVideoMapper.insert(contentVideo);
                
                log.info("[SingleVideoUploadStrategy][创建视频记录成功] contentId: {}, videoId: {}", 
                    contentId, contentVideo.getId());
            }

            // 3. 返回处理结果
            Map<String, Object> resultData = new HashMap<>();
            resultData.put("videoUrl", uploadResult.getUrl());
            resultData.put("fileSize", uploadResult.getSize());
            resultData.put("isCompressed", uploadResult.getIsCompressed());
            resultData.put("savedSpace", uploadResult.getSavedSpace());

            return new ContentUploadResult(true, "单个视频上传成功", resultData);

        } catch (Exception e) {
            log.error("[SingleVideoUploadStrategy][处理单个视频上传失败] contentId: {}, fileName: {}", 
                contentId, fileName, e);
            return new ContentUploadResult(false, "处理单个视频上传失败: " + e.getMessage());
        }
    }

    /**
     * 根据文件名获取视频MIME类型
     */
    private String getVideoMimeType(String fileName) {
        if (fileName == null) {
            return "video/mp4";
        }
        
        String extension = fileName.substring(fileName.lastIndexOf('.') + 1).toLowerCase();
        switch (extension) {
            case "mp4":
                return "video/mp4";
            case "avi":
                return "video/avi";
            case "mov":
                return "video/quicktime";
            case "wmv":
                return "video/x-ms-wmv";
            case "flv":
                return "video/x-flv";
            case "mkv":
                return "video/x-matroska";
            default:
                return "video/mp4";
        }
    }

    /**
     * 从扩展参数中提取视频时长
     */
    private Integer extractDurationFromParams(Map<String, Object> extParams) {
        if (extParams != null && extParams.containsKey("duration")) {
            Object duration = extParams.get("duration");
            if (duration instanceof Integer) {
                return (Integer) duration;
            } else if (duration instanceof String) {
                try {
                    return Integer.parseInt((String) duration);
                } catch (NumberFormatException e) {
                    log.warn("[SingleVideoUploadStrategy][解析视频时长失败] duration: {}", duration);
                }
            }
        }
        return null; // 如果没有提供时长，返回null
    }

    /**
     * 从扩展参数中提取视频分辨率
     */
    private String extractResolutionFromParams(Map<String, Object> extParams) {
        if (extParams != null && extParams.containsKey("resolution")) {
            Object resolution = extParams.get("resolution");
            if (resolution instanceof String) {
                return (String) resolution;
            }
        }
        return "1080p"; // 默认分辨率
    }

}
