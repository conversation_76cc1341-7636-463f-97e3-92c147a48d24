package cn.iocoder.yudao.module.tv.controller.app.history.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDateTime;

@Data
public class AppHistoryBaseVO {

    @Schema(description = "用户编号", required = true, example = "1024")
    private Long userId;

    @Schema(description = "视频编号", required = true, example = "2048")
    private Long videoId;

    @Schema(description = "观看进度", required = true, example = "100")
    private Integer progress;

    @Schema(description = "是否看完", required = true, example = "true")
    private Boolean finished;

    @Schema(description = "创建时间", required = true)
    private LocalDateTime createTime;

    @Schema(description = "更新时间", required = true)
    private LocalDateTime updateTime;
} 