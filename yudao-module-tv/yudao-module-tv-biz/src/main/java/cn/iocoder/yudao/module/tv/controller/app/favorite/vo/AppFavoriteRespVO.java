package cn.iocoder.yudao.module.tv.controller.app.favorite.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDateTime;

@Schema(description = "用户 APP - 收藏 Response VO")
@Data
public class AppFavoriteRespVO {

    @Schema(description = "收藏编号", required = true, example = "1024")
    private Long id;

    @Schema(description = "用户编号", required = true, example = "2048")
    private Long userId;

    @Schema(description = "视频编号", required = true, example = "3072")
    private Long videoId;

    @Schema(description = "创建时间", required = true)
    private LocalDateTime createTime;
} 