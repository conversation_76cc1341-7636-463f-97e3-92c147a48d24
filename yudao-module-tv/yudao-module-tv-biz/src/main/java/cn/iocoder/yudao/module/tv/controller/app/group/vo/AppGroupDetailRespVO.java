package cn.iocoder.yudao.module.tv.controller.app.group.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

@Schema(description = "用户 App - 拼团详情 Response VO")
@Data
public class AppGroupDetailRespVO {

    @Schema(description = "拼团ID", example = "1024")
    private Long id;

    @Schema(description = "发起人用户ID", example = "2048")
    private Long creatorId;

    @Schema(description = "发起人昵称", example = "张三")
    private String creatorNickname;

    @Schema(description = "发起人头像", example = "https://example.com/avatar.jpg")
    private String creatorAvatar;

    @Schema(description = "奖品ID", example = "3072")
    private Long prizeId;

    @Schema(description = "奖品名称", example = "iPhone 15")
    private String prizeName;

    @Schema(description = "奖品图片", example = "https://example.com/prize.jpg")
    private String prizeImage;

    @Schema(description = "奖品价值", example = "6999.00")
    private BigDecimal prizeValue;

    @Schema(description = "目标人数", example = "10")
    private Integer targetSize;

    @Schema(description = "当前参与人数", example = "7")
    private Integer currentSize;

    @Schema(description = "参团费用", example = "9.9")
    private BigDecimal entryFee;

    @Schema(description = "状态", example = "1")
    private Integer status;

    @Schema(description = "状态名称", example = "拼团中")
    private String statusName;

    @Schema(description = "中奖用户ID", example = "4096")
    private Long winnerId;

    @Schema(description = "中奖用户昵称", example = "李四")
    private String winnerNickname;

    @Schema(description = "随机种子", example = "abc123")
    private String randomSeed;

    @Schema(description = "开奖时间")
    private LocalDateTime drawTime;

    @Schema(description = "截止时间")
    private LocalDateTime expireTime;

    @Schema(description = "创建时间")
    private LocalDateTime createTime;

    @Schema(description = "是否已参与", example = "true")
    private Boolean hasJoined;

    @Schema(description = "参与用户列表")
    private List<ParticipantInfo> participants;

    @Schema(description = "参与用户信息")
    @Data
    public static class ParticipantInfo {
        @Schema(description = "用户ID", example = "1024")
        private Long userId;

        @Schema(description = "用户昵称", example = "王五")
        private String nickname;

        @Schema(description = "用户头像", example = "https://example.com/avatar.jpg")
        private String avatar;

        @Schema(description = "参与时间")
        private LocalDateTime joinTime;

        @Schema(description = "序号", example = "1")
        private Integer seqNo;
    }
}
