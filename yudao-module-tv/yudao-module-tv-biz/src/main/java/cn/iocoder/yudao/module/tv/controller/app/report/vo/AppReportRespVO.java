package cn.iocoder.yudao.module.tv.controller.app.report.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;

import java.time.LocalDateTime;

@Schema(description = "用户 App - 我的举报 Response VO")
@Data
public class AppReportRespVO {

    @Schema(description = "主键", requiredMode = Schema.RequiredMode.REQUIRED, example = "1024")
    private Long id;

    @Schema(description = "业务类型", requiredMode = Schema.RequiredMode.REQUIRED, example = "content")
    private String businessType;

    @Schema(description = "业务类型名称", example = "内容举报")
    private String businessTypeName;

    @Schema(description = "业务ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "1024")
    private Long businessId;

    @Schema(description = "举报类型", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
    private Integer reportType;

    @Schema(description = "举报类型名称", example = "色情低俗")
    private String reportTypeName;

    @Schema(description = "举报原因描述", example = "内容涉及色情低俗")
    private String reportReason;

    @Schema(description = "举报截图", example = "[\"http://example.com/image1.jpg\"]")
    private Object reportImages; // 可以是String(JSON)或List<String>

    @Schema(description = "处理状态", example = "0")
    private Integer status;

    @Schema(description = "处理状态名称", example = "待处理")
    private String statusName;

    @Schema(description = "处理结果说明", example = "经核实，该内容确实违规，已下架处理")
    private String handleResult;

    @Schema(description = "处理时间")
    private LocalDateTime handleTime;

    @Schema(description = "创建时间", requiredMode = Schema.RequiredMode.REQUIRED)
    private LocalDateTime createTime;

    @Schema(description = "业务对象信息", example = "被举报的内容标题等")
    private Object businessDetail;

}
