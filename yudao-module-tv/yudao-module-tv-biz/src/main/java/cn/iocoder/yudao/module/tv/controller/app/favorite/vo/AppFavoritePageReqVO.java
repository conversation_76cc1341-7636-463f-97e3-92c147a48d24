package cn.iocoder.yudao.module.tv.controller.app.favorite.vo;

import cn.iocoder.yudao.framework.common.pojo.PageParam;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

@Schema(description = "用户 APP - 收藏分页 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class AppFavoritePageReqVO extends PageParam {

    @Schema(description = "用户编号", required = true, example = "1024")
    private Long userId;
} 