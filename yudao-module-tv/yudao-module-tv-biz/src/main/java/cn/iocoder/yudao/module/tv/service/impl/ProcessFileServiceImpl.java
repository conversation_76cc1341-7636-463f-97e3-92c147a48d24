package cn.iocoder.yudao.module.tv.service.impl;

import cn.hutool.core.io.FileUtil;
import cn.iocoder.yudao.module.tv.service.ProcessService;
import cn.iocoder.yudao.module.tv.service.dto.VideoProcessResult;
import cn.iocoder.yudao.module.tv.util.FFmpegVideoUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.io.File;

/**
 * 本地File的方式来实现的。
 * 返回的结果集是绝对路径。需要程序再次进行处理的内容
 */
@Slf4j
@Service
public class ProcessFileServiceImpl implements ProcessService {

    @Override
    public VideoProcessResult processVideo(File videoFile) {
        VideoProcessResult result = new VideoProcessResult();

        // 1. 获取视频时长
        int duration = FFmpegVideoUtils.getDuration(videoFile);
        result.setDuration(duration);

        // 2. 生成首帧图
        File firstFrame = extractCoverImage(videoFile);
        if (firstFrame != null) {
            // 这里假设静态资源URL规则为 /static/video/，可根据实际情况调整
            result.setFirstFrameImageUrl(firstFrame.getAbsolutePath());
        }

        // 3. 优化MP4流式播放
        boolean optimized = FFmpegVideoUtils.optimizeMp4ForStreaming(videoFile);
        if (optimized) {
            result.setOptimizedMp4Url(videoFile.getAbsolutePath());
        }

        return result;
    }

    @Override
    public File optimizedImage(File imageFile) {
        try {
            if(!FileUtil.exist(imageFile)) return imageFile;

            log.info("[optimizedImage][开始优化图片: {}]", imageFile.getName());

            // 生成封面文件名
            File coverFile = getAvifFile(imageFile);

            // 如果封面文件已存在，直接返回
            if (coverFile.exists() && coverFile.length() > 0) {
                log.info("[optimizedImage][优化图片文件已存在: {}]", coverFile.getName());
                return coverFile;
            }

            // 使用FFmpeg提取第一帧
            File extractedFrame = FFmpegVideoUtils.optimizeImageToAvif(imageFile,null);
            if (extractedFrame != null) {
                try {
                    // 复制到目标位置
                    FileUtil.copyFile(extractedFrame, coverFile);
                    // 处理文件.
                    FFmpegVideoUtils.markFileAsProcessed(imageFile);
                    log.info("[optimizedImage][优化图片成功: {} -> {}]", imageFile.getName(), coverFile.getName());
                    return coverFile;
                } finally {
                    // 删除临时文件
                    if (extractedFrame.exists()) {
                        extractedFrame.delete();
                    }
                }
            } else {
                log.warn("[optimizedImage][优化图片失败: {}]", imageFile.getName());
                return imageFile;
            }


        } catch (Exception e) {
            log.error("[optimizedImage][优化图片异常: {}]", imageFile.getName(), e);
            return imageFile;
        }
    }

    /**
     * 提取视频第一帧作为封面图
     *
     * @param videoFile 视频文件
     * @return 封面图文件，失败返回null
     */
    @Override
    public File extractCoverImage(File videoFile) {
        try {
            log.info("[extractCoverImage][开始提取视频封面: {}]", videoFile.getName());

            // 生成封面文件名
            File coverFile = getAvifFile(videoFile);

            // 如果封面文件已存在，直接返回
            if (coverFile.exists() && coverFile.length() > 0) {
                log.info("[extractCoverImage][封面文件已存在: {}]", coverFile.getName());
                return coverFile;
            }

            // 使用FFmpeg提取第一帧
            File extractedFrame = FFmpegVideoUtils.extractFirstFrame(videoFile, null);
            if (extractedFrame != null) {
                try {
                    // 复制到目标位置
                    FileUtil.copyFile(extractedFrame, coverFile);
                    log.info("[extractCoverImage][封面提取成功: {} -> {}]", videoFile.getName(), coverFile.getName());
                    return coverFile;
                } finally {
                    // 删除临时文件
                    if (extractedFrame.exists()) {
                        extractedFrame.delete();
                    }
                }
            } else {
                log.warn("[extractCoverImage][封面提取失败: {}]", videoFile.getName());
                return null;
            }

        } catch (Exception e) {
            log.error("[extractCoverImage][封面提取异常: {}]", videoFile.getName(), e);
            return null;
        }
    }

    /**
     * 获得同名的，但是后缀名为 avif的文件
     * @param file
     * @return
     */
    private static File getAvifFile(File file) {
        String videoFileName = file.getName();
        String baseName = videoFileName.substring(0, videoFileName.lastIndexOf('.'));
        String coverFileName = baseName + ".avif";
        return new File(file.getParent(), coverFileName);
    }
} 