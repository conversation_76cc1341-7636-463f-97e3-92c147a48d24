package cn.iocoder.yudao.module.tv.service.privilege;

import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.module.tv.controller.admin.privilege.vo.PrivilegePageReqVO;
import cn.iocoder.yudao.module.tv.controller.admin.privilege.vo.PrivilegeSaveReqVO;
import cn.iocoder.yudao.module.tv.controller.app.privilege.vo.AppPrivilegeRespVO;
import cn.iocoder.yudao.module.tv.dal.dataobject.privilege.PrivilegeDO;

import javax.validation.Valid;

/**
 * 特权 Service 接口
 *
 * <AUTHOR>
 */
public interface PrivilegeService {

    /**
     * 创建特权
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    Long createPrivilege(@Valid PrivilegeSaveReqVO createReqVO);

    /**
     * 更新特权
     *
     * @param updateReqVO 更新信息
     */
    void updatePrivilege(@Valid PrivilegeSaveReqVO updateReqVO);

    /**
     * 删除特权
     *
     * @param id 编号
     */
    void deletePrivilege(Long id);

    /**
     * 获得特权
     *
     * @param id 编号
     * @return 特权
     */
    PrivilegeDO getPrivilege(Long id);

    /**
     * 获得特权分页
     *
     * @param pageReqVO 分页查询
     * @return 特权分页
     */
    PageResult<PrivilegeDO> getPrivilegePage(PrivilegePageReqVO pageReqVO);

    /**
     * 开通特权
     *
     * @param userId 用户编号
     * @param privilegeId 特权编号
     * @return 用户特权编号
     */
    Long openPrivilege(Long userId, Long privilegeId);

    /**
     * 检查用户是否有特权
     *
     * @param userId 用户编号
     * @return 是否有特权
     */
    boolean hasPrivilege(Long userId);

    /**
     * 获取用户特权信息
     *
     * @param userId 用户编号
     * @return 特权信息
     */
    AppPrivilegeRespVO getUserPrivilege(Long userId);

} 