package cn.iocoder.yudao.module.tv.dal.dataobject.prize;

import cn.iocoder.yudao.framework.mybatis.core.dataobject.BaseDO;
import cn.iocoder.yudao.framework.tenant.core.db.TenantBaseDO;
import com.baomidou.mybatisplus.annotation.KeySequence;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.ToString;

import java.math.BigDecimal;

/**
 * 奖品 DO
 *
 * <AUTHOR>
 */
@TableName("tv_prize")
@KeySequence("tv_prize_seq")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class PrizeDO extends BaseDO {

    /**
     * 奖品ID
     */
    @TableId
    private Long id;

    /**
     * 奖品名称
     */
    private String name;

    /**
     * 奖品描述
     */
    private String description;

    /**
     * 奖品图片
     */
    private String imageUrl;

    /**
     * 奖品类型：1-实物，2-虚拟（积分），3-虚拟（余额），4-优惠券
     * 
     * 枚举 {@link cn.iocoder.yudao.module.tv.enums.prize.PrizeTypeEnum}
     */
    private Integer type;

    /**
     * 奖品价值
     */
    private BigDecimal value;

    /**
     * 库存数量，-1表示无限制
     */
    private Integer stock;

    /**
     * 状态：0-禁用，1-启用
     * 
     * 枚举 {@link cn.iocoder.yudao.module.tv.enums.prize.PrizeStatusEnum}
     */
    private Integer status;

    /**
     * 排序
     */
    private Integer sort;
}
