package cn.iocoder.yudao.module.tv.controller.app.category;

import cn.iocoder.yudao.framework.common.pojo.CommonResult;
import cn.iocoder.yudao.module.tv.controller.app.category.vo.AppCategoryRespVO;
import cn.iocoder.yudao.module.tv.convert.category.AppCategoryConvert;
import cn.iocoder.yudao.module.tv.dal.dataobject.category.CategoryDO;
import cn.iocoder.yudao.module.tv.service.category.CategoryService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.annotation.security.PermitAll;
import java.util.List;

import static cn.iocoder.yudao.framework.common.pojo.CommonResult.success;

@Tag(name = "用户 APP - 视频分类")
@RestController
@RequestMapping("/tv/category")
@Validated
public class AppTvCategoryController {

    @Resource
    private CategoryService categoryService;

    @GetMapping("/list")
    @Operation(summary = "获得视频分类列表")
    @PermitAll
    public CommonResult<List<AppCategoryRespVO>> getCategoryList() {
        // 获取启用状态的分类列表
        List<CategoryDO> list = categoryService.getCategoryListByStatus(0); // 1 表示启用状态
        // 转换为响应 VO
        return success(AppCategoryConvert.INSTANCE.convertList(list));
    }

}
