package cn.iocoder.yudao.module.tv.convert.category;

import cn.iocoder.yudao.module.tv.controller.app.category.vo.AppCategoryRespVO;
import cn.iocoder.yudao.module.tv.dal.dataobject.category.CategoryDO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * 视频分类 Convert
 */
@Mapper
public interface AppCategoryConvert {

    AppCategoryConvert INSTANCE = Mappers.getMapper(AppCategoryConvert.class);

    AppCategoryRespVO convert(CategoryDO bean);

    List<AppCategoryRespVO> convertList(List<CategoryDO> list);

}
