package cn.iocoder.yudao.module.tv.dal.mysql.history;

import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.mybatis.core.mapper.BaseMapperX;
import cn.iocoder.yudao.framework.mybatis.core.query.LambdaQueryWrapperX;
import cn.iocoder.yudao.module.tv.controller.app.history.vo.AppHistoryPageReqVO;
import cn.iocoder.yudao.module.tv.controller.app.history.vo.AppHistoryVideoPageReqVO;
import cn.iocoder.yudao.module.tv.dal.dataobject.history.HistoryDO;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
 * 观看历史 Mapper
 */
@Mapper
public interface HistoryMapper extends BaseMapperX<HistoryDO> {

    default PageResult<HistoryDO> selectPage(AppHistoryPageReqVO pageReqVO) {
        return selectPage(pageReqVO, new LambdaQueryWrapperX<HistoryDO>()
                .eq(HistoryDO::getUserId, pageReqVO.getUserId())
                .orderByDesc(HistoryDO::getUpdateTime));
    }

    default List<HistoryDO> selectListByUserId(Long userId) {
        return selectList(new LambdaQueryWrapperX<HistoryDO>()
                .eq(HistoryDO::getUserId, userId)
                .orderByDesc(HistoryDO::getUpdateTime));
    }

    default HistoryDO selectByUserIdAndVideoId(Long userId, Long videoId) {
        return selectOne(new LambdaQueryWrapperX<HistoryDO>()
                .eq(HistoryDO::getUserId, userId)
                .eq(HistoryDO::getVideoId, videoId));
    }

    default void deleteByUserIdAndVideoId(Long userId, Long videoId) {
        delete(new LambdaQueryWrapperX<HistoryDO>()
                .eq(HistoryDO::getUserId, userId)
                .eq(HistoryDO::getVideoId, videoId));
    }

    default void deleteByUserId(Long userId) {
        delete(new LambdaQueryWrapperX<HistoryDO>()
                .eq(HistoryDO::getUserId, userId));
    }

    default List<HistoryDO> selectListByVideoId(Long videoId) {
        return selectList(new LambdaQueryWrapperX<HistoryDO>()
                .eq(HistoryDO::getVideoId, videoId)
                .orderByDesc(HistoryDO::getUpdateTime));
    }

    /**
     * 根据用户编号和分页参数查询观看历史分页
     *
     * @param userId 用户编号
     * @param pageReqVO 分页参数
     * @return 观看历史分页
     */
    default PageResult<HistoryDO> selectPageByUserId(Long userId, AppHistoryVideoPageReqVO pageReqVO) {
        return selectPage(pageReqVO, new LambdaQueryWrapperX<HistoryDO>()
                .eq(HistoryDO::getUserId, userId)
                .orderByDesc(HistoryDO::getUpdateTime));
    }
}