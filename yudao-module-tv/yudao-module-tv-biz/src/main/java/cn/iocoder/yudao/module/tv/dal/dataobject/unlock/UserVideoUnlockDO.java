package cn.iocoder.yudao.module.tv.dal.dataobject.unlock;

import cn.iocoder.yudao.framework.mybatis.core.dataobject.BaseDO;
import com.baomidou.mybatisplus.annotation.KeySequence;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import lombok.experimental.Accessors;

import java.time.LocalDateTime;

/**
 * 用户视频解锁记录 DO
 */
@TableName("tv_user_video_unlock")
@KeySequence("tv_user_video_unlock_seq")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Accessors(chain = true)
public class UserVideoUnlockDO extends BaseDO {

    /**
     * 编号
     */
    @TableId
    private Long id;

    /**
     * 用户编号
     */
    private Long userId;

    /**
     * 视频编号
     */
    private Long videoId;

    /**
     * 消费积分数
     */
    private Integer points;

    /**
     * 解锁时间
     */
    private LocalDateTime unlockTime;

}
