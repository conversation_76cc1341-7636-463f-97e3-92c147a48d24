package cn.iocoder.yudao.module.tv.controller.app.linking.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDateTime;

@Schema(description = "用户 APP - 深度链接 Response VO")
@Data
public class AppDeepLinkingCreateRespVO {

    @Schema(description = "短链接uuid", example = "userId+flag+serviceId")
    private String linking;

    @Schema(description = "完整url", required = true, example = "https://x.y/abc123")
    private String deepLinkingUrl;

} 