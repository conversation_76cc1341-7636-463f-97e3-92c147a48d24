package cn.iocoder.yudao.module.tv.util;

import cn.hutool.core.io.FileUtil;
import cn.hutool.core.io.file.PathUtil;
import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.StrUtil;
import lombok.extern.slf4j.Slf4j;

import javax.imageio.ImageIO;
import java.awt.image.BufferedImage;
import java.io.BufferedReader;
import java.io.File;
import java.io.InputStreamReader;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * 基于FFmpeg命令行的视频工具类
 *
 * 使用Hutool的RuntimeUtil执行FFmpeg命令
 */
@Slf4j
public class FFmpegVideoUtils {

    /**
     * 检查FFmpeg是否可用
     */
    private static final boolean FFMPEG_AVAILABLE;

    static {
        boolean available = false;
        try {
            // 使用简单的 ProcessBuilder 检查 FFmpeg 是否可用
            ProcessBuilder processBuilder = new ProcessBuilder("ffmpeg", "-version");
            processBuilder.redirectErrorStream(true);
            Process process = processBuilder.start();

            StringBuilder output = new StringBuilder();
            try (BufferedReader reader = new BufferedReader(new InputStreamReader(process.getInputStream()))) {
                String line;
                while ((line = reader.readLine()) != null) {
                    output.append(line).append("\n");
                }
            }

            int exitCode = process.waitFor();
            String version = output.toString();

            if (exitCode == 0 && StrUtil.isNotBlank(version) && version.contains("ffmpeg")) {
                available = true;
                log.info("[FFmpegVideoUtils][FFmpeg可用，版本信息: {}]", version.split("\\n")[0]);
            } else {
                log.warn("[FFmpegVideoUtils][FFmpeg不可用，退出码: {}, 输出: {}]", exitCode, version);
            }
        } catch (Exception e) {
            log.warn("[FFmpegVideoUtils][FFmpeg不可用: {}]", e.getMessage());
        }
        FFMPEG_AVAILABLE = available;
    }

    /**
     * 获取视频时长（秒）
     *
     * @param videoFile 视频文件
     * @return 视频时长（秒），如果获取失败则返回0
     */
    public static int getDuration(File videoFile) {
        if (videoFile == null || !videoFile.exists()) {
            return 0;
        }

        if (!FFMPEG_AVAILABLE) {
            log.warn("[getDuration][FFmpeg不可用，无法获取视频时长]");
            return 0;
        }

        try {
            // 使用自定义方法执行FFprobe命令获取视频时长
            String result = executeFFmpegCommand("ffprobe", "-v", "error", "-show_entries", "format=duration",
                    "-of", "default=noprint_wrappers=1:nokey=1", videoFile.getAbsolutePath());
            if (StrUtil.isBlank(result)) {
                log.warn("[getDuration][获取视频({})时长失败: 命令返回为空]", videoFile.getName());
                return 0;
            }

            // 解析时长（秒）
            try {
                double durationInSeconds = Double.parseDouble(result.trim());
                // 验证时长合理性
                if (durationInSeconds > 0 && durationInSeconds < 86400) { // 最大24小时
                    return (int) Math.round(durationInSeconds);
                } else {
                    log.warn("[getDuration][视频({})时长异常: {}秒]", videoFile.getName(), durationInSeconds);
                }
            } catch (NumberFormatException e) {
                log.warn("[getDuration][解析视频({})时长失败: {}]", videoFile.getName(), result.trim());
            }

            // 如果上面的方法失败，尝试使用另一种方式
            return getDurationAlternative(videoFile);
        } catch (Exception e) {
            log.error("[getDuration][获取视频({})时长异常]", videoFile.getName(), e);
            return 0;
        }
    }
    private static String quotePath(File videoFile){
//        return String.format("\"%s\"", videoFile.getAbsolutePath());
        return videoFile.getAbsolutePath();
    }
    /**
     * 使用替代方法获取视频时长
     *
     * @param videoFile 视频文件
     * @return 视频时长（秒）
     */
    private static int getDurationAlternative(File videoFile) {
        try {
            // 使用自定义方法执行FFmpeg命令获取视频信息
            String command = String.format("ffmpeg -i \"%s\" 2>&1 | grep \"Duration\"", videoFile.getAbsolutePath());
            String result = executeCommand(command);
            if (StrUtil.isBlank(result)) {
                log.warn("[getDurationAlternative][获取视频({})时长失败: 命令返回为空]", videoFile.getName());
                return 0;
            }

            // 解析时长，格式如: "Duration: 00:02:23.84, start: 0.000000, bitrate: 1069 kb/s"
            Pattern pattern = Pattern.compile("Duration: (\\d{2}):(\\d{2}):(\\d{2})\\.(\\d{2})");
            Matcher matcher = pattern.matcher(result);

            if (matcher.find()) {
                int hours = Integer.parseInt(matcher.group(1));
                int minutes = Integer.parseInt(matcher.group(2));
                int seconds = Integer.parseInt(matcher.group(3));

                int totalSeconds = hours * 3600 + minutes * 60 + seconds;
                log.info("[getDurationAlternative][视频({})时长: {}秒]", videoFile.getName(), totalSeconds);
                return totalSeconds;
            } else {
                log.warn("[getDurationAlternative][解析视频({})时长失败: {}]", videoFile.getName(), result);
                return 0;
            }
        } catch (Exception e) {
            log.error("[getDurationAlternative][获取视频({})时长异常]", videoFile.getName(), e);
            return 0;
        }
    }

    /**
     * 从视频中提取第一帧并保存为图片
     *
     * @param videoFile 视频文件
     * @param outputDir 输出目录，如果为null则使用系统临时目录
     * @return 提取的图片文件，如果提取失败则返回null
     */
    public static File extractFirstFrame(File videoFile, String outputDir) {
        if (videoFile == null || !videoFile.exists()) {
            return null;
        }

        if (!FFMPEG_AVAILABLE) {
            log.warn("[extractFirstFrame][FFmpeg不可用，无法提取视频帧]");
            return null;
        }

        // 如果输出目录为null，使用系统临时目录
        if (outputDir == null) {
            outputDir = System.getProperty("java.io.tmpdir");
        }

        // 确保输出目录存在
        File outputDirFile = new File(outputDir);
        if (!outputDirFile.exists()) {
            outputDirFile.mkdirs();
        }

        // 生成输出文件名
        String outputFileName = IdUtil.fastSimpleUUID() + ".avif";
        File outputFile = new File(outputDirFile, outputFileName);

        try {
            // 使用自定义方法执行FFmpeg命令提取第一帧
            // 先尝试提取1秒处的帧，避免黑屏
            executeFFmpegCommand("ffmpeg", "-y", "-ss", "00:00:01", "-i", quotePath(videoFile),
                    "-vframes", "1", "-q:v", "2", quotePath(outputFile) );

            // 检查输出文件是否有效
            if (outputFile.exists() && outputFile.length() > 0 && isImageFileComplete(outputFile)) {
                log.info("[extractFirstFrame][成功从视频({})提取1秒处的帧]", videoFile.getName());
                return outputFile;
            }

            // 如果1秒处提取失败，尝试提取第一帧
            executeFFmpegCommand("ffmpeg", "-y", "-i", quotePath(videoFile) ,
                    "-vframes", "1", "-q:v", "2", quotePath(outputFile));

            // 检查输出文件是否有效
//            if (outputFile.exists() && outputFile.length() > 0 && isImageFileComplete(outputFile)) {
            if (outputFile.exists() && outputFile.length() > 0) {
                log.info("[extractFirstFrame][成功从视频({})提取第一帧]", videoFile.getName());
                return outputFile;
            }

            log.warn("[extractFirstFrame][无法从视频({})提取帧]", videoFile.getName());
            return null;
        } catch (Exception e) {
            log.error("[extractFirstFrame][从视频({})提取帧失败]", videoFile.getName(), e);
            // 清理可能创建的无效文件
            if (outputFile.exists()) {
                FileUtil.del(outputFile);
            }
            return null;
        }
    }

    /**
     * 检查图片文件是否完整
     *
     * @param imageFile 图片文件
     * @return 是否完整
     */
    public static boolean isImageFileComplete(File imageFile) {
        if (imageFile == null || !imageFile.exists() || imageFile.length() == 0) {
            return false;
        }

        try {
            // 尝试读取图片，如果能成功读取则认为图片完整
            BufferedImage image = ImageIO.read(imageFile);
            return image != null && image.getWidth() > 0 && image.getHeight() > 0;
        } catch (Exception e) {
            log.warn("[isImageFileComplete][检查图片({})异常]", imageFile.getName(), e);
            return false;
        }
    }

    /**
     * 使用FFmpeg命令行检查视频文件是否完整
     *
     * 通过尝试解码整个视频文件来检测是否有损坏
     *
     * @param videoFile 视频文件
     * @return 是否完整
     */
    public static boolean isVideoFileComplete(File videoFile) {
        if (videoFile == null || !videoFile.exists() || videoFile.length() == 0) {
            log.warn("[isVideoFileComplete][视频文件[{}]为空/不存在]",videoFile);
            return false;
        }

        if (!FFMPEG_AVAILABLE) {
            log.warn("[isVideoFileComplete][FFmpeg不可用，无法检查视频完整性]");
            return false;
        }

        try {
            // 使用自定义方法执行FFmpeg命令检查视频完整性
            // 方案1: 使用多参数方式
            String result = executeFFmpegCommand("ffmpeg", "-v", "error", "-i",  quotePath(videoFile), "-f", "null", "-");

            // 如果没有错误输出，则视频完整
            if (StrUtil.isBlank(result)) {
                log.info("[isVideoFileComplete][视频({})完整性检查通过]", videoFile.getName());
                return true;
            } else {
                log.warn("[isVideoFileComplete][视频({})不完整，FFmpeg输出: {}]", videoFile.getName(), result);

                // 如果多参数方式失败，尝试使用 sh -c 方式
                log.info("[isVideoFileComplete][尝试使用 sh -c 方式重新检查视频: {}]", videoFile.getName());
                String command = String.format("ffmpeg -v error -i \"%s\" -f null -", videoFile.getAbsolutePath());
                String result2 = executeCommand(command);

                if (StrUtil.isBlank(result2)) {
                    log.info("[isVideoFileComplete][视频({})通过 sh -c 方式检查通过]", videoFile.getName());
                    return true;
                } else {
                    log.warn("[isVideoFileComplete][视频({})两种方式都检查失败，输出1: {}, 输出2: {}]",
                            videoFile.getName(), result, result2);
                    return false;
                }
            }
        } catch (Exception e) {
            log.error("[isVideoFileComplete][检查视频({})完整性异常]", videoFile.getName(), e);
            return false;
        }
    }

    /**
     * 检查视频文件是否完整可播放
     *
     * 首先检查视频是否完整，然后检查是否可播放
     *
     * @param videoFile 视频文件
     * @return 是否完整可播放
     */
    public static boolean isVideoFilePlayable(File videoFile) {

        if (!FFMPEG_AVAILABLE) {
            log.warn("[isVideoFilePlayable][FFmpeg不可用，无法检查视频是否可播放]");
            return false;
        }

        // 首先检查视频是否完整
        if (!isVideoFileComplete(videoFile)) {
            log.warn("[isVideoFilePlayable][视频({})不完整]", videoFile.getName());
            return false;
        }

        try {
            // 使用自定义方法执行FFprobe命令获取视频信息
            String result = executeFFmpegCommand("ffprobe", "-v", "error", "-select_streams", "v:0",
                    "-show_entries", "stream=width,height,codec_type", "-of", "csv=s=,:p=0", videoFile.getAbsolutePath());

            // 检查结果是否包含视频信息
            if (StrUtil.isBlank(result)) {
                log.warn("[isVideoFilePlayable][视频({})无法获取视频流信息]", videoFile.getName());
                return false;
            }

            // 解析结果，格式如: "video,1280,720"
            String[] parts = result.trim().split(",");
            if (parts.length < 3 || !"video".equals(parts[0])) {
                log.warn("[isVideoFilePlayable][视频({})不包含视频流: {}]", videoFile.getName(), result);
                return false;
            }

            // 检查分辨率
            try {
                int width = Integer.parseInt(parts[1]);
                int height = Integer.parseInt(parts[2]);

                if (width <= 0 || height <= 0) {
                    log.warn("[isVideoFilePlayable][视频({})分辨率异常: 宽度={}, 高度={}]",
                            videoFile.getName(), width, height);
                    return false;
                }
            } catch (NumberFormatException e) {
                log.warn("[isVideoFilePlayable][解析视频({})分辨率失败: {}]", videoFile.getName(), result);
                return false;
            }

            // 检查是否可以解码一些帧
            result = executeFFmpegCommand("ffmpeg", "-v", "error", "-i", quotePath(videoFile),
                    "-frames:v", "5", "-f", "null", "-");

            // 如果没有错误输出，则视频可播放
            if (StrUtil.isBlank(result)) {
                log.info("[isVideoFilePlayable][视频({})可播放性检查通过]", videoFile.getName());
                return true;
            } else {
                log.warn("[isVideoFilePlayable][视频({})不可播放: {}]", videoFile.getName(), result);
                return false;
            }
        } catch (Exception e) {
            log.error("[isVideoFilePlayable][检查视频({})可播放性异常]", videoFile.getName(), e);
            return false;
        }
    }

    /**
     * 自定义命令执行方法，避免 RuntimeUtil 的命令解析问题
     *
     * @param command 要执行的命令
     * @return 命令输出结果
     */
    private static String executeCommand(String command) {
        try {
            log.debug("[executeCommand][执行命令: {}]", command);

            // 使用 ProcessBuilder 执行命令
            ProcessBuilder processBuilder = new ProcessBuilder("sh", "-c", command);
            processBuilder.redirectErrorStream(true); // 将错误输出合并到标准输出

            Process process = processBuilder.start();

            // 读取输出
            StringBuilder output = new StringBuilder();
            try (BufferedReader reader = new BufferedReader(new InputStreamReader(process.getInputStream()))) {
                String line;
                while ((line = reader.readLine()) != null) {
                    output.append(line).append("\n");
                }
            }

            // 等待进程完成
            int exitCode = process.waitFor();
            String result = output.toString().trim();

            log.debug("[executeCommand][命令执行完成，退出码: {}, 输出: {}]", exitCode, result);
            return result;
        } catch (Exception e) {
            log.error("[executeCommand][命令执行异常: {}]", command, e);
            return "";
        }
    }

    /**
     * 使用多参数方式执行 FFmpeg 命令，避免命令解析问题
     *
     * @param args 命令参数数组
     * @return 命令输出结果
     */
    private static String executeFFmpegCommand(String... args) {
        try {
            log.debug("[executeFFmpegCommand][执行FFmpeg命令，参数: {}]", String.join(" ", args));

            // 使用 ProcessBuilder 执行命令
            ProcessBuilder processBuilder = new ProcessBuilder(args);
            processBuilder.redirectErrorStream(true); // 将错误输出合并到标准输出

            Process process = processBuilder.start();

            // 读取输出
            StringBuilder output = new StringBuilder();
            try (BufferedReader reader = new BufferedReader(new InputStreamReader(process.getInputStream()))) {
                String line;
                while ((line = reader.readLine()) != null) {
                    output.append(line).append("\n");
                }
            }

            // 等待进程完成
            int exitCode = process.waitFor();
            String result = output.toString().trim();

            log.debug("[executeFFmpegCommand][命令执行完成，退出码: {}, 输出: {}]", exitCode, result);
            return result;
        } catch (Exception e) {
            log.error("[executeFFmpegCommand][FFmpeg命令执行异常，参数: {}]", String.join(" ", args), e);
            return "";
        }
    }

    /**
     * 优化MP4文件，将moov box移动到文件开头，提升流式播放性能
     *
     * 注意：
     * 1. 直接在原始文件上进行修改，不产生新文件
     * 2. 原始文件的绝对路径不变
     * 3. moov box 移动到原始文件的开头
     *
     * @param mp4File MP4文件
     * @return 是否优化成功
     */
    public static boolean optimizeMp4ForStreaming(File mp4File) {
        if (mp4File == null || !mp4File.exists() || !mp4File.isFile()) {
            log.warn("[optimizeMp4ForStreaming][文件不存在或不是文件: {}]", mp4File);
            return false;
        }

        if (!mp4File.getName().toLowerCase().endsWith(".mp4")) {
            log.debug("[optimizeMp4ForStreaming][不是MP4文件，跳过优化: {}]", mp4File.getName());
            return true; // 不是MP4文件，认为优化成功
        }

        // 对于很小的文件（小于1MB），通常不需要优化，因为整个文件很快就能下载完
        long fileSize = mp4File.length();
        if (fileSize < 1024 * 1024) { // 1MB
            log.debug("[optimizeMp4ForStreaming][文件较小({} bytes)，跳过优化: {}]", fileSize, mp4File.getName());
            return true;
        }

        if (!FFMPEG_AVAILABLE) {
            log.warn("[optimizeMp4ForStreaming][FFmpeg不可用，无法优化MP4文件]");
            return false;
        }

        long startTime = System.currentTimeMillis();
        try {
            log.info("[optimizeMp4ForStreaming][开始优化MP4文件: {} ({}MB)]",
                mp4File.getName(), fileSize / 1024 / 1024);

            // 创建临时文件路径，使用.mp4扩展名让FFmpeg能正确识别格式
            String tempFilePath = mp4File.getAbsolutePath() + ".tmp.mp4";
            File tempFile = new File(tempFilePath);

            try {
                // 使用FFmpeg的qt-faststart功能优化MP4文件
                // -movflags faststart 将moov box移动到文件开头
                // -f mp4 显式指定输出格式
                executeFFmpegCommand("ffmpeg", "-y", "-i", quotePath(mp4File),
                        "-c", "copy", "-movflags", "faststart", "-f", "mp4", quotePath(tempFile));

                // 检查临时文件是否创建成功
                if (!tempFile.exists() || tempFile.length() == 0) {
                    log.error("[optimizeMp4ForStreaming][临时文件创建失败: {}]", tempFilePath);
                    return false;
                }

                // 简单检查临时文件大小是否合理（应该与原文件大小相近）
                long tempFileSize = tempFile.length();
                if (tempFileSize < fileSize * 0.8 || tempFileSize > fileSize * 1.2) {
                    log.warn("[optimizeMp4ForStreaming][临时文件大小异常: 原文件{}bytes, 临时文件{}bytes]",
                        fileSize, tempFileSize);
                    // 不直接返回失败，因为有些情况下文件大小可能会有变化
                }

                // 原子性替换原文件
                // 先备份原文件
                String backupPath = mp4File.getAbsolutePath() + ".backup";
                File backupFile = new File(backupPath);

                try {
                    // 重命名原文件为备份文件
                    if (!mp4File.renameTo(backupFile)) {
                        log.error("[optimizeMp4ForStreaming][无法创建备份文件: {}]", backupPath);
                        FileUtil.del(tempFile);
                        return false;
                    }

                    // 重命名临时文件为原文件
                    if (!tempFile.renameTo(mp4File)) {
                        log.error("[optimizeMp4ForStreaming][无法替换原文件，恢复备份: {}]", mp4File.getName());
                        // 恢复备份文件
                        backupFile.renameTo(mp4File);
                        FileUtil.del(tempFile);
                        return false;
                    }

                    // 删除备份文件
                    FileUtil.del(backupFile);

                    long endTime = System.currentTimeMillis();
                    long duration = endTime - startTime;
                    log.info("[optimizeMp4ForStreaming][MP4文件优化成功: {} (耗时{}ms)]",
                        mp4File.getName(), duration);
                    return true;

                } catch (Exception e) {
                    log.error("[optimizeMp4ForStreaming][文件替换过程中发生异常: {}]", mp4File.getName(), e);
                    // 尝试恢复
                    if (backupFile.exists()) {
                        backupFile.renameTo(mp4File);
                    }
                    FileUtil.del(tempFile);
                    return false;
                }

            } finally {
                // 清理临时文件
                if (tempFile.exists()) {
                    FileUtil.del(tempFile);
                }
            }

        } catch (Exception e) {
            log.error("[optimizeMp4ForStreaming][优化MP4文件失败: {}]", mp4File.getName(), e);
            return false;
        }
    }

    /**
     * 将MP4文件转换为M3U8格式
     * 先尝试流复制模式（快速），失败后使用压缩编码模式
     *
     * @param mp4File MP4文件
     * @return M3U8文件，如果转换失败则返回null
     */
    public static File convertMp4ToM3u8(File mp4File) {
        if (mp4File == null || !mp4File.exists() || !mp4File.isFile()) {
            log.warn("[convertMp4ToM3u8][文件不存在或不是文件: {}]", mp4File);
            return null;
        }

        if (!mp4File.getName().toLowerCase().endsWith(".mp4")) {
            log.debug("[convertMp4ToM3u8][不是MP4文件，跳过转换: {}]", mp4File.getName());
            return null;
        }

        if (!FFMPEG_AVAILABLE) {
            log.warn("[convertMp4ToM3u8][FFmpeg不可用，无法转换视频]");
            return null;
        }

        long startTime = System.currentTimeMillis();
        long fileSize = mp4File.length();

        try {
            log.info("[convertMp4ToM3u8][开始转换MP4为M3U8: {} ({}MB)]",
                mp4File.getName(), fileSize / 1024 / 1024);

            // 创建输出目录和文件
            String mp4FileName = mp4File.getName();
            String baseName = mp4FileName.substring(0, mp4FileName.lastIndexOf('.'));
            File outputDir = new File(mp4File.getParent(), baseName);

            // 确保输出目录存在
            if (!outputDir.exists()) {
                outputDir.mkdirs();
            }

            File m3u8File = new File(outputDir, "index.m3u8");

            // 如果M3U8文件已存在且有效，直接返回
            if (m3u8File.exists() && m3u8File.length() > 0) {
                log.info("[convertMp4ToM3u8][M3U8文件已存在: {}]", m3u8File.getAbsolutePath());
                return m3u8File;
            }

            String segmentPattern = new File(outputDir, "segment_%03d.ts").getAbsolutePath();

            // 第一次尝试：使用流复制模式（快速）
            log.info("[convertMp4ToM3u8][尝试流复制模式转换: {}]", mp4File.getName());
            boolean streamCopySuccess = convertWithStreamCopy(mp4File, m3u8File, segmentPattern);

            if (streamCopySuccess) {
                long endTime = System.currentTimeMillis();
                long duration = endTime - startTime;
                log.info("[convertMp4ToM3u8][流复制模式转换成功: {} (耗时{}ms)]", mp4File.getName(), duration);
                return m3u8File;
            }

            // 第二次尝试：使用压缩编码模式
//            log.warn("[convertMp4ToM3u8][流复制模式失败，尝试压缩编码模式: {}]", mp4File.getName());
//            boolean compressSuccess = convertWithCompression(mp4File, m3u8File, segmentPattern);
//
//            if (compressSuccess) {
//                long endTime = System.currentTimeMillis();
//                long duration = endTime - startTime;
//                log.info("[convertMp4ToM3u8][压缩编码模式转换成功: {} (耗时{}ms)]", mp4File.getName(), duration);
//                return m3u8File;
//            }

            log.error("[convertMp4ToM3u8][两种模式都转换失败: {}]", mp4File.getName());
            return null;

        } catch (Exception e) {
            log.error("[convertMp4ToM3u8][转换MP4为M3U8失败: {}]", mp4File.getName(), e);
            return null;
        }
    }

    /**
     * 使用流复制模式转换（快速，不重新编码）
     */
    private static boolean convertWithStreamCopy(File mp4File, File m3u8File, String segmentPattern) {
        try {
            // 清理可能存在的文件
            if (m3u8File.exists()) {
                m3u8File.delete();
            }

            // 使用流复制模式，跳过重新编码
            executeFFmpegCommand("ffmpeg", "-y", "-i", quotePath(mp4File),
                    "-c", "copy",  // 流复制，不重新编码
                    "-hls_time", "10",
                    "-hls_list_size", "0",
                    "-hls_segment_filename", segmentPattern,
                    "-f", "hls", quotePath(m3u8File));

            // 检查转换结果
            if (!m3u8File.exists() || m3u8File.length() == 0) {
                log.warn("[convertWithStreamCopy][流复制模式转换失败，M3U8文件未生成]");
                return false;
            }

            // 检查是否有TS片段文件
            File outputDir = m3u8File.getParentFile();
            File[] tsFiles = outputDir.listFiles((dir, name) -> name.endsWith(".ts"));
            if (tsFiles == null || tsFiles.length == 0) {
                log.warn("[convertWithStreamCopy][流复制模式转换失败，没有生成TS片段文件]");
                return false;
            }

            log.info("[convertWithStreamCopy][流复制模式转换成功，生成{}个TS片段]", tsFiles.length);
            return true;

        } catch (Exception e) {
            log.warn("[convertWithStreamCopy][流复制模式转换异常: {}]", e.getMessage());
            return false;
        }
    }

    /**
     * 使用压缩编码模式转换（压缩体积，降低清晰度）
     */
    private static boolean convertWithCompression(File mp4File, File m3u8File, String segmentPattern) {
        try {
            // 清理可能存在的文件
            if (m3u8File.exists()) {
                m3u8File.delete();
            }

            // 使用压缩编码模式，优先考虑体积压缩
            executeFFmpegCommand("ffmpeg", "-y", "-i", quotePath(mp4File),
                    "-c:v", "libx264",
                    "-preset", "fast",        // 快速编码预设
                    "-crf", "28",            // 较高的CRF值，降低质量但减小体积
                    "-maxrate", "1000k",     // 最大码率1000k
                    "-bufsize", "2000k",     // 缓冲区大小
                    "-c:a", "aac",
                    "-b:a", "64k",           // 音频码率64k，降低音频质量
                    "-hls_time", "10",
                    "-hls_list_size", "0",
                    "-hls_segment_filename", segmentPattern,
                    "-f", "hls", quotePath(m3u8File));

            // 检查转换结果
            if (!m3u8File.exists() || m3u8File.length() == 0) {
                log.error("[convertWithCompression][压缩编码模式转换失败，M3U8文件未生成]");
                return false;
            }

            // 检查是否有TS片段文件
            File outputDir = m3u8File.getParentFile();
            File[] tsFiles = outputDir.listFiles((dir, name) -> name.endsWith(".ts"));
            if (tsFiles == null || tsFiles.length == 0) {
                log.error("[convertWithCompression][压缩编码模式转换失败，没有生成TS片段文件]");
                return false;
            }

            log.info("[convertWithCompression][压缩编码模式转换成功，生成{}个TS片段]", tsFiles.length);
            return true;

        } catch (Exception e) {
            log.error("[convertWithCompression][压缩编码模式转换异常]", e);
            return false;
        }
    }

    /**
     * 将文件长度设置为1字节（标记为已处理）
     *
     * @param mp4File MP4文件
     * @return 是否设置成功
     */
    public static boolean markFileAsProcessed(File mp4File) {
        if (mp4File == null || !mp4File.exists()) {
            log.warn("[markMp4AsProcessed][文件不存在: {}]", mp4File);
            return false;
        }

        try {
            // 将文件内容截断为1字节
            try (java.io.RandomAccessFile raf = new java.io.RandomAccessFile(mp4File, "rw")) {
                raf.setLength(1);
                raf.writeByte(0); // 写入一个字节
            }

            log.info("[markMp4AsProcessed][MP4文件已标记为已处理: {}]", mp4File.getName());
            return true;

        } catch (Exception e) {
            log.error("[markMp4AsProcessed][标记MP4文件失败: {}]", mp4File.getName(), e);
            return false;
        }
    }

    /**
     * 检查文件是否已被处理（文件大小为1字节）
     *
     * @param mp4File MP4文件
     * @return 是否已处理
     */
    public static boolean isFileProcessed(File mp4File) {
        if (mp4File == null || !mp4File.exists()) {
            return false;
        }

        return mp4File.length() == 1;
    }

    /**
     * 将MP4文件转换为M3U8格式（多码率版本）
     *
     * @param mp4File MP4文件
     * @return M3U8文件，如果转换失败则返回null
     */
    public static File convertMp4ToM3u8WithMultipleBitrates(File mp4File) {
        if (mp4File == null || !mp4File.exists() || !mp4File.isFile()) {
            log.warn("[convertMp4ToM3u8WithMultipleBitrates][文件不存在或不是文件: {}]", mp4File);
            return null;
        }

        if (!mp4File.getName().toLowerCase().endsWith(".mp4")) {
            log.debug("[convertMp4ToM3u8WithMultipleBitrates][不是MP4文件，跳过转换: {}]", mp4File.getName());
            return null;
        }

        if (!FFMPEG_AVAILABLE) {
            log.warn("[convertMp4ToM3u8WithMultipleBitrates][FFmpeg不可用，无法转换视频]");
            return null;
        }

        long startTime = System.currentTimeMillis();
        long fileSize = mp4File.length();

        try {
            log.info("[convertMp4ToM3u8WithMultipleBitrates][开始转换MP4为多码率M3U8: {} ({}MB)]",
                mp4File.getName(), fileSize / 1024 / 1024);

            // 创建输出目录和文件
            String mp4FileName = mp4File.getName();
            String baseName = mp4FileName.substring(0, mp4FileName.lastIndexOf('.'));
            File outputDir = new File(mp4File.getParent(), baseName);

            // 确保输出目录存在
            if (!outputDir.exists()) {
                outputDir.mkdirs();
            }

            File masterM3u8File = new File(outputDir, "master.m3u8");

            // 如果主M3U8文件已存在且有效，直接返回
            if (masterM3u8File.exists() && masterM3u8File.length() > 0) {
                log.info("[convertMp4ToM3u8WithMultipleBitrates][主M3U8文件已存在: {}]", masterM3u8File.getAbsolutePath());
                return masterM3u8File;
            }

            // 创建不同码率的子目录
            File dir720p = new File(outputDir, "720p");
            File dir480p = new File(outputDir, "480p");
            File dir360p = new File(outputDir, "360p");

            dir720p.mkdirs();
            dir480p.mkdirs();
            dir360p.mkdirs();

            // 转换720p
            String segment720p = new File(dir720p, "segment_%03d.ts").getAbsolutePath();
            File m3u8_720p = new File(dir720p, "index.m3u8");
            executeFFmpegCommand("ffmpeg", "-y", "-i", quotePath(mp4File),
                    "-c:v", "libx264", "-c:a", "aac",
                    "-b:v", "2500k", "-b:a", "128k",
                    "-s", "1280x720",
                    "-hls_time", "10",
                    "-hls_list_size", "0",
                    "-hls_segment_filename", segment720p,
                    "-f", "hls", quotePath(m3u8_720p));

            // 转换480p
            String segment480p = new File(dir480p, "segment_%03d.ts").getAbsolutePath();
            File m3u8_480p = new File(dir480p, "index.m3u8");
            executeFFmpegCommand("ffmpeg", "-y", "-i", quotePath(mp4File),
                    "-c:v", "libx264", "-c:a", "aac",
                    "-b:v", "1500k", "-b:a", "128k",
                    "-s", "854x480",
                    "-hls_time", "10",
                    "-hls_list_size", "0",
                    "-hls_segment_filename", segment480p,
                    "-f", "hls", quotePath(m3u8_480p));

            // 转换360p
            String segment360p = new File(dir360p, "segment_%03d.ts").getAbsolutePath();
            File m3u8_360p = new File(dir360p, "index.m3u8");
            executeFFmpegCommand("ffmpeg", "-y", "-i", quotePath(mp4File),
                    "-c:v", "libx264", "-c:a", "aac",
                    "-b:v", "800k", "-b:a", "96k",
                    "-s", "640x360",
                    "-hls_time", "10",
                    "-hls_list_size", "0",
                    "-hls_segment_filename", segment360p,
                    "-f", "hls", quotePath(m3u8_360p));

            // 创建主播放列表
            StringBuilder masterPlaylist = new StringBuilder();
            masterPlaylist.append("#EXTM3U\n");
            masterPlaylist.append("#EXT-X-VERSION:3\n");

            if (m3u8_720p.exists()) {
                masterPlaylist.append("#EXT-X-STREAM-INF:BANDWIDTH=2628000,RESOLUTION=1280x720\n");
                masterPlaylist.append("720p/index.m3u8\n");
            }

            if (m3u8_480p.exists()) {
                masterPlaylist.append("#EXT-X-STREAM-INF:BANDWIDTH=1628000,RESOLUTION=854x480\n");
                masterPlaylist.append("480p/index.m3u8\n");
            }

            if (m3u8_360p.exists()) {
                masterPlaylist.append("#EXT-X-STREAM-INF:BANDWIDTH=896000,RESOLUTION=640x360\n");
                masterPlaylist.append("360p/index.m3u8\n");
            }

            // 写入主播放列表文件
            FileUtil.writeUtf8String(masterPlaylist.toString(), masterM3u8File);

            // 检查转换结果
            if (!masterM3u8File.exists() || masterM3u8File.length() == 0) {
                log.error("[convertMp4ToM3u8WithMultipleBitrates][主M3U8文件创建失败: {}]", masterM3u8File.getAbsolutePath());
                return null;
            }

            long endTime = System.currentTimeMillis();
            long duration = endTime - startTime;
            log.info("[convertMp4ToM3u8WithMultipleBitrates][MP4转多码率M3U8成功: {} -> {} (耗时{}ms)]",
                mp4File.getName(), masterM3u8File.getAbsolutePath(), duration);

            return masterM3u8File;

        } catch (Exception e) {
            log.error("[convertMp4ToM3u8WithMultipleBitrates][转换MP4为多码率M3U8失败: {}]", mp4File.getName(), e);
            return null;
        }
    }

    /**
     * 使用FFmpeg将图片（如jpg/png）优化为avif格式
     * @param imageFile 原始图片文件
     * @param outputDir 输出目录（为null则用系统临时目录）
     * @return 优化后的avif图片文件，失败返回null
     */
    public static File optimizeImageToAvif(File imageFile, String outputDir) {
        if (imageFile == null || !imageFile.exists()) {
            return null;
        }
        if (!FFMPEG_AVAILABLE) {
            log.warn("[optimizeImageToAvif][FFmpeg不可用，无法优化图片]");
            return null;
        }
        // 输出目录
        if (outputDir == null) {
            outputDir = System.getProperty("java.io.tmpdir");
        }
        File outputDirFile = new File(outputDir);
        if (!outputDirFile.exists()) {
            outputDirFile.mkdirs();
        }
        // 输出文件名
        String baseName = imageFile.getName();
        int dotIdx = baseName.lastIndexOf('.');
        if (dotIdx > 0) baseName = baseName.substring(0, dotIdx);
        String outputFileName = baseName + "_optimized.avif";
        File outputFile = new File(outputDirFile, outputFileName);
        try {
            executeFFmpegCommand("ffmpeg", "-y", "-i", quotePath(imageFile),
                    "-vf" ,"scale='if(gt(iw,2048),2048,iw)':-1" // 图片太大了（16464x10976，约 180MP 超高清），系统在转码过程中内存耗尽，被操作系统“强制终止”了 ==》〉 加缩放，如果原始宽度超过 4096，就缩小；否则保持原尺寸
                    , quotePath(outputFile) );
            if (outputFile.exists() && outputFile.length() > 0 ) {
                log.info("[optimizeImageToAvif][图片({})优化为avif成功]", imageFile.getName());
                return outputFile;
            } else {
                log.warn("[optimizeImageToAvif][图片({})优化为avif失败]", imageFile.getName());
                if (outputFile.exists()) FileUtil.del(outputFile);
                return null;
            }
        } catch (Exception e) {
            log.error("[optimizeImageToAvif][图片({})优化为avif异常]", imageFile.getName(), e);
            if (outputFile.exists()) FileUtil.del(outputFile);
            return null;
        }
    }

    /**
     * 压缩视频文件
     * @param inputFile 输入视频文件
     * @param outputFile 输出视频文件
     * @param videoBitrate 视频码率（kbps）
     * @param audioBitrate 音频码率（kbps）
     * @param videoScale 视频分辨率缩放（如"1280x720"）
     * @return 是否成功
     */
    public static boolean compressVideo(File inputFile, File outputFile, Integer videoBitrate, Integer audioBitrate, String videoScale) {
        if (inputFile == null || !inputFile.exists() || outputFile == null) {
            return false;
        }
        if (!FFMPEG_AVAILABLE) {
            log.warn("[compressVideo][FFmpeg不可用，无法压缩视频]");
            return false;
        }

        try {
            // 构建FFmpeg命令
            String[] command = buildCompressVideoCommand(inputFile, outputFile, videoBitrate, audioBitrate, videoScale);
            executeFFmpegCommand(command);

            if (outputFile.exists() && outputFile.length() > 0) {
                log.info("[compressVideo][视频压缩成功] input: {}, output: {}", inputFile.getName(), outputFile.getName());
                return true;
            } else {
                log.warn("[compressVideo][视频压缩失败，输出文件不存在或为空]");
                return false;
            }
        } catch (Exception e) {
            log.error("[compressVideo][视频压缩异常] input: {}", inputFile.getName(), e);
            return false;
        }
    }

    /**
     * 构建视频压缩命令
     */
    private static String[] buildCompressVideoCommand(File inputFile, File outputFile, Integer videoBitrate, Integer audioBitrate, String videoScale) {
        java.util.List<String> command = new java.util.ArrayList<>();
        command.add("ffmpeg");
        command.add("-y"); // 覆盖输出文件
        command.add("-i");
        command.add(quotePath(inputFile));

        // 视频编码参数
        command.add("-c:v");
        command.add("libx264");
        command.add("-preset");
        command.add("fast");

        // 视频码率
        if (videoBitrate != null && videoBitrate > 0) {
            command.add("-b:v");
            command.add(videoBitrate + "k");
        }

        // 视频分辨率
        if (videoScale != null && !videoScale.trim().isEmpty()) {
            command.add("-s");
            command.add(videoScale);
        }

        // 音频编码参数
        command.add("-c:a");
        command.add("aac");

        // 音频码率
        if (audioBitrate != null && audioBitrate > 0) {
            command.add("-b:a");
            command.add(audioBitrate + "k");
        }

        command.add(quotePath(outputFile));

        return command.toArray(new String[0]);
    }

    /**
     * 转换图片为WebP格式
     * @param inputFile 输入图片文件
     * @param outputFile 输出WebP文件
     * @param quality 质量（1-100）
     * @return 是否成功
     */
    public static boolean convertImageToWebp(File inputFile, File outputFile, Integer quality) {
        if (inputFile == null || !inputFile.exists() || outputFile == null) {
            return false;
        }
        if (!FFMPEG_AVAILABLE) {
            log.warn("[convertImageToWebp][FFmpeg不可用，无法转换图片]");
            return false;
        }

        try {
            executeFFmpegCommand("ffmpeg", "-y", "-i", quotePath(inputFile),
                    "-c:v", "libwebp",
                    "-quality", String.valueOf(quality != null ? quality : 80),
                    "-preset", "default",
                    quotePath(outputFile));

            if (outputFile.exists() && outputFile.length() > 0) {
                log.info("[convertImageToWebp][图片转WebP成功] input: {}, output: {}", inputFile.getName(), outputFile.getName());
                return true;
            } else {
                log.warn("[convertImageToWebp][图片转WebP失败，输出文件不存在或为空]");
                return false;
            }
        } catch (Exception e) {
            log.error("[convertImageToWebp][图片转WebP异常] input: {}", inputFile.getName(), e);
            return false;
        }
    }

}
