package cn.iocoder.yudao.module.tv.service.message;

import cn.hutool.core.exceptions.ExceptionUtil;
import cn.hutool.core.util.StrUtil;
import cn.iocoder.yudao.framework.tenant.core.job.TenantJob;
import cn.iocoder.yudao.framework.tenant.core.service.TenantFrameworkService;
import cn.iocoder.yudao.framework.tenant.core.util.TenantUtils;
import cn.iocoder.yudao.module.system.api.tenant.TenantApi;
import cn.iocoder.yudao.module.tv.dal.dataobject.message.LocalMessageDO;
import cn.iocoder.yudao.module.tv.service.message.handler.MessageHandler;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.CommandLineRunner;
import org.springframework.scheduling.annotation.Async;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.atomic.AtomicBoolean;

/**
 * 异步消息处理服务
 *
 * <AUTHOR>
 */
@Service
@Slf4j
public class AsyncMessageProcessService {

    @Resource
    private LocalMessageService localMessageService;

    @Resource
    private List<MessageHandler> messageHandlers;

    @Resource
    private TenantApi tenantApi;

    // 消息处理器映射表
    private final Map<String, MessageHandler> handlerMap = new HashMap<>();

    // 服务运行状态
    private final AtomicBoolean running = new AtomicBoolean(false);

    @PostConstruct
    public void initHandlers() {
        for (MessageHandler handler : messageHandlers) {
            handlerMap.put(handler.getSupportedMessageType(), handler);
            log.info("[AsyncMessageProcessService][注册消息处理器] type: {}, handler: {}", 
                handler.getSupportedMessageType(), handler.getClass().getSimpleName());
        }
        running.set(true);
    }
    /**
     * 定时处理待发送和重试消息
     */
    @Scheduled(fixedDelay = 30000) // 每30秒执行一次
    @TenantJob
    public void scheduledProcessMessages() {
        if (!running.get()) {
            return;
        }
        try {
            // 处理待发送消息
            processPendingMessages();
            
            // 处理重试消息
            processRetryMessages();
            
        } catch (Exception e) {
            log.error("[scheduledProcessMessages][定时处理消息异常]", e);
        }
    }

    /**
     * 定时清理过期成功消息
     */
    @Scheduled(cron = "0 0 2 * * ?") // 每天凌晨2点执行
    @TenantJob
    public void cleanExpiredMessages() {
        try {
            int count = localMessageService.cleanExpiredSuccessMessages(7); // 保留7天
            log.info("[cleanExpiredMessages][清理过期消息完成] count: {}", count);
        } catch (Exception e) {
            log.error("[cleanExpiredMessages][清理过期消息异常]", e);
        }
    }

    /**
     * 加载并处理待处理消息（应用启动时调用）
     */
    
    public void loadAndProcessPendingMessages() {
        CompletableFuture.runAsync(() -> {
            try {
                log.info("[loadAndProcessPendingMessages][开始加载待处理消息]");
                
                // 处理待发送消息
                List<LocalMessageDO> pendingMessages = localMessageService.getPendingMessages(100);
                log.info("[loadAndProcessPendingMessages][加载待发送消息] count: {}", pendingMessages.size());
                
                for (LocalMessageDO message : pendingMessages) {
                    processMessageAsync(message);
                }
                
                // 处理重试消息
                List<LocalMessageDO> retryMessages = localMessageService.getRetryMessages(100);
                log.info("[loadAndProcessPendingMessages][加载重试消息] count: {}", retryMessages.size());
                
                for (LocalMessageDO message : retryMessages) {
                    processMessageAsync(message);
                }
                
            } catch (Exception e) {
                log.error("[loadAndProcessPendingMessages][加载待处理消息异常]", e);
            }
        });
    }

    /**
     * 处理待发送消息
     */
    private void processPendingMessages() {
        List<LocalMessageDO> messages = localMessageService.getPendingMessages(50);
        if (messages.isEmpty()) {
            return;
        }

        log.debug("[processPendingMessages][处理待发送消息] count: {}", messages.size());
        
        for (LocalMessageDO message : messages) {
            processMessageAsync(message);
        }
    }

    /**
     * 处理重试消息
     */
    private void processRetryMessages() {
        List<LocalMessageDO> messages = localMessageService.getRetryMessages(50);
        if (messages.isEmpty()) {
            return;
        }

        log.debug("[processRetryMessages][处理重试消息] count: {}", messages.size());
        
        for (LocalMessageDO message : messages) {
            processMessageAsync(message);
        }
    }

    /**
     * 异步处理单个消息
     */
    public void processMessageAsync(LocalMessageDO message) {
        try {
            // 标记消息为发送中
            boolean marked = localMessageService.markMessageSending(message.getMessageId());
            if (!marked) {
                log.warn("[processMessageAsync][标记消息发送中失败] messageId: {}", message.getMessageId());
                return;
            }

            // 获取消息处理器
            MessageHandler handler = handlerMap.get(message.getMessageType());
            if (handler == null) {
                localMessageService.markMessageFailed(message.getMessageId(), "未找到对应的消息处理器");
                log.error("[processMessageAsync][未找到消息处理器] messageId: {}, messageType: {}", 
                        message.getMessageId(), message.getMessageType());
                return;
            }

            // 处理消息
            MessageHandler.MessageHandleResult result = handler.handleMessage(message);
            
            if (result.isSuccess()) {
                localMessageService.markMessageSuccess(message.getMessageId());
                log.info("[processMessageAsync][消息处理成功] messageId: {}, result: {}", 
                        message.getMessageId(), result.getMessage());
            } else {
                localMessageService.markMessageFailed(message.getMessageId(), result.getMessage());
                log.warn("[processMessageAsync][消息处理失败] messageId: {}, error: {}", 
                        message.getMessageId(), result.getMessage());
            }

        } catch (Exception e) {
            localMessageService.markMessageFailed(message.getMessageId(), "处理异常: " + e.getMessage());
            log.error("[processMessageAsync][消息处理异常] messageId: {}", message.getMessageId(), e);
        }
    }

    /**
     * 停止服务
     */
    public void stop() {
        running.set(false);
        log.info("[AsyncMessageProcessService][服务已停止]");
    }

}
