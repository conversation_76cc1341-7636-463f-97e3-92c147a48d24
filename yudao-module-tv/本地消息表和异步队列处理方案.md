# 本地消息表和异步队列处理方案

## 方案概述

本方案解决了内容上传过程中的长事务问题，通过本地消息表实现了消息发送和业务数据操作的一致性，并采用异步队列处理方式来处理耗时的文件上传操作。

## 核心特性

1. **事务一致性**：使用本地消息表确保业务操作和消息发送的原子性
2. **异步处理**：将耗时的文件处理操作异步化，避免长事务
3. **自动重试**：支持消息处理失败后的自动重试机制
4. **重启恢复**：应用重启后自动加载未处理的消息继续处理
5. **文件管理**：临时文件在处理成功后自动删除，失败时保留用于重试

## 架构设计

### 数据库表结构

#### local_message（本地消息表）
- `message_id`：消息唯一标识
- `topic`：消息主题/队列名称
- `message_type`：消息类型（CONTENT_UPLOAD等）
- `payload`：消息内容（JSON格式）
- `status`：消息状态（0-待发送，1-发送中，2-发送成功，3-发送失败）
- `retry_count`：重试次数
- `max_retry_count`：最大重试次数
- `next_retry_time`：下次重试时间
- `business_id`：业务ID（如contentId）
- `business_type`：业务类型

#### message_process_record（消息处理记录表）
- 用于幂等性控制，防止消息重复处理

### 核心组件

#### 1. LocalMessageService
- 负责本地消息的发送、状态更新、查询等操作
- 支持指数退避重试策略

#### 2. MessageHandler接口
- 定义消息处理器的统一接口
- 支持不同类型消息的处理逻辑

#### 3. ContentUploadMessageHandler
- 专门处理内容上传消息
- 调用对应的ContentUploadStrategy处理文件
- 处理成功后删除临时文件

#### 4. AsyncMessageProcessService
- 异步消息处理服务
- 定时扫描待处理消息
- 应用启动时自动加载未处理消息
- 支持消息重试和清理

## 处理流程

### 1. 内容上传流程

```
用户上传 -> 保存封面 -> 创建内容记录(状态:处理中) -> 保存临时文件 -> 发送消息到本地消息表 -> 返回响应
                                                                    ↓
                                                            异步处理消息
                                                                    ↓
                                                            调用上传策略处理文件
                                                                    ↓
                                                    成功：更新内容状态+删除临时文件
                                                    失败：标记消息失败+保留临时文件用于重试
```

### 2. 消息处理流程

```
定时扫描 -> 获取待处理消息 -> 标记为发送中 -> 调用消息处理器 -> 更新消息状态
    ↓                                                              ↓
重启加载                                                    成功：标记成功
    ↓                                                              ↓
自动处理                                                    失败：标记失败+计算重试时间
```

## 关键改进

### 1. 解决长事务问题
- **原来**：文件上传和数据库操作在同一个事务中，容易导致锁等待超时
- **现在**：快速完成数据库操作，文件处理异步进行

### 2. 提高系统可靠性
- **消息持久化**：消息保存在数据库中，不会丢失
- **自动重试**：失败消息自动重试，支持指数退避
- **重启恢复**：应用重启后自动处理未完成的消息

### 3. 优化用户体验
- **快速响应**：用户上传后立即得到响应，不需要等待文件处理完成
- **状态跟踪**：可以通过contentId查询处理状态

### 4. 文件路径改进
- **原来**：使用InputStream，无法在消息中传递
- **现在**：使用临时文件路径，支持异步处理和重试

## 配置说明

### 定时任务配置
- **消息处理**：每30秒扫描一次待处理消息
- **消息清理**：每天凌晨2点清理7天前的成功消息

### 重试策略
- **最大重试次数**：默认3次
- **重试间隔**：指数退避，最大5分钟
- **计算公式**：min(300, 2^retryCount * 30) 秒

### 临时文件管理
- **存储位置**：系统临时目录/content-upload/
- **文件命名**：UUID + 原始扩展名
- **清理策略**：处理成功后立即删除，失败时保留用于重试

## 使用示例

### 1. 发送消息
```java
String messageId = localMessageService.sendMessage(
    "content.upload",                    // 主题
    MessageTypeEnum.CONTENT_UPLOAD.getType(), // 消息类型
    uploadMessage,                       // 消息内容
    contentId.toString(),               // 业务ID
    "CONTENT_UPLOAD"                    // 业务类型
);
```

### 2. 处理消息
```java
@Component
public class CustomMessageHandler implements MessageHandler {
    
    @Override
    public String getSupportedMessageType() {
        return "CUSTOM_TYPE";
    }
    
    @Override
    public MessageHandleResult handleMessage(LocalMessageDO message) {
        // 处理逻辑
        return MessageHandleResult.success("处理成功");
    }
}
```

### 3. 查询消息状态
```java
LocalMessageDO message = localMessageService.getMessage(messageId);
// 0-待发送，1-发送中，2-发送成功，3-发送失败
Integer status = message.getStatus();
```

## 监控和运维

### 1. 消息状态监控
- 监控待处理消息数量
- 监控失败消息数量
- 监控重试次数分布

### 2. 性能监控
- 消息处理耗时
- 文件处理成功率
- 临时文件清理情况

### 3. 告警机制
- 消息积压告警
- 处理失败率过高告警
- 临时文件占用空间告警

## 扩展性

### 1. 新增消息类型
1. 创建消息DTO类
2. 实现MessageHandler接口
3. 注册为Spring Bean

### 2. 自定义重试策略
- 可以为不同消息类型配置不同的重试次数
- 可以自定义重试间隔计算逻辑

### 3. 消息路由
- 支持根据消息类型路由到不同的处理器
- 支持消息优先级处理

## 注意事项

1. **临时文件清理**：确保临时文件目录有足够空间，定期清理过期文件
2. **消息积压**：监控消息处理速度，避免消息大量积压
3. **数据库性能**：本地消息表可能成为性能瓶颈，需要合理设置索引
4. **事务边界**：确保消息发送和业务操作在同一个事务中
5. **幂等性**：消息处理逻辑需要支持幂等性，避免重复处理问题

## 总结

本方案通过本地消息表和异步队列处理，有效解决了内容上传过程中的长事务问题，提高了系统的可靠性和用户体验。同时保证了数据一致性，支持自动重试和重启恢复，是一个完整的企业级解决方案。
