# 🎯 拼团抽奖功能使用说明

## 功能概述

本功能实现了完整的**自发发起拼团抽奖**系统，支持用户选择奖品并设定拼团人数，支付后邀请其他人参团。当拼团人数满足条件时，系统进行抽奖，产生中奖者并发放奖励。

## 核心特性

1. **用户自发拼团**：用户可选择奖品、设定人数、费用和时长发起拼团
2. **权重随机算法**：参与次数越多，中奖概率越高
3. **保底必中机制**：连续50次未中奖的用户下次必中
4. **透明开奖**：生成随机种子，开奖过程可验证
5. **自动处理**：满员自动开奖，超时自动退款
6. **多租户支持**：完全支持多租户架构

## 数据库表结构

### 核心表

1. **tv_group_info** - 拼团信息表
2. **tv_group_order** - 参与记录表  
3. **tv_group_result** - 开奖记录表
4. **tv_user_lottery_stat** - 用户抽奖统计表
5. **tv_prize** - 奖品表

### 表关系

```
tv_group_info (1) ←→ (N) tv_group_order
tv_group_info (1) ←→ (1) tv_group_result
tv_prize (1) ←→ (N) tv_group_info
tv_user_lottery_stat (1) ←→ (N) tv_group_order
```

## 业务流程

### 1. 发起拼团流程

```
用户选择奖品 → 设定参数 → 支付费用 → 创建拼团 → 邀请好友
```

### 2. 参与拼团流程

```
扫码/链接 → 查看详情 → 支付参团 → 等待满员 → 自动开奖
```

### 3. 开奖流程

```
满员检测 → 获取用户统计 → 执行算法 → 更新状态 → 发放奖励
```

## 开奖算法

### 权重随机算法

1. **权重计算**：基础权重1 + 参与次数增长
2. **随机抽取**：根据权重比例进行加权随机
3. **透明验证**：使用MD5哈希生成可验证的随机数

### 保底必中机制

1. **阈值设定**：连续50次未中奖触发保底
2. **优先级**：失败次数多的用户优先中奖
3. **权重重置**：中奖后权重重置为1

## API接口

### 用户端接口

#### 发起拼团
```http
POST /tv/app-group-lottery/create
Content-Type: application/json

{
  "prizeId": 1024,
  "targetSize": 10,
  "entryFee": 9.9,
  "durationHours": 24
}
```

#### 参与拼团
```http
POST /tv/app-group-lottery/join
Content-Type: application/json

{
  "groupId": 1024
}
```

#### 获取拼团详情
```http
GET /tv/app-group-lottery/detail?groupId=1024
```

#### 我的拼团列表
```http
GET /tv/app-group-lottery/my-groups
Authorization: Bearer {token}
```

#### 热门拼团列表
```http
GET /tv/app-group-lottery/hot-groups?limit=10
```

### 管理后台接口

#### 拼团信息分页
```http
GET /tv/group-lottery/group-info/page?pageNo=1&pageSize=10
Authorization: Bearer {admin_token}
```

#### 开奖记录分页
```http
GET /tv/group-lottery/group-result/page?pageNo=1&pageSize=10
Authorization: Bearer {admin_token}
```

#### 手动开奖
```http
POST /tv/group-lottery/draw?groupId=1024
Authorization: Bearer {admin_token}
```

#### 奖品管理
```http
GET /tv/prize/page?pageNo=1&pageSize=10
POST /tv/prize/create
PUT /tv/prize/update
DELETE /tv/prize/delete?id=1024
```

## 定时任务

### GroupLotteryJob

**执行频率**：每5分钟执行一次

**主要功能**：
1. 处理超时拼团（自动退款）
2. 处理满员拼团（自动开奖）

**配置示例**：
```sql
INSERT INTO system_job (name, status, handler_name, handler_param, cron_expression, retry_count, retry_interval, monitor_timeout, creator, create_time, updater, update_time, deleted) 
VALUES ('拼团抽奖处理', 1, 'groupLotteryJob', '', '0 */5 * * * ?', 3, 0, 0, 'admin', NOW(), 'admin', NOW(), 0);
```

## 权限配置

需要在系统中配置以下权限：

### 拼团管理权限
- `tv:group:query` - 查询拼团信息
- `tv:group:draw` - 手动开奖
- `tv:group:manage` - 拼团管理

### 奖品管理权限
- `tv:prize:create` - 创建奖品
- `tv:prize:update` - 更新奖品
- `tv:prize:delete` - 删除奖品
- `tv:prize:query` - 查询奖品

## 使用示例

### 用户发起拼团

```java
// 1. 获取可用奖品列表
List<PrizeDO> prizes = prizeService.getAvailablePrizes();

// 2. 发起拼团
AppGroupCreateReqVO createReq = new AppGroupCreateReqVO();
createReq.setPrizeId(1024L);
createReq.setTargetSize(10);
createReq.setEntryFee(new BigDecimal("9.9"));
createReq.setDurationHours(24);

Long groupId = groupLotteryService.createGroup(userId, createReq);
```

### 用户参与拼团

```java
// 1. 查看拼团详情
AppGroupDetailRespVO detail = groupLotteryService.getGroupDetail(userId, groupId);

// 2. 参与拼团
AppGroupJoinReqVO joinReq = new AppGroupJoinReqVO();
joinReq.setGroupId(groupId);

Long orderId = groupLotteryService.joinGroup(userId, joinReq);
```

### 管理员手动开奖

```java
// 手动触发开奖
Long resultId = groupLotteryService.drawLottery(groupId);

// 查看开奖结果
GroupResultDO result = groupLotteryService.getGroupResult(resultId);
```

## 注意事项

1. **支付集成**：当前代码中支付部分为TODO，需要集成实际的支付模块
2. **奖励发放**：实物奖品需要对接物流系统，虚拟奖品需要对接相应的系统
3. **风控机制**：建议添加用户参团次数限制、设备限制等风控措施
4. **性能优化**：大量用户参与时可考虑使用Redis缓存热门拼团数据
5. **合规风险**：涉及现金奖品时需要注意相关法律法规

## 扩展功能

1. **多人中奖**：可扩展支持一个拼团多个中奖者
2. **拼团分享**：可添加分享功能，提高传播效果
3. **中奖通知**：可添加短信、推送等通知功能
4. **数据统计**：可添加拼团数据分析和报表功能
5. **活动配置**：可添加拼团活动的时间、地域等限制配置
