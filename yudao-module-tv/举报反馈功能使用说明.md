# 举报反馈功能使用说明

## 功能概述

本功能实现了一个通用的举报反馈系统，支持对不同业务模块的内容进行举报，并提供了业务解耦的处理机制。

## 核心特性

1. **通用性设计**：支持多种业务类型的举报（内容、用户、评论等）
2. **业务解耦**：通过处理器模式，不同业务模块可以实现自己的举报处理逻辑
3. **自动处理**：支持根据举报次数自动处理违规内容
4. **多租户支持**：完全支持多租户架构
5. **完整的API**：提供管理后台和APP端的完整API接口

## 数据库表结构

### tv_report 表字段说明

- `business_type`: 业务类型（content-内容举报，user-用户举报等）
- `business_id`: 业务ID（如内容ID、用户ID等）
- `report_type`: 举报类型（1-色情低俗，2-违法违规，3-虚假信息，4-恶意营销，5-侵权盗版，6-其他）
- `report_reason`: 举报原因描述
- `report_images`: 举报截图（JSON数组格式）
- `reporter_id`: 举报人ID（匿名举报时为NULL）
- `reporter_ip`: 举报人IP地址
- `status`: 处理状态（0-待处理，1-处理中，2-已处理，3-已驳回）
- `handle_result`: 处理结果说明
- `handle_user_id`: 处理人ID
- `handle_time`: 处理时间

## API接口

### APP端接口

#### 创建举报
```http
POST /tv/report/create
Content-Type: application/json
Authorization: Bearer {token}

{
  "businessType": "content",
  "businessId": 1024,
  "reportType": 1,
  "reportReason": "内容涉及色情低俗",
  "reportImages": ["http://example.com/image1.jpg"]
}
```

### 管理后台接口

#### 获取举报列表
```http
GET /tv/report/page?pageNo=1&pageSize=10&businessType=content&status=0
Authorization: Bearer {admin_token}
```

#### 处理举报
```http
PUT /tv/report/handle?id=1&handleResult=经核实确实违规，已下架&isViolation=true
Authorization: Bearer {admin_token}
```

## 业务处理器扩展

### 实现自定义处理器

```java
@Component
public class CustomReportHandler implements ReportHandler {

    @Override
    public String getBusinessType() {
        return "custom"; // 自定义业务类型
    }

    @Override
    public void afterReportCreate(ReportDO report) {
        // 举报创建后的处理逻辑
        log.info("收到新举报：{}", report.getBusinessId());
    }

    @Override
    public void afterReportHandle(ReportDO report, boolean isViolation) {
        if (isViolation) {
            // 违规处理逻辑
            handleViolation(report.getBusinessId());
        }
    }

    @Override
    public boolean shouldAutoHandle(Long businessId, Long reportCount) {
        // 自定义自动处理条件
        return reportCount >= 10;
    }

    @Override
    public void autoHandle(Long businessId, Long reportCount) {
        // 自动处理逻辑
        log.warn("业务对象{}被举报{}次，执行自动处理", businessId, reportCount);
    }
}
```

## 内容举报处理器

已实现的`ContentReportHandler`提供了以下功能：

1. **自动下架**：当内容被举报超过5次时自动下架
2. **违规处理**：管理员确认违规后自动下架内容
3. **状态管理**：只有发布状态的内容才能被下架

### 使用示例

```java
// 举报某个内容
AppReportCreateReqVO reportReq = new AppReportCreateReqVO();
reportReq.setBusinessType("content");
reportReq.setBusinessId(1024L);
reportReq.setReportType(1); // 色情低俗
reportReq.setReportReason("内容不当");

Long reportId = reportService.createAppReport(userId, reportReq, userIp);
```

## 权限配置

需要在系统中配置以下权限：

- `tv:report:create` - 创建举报
- `tv:report:query` - 查询举报
- `tv:report:update` - 更新举报
- `tv:report:handle` - 处理举报
- `tv:report:delete` - 删除举报
- `tv:report:export` - 导出举报数据

## 注意事项

1. **业务解耦**：新增业务类型时，需要实现对应的`ReportHandler`
2. **自动处理**：谨慎设置自动处理的阈值，避免误操作
3. **数据安全**：举报图片等敏感信息需要做好访问控制
4. **性能考虑**：大量举报数据需要考虑分页和索引优化

## 新增功能特性

### 用户侧功能
1. **我的举报列表**：用户可以查看自己的举报记录和处理结果
2. **举报限制检查**：防止重复举报和恶意举报
3. **举报次数统计**：显示用户今日举报次数

### 运营侧功能
1. **举报详情页**：包含举报人信息、被举报对象详情、历史举报记录
2. **审核操作**：支持采纳、驳回、二次确认等审核流程
3. **批量审核**：支持批量处理多个举报
4. **统计报表**：按时间、类型、模块统计举报数据
5. **待办队列**：专门的待处理举报列表

### 反滥用机制
1. **恶意举报检测**：基于举报频率和驳回率检测恶意用户
2. **积分惩罚**：对恶意举报用户扣除积分
3. **举报限流**：限制用户举报频率（每小时3次，每天10次）
4. **重复举报检查**：防止对同一对象重复举报

## API接口完整列表

### APP端接口
```http
# 创建举报
POST /tv/report/create

# 我的举报列表
GET /tv/report/my-page

# 检查是否可以举报
GET /tv/report/can-report?businessType=content&businessId=1024

# 获取今日举报次数
GET /tv/report/today-count
```

### 管理后台接口
```http
# 举报分页查询
GET /tv/report/page

# 待办举报队列
GET /tv/report/pending-page

# 举报详情
GET /tv/report/detail?id=1024

# 举报统计
GET /tv/report/statistics

# 审核举报
PUT /tv/report/audit

# 批量审核
PUT /tv/report/batch-audit

# 处理举报（旧接口，建议使用audit）
PUT /tv/report/handle

# 导出Excel
GET /tv/report/export-excel
```

## 权限配置

需要在系统中配置以下权限：

- `tv:report:create` - 创建举报
- `tv:report:query` - 查询举报
- `tv:report:update` - 更新举报
- `tv:report:handle` - 处理举报
- `tv:report:audit` - 审核举报
- `tv:report:delete` - 删除举报
- `tv:report:export` - 导出举报数据

## 扩展建议

1. **通知机制**：可以集成消息推送，及时通知管理员处理举报
2. **统计分析**：可以添加举报数据的统计分析功能
3. **黑名单机制**：可以基于举报数据建立用户黑名单
4. **AI辅助**：可以集成AI内容审核，提高处理效率
5. **积分服务集成**：完善积分扣除功能，需要集成会员积分服务
6. **用户服务集成**：完善举报人信息展示，需要集成用户服务

五、扩展与优化

自动化审核：接入 NLP、内容安全检测（涉黄涉政、涉黑）先行过滤。

多级处理：严重举报可触发二次审核或直接上报法律部门。

反滥用机制：对恶意频繁举报的用户可限流或惩罚。

多语言支持：适配国际化场景。

配置化：支持动态添加举报原因、处理策略。