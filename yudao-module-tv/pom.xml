<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>cn.iocoder.boot</groupId>
        <artifactId>yudao</artifactId>
        <version>${revision}</version> <!-- 1. 修改 version 为 ${revision} -->
    </parent>

    <artifactId>yudao-module-tv</artifactId>
    <packaging>pom</packaging> <!-- 2. 新增 packaging 为 pom -->

    <name>${project.artifactId}</name> <!-- 3. 新增 name 为 ${project.artifactId} -->
    <description> <!-- 4. 新增 description 为该模块的描述 -->
        tv 模块，主要实现 视频信息，分类，播放历史，评论，收藏，点赞 等相关 功能。
    </description>
    <modules>
        <module>yudao-module-tv-api</module>
        <module>yudao-module-tv-biz</module>
    </modules>

</project>