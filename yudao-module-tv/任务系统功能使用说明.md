# 任务系统功能使用说明

## 功能概述

本功能实现了一个完整的任务系统，支持用户完成指定任务并获得积分奖励。系统包含图片哈希比对功能，防止用户重复提交相同或相似的截图。

## 核心特性

1. **任务管理**：支持创建、编辑、启用/禁用任务
2. **图片哈希比对**：使用感知哈希算法检测重复/相似截图
3. **积分奖励**：完成任务后自动发放积分奖励
4. **审核机制**：管理员审核任务提交，确保任务完成质量
5. **防重复提交**：通过图片哈希值防止用户重复提交相同截图
6. **多租户支持**：完全支持多租户架构

## 数据库表结构

### tv_task 任务定义表
- `name`: 任务名称
- `description`: 任务描述
- `type`: 任务类型（1-分享任务，2-短视频任务）
- `reward_points`: 奖励积分
- `daily_limit`: 每日完成次数限制
- `total_limit`: 总完成次数限制
- `status`: 任务状态（0-禁用，1-启用）

### tv_user_task 用户任务记录表
- `user_id`: 用户ID
- `task_id`: 任务ID
- `completed_count`: 已完成次数
- `total_reward_points`: 总获得积分

### tv_task_submission 任务提交记录表
- `user_id`: 用户ID
- `task_id`: 任务ID
- `screenshot_base64`: 截图base64数据
- `screenshot_hash`: 截图哈希值
- `status`: 审核状态（0-待审核，1-审核通过，2-审核拒绝）
- `reward_points`: 实际奖励积分

## 默认任务配置

系统预置了两个默认任务：

1. **分享任务**
   - 任务名称：分享网站到微信群
   - 奖励积分：20分
   - 每日限制：1次

2. **短视频任务**
   - 任务名称：发布短视频体验
   - 奖励积分：40分
   - 每日限制：1次

## API接口

### APP端接口

#### 获取可用任务列表
```http
GET /tv/task/list
Authorization: Bearer {token}
```

#### 提交任务
```http
POST /tv/task/submit
Content-Type: application/json
Authorization: Bearer {token}

{
  "taskId": 1,
  "screenshotBase64": "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAA...",
  "description": "已分享到微信群并获得多个点赞"
}
```

#### 获取我的任务提交记录
```http
GET /tv/task/submission/page?pageNo=1&pageSize=10
Authorization: Bearer {token}
```

### 管理后台接口

#### 创建任务
```http
POST /tv/task/create
Content-Type: application/json
Authorization: Bearer {admin_token}

{
  "name": "新任务",
  "description": "任务描述",
  "type": 1,
  "rewardPoints": 30,
  "dailyLimit": 2,
  "status": 1
}
```

#### 审核任务提交
```http
PUT /tv/task/submission/audit
Content-Type: application/json
Authorization: Bearer {admin_token}

{
  "id": 1024,
  "action": "approve",
  "auditResult": "截图清晰，分享内容符合要求",
  "rewardPoints": 20
}
```

#### 获取待审核列表
```http
GET /tv/task/submission/pending
Authorization: Bearer {admin_token}
```

## 图片哈希比对机制

### 算法原理
系统使用感知哈希算法（pHash）来检测图片相似度：

1. **图片预处理**：将图片缩放到32x32像素并转换为灰度图
2. **DCT变换**：计算离散余弦变换（简化版本）
3. **特征提取**：提取左上角8x8区域的低频信息
4. **哈希生成**：根据像素值与平均值的比较生成64位哈希值
5. **相似度比较**：通过汉明距离判断图片相似度

### 防重复机制
- **哈希存储**：每张提交的截图都会计算并存储哈希值
- **相似度检测**：新提交的截图会与历史截图进行相似度比较
- **阈值控制**：汉明距离小于等于5认为是相似图片
- **重复拒绝**：检测到相似截图时拒绝提交

## 积分奖励机制

### 奖励发放流程
1. 用户提交任务截图
2. 管理员审核通过
3. 系统自动调用`memberPointApi.addPoint()`发放积分
4. 更新用户任务完成记录

### 积分类型
目前使用`MemberPointBizTypeEnum.SIGN`类型，建议在实际使用时添加专门的`TASK_REWARD`类型。

## 使用示例

### 用户完成任务流程
```java
// 1. 获取可用任务
List<AppTaskRespVO> tasks = taskService.getUserAvailableTasks(userId);

// 2. 提交任务
AppTaskSubmissionCreateReqVO submitReq = new AppTaskSubmissionCreateReqVO();
submitReq.setTaskId(1L);
submitReq.setScreenshotBase64("data:image/png;base64,...");
submitReq.setDescription("已完成分享任务");

Long submissionId = taskService.submitTask(userId, submitReq);

// 3. 查看提交记录
PageResult<AppTaskSubmissionRespVO> submissions = 
    taskService.getUserTaskSubmissionPage(userId, pageReq);
```

### 管理员审核流程
```java
// 1. 获取待审核列表
List<TaskSubmissionDO> pending = taskService.getPendingTaskSubmissions();

// 2. 审核任务提交
TaskSubmissionAuditReqVO auditReq = new TaskSubmissionAuditReqVO();
auditReq.setId(submissionId);
auditReq.setAction("approve");
auditReq.setAuditResult("任务完成质量良好");
auditReq.setRewardPoints(20);

taskService.auditTaskSubmission(auditReq, adminUserId);
```

## 权限配置

需要在系统中配置以下权限：

- `tv:task:create` - 创建任务
- `tv:task:update` - 更新任务
- `tv:task:delete` - 删除任务
- `tv:task:query` - 查询任务
- `tv:task:audit` - 审核任务提交

## 注意事项

1. **图片大小限制**：建议限制上传图片的大小，避免base64数据过大
2. **哈希算法优化**：当前使用简化的DCT算法，可以考虑使用更精确的实现
3. **积分类型**：需要在`MemberPointBizTypeEnum`中添加`TASK_REWARD`类型
4. **性能考虑**：大量图片哈希比对可能影响性能，可以考虑异步处理
5. **存储优化**：base64图片数据占用空间较大，可以考虑存储到文件系统

## 扩展建议

1. **任务模板**：可以创建任务模板，快速创建相似任务
2. **批量审核**：支持批量审核多个任务提交
3. **统计报表**：添加任务完成情况的统计分析
4. **通知机制**：任务审核结果通知用户
5. **等级限制**：根据用户等级限制可参与的任务
6. **时间限制**：支持任务的有效期设置
