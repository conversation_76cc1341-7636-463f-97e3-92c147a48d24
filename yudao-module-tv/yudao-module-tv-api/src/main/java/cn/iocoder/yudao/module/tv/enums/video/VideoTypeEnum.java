package cn.iocoder.yudao.module.tv.enums.video;

import cn.iocoder.yudao.framework.common.core.IntArrayValuable;
import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Arrays;

/**
 * 视频类型枚举
 *
 * <AUTHOR>
 */
@Getter
@AllArgsConstructor
public enum VideoTypeEnum implements IntArrayValuable {
    SINGLE(0, "单个视频"),
    MULTIPLE(1, "多个视频"),
    MIXED(2, "图文混合");

    public static final int[] ARRAYS = Arrays.stream(values()).mapToInt(VideoTypeEnum::getType).toArray();

    /**
     * 状态值
     */
    private final Integer type;
    /**
     * 状态名
     */
    private final String name;

    @Override
    public int[] array() {
        return ARRAYS;
    }

}
