package cn.iocoder.yudao.module.tv.enums.video;

import cn.iocoder.yudao.framework.common.core.IntArrayValuable;
import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Arrays;

/**
 * 付费视频枚举
 *
 * <AUTHOR>
 */
@Getter
@AllArgsConstructor
public enum VideoPaidEnum implements IntArrayValuable {
    FREE(0, "免费"),
    PAID(1, "付费");

    public static final int[] ARRAYS = Arrays.stream(values()).mapToInt(VideoPaidEnum::getPaid).toArray();

    /**
     * 状态值
     */
    private final Integer paid;
    /**
     * 状态名
     */
    private final String name;

    @Override
    public int[] array() {
        return ARRAYS;
    }

}
