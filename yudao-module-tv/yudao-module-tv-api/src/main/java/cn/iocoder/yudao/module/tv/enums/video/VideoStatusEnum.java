package cn.iocoder.yudao.module.tv.enums.video;

import cn.iocoder.yudao.framework.common.core.IntArrayValuable;
import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Arrays;

/**
 * 视频状态枚举
 *
 * <AUTHOR>
 */
@Getter
@AllArgsConstructor
public enum VideoStatusEnum implements IntArrayValuable {
    DRAFT(0, "草稿"),
    RELEASE(1, "发布"),
    SHAKEDOWN(2, "下架");

    public static final int[] ARRAYS = Arrays.stream(values()).mapToInt(VideoStatusEnum::getStatus).toArray();

    /**
     * 状态值
     */
    private final Integer status;
    /**
     * 状态名
     */
    private final String name;

    @Override
    public int[] array() {
        return ARRAYS;
    }

}
