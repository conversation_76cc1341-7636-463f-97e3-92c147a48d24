package cn.iocoder.yudao.module.tv.enums;

import cn.iocoder.yudao.framework.common.exception.ErrorCode;

/**
 * tv 错误码枚举类
 * <p>
 * tv 系统，使用 1-902-000-000 段
 */
public interface ErrorCodeConstants {

    // ========== 视频相关 1-902-000-000 ==========
    ErrorCode VIDEO_NOT_EXISTS = new ErrorCode(1_902_000_001, "视频不存在");
    ErrorCode VIDEO_CATEGORY_NOT_EXISTS = new ErrorCode(1_902_000_002, "视频分类不存在");
    ErrorCode VIDEO_TAG_NOT_EXISTS = new ErrorCode(1_902_000_003, "视频标签不存在");

    // ========== 观看历史相关 1-902-001-000 ==========
    ErrorCode HISTORY_NOT_EXISTS = new ErrorCode(1_902_001_001, "观看历史不存在");

    // ========== 评论相关 1-902-002-000 ==========
    ErrorCode COMMENT_NOT_EXISTS = new ErrorCode(1_902_002_001, "评论不存在");
    ErrorCode COMMENT_PARENT_NOT_EXISTS = new ErrorCode(1_902_002_002, "父评论不存在");

    // ========== 推荐相关 1-902-003-000 ==========
    ErrorCode RECOMMEND_NOT_EXISTS = new ErrorCode(1_902_003_001, "推荐不存在");

    // ========== 分类相关 1-902-004-000 ==========
    ErrorCode TV_CATEGORY_NOT_EXISTS = new ErrorCode(1_902_004_000, "视频分类不存在");

    // ========== 特权相关 1-902-005-000 ==========
    ErrorCode PRIVILEGE_NOT_EXISTS = new ErrorCode(1_902_005_000, "特权不存在");

    // ========== 内容主体 1-902-006-000 ==========
    ErrorCode CONTENT_NOT_EXISTS = new ErrorCode(1_902_006_000, "内容主体不存在");
    ErrorCode CONTENT_ILLEGAL_PREVIEW = new ErrorCode(1_902_006_001, "预览内容超过25");
    ErrorCode CONTENT_UPLOAD = new ErrorCode(1_902_006_002, "封面文件上传失败");
    ErrorCode CONTENT_ITEM_UPLOAD = new ErrorCode(1_902_006_003, "策略上传失败:[{}]");

    // ========== 举报反馈 1-902-007-000 ==========
    ErrorCode REPORT_NOT_EXISTS = new ErrorCode(1_902_007_000, "举报反馈不存在");
    ErrorCode REPORT_DUPLICATE_OR_FREQUENT = new ErrorCode(1_902_007_001, "举报失败，请勿重复举报或频繁举报");
    ErrorCode REPORT_INVALID_AUDIT_ACTION = new ErrorCode(1_902_007_002, "无效的审核动作");
    ErrorCode REPORT_NOT_PENDING_CONFIRM = new ErrorCode(1_902_007_003, "该举报不在待确认状态");

    // ========== 任务系统 1-902-008-000 ==========
    ErrorCode TASK_NOT_EXISTS = new ErrorCode(1_902_008_000, "任务不存在");
    ErrorCode TASK_NOT_ENABLED = new ErrorCode(1_902_008_001, "任务未启用");
    ErrorCode TASK_DAILY_LIMIT_EXCEEDED = new ErrorCode(1_902_008_002, "今日任务完成次数已达上限");
    ErrorCode TASK_DUPLICATE_SCREENSHOT = new ErrorCode(1_902_008_003, "检测到相似截图，请勿重复提交");
    ErrorCode TASK_SUBMISSION_NOT_EXISTS = new ErrorCode(1_902_008_004, "任务提交记录不存在");



    // ========== 视频相关 1-008-001-000 ==========
    ErrorCode VIDEO_NOT_PUBLISHED = new ErrorCode(1_008_001_001, "视频未发布");

    // ========== 任务相关 1-008-002-000 ==========
    ErrorCode TASK_TOTAL_LIMIT_EXCEEDED = new ErrorCode(1_008_002_003, "任务总完成次数已达上限");
    ErrorCode TASK_SUBMISSION_ALREADY_AUDITED = new ErrorCode(1_008_002_005, "任务提交记录已审核");
    ErrorCode TASK_IMAGE_SIMILAR = new ErrorCode(1_008_002_006, "检测到相似截图，请提交不同的截图");

    // ========== 拼团相关 1-008-003-000 ==========
    ErrorCode GROUP_NOT_EXISTS = new ErrorCode(1_008_003_000, "拼团不存在");
    ErrorCode GROUP_NOT_JOINABLE = new ErrorCode(1_008_003_001, "拼团不可参与");
    ErrorCode GROUP_EXPIRED = new ErrorCode(1_008_003_002, "拼团已过期");
    ErrorCode GROUP_FULL = new ErrorCode(1_008_003_003, "拼团已满员");
    ErrorCode GROUP_ALREADY_JOINED = new ErrorCode(1_008_003_004, "您已参与该拼团");
    ErrorCode GROUP_NOT_IN_PROGRESS = new ErrorCode(1_008_003_005, "拼团不在进行中");
    ErrorCode GROUP_NOT_FULL = new ErrorCode(1_008_003_006, "拼团未满员，无法开奖");
    ErrorCode GROUP_NO_PARTICIPANTS = new ErrorCode(1_008_003_007, "拼团无参与者");

    // ========== 奖品相关 1-008-004-000 ==========
    ErrorCode PRIZE_NOT_EXISTS = new ErrorCode(1_008_004_000, "奖品不存在");
    ErrorCode PRIZE_NOT_ENABLED = new ErrorCode(1_008_004_001, "奖品未启用");
    ErrorCode PRIZE_STOCK_NOT_ENOUGH = new ErrorCode(1_008_004_002, "奖品库存不足");

    // ========== 内容相关 1-008-005-000 ==========
    ErrorCode CONTENT_NOT_PUBLISHED = new ErrorCode(1_008_005_001, "内容未发布");
    ErrorCode CONTENT_ALREADY_UNLOCKED = new ErrorCode(1_008_005_002, "内容已解锁");
    ErrorCode CONTENT_POINTS_NOT_ENOUGH = new ErrorCode(1_008_005_003, "积分不足，无法解锁内容");

    // ========== 收藏相关 1-008-006-000 ==========
    ErrorCode FAVORITE_EXISTS = new ErrorCode(1_008_006_000, "已收藏该内容");
    ErrorCode FAVORITE_NOT_EXISTS = new ErrorCode(1_008_006_001, "收藏记录不存在");

    // ========== 点赞相关 1-008-007-000 ==========
    ErrorCode LIKE_EXISTS = new ErrorCode(1_008_007_000, "已点赞该内容");
    ErrorCode LIKE_NOT_EXISTS = new ErrorCode(1_008_007_001, "点赞记录不存在");

    // ========== 评论相关 1-008-008-000 ==========
    ErrorCode COMMENT_DISABLED = new ErrorCode(1_008_008_001, "评论功能已禁用");

    // ========== 举报相关 1-008-009-000 ==========
    ErrorCode REPORT_ALREADY_PROCESSED = new ErrorCode(1_008_009_001, "举报记录已处理");

    // ========== 分类相关 1-008-010-000 ==========
    ErrorCode CATEGORY_NOT_EXISTS = new ErrorCode(1_008_010_000, "分类不存在");
    ErrorCode CATEGORY_DISABLED = new ErrorCode(1_008_010_001, "分类已禁用");
    ErrorCode CATEGORY_HAS_CHILDREN = new ErrorCode(1_008_010_002, "分类存在子分类，无法删除");
    ErrorCode CATEGORY_HAS_CONTENT = new ErrorCode(1_008_010_003, "分类下存在内容，无法删除");
}
