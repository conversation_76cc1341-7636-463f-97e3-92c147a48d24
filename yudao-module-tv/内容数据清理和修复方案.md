# 内容数据清理和修复方案

## 方案概述

本方案用于清理和修复`tv_content`和`tv_content_media`表的历史数据问题，确保数据的完整性和一致性。

## 数据问题分析

### 表关联关系
- `tv_content`表：内容主体表
- `tv_content_media`表：图片和视频混合资源表
- 关联条件：`tv_content.type = 5` 且 `tv_content.id = tv_content_media.content_id`

### 发现的数据问题

1. **垃圾数据问题**：
   - `tv_content`中`type=5`的记录，对应的`tv_content_media`数据量少于4条
   - 这些数据不符合业务规则，应该被清理

2. **封面缺失问题**：
   - `tv_content.cover_url`字段为空或null
   - 需要从关联的媒体资源中自动获取封面

3. **试看内容配置问题**：
   - 缺少试看内容（`is_preview = 1`）
   - 试看内容超过6个
   - 付费内容（`is_preview = 0`）少于等于1个
   - 付费内容数量不比免费内容多

## 解决方案

### 核心组件

#### 1. ContentDataCleanupService
- **功能**：数据清理和修复的核心服务
- **主要方法**：
  - `executeCleanup()`：执行完整的数据清理和修复
  - `checkDataIssues()`：检查数据问题（不执行修复）

#### 2. ContentDataCleanupJob
- **功能**：定时任务，自动执行数据清理
- **执行时间**：每天凌晨3点（`0 0 3 * * ?`）
- **支持多租户**：使用`@TenantJob`注解

#### 3. ContentDataCleanupController
- **功能**：提供手动触发的管理接口
- **权限控制**：需要`tv:content:cleanup`权限

### 修复逻辑

#### 1. 垃圾数据清理
```sql
-- 查找媒体数量少于4的type=5内容
SELECT c.id, COUNT(m.id) as media_count 
FROM tv_content c 
LEFT JOIN tv_content_media m ON c.id = m.content_id 
WHERE c.type = 5 
GROUP BY c.id 
HAVING media_count < 4;

-- 删除这些内容及其关联媒体
DELETE FROM tv_content_media WHERE content_id IN (...);
DELETE FROM tv_content WHERE id IN (...);
```

#### 2. 封面URL修复
```sql
-- 查找缺失封面的内容
SELECT * FROM tv_content 
WHERE type = 5 AND (cover_url IS NULL OR cover_url = '');

-- 修复策略：
-- 1. 优先使用type=0的图片作为封面
-- 2. 如果没有图片，使用type=1视频的cover_url
```

#### 3. 试看内容修复
- **分配策略**：
  - 试看内容：总数的1/3，但不超过6个，至少1个
  - 付费内容：剩余的，确保比试看内容多
- **排序规则**：按`sort`字段排序，前面的设为试看，后面的设为付费

### 使用方式

#### 1. 自动执行（推荐）
定时任务会每天凌晨3点自动执行数据清理：
```java
// 在infra_job表中已配置
INSERT INTO infra_job (name, handler_name, cron_expression) 
VALUES ('内容数据清理任务', 'contentDataCleanupJob', '0 0 3 * * ?');
```

#### 2. 手动执行
通过管理后台API手动触发：
```http
POST /tv/content-cleanup/execute
Authorization: Bearer {admin_token}
```

#### 3. 数据检查
检查数据问题但不执行修复：
```http
POST /tv/content-cleanup/check
Authorization: Bearer {admin_token}
```

### 执行结果

#### ContentCleanupResult
```json
{
  "success": true,
  "message": "数据清理和修复完成",
  "deletedGarbageContentCount": 5,
  "fixedCoverUrlCount": 12,
  "fixedPreviewContentCount": 8
}
```

#### ContentDataCheckResult
```json
{
  "garbageContentCount": 5,
  "missingCoverContentCount": 12,
  "invalidPreviewContentCount": 8,
  "summary": "发现数据问题 - 垃圾内容: 5个, 缺失封面: 12个, 试看配置异常: 8个"
}
```

## 安全保障

### 1. 事务保护
- 所有修复操作都在事务中执行
- 出现异常时自动回滚，保证数据一致性

### 2. 详细日志
- 记录每个修复步骤的详细日志
- 便于问题排查和操作审计

### 3. 权限控制
- 手动执行需要管理员权限
- 防止误操作

### 4. 分步执行
- 清理、修复操作分步进行
- 每步都有独立的错误处理

## 监控和告警

### 1. 执行监控
- 定时任务执行状态监控
- 执行耗时监控
- 修复数量统计

### 2. 数据质量监控
- 垃圾数据数量趋势
- 封面缺失率
- 试看配置异常率

### 3. 告警机制
- 执行失败告警
- 数据问题数量异常告警
- 执行耗时过长告警

## 扩展性

### 1. 新增检查规则
可以轻松添加新的数据检查和修复规则：
```java
// 在ContentDataCleanupService中添加新方法
private int fixNewDataIssue() {
    // 新的修复逻辑
    return fixedCount;
}
```

### 2. 自定义修复策略
可以根据业务需求调整修复策略：
- 试看内容比例调整
- 封面选择优先级调整
- 垃圾数据判断条件调整

### 3. 多表支持
可以扩展到其他相关表的数据清理：
- `tv_content_video`表
- `tv_content_collection_item`表

## 注意事项

1. **数据备份**：执行清理前建议备份相关表数据
2. **业务影响**：清理过程中可能影响相关内容的访问
3. **执行时间**：建议在业务低峰期执行
4. **权限管理**：严格控制清理操作的权限
5. **监控观察**：执行后观察业务指标是否正常

## 总结

本方案提供了完整的内容数据清理和修复解决方案，通过自动化的方式保证数据质量，减少人工维护成本。同时提供了灵活的手动执行和检查功能，满足不同场景的需求。
