# 内容搜索API文档

## 接口概述

新增了一个强大的内容搜索接口，支持多维度条件搜索和自定义排序。

## 接口信息

- **URL**: `/tv/content/search`
- **方法**: `GET`
- **描述**: 搜索内容，支持关键词搜索、多种过滤条件和自定义排序

## 请求参数

### 基础分页参数
- `pageNo` (Integer): 页码，默认1
- `pageSize` (Integer): 每页大小，默认10

### 搜索条件参数
- `keyword` (String): 关键词搜索（在标题和描述中搜索）
- `type` (Integer): 视频类型
  - 0: 单个视频
  - 1: 多个视频  
  - 2: 图集
  - 3: 图文
  - 4: 合集
  - 5: 图片和视频混合
- `categoryId` (Long): 分类编号
- `status` (Integer): 状态（默认1-已发布）
  - 0: 草稿
  - 1: 发布
  - 2: 下架
- `isPaid` (Integer): 是否付费
  - 0: 免费
  - 1: 付费

### 价格范围参数
- `minPrice` (BigDecimal): 最小价格
- `maxPrice` (BigDecimal): 最大价格

### 时间范围参数
- `publishTimeStart` (LocalDateTime): 发布时间开始
- `publishTimeEnd` (LocalDateTime): 发布时间结束

### 统计数据过滤参数
- `minPlayCount` (Integer): 最小播放次数
- `minLikeCount` (Integer): 最小点赞次数
- `minFavoriteCount` (Integer): 最小收藏次数

### 排序参数
- `sortField` (String): 排序字段（默认createTime）
  - `createTime`: 创建时间
  - `publishTime`: 发布时间
  - `playCount`: 播放次数
  - `likeCount`: 点赞次数
  - `favoriteCount`: 收藏次数
  - `price`: 价格
  - `recommendWeight`: 推荐权重
- `sortOrder` (String): 排序方向（默认desc）
  - `asc`: 升序
  - `desc`: 降序

## 请求示例

### 1. 基础关键词搜索
```
GET /tv/content/search?keyword=搞笑&pageNo=1&pageSize=10
```

### 2. 按类型和分类搜索
```
GET /tv/content/search?type=0&categoryId=1&pageNo=1&pageSize=10
```

### 3. 价格范围搜索
```
GET /tv/content/search?minPrice=0&maxPrice=50&isPaid=1&pageNo=1&pageSize=10
```

### 4. 按播放量排序的热门内容
```
GET /tv/content/search?minPlayCount=1000&sortField=playCount&sortOrder=desc&pageNo=1&pageSize=10
```

### 5. 综合搜索示例
```
GET /tv/content/search?keyword=电影&type=0&categoryId=2&minPrice=0&maxPrice=100&minPlayCount=500&sortField=likeCount&sortOrder=desc&pageNo=1&pageSize=20
```

### 6. 时间范围搜索
```
GET /tv/content/search?publishTimeStart=2024-01-01 00:00:00&publishTimeEnd=2024-12-31 23:59:59&sortField=publishTime&sortOrder=desc
```

## 响应格式

```json
{
  "code": 0,
  "data": {
    "list": [
      {
        "id": 1,
        "title": "内容标题",
        "description": "内容描述",
        "coverUrl": "封面图片URL",
        "type": 0,
        "playCount": 1000,
        "likeCount": 50,
        "favoriteCount": 20,
        "commentCount": 10,
        "status": 1,
        "categoryId": 1,
        "publishTime": "2024-01-01T10:00:00",
        "isPaid": 0,
        "price": 0.00,
        "createTime": "2024-01-01T09:00:00",
        "updateTime": "2024-01-01T09:00:00"
      }
    ],
    "total": 100
  },
  "msg": "操作成功"
}
```

## 特性说明

1. **智能关键词搜索**: 同时在标题和描述中搜索关键词
2. **多维度过滤**: 支持类型、分类、状态、付费状态等多种过滤条件
3. **范围查询**: 支持价格范围、时间范围、统计数据范围查询
4. **灵活排序**: 支持多种字段的升序/降序排序
5. **默认值**: 自动设置合理的默认值（只查询已发布内容，按创建时间降序）
6. **性能优化**: 使用数据库索引优化查询性能

## 使用建议

1. 建议为常用的搜索字段（如categoryId、type、status）建立数据库索引
2. 关键词搜索建议为title和description字段建立全文索引
3. 大数据量情况下建议限制pageSize的最大值
4. 可以根据业务需要扩展更多的搜索条件和排序字段
