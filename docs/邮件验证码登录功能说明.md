# 邮件验证码登录功能说明

## 功能概述

本功能模仿短信验证码登录的实现，为系统添加了邮件验证码登录功能，支持用户通过邮箱+验证码的方式进行登录。

## 新增接口

### 1. 邮箱验证码登录
- **接口路径**: `POST /app-api/member/auth/email-login`
- **功能**: 使用邮箱 + 验证码登录
- **请求参数**:
  ```json
  {
    "email": "<EMAIL>",
    "code": "1234",
    "socialType": 10,  // 可选，社交平台类型
    "socialCode": "xxx", // 可选，社交授权码
    "socialState": "xxx" // 可选，社交state
  }
  ```

### 2. 发送邮箱验证码
- **接口路径**: `POST /app-api/member/auth/send-email-code`
- **功能**: 发送邮箱验证码
- **请求参数**:
  ```json
  {
    "email": "<EMAIL>",
    "scene": 1  // 发送场景，1=登录，2=修改邮箱，3=修改密码，4=忘记密码
  }
  ```

### 3. 校验邮箱验证码
- **接口路径**: `POST /app-api/member/auth/validate-email-code`
- **功能**: 校验邮箱验证码是否正确
- **请求参数**:
  ```json
  {
    "email": "<EMAIL>",
    "scene": 1,
    "code": "1234"
  }
  ```

## 邮件场景枚举

- `MEMBER_LOGIN(1)`: 会员用户 - 邮箱登陆
- `MEMBER_UPDATE_EMAIL(2)`: 会员用户 - 修改邮箱
- `MEMBER_UPDATE_PASSWORD(3)`: 会员用户 - 修改密码
- `MEMBER_RESET_PASSWORD(4)`: 会员用户 - 忘记密码
- `ADMIN_MEMBER_LOGIN(21)`: 后台用户 - 邮箱登录

## 数据库变更

### 1. 新增邮件验证码表
```sql
-- 执行 sql/mysql/system_mail_code.sql
```

### 2. 用户表添加邮箱字段
```sql
-- 执行 sql/mysql/member_user_add_email.sql
```

### 3. 邮件模板数据
```sql
-- 执行 sql/mysql/mail_templates.sql
```

## 配置说明

在 `application.yaml` 中添加了邮件验证码相关配置：

```yaml
yudao:
  mail:
    code:
      expire-times: 10m  # 验证码过期时间
      send-frequency: 1m # 发送频率限制
      send-maximum-quantity-per-day: 10 # 每日最大发送数量
      begin-code: 9999   # 验证码最小值（测试用）
      end-code: 9999     # 验证码最大值（测试用）
```

## 使用流程

1. 用户输入邮箱，调用发送验证码接口
2. 系统生成验证码并发送邮件
3. 用户收到邮件，获取验证码
4. 用户输入邮箱和验证码，调用登录接口
5. 系统验证验证码，创建或获取用户，返回登录token

## 注意事项

1. 需要先配置邮件账号和模板
2. 验证码默认6位数字，有效期10分钟
3. 支持频率限制和每日发送数量限制
4. 支持社交登录绑定
5. 如果用户不存在会自动创建
6. 邮箱字段已添加到用户表中

## 测试建议

1. 配置邮件服务器
2. 创建邮件模板
3. 测试发送验证码功能
4. 测试登录功能
5. 测试各种异常情况（验证码错误、过期等）
