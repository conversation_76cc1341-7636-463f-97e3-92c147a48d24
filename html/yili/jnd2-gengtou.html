<!DOCTYPE html>
<html>

<head>
    <title>Lottery Data Analyzer</title>
    <meta charset="UTF-8" />
</head>
<style>
    #output {
        font-size: 12px;  /* 字体变小 */
        font-family: Arial, sans-serif;
    }
</style>
<body>
    <div id="output"></div>
    <script src="./str_1.js"></script>
    <script src="./str_2.js"></script>
    <script src="./str_3.js"></script>
    <script src="./str_3.5.js"></script>
    <script>
        const STARTING_AMOUNT = 10; // 初始投注金额
        /**
 * 更新网页上的输出内容
 */
        function logToPage(message) {
            const outputDiv = document.getElementById("output");
            outputDiv.innerHTML += message + "<br>"; // 添加内容并换行
        }

        /**
         * 彩票数据分析器类
         */
        class LotteryDataAnalyzer {
            constructor() {
                this.longStreak = 0;
                this.huStreak = 0;
                this.threshold = 7; // 当“龙”或“虎”连续出现2-3次后开始跟投
                this.currentAmount = STARTING_AMOUNT;
            }

            testHunhe(curType) {
                if (curType === "龙") {

                    this.longStreak++;
                    if (this.huStreak >= this.threshold) {
                        this.currentAmount *= 2;
                    }
                    if (this.longStreak > this.threshold) {
                        this.currentAmount = STARTING_AMOUNT;
                    }
                    this.huStreak = 0;

                } else if (curType === "虎") {

                    this.huStreak++;
                    if (this.longStreak >= this.threshold) {
                        this.currentAmount *= 2;
                    }
                    if (this.huStreak > this.threshold) {
                        this.currentAmount = STARTING_AMOUNT;
                    }
                    this.longStreak = 0;
                } else {
                    if (this.longStreak >= this.threshold) {
                        this.currentAmount *= 2;
                    }
                    this.longStreak = 0;
                    if (this.huStreak >= this.threshold) {
                        this.currentAmount *= 2;
                    }
                    this.huStreak = 0;

                }



                if (this.longStreak >= this.threshold) {
                    return { type: "龙", amount: this.currentAmount };
                } else if (this.huStreak >= this.threshold) {
                    return { type: "虎", amount: this.currentAmount };
                }
                return null;
            }


        }

        /**
         * 创建分析器实例并运行分析
         */
        function runHistoryData(splitStr, historyData) {
            const analyzer = new LotteryDataAnalyzer();
            let totalBetCount = 0;
            let totalBetAmount = 0;
            let maxBetAmount = 0;
            let profitSituation = 0;


            for (let i = 10; i < historyData.length - 1; i++) {
                let curTypeName = historyData[i];
                let apply = analyzer.testHunhe(curTypeName);
                let nextTypeName = historyData[i + 1];

                if (apply) {
                    totalBetCount++;
                    maxBetAmount = Math.max(maxBetAmount, apply.amount);
                    totalBetAmount += apply.amount;
                    profitSituation += apply.amount * 0.04; // 流水

                    if (nextTypeName === apply.type) {
                        profitSituation += apply.amount;
                    } else {
                        profitSituation -= apply.amount;
                    }
                }
            }

            // 输出到 console 和页面
            const result = `
        <br>------${splitStr}-- 统计分析 --------
        <br>投注总次数: ${totalBetCount}
        <br>总投注金额: ${totalBetAmount}
        <br>最大单次投注: ${maxBetAmount}
        <br>需要准备的金额: ${maxBetAmount * 2} ${maxBetAmount * 2 > 200000 ? '❌ 不可行 (大于20W)' : '✅ 可行'}
        <br>盈利情况 (不带流水): ${profitSituation - totalBetAmount * 0.04}
        <br>总体盈利情况: ${profitSituation}
        <br>获得的倍数情况: ${(profitSituation / (maxBetAmount * 2)).toFixed(2)}
    `;

            console.log(result.replace(/<br>/g, "\n")); // 替换 `<br>` 为 `\n` 以便控制台显示正常
            logToPage(result);
        }   

        runHistoryData("24年03月至今", str1.split(''))
        runHistoryData("23年03月至今", str2.split(''))
        runHistoryData("22年03月至今", str3.split(''))
        runHistoryData("21年09月至今", str3_5.split(''))

    </script>
</body>

</html>