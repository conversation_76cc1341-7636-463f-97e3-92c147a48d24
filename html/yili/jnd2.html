<!DOCTYPE html>
<html>

<head>
    <title>Lottery Data Analyzer</title>
    <meta charset="UTF-8" />
</head>

<body>
    <div id="output"></div>
    <script src="./str.js"></script>
    <script>
        const STARTING_AMOUNT = 10; // 初始投注金额

        /**
         * 彩票数据分析器类
         * 
         * 
四项 200000封顶
大双 小单 55000封顶
大单 小双 60000封顶
特码 10000封顶
极值 10000封顶
对子 70000封顶
顺子 10000封顶
豹子 2000封顶
龙 200000封顶
虎 200000封顶
和 20000封顶

10 - 2 -- 0.77
6  - 2 -- 1.12
12 - 2 -- 0.8 （keyi）
13 - 2 -- 0.8  
15 - 2 -- 2.0 （可以）

11 - 3 -- 0.46
12 - 3 -- 0.508
13 - 3 -- 0.96
14 - 3 -- 1.15
15 - 3 -- 1.13

12 - 4 -- 0.53
13 - 4 -- 0.53
14 - 4 -- 0.64

         */
        class LotteryDataAnalyzer {
            constructor() {
                this.noLongStreak = 0;
                this.noHuStreak = 0;
                this.threshold = 15; //  
                this.maxThreshold = this.threshold + 2; //最大投注的次数 11


                this.betAmountCount = 0;
                this.totalBetAmount = 0;
                this.currentAmount = STARTING_AMOUNT;
            }

            testHunhe(typeName) {
                let longBetBo = this.betZhuiTouLongOfMemory(typeName);
                let huBetBo = this.betZhuiTouHuOfMemory(typeName);
                
                if (this.noHuStreak >= this.threshold && this.noLongStreak >= this.threshold) {
                    console.log("出现同时");
                }
                return longBetBo || huBetBo;
            }

            betZhuiTouLongOfMemory(curType) {
                let betType = "龙";
                if (curType === betType) {
                    if (this.noLongStreak >= this.threshold && this.noLongStreak < this.maxThreshold) {
                        this.currentAmount = STARTING_AMOUNT;
                    }
                    this.noLongStreak = 0;
                    return null;
                }

                this.noLongStreak++;
                if (this.noLongStreak < this.threshold) {
                    return null;
                } else if (this.noLongStreak < this.maxThreshold) {
                    let betBo = { type: betType, amount: this.currentAmount };
                    this.currentAmount *= 2;
                    return betBo;
                }
                return null;
            }

            betZhuiTouHuOfMemory(curType) {
                let betType = "虎";
                if (curType === betType) {
                    if (this.noHuStreak >= this.threshold && this.noHuStreak < this.maxThreshold) {
                        this.currentAmount = STARTING_AMOUNT;
                    }
                    this.noHuStreak = 0;
                    return null;
                }

                this.noHuStreak++;
                if (this.noHuStreak < this.threshold) {
                    return null;
                } else if (this.noHuStreak < this.maxThreshold) {
                    let betBo = { type: betType, amount: this.currentAmount };
                    this.currentAmount *= 2;
                    return betBo;
                }
                return null;
            }

            bet(type) {
                const amount = STARTING_AMOUNT * Math.pow(2, this.betAmountCount);
                
                this.totalBetAmount += amount;
                this.betAmountCount++;
                return { type, amount };
            }

            resetBetting() {
                if (this.betAmountCount > 0) {
                
                    this.betAmountCount = 0;
                }
            }

        
        }

        /**
         * 创建分析器实例并运行分析
         */
        const analyzer = new LotteryDataAnalyzer();
        let totalBetCount = 0;
        let totalBetAmount = 0;
        let maxBetAmount = 0;
        let profitSituation = 0;
        const historyData = str.split('');

        for (let i = 10; i < historyData.length - 1; i++) {
            let curTypeName = historyData[i];
            let apply = analyzer.testHunhe(curTypeName);
            let nextTypeName = historyData[i + 1];

            if (apply) {
                totalBetCount++;
                maxBetAmount = Math.max(maxBetAmount, apply.amount);
                totalBetAmount += apply.amount;
                profitSituation += apply.amount * 0.04; // 流水
                
                if (nextTypeName === apply.type) {
                    profitSituation += apply.amount;
                } else {
                    profitSituation -= apply.amount;
                }
            }
        }

        console.error('-------- 统计分析 --------');
        console.log(`投注总次数: ${totalBetCount}`);
        console.log(`总投注金额: ${totalBetAmount}`);
        console.log(`最大单次投注: ${maxBetAmount}`);
        console.log(`需要准备的金额: ${maxBetAmount*2}`);
        console.log(`盈利情况不带流水: ${profitSituation - totalBetAmount*0.04}`);
        console.log(`总体盈利情况: ${profitSituation}`);
        console.log(`获得的倍数情况: ${profitSituation/(maxBetAmount*2)}`);

    </script>
</body>

</html>
