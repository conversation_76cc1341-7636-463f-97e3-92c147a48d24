package cn.iocoder.yudao.module.vote.enums;

import cn.iocoder.yudao.framework.common.exception.ErrorCode;
import cn.iocoder.yudao.framework.common.exception.enums.ServiceErrorCodeRange;

/**
 * Member 错误码枚举类
 *
 * vote 系统，使用 1-020-000-000 段
 * 一共 10 位，分成四段
 *
 * 第一段，1 位，类型
 *      1 - 业务级别异常
 *      x - 预留
 * 第二段，3 位，系统类型
 *      020 - 投票系统
 *      ... - ...
 * 第三段，3 位，模块
 *      不限制规则。
 *      一般建议，每个系统里面，可能有多个模块，可
 *          001 - 活动 模块
 *          002 - 作品 模块
 * 第四段，3 位，错误码
 *       不限制规则。
 *       一般建议，每个模块自增。
 * @see ServiceErrorCodeRange
 */
public interface ErrorCodeConstants {
    // ========== 职位信息  ==========
    ErrorCode JOB_POSITIONS_NOT_EXISTS = new ErrorCode(1020003001, "职位信息不存在");
    // ========== 投票活动 1020001xxx
    ErrorCode ACTIVITY_NOT_EXISTS = new ErrorCode(1020001001, "投票活动不存在");
    ErrorCode MATCH_NOT_EXISTS = new ErrorCode(1020002001, "比赛不存在");
    ErrorCode MATCH_TIME_NOT_EXISTS = new ErrorCode(1020002002, "比赛时间不存在");
    // ========== 彩票活动 1020003xxx
    ErrorCode MACAUJC_NOT_EXISTS = new ErrorCode(1020003001, "彩票不存在");
    // ========== kuai28.com  1020004xxx==========
    ErrorCode LOTTERY_NOT_EXISTS = new ErrorCode(1020004001, "kuai28.com不存在");
    // ========== 网红主播 1020005xxx ==========
    ErrorCode CYBER_STAR_NOT_EXISTS = new ErrorCode(1020005001, "网红主播不存在");
}
