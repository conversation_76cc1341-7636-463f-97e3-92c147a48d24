<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
  xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
  xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
  <parent>
    <artifactId>yudao-module-vote</artifactId>
    <groupId>cn.iocoder.boot</groupId>
    <version>${revision}</version> <!-- 1. 修改 version 为 ${revision} -->
  </parent>
  <modelVersion>4.0.0</modelVersion>

  <artifactId>yudao-module-vote-api</artifactId>
  <packaging>jar</packaging> <!-- 2. 新增 packaging 为 jar -->

  <name>${project.artifactId}</name> <!-- 3. 新增 name 为 ${project.artifactId} -->


  <description> <!-- 4. 新增 description 为该模块的描述 -->
    投票 模块 API，暴露给其它模块调用
  </description>


  <properties>

  </properties>

  <dependencies>  <!-- 5. 新增 yudao-common 依赖 -->
    <dependency>
      <groupId>cn.iocoder.boot</groupId>
      <artifactId>yudao-common</artifactId>
    </dependency>
  </dependencies>
</project>