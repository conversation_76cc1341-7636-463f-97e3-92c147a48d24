<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
  xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
  xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
  <parent>
    <artifactId>yudao</artifactId>
    <groupId>cn.iocoder.boot</groupId>
    <version>${revision}</version> <!-- 1. 修改 version 为 ${revision} -->
  </parent>
  <modelVersion>4.0.0</modelVersion>

  <artifactId>yudao-module-vote</artifactId>
  <packaging>pom</packaging> <!-- 2. 新增 packaging 为 pom -->

  <name>${project.artifactId}</name>
  <modules>
    <module>yudao-module-vote-api</module>
    <module>yudao-module-vote-biz</module>
  </modules> <!-- 3. 新增 name 为 ${project.artifactId} -->
  <properties>

  </properties>

  <description> <!-- 4. 新增 description 为该模块的描述 -->
     投票 模块，主要实现 活动以及作品、作品的投票内容 等功能。
  </description>
</project>