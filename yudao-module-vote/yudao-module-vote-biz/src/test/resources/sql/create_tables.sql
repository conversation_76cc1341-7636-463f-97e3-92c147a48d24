-- 将该建表 SQL 语句，添加到 yudao-module-vote-biz 模块的 test/resources/sql/create_tables.sql 文件里
CREATE TABLE IF NOT EXISTS "vote_activity" (
                                               "id" bigint NOT NULL GENERATED BY DEFAULT AS IDENTITY,
                                               "name" varchar,
                                               "banner_image" varchar,
                                               "home_image" varchar,
                                               "prize_image" varchar,
                                               "introduce_image" varchar,
                                               "share_title" varchar,
                                               "notice" varchar,
                                               "background" varchar,
                                               "start_time" varchar,
                                               "end_time" varchar,
                                               "view_number" int,
                                               "vote_number" int,
                                               "gift_show_number" int,
                                               "go_wx_appid" varchar,
                                               "status" varchar NOT NULL,
                                               "votes_per_person" int,
                                               "player_max_count" int,
                                               "theme" varchar,
                                               "revision" int,
                                               "creator" varchar DEFAULT '',
                                               "create_time" datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
                                               "updater" varchar DEFAULT '',
                                               "update_time" datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                                               "deleted" bit NOT NULL DEFAULT FALSE,
                                               "tenant_id" bigint NOT NULL,
                                               PRIMARY KEY ("id")
    ) COMMENT '投票活动';


