package cn.iocoder.yudao.module.vote.service.lottery;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.collection.ListUtil;
import cn.hutool.core.io.FileUtil;
import cn.hutool.core.io.resource.ResourceUtil;
import cn.hutool.core.text.csv.CsvData;
import cn.hutool.core.text.csv.CsvReader;
import cn.hutool.core.text.csv.CsvRow;
import cn.hutool.core.text.csv.CsvUtil;
import cn.hutool.core.util.ArrayUtil;
import cn.hutool.core.util.StrUtil;
import cn.iocoder.yudao.framework.common.statistics.LongestStatistical;
import cn.iocoder.yudao.framework.common.util.collection.ArrayUtils;
import cn.iocoder.yudao.framework.common.util.collection.ListCountUtils;
import cn.iocoder.yudao.module.vote.dal.dataobject.lottery.LotteryDO;
import lombok.extern.slf4j.Slf4j;
import org.jetbrains.annotations.NotNull;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

import java.util.*;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Collectors;

import static cn.iocoder.yudao.framework.common.util.collection.ListCountUtils.*;

@Slf4j
public class CsvLotteryDataAnalyzer {
    List<LotteryDO> data;
    LongestStatistical<Integer,LotteryDO> longestStatistical;
    final List<Integer> countList = new ArrayList<>();
    AtomicInteger atomicInteger = new AtomicInteger();
    // 重置
    @BeforeEach
    public  void reset(){

        longestStatistical = new LongestStatistical<Integer,LotteryDO>() {
            @Override
            public Collection<Integer> incrementKeys(LotteryDO obj) {
                List<Integer> list = Arrays.stream(ArrayUtil.range(0,28)).boxed().collect(Collectors.toList()) ;
                list.remove(obj.getSum());
                return list;
            }

            @Override
            public Collection<Integer> resetKeys(LotteryDO obj) {
                Integer sum = obj.getSum();
                if(sum == 0 || sum ==27){
                    Integer count = this.currentMissCount.get(sum);
                    countList.add(count);
                    if(count>994 && count < 994+101){
                        atomicInteger.addAndGet(980);
                        System.out.println("中奖:"+ sum + ":" + count);
                    } else if (count >= 994+101) {
                        atomicInteger.addAndGet(-100);
                        System.out.println("没中奖:"+ sum + ":" + count);
                    }

                }
                return Arrays.asList(sum);
            }
        };
        CsvReader reader = CsvUtil.getReader();
        data = reader.read(ResourceUtil.getUtf8Reader("lottery/vote_lottery.csv") , LotteryDO.class);
        // 按照时间从远到近，开始排序操作。
        data = data.stream().sorted(Comparator.comparing(LotteryDO::getOpenTime)).collect(Collectors.toList());
    }


    @NotNull
    private List<String> getTypeList() {
        List<String> typeList = new ArrayList<>();
        // 从文件中读取CSV数据.

        for (LotteryDO lotteryDO : data) {
            String openCode = lotteryDO.getOpenCode();
            int firstNum = Integer.parseInt(openCode.substring(0, 1));
            int lastNum = Integer.parseInt(openCode.substring(openCode.length() - 1));
            String type_name = "";
            if (firstNum > lastNum) {
                type_name = "龙";
            } else if (firstNum < lastNum) {
                type_name = "虎";
            } else {
                type_name = "和";
            }
            typeList.add(type_name);
        }
        return typeList;
    }
    /**
     * 测试 追投的机会 ‘龙’ ‘虎’
     */
    @Test
    public void testLongestStatistical() {
        for (LotteryDO lotteryDO : data) {
            longestStatistical.put(lotteryDO);
        }
        System.out.println(longestStatistical.copyOf());

        System.out.println(ListCountUtils.average(countList));
        System.out.println(atomicInteger.get());
    }
    /**
     * 测试 追投的机会 ‘龙’ ‘虎’
     */
    @Test
    public void testZhuitou() {
        List<String> typeList = getTypeList();
        List<Integer> consecutiveFourCount = new ArrayList<>();
        // 下面就是不同的策略验证方法啦。
        // 1. 查看连续5把及其以上是相同的类型的。有多少个。
        // 检查连续5把及其以上是相同的类型
        // 检查连续4把相同的类型

        // 统计连续不出现的次数
        List<Integer> longStreaks = new ArrayList<>();
        List<Integer> huStreaks = new ArrayList<>();
        int noLongStreak = 0;
        int noHuStreak = 0;
        int threshold = 7;
        for (int i = 0; i < typeList.size(); i++) {
            String typeName = typeList.get(i);

            // 计算不出龙的连续次数
            if (!typeName.equals("龙")) {
                noLongStreak++;
            } else {
                if (noLongStreak >= threshold) {
                    longStreaks.add(noLongStreak);
                    consecutiveFourCount.add(noLongStreak);
                }
                noLongStreak = 0;
            }

            // 计算不出虎的连续次数
            if (!typeName.equals("虎")) {
                noHuStreak++;
            } else {
                if (noHuStreak >= threshold) {
                    huStreaks.add(noHuStreak);
                    consecutiveFourCount.add(noHuStreak);
                }
                noHuStreak = 0;
            }

        }

        // 检查最后一段
        if (noLongStreak >= threshold) {
            longStreaks.add(noLongStreak);
            consecutiveFourCount.add(noLongStreak);
        }
        if (noHuStreak >= threshold) {
            huStreaks.add(noHuStreak);
            consecutiveFourCount.add(noHuStreak);
        }
        // 143
        int maxSize = threshold + 2;
        List<Integer> countConsecutiveThanLength = countConsecutiveThanLength(consecutiveFourCount, maxSize);
        System.out.println("最大连续不出的情况是："+max(consecutiveFourCount));
        log.debug("[{}],连续4把相同的类型次数分布情况: {}" , consecutiveFourCount.size(),consecutiveFourCount);
        log.debug("[{}],统计列表中连续高于{}的子序列数: {}" ,countConsecutiveThanLength.size(),maxSize, countConsecutiveThanLength);
    }


    /**
     * 测试 追投的机会 ‘和’
     */
    @Test
    public void testZhuitouHe() {
        List<String> typeList = getTypeList();
        List<Integer> consecutiveFourCount = new ArrayList<>();
        // 下面就是不同的策略验证方法啦。
        // 1. 查看连续5把及其以上是相同的类型的。有多少个。
        // 检查连续5把及其以上是相同的类型
        // 检查连续4把相同的类型

        int noHe = 0;
        int threshold = 35;
        for (int i = 0; i < typeList.size(); i++) {
            String typeName = typeList.get(i);
            // noHe
            if (!typeName.equals("和")) {
                noHe++;
            } else {
                if (noHe >= threshold) {
                    consecutiveFourCount.add(noHe);
                }
                noHe = 0;
            }
        }

        // 检查最后一段
        if (noHe >= threshold) {
            consecutiveFourCount.add(noHe);
        }
        // 143
        int than = threshold + 9;
        List<Integer> countConsecutiveThanLength = countConsecutiveThanLength(consecutiveFourCount, than);
        log.debug("[{}把],连续{}把相同的类型次数分布情况: {}" , consecutiveFourCount.size(),threshold,consecutiveFourCount);
        log.debug("[{}把],统计列表中连续高于{}的子序列数: {}" ,countConsecutiveThanLength.size(), than,countConsecutiveThanLength);
    }


    @Test
    public void testCreateActivity_success() {
        List<String> typeList = getTypeList();
        log.debug("[{}]" , typeList);
        // 下面就是不同的策略验证方法啦。
        // 1. 查看连续5把及其以上是相同的类型的。有多少个。
        // 检查连续5把及其以上是相同的类型
        // 检查连续4把相同的类型
        List<Integer> consecutiveFourCount = countConsecutiveSameType(typeList, 5);
        List<Integer> countConsecutiveLessThanEight = countConsecutiveLessThanLength(consecutiveFourCount, 8);
        List<Integer> countConsecutiveThanLength = countConsecutiveThanLength(countConsecutiveLessThanEight, 18);
        log.debug("[{}],连续4把相同的类型次数分布情况: {}" , consecutiveFourCount.size(),consecutiveFourCount);
        log.debug("[{}],统计列表中连续低于7的子序列数: {}" ,countConsecutiveLessThanEight.size(), countConsecutiveLessThanEight);
        log.debug("[{}],统计列表中连续高于15的子序列数: {}" ,countConsecutiveThanLength.size(), countConsecutiveThanLength);
        //
    }



    /**
     * 混合策略。
     * 策略 1 (虎虎虎虎 预测：虎虎虎)
     * 策略 2 (龙龙龙龙 预测：龙龙龙)
     * 策略 3 (龙虎虎龙虎虎 预测：龙虎虎)
     * 策略 4 (虎龙龙虎龙龙 预测：虎龙龙)
     * 策略 5 (龙虎龙 预测：虎龙虎)
     * 策略 6 (虎龙虎 预测：龙虎龙)
     * 策略 7 (龙龙虎虎 预测：龙龙虎)
     * 策略 8 (虎虎龙龙 预测：虎虎龙)
     */
    @Test
    public void testHunhe() {
        List<String> typeList = getTypeList();
        String allResult = StrUtil.join("", typeList);
//        allResult = "龙和虎龙和虎龙和虎";
        System.out.println(allResult);
        List<Integer> list = new ArrayList<>();
        // 龙和虎龙和虎龙和虎. 龙龙和龙龙和龙虎龙龙龙龙龙龙龙
        for (int i = 5; i < allResult.length()-5; i++) {
            int resInt = 0;

            String ce1_2_7_8 = allResult.substring(i - 3, i+1);
            String ce1_2_7_8_res = allResult.substring(i+1, i+4);
            // 策略1
            if("虎虎虎虎".equals(ce1_2_7_8)){
                if("虎虎虎".equals(ce1_2_7_8_res)){
                    resInt = 1;
                }else{
                    resInt = -1;
                }
            }
            // 策略2
            if("龙龙龙龙".equals(ce1_2_7_8)){
                if("龙龙龙".equals(ce1_2_7_8_res)){
                    resInt = 2;
                }else{
                    resInt = -2;
                }
            }
            // 策略7
            if("龙龙虎虎".equals(ce1_2_7_8)){
                if("龙龙虎".equals(ce1_2_7_8_res)){
                    resInt = 7;
                }else{
                    resInt = -7;
                }
            }
            // 策略8
            if("虎虎龙龙".equals(ce1_2_7_8)){
                if("虎虎龙".equals(ce1_2_7_8_res)){
                    resInt = 8;
                }else{
                    resInt = -8;
                }
            }
            String ce3_4 = allResult.substring(i - 5, i+1);
            String ce3_4_res = allResult.substring(i+1, i+4);
            // 策略3
            if("龙虎虎龙虎虎".equals(ce3_4)){
                if("龙虎虎".equals(ce3_4_res)){
                    resInt = 3;
                }else{
                    resInt = -3;
                }
            }
            // 策略 4 (虎龙龙虎龙龙 预测：虎龙龙)
            if("虎龙龙虎龙龙".equals(ce3_4)){
                if("虎龙龙".equals(ce3_4_res)){
                    resInt = 4;
                }else{
                    resInt = -4;
                }
            }
            //
            String ce5_6 = allResult.substring(i - 2, i+1);
            String ce5_6_res = allResult.substring(i+1, i+4);
            // 策略5
            if("龙虎龙".equals(ce5_6)){
                if("虎龙虎".equals(ce5_6_res)){
                    resInt = 5;
                }else{
                    resInt = -5;
                }
            }
            // 策略6
            if("虎龙虎".equals(ce5_6)){
                if("龙虎龙".equals(ce5_6_res)){
                    resInt = 6;
                }else{
                    resInt = -6;
                }
            }
            // 没有对应的策略。
            if(resInt != 0){
                list.add(resInt);
            }

        }
        List<Integer> countList = new ArrayList<>();
        countList.addAll(diffConsecutiveSame(list,7));
        countList.addAll(diffConsecutiveSame(list,8));
//        countList = list; // 不过滤
        //
        List<Integer> countConsecutiveLessThanEight = countConsecutiveLessThanLength(countList, 0);
        List<Integer> countConsecutiveThanLength = countConsecutiveThanLength(countConsecutiveLessThanEight, 18);
        log.debug("[{}],混合策略: {}" , countList.size(),countList);
        log.debug("[{}],18大轮----统计列表中连续低于0的子序列数: {}" ,countConsecutiveLessThanEight.size(), countConsecutiveLessThanEight);
        log.debug("[{}],统计列表中连续高于18的子序列数: {}" ,countConsecutiveThanLength.size(), countConsecutiveThanLength);
    }


}
