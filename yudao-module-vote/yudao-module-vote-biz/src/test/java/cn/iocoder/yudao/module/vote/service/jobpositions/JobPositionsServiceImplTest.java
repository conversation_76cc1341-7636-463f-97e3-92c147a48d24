package cn.iocoder.yudao.module.vote.service.jobpositions;

import org.junit.jupiter.api.Disabled;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.mock.mockito.MockBean;

import javax.annotation.Resource;

import cn.iocoder.yudao.framework.test.core.ut.BaseDbUnitTest;

import cn.iocoder.yudao.module.vote.controller.admin.jobpositions.vo.*;
import cn.iocoder.yudao.module.vote.dal.dataobject.jobpositions.JobPositionsDO;
import cn.iocoder.yudao.module.vote.dal.mysql.jobpositions.JobPositionsMapper;
import cn.iocoder.yudao.framework.common.pojo.PageResult;

import javax.annotation.Resource;
import org.springframework.context.annotation.Import;
import java.util.*;
import java.time.LocalDateTime;

import static cn.hutool.core.util.RandomUtil.*;
import static cn.iocoder.yudao.module.vote.enums.ErrorCodeConstants.*;
import static cn.iocoder.yudao.framework.test.core.util.AssertUtils.*;
import static cn.iocoder.yudao.framework.test.core.util.RandomUtils.*;
import static cn.iocoder.yudao.framework.common.util.date.LocalDateTimeUtils.*;
import static cn.iocoder.yudao.framework.common.util.object.ObjectUtils.*;
import static cn.iocoder.yudao.framework.common.util.date.DateUtils.*;
import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;

/**
 * {@link JobPositionsServiceImpl} 的单元测试类
 *
 * <AUTHOR>
 */
@Import(JobPositionsServiceImpl.class)
public class JobPositionsServiceImplTest extends BaseDbUnitTest {

    @Resource
    private JobPositionsServiceImpl jobPositionsService;

    @Resource
    private JobPositionsMapper jobPositionsMapper;

    @Test
    public void testCreateJobPositions_success() {
        // 准备参数
        JobPositionsCreateReqVO reqVO = randomPojo(JobPositionsCreateReqVO.class);

        // 调用
        Integer jobPositionsId = jobPositionsService.createJobPositions(reqVO);
        // 断言
        assertNotNull(jobPositionsId);
        // 校验记录的属性是否正确
        JobPositionsDO jobPositions = jobPositionsMapper.selectById(jobPositionsId);
        assertPojoEquals(reqVO, jobPositions);
    }

    @Test
    public void testUpdateJobPositions_success() {
        // mock 数据
        JobPositionsDO dbJobPositions = randomPojo(JobPositionsDO.class);
        jobPositionsMapper.insert(dbJobPositions);// @Sql: 先插入出一条存在的数据
        // 准备参数
        JobPositionsUpdateReqVO reqVO = randomPojo(JobPositionsUpdateReqVO.class, o -> {
            o.setPositionId(dbJobPositions.getPositionId()); // 设置更新的 ID
        });

        // 调用
        jobPositionsService.updateJobPositions(reqVO);
        // 校验是否更新正确
        JobPositionsDO jobPositions = jobPositionsMapper.selectById(reqVO.getPositionId()); // 获取最新的
        assertPojoEquals(reqVO, jobPositions);
    }

    @Test
    public void testUpdateJobPositions_notExists() {
        // 准备参数
        JobPositionsUpdateReqVO reqVO = randomPojo(JobPositionsUpdateReqVO.class);

        // 调用, 并断言异常
        assertServiceException(() -> jobPositionsService.updateJobPositions(reqVO), JOB_POSITIONS_NOT_EXISTS);
    }

    @Test
    public void testDeleteJobPositions_success() {
        // mock 数据
        JobPositionsDO dbJobPositions = randomPojo(JobPositionsDO.class);
        jobPositionsMapper.insert(dbJobPositions);// @Sql: 先插入出一条存在的数据
        // 准备参数
        Integer id = dbJobPositions.getPositionId();

        // 调用
        jobPositionsService.deleteJobPositions(id);
       // 校验数据不存在了
       assertNull(jobPositionsMapper.selectById(id));
    }

    @Test
    public void testDeleteJobPositions_notExists() {
        // 准备参数
        Integer id = -1;

        // 调用, 并断言异常
        assertServiceException(() -> jobPositionsService.deleteJobPositions(id), JOB_POSITIONS_NOT_EXISTS);
    }

    @Test
    @Disabled  // TODO 请修改 null 为需要的值，然后删除 @Disabled 注解
    public void testGetJobPositionsPage() {
       // mock 数据
       JobPositionsDO dbJobPositions = randomPojo(JobPositionsDO.class, o -> { // 等会查询到
           o.setCompanyId(null);
           o.setCompanyName(null);
           o.setAnnoId(null);
           o.setAnnoName(null);
           o.setAnnoLink(null);
           o.setType(null);
           o.setTitle(null);
           o.setLocation(null);
           o.setLink(null);
           o.setSpecialty(null);
           o.setEducation(null);
           o.setEducationCnlist(null);
           o.setExperience(null);
           o.setSalaryRange(null);
           o.setContent(null);
           o.setJobStartTime(null);
           o.setJobEndTime(null);
           o.setJobStartTimeCn(null);
           o.setJobEndTimeCn(null);
           o.setIsCollect(null);
           o.setCreateTime(null);
       });
       jobPositionsMapper.insert(dbJobPositions);
       // 测试 companyId 不匹配
       jobPositionsMapper.insert(cloneIgnoreId(dbJobPositions, o -> o.setCompanyId(null)));
       // 测试 companyName 不匹配
       jobPositionsMapper.insert(cloneIgnoreId(dbJobPositions, o -> o.setCompanyName(null)));
       // 测试 annoId 不匹配
       jobPositionsMapper.insert(cloneIgnoreId(dbJobPositions, o -> o.setAnnoId(null)));
       // 测试 annoName 不匹配
       jobPositionsMapper.insert(cloneIgnoreId(dbJobPositions, o -> o.setAnnoName(null)));
       // 测试 annoLink 不匹配
       jobPositionsMapper.insert(cloneIgnoreId(dbJobPositions, o -> o.setAnnoLink(null)));
       // 测试 type 不匹配
       jobPositionsMapper.insert(cloneIgnoreId(dbJobPositions, o -> o.setType(null)));
       // 测试 title 不匹配
       jobPositionsMapper.insert(cloneIgnoreId(dbJobPositions, o -> o.setTitle(null)));
       // 测试 location 不匹配
       jobPositionsMapper.insert(cloneIgnoreId(dbJobPositions, o -> o.setLocation(null)));
       // 测试 link 不匹配
       jobPositionsMapper.insert(cloneIgnoreId(dbJobPositions, o -> o.setLink(null)));
       // 测试 specialty 不匹配
       jobPositionsMapper.insert(cloneIgnoreId(dbJobPositions, o -> o.setSpecialty(null)));
       // 测试 education 不匹配
       jobPositionsMapper.insert(cloneIgnoreId(dbJobPositions, o -> o.setEducation(null)));
       // 测试 educationCnlist 不匹配
       jobPositionsMapper.insert(cloneIgnoreId(dbJobPositions, o -> o.setEducationCnlist(null)));
       // 测试 experience 不匹配
       jobPositionsMapper.insert(cloneIgnoreId(dbJobPositions, o -> o.setExperience(null)));
       // 测试 salaryRange 不匹配
       jobPositionsMapper.insert(cloneIgnoreId(dbJobPositions, o -> o.setSalaryRange(null)));
       // 测试 content 不匹配
       jobPositionsMapper.insert(cloneIgnoreId(dbJobPositions, o -> o.setContent(null)));
       // 测试 jobStartTime 不匹配
       jobPositionsMapper.insert(cloneIgnoreId(dbJobPositions, o -> o.setJobStartTime(null)));
       // 测试 jobEndTime 不匹配
       jobPositionsMapper.insert(cloneIgnoreId(dbJobPositions, o -> o.setJobEndTime(null)));
       // 测试 jobStartTimeCn 不匹配
       jobPositionsMapper.insert(cloneIgnoreId(dbJobPositions, o -> o.setJobStartTimeCn(null)));
       // 测试 jobEndTimeCn 不匹配
       jobPositionsMapper.insert(cloneIgnoreId(dbJobPositions, o -> o.setJobEndTimeCn(null)));
       // 测试 isCollect 不匹配
       jobPositionsMapper.insert(cloneIgnoreId(dbJobPositions, o -> o.setIsCollect(null)));
       // 测试 createTime 不匹配
       jobPositionsMapper.insert(cloneIgnoreId(dbJobPositions, o -> o.setCreateTime(null)));
       // 准备参数
       JobPositionsPageReqVO reqVO = new JobPositionsPageReqVO();
       reqVO.setCompanyId(null);
       reqVO.setCompanyName(null);
       reqVO.setAnnoId(null);
       reqVO.setAnnoName(null);
       reqVO.setAnnoLink(null);
       reqVO.setType(null);
       reqVO.setTitle(null);
       reqVO.setLocation(null);
       reqVO.setLink(null);
       reqVO.setSpecialty(null);
       reqVO.setEducation(null);
       reqVO.setEducationCnlist(null);
       reqVO.setExperience(null);
       reqVO.setSalaryRange(null);
       reqVO.setContent(null);
       reqVO.setJobStartTime(buildBetweenTime(2023, 2, 1, 2023, 2, 28));
       reqVO.setJobEndTime(buildBetweenTime(2023, 2, 1, 2023, 2, 28));
       reqVO.setJobStartTimeCn(null);
       reqVO.setJobEndTimeCn(null);
       reqVO.setIsCollect(null);
       reqVO.setCreateTime(buildBetweenTime(2023, 2, 1, 2023, 2, 28));

       // 调用
       PageResult<JobPositionsDO> pageResult = jobPositionsService.getJobPositionsPage(reqVO);
       // 断言
       assertEquals(1, pageResult.getTotal());
       assertEquals(1, pageResult.getList().size());
       assertPojoEquals(dbJobPositions, pageResult.getList().get(0));
    }

    @Test
    @Disabled  // TODO 请修改 null 为需要的值，然后删除 @Disabled 注解
    public void testGetJobPositionsList() {
       // mock 数据
       JobPositionsDO dbJobPositions = randomPojo(JobPositionsDO.class, o -> { // 等会查询到
           o.setCompanyId(null);
           o.setCompanyName(null);
           o.setAnnoId(null);
           o.setAnnoName(null);
           o.setAnnoLink(null);
           o.setType(null);
           o.setTitle(null);
           o.setLocation(null);
           o.setLink(null);
           o.setSpecialty(null);
           o.setEducation(null);
           o.setEducationCnlist(null);
           o.setExperience(null);
           o.setSalaryRange(null);
           o.setContent(null);
           o.setJobStartTime(null);
           o.setJobEndTime(null);
           o.setJobStartTimeCn(null);
           o.setJobEndTimeCn(null);
           o.setIsCollect(null);
           o.setCreateTime(null);
       });
       jobPositionsMapper.insert(dbJobPositions);
       // 测试 companyId 不匹配
       jobPositionsMapper.insert(cloneIgnoreId(dbJobPositions, o -> o.setCompanyId(null)));
       // 测试 companyName 不匹配
       jobPositionsMapper.insert(cloneIgnoreId(dbJobPositions, o -> o.setCompanyName(null)));
       // 测试 annoId 不匹配
       jobPositionsMapper.insert(cloneIgnoreId(dbJobPositions, o -> o.setAnnoId(null)));
       // 测试 annoName 不匹配
       jobPositionsMapper.insert(cloneIgnoreId(dbJobPositions, o -> o.setAnnoName(null)));
       // 测试 annoLink 不匹配
       jobPositionsMapper.insert(cloneIgnoreId(dbJobPositions, o -> o.setAnnoLink(null)));
       // 测试 type 不匹配
       jobPositionsMapper.insert(cloneIgnoreId(dbJobPositions, o -> o.setType(null)));
       // 测试 title 不匹配
       jobPositionsMapper.insert(cloneIgnoreId(dbJobPositions, o -> o.setTitle(null)));
       // 测试 location 不匹配
       jobPositionsMapper.insert(cloneIgnoreId(dbJobPositions, o -> o.setLocation(null)));
       // 测试 link 不匹配
       jobPositionsMapper.insert(cloneIgnoreId(dbJobPositions, o -> o.setLink(null)));
       // 测试 specialty 不匹配
       jobPositionsMapper.insert(cloneIgnoreId(dbJobPositions, o -> o.setSpecialty(null)));
       // 测试 education 不匹配
       jobPositionsMapper.insert(cloneIgnoreId(dbJobPositions, o -> o.setEducation(null)));
       // 测试 educationCnlist 不匹配
       jobPositionsMapper.insert(cloneIgnoreId(dbJobPositions, o -> o.setEducationCnlist(null)));
       // 测试 experience 不匹配
       jobPositionsMapper.insert(cloneIgnoreId(dbJobPositions, o -> o.setExperience(null)));
       // 测试 salaryRange 不匹配
       jobPositionsMapper.insert(cloneIgnoreId(dbJobPositions, o -> o.setSalaryRange(null)));
       // 测试 content 不匹配
       jobPositionsMapper.insert(cloneIgnoreId(dbJobPositions, o -> o.setContent(null)));
       // 测试 jobStartTime 不匹配
       jobPositionsMapper.insert(cloneIgnoreId(dbJobPositions, o -> o.setJobStartTime(null)));
       // 测试 jobEndTime 不匹配
       jobPositionsMapper.insert(cloneIgnoreId(dbJobPositions, o -> o.setJobEndTime(null)));
       // 测试 jobStartTimeCn 不匹配
       jobPositionsMapper.insert(cloneIgnoreId(dbJobPositions, o -> o.setJobStartTimeCn(null)));
       // 测试 jobEndTimeCn 不匹配
       jobPositionsMapper.insert(cloneIgnoreId(dbJobPositions, o -> o.setJobEndTimeCn(null)));
       // 测试 isCollect 不匹配
       jobPositionsMapper.insert(cloneIgnoreId(dbJobPositions, o -> o.setIsCollect(null)));
       // 测试 createTime 不匹配
       jobPositionsMapper.insert(cloneIgnoreId(dbJobPositions, o -> o.setCreateTime(null)));
       // 准备参数
       JobPositionsExportReqVO reqVO = new JobPositionsExportReqVO();
       reqVO.setCompanyId(null);
       reqVO.setCompanyName(null);
       reqVO.setAnnoId(null);
       reqVO.setAnnoName(null);
       reqVO.setAnnoLink(null);
       reqVO.setType(null);
       reqVO.setTitle(null);
       reqVO.setLocation(null);
       reqVO.setLink(null);
       reqVO.setSpecialty(null);
       reqVO.setEducation(null);
       reqVO.setEducationCnlist(null);
       reqVO.setExperience(null);
       reqVO.setSalaryRange(null);
       reqVO.setContent(null);
       reqVO.setJobStartTime(buildBetweenTime(2023, 2, 1, 2023, 2, 28));
       reqVO.setJobEndTime(buildBetweenTime(2023, 2, 1, 2023, 2, 28));
       reqVO.setJobStartTimeCn(null);
       reqVO.setJobEndTimeCn(null);
       reqVO.setIsCollect(null);
       reqVO.setCreateTime(buildBetweenTime(2023, 2, 1, 2023, 2, 28));

       // 调用
       List<JobPositionsDO> list = jobPositionsService.getJobPositionsList(reqVO);
       // 断言
       assertEquals(1, list.size());
       assertPojoEquals(dbJobPositions, list.get(0));
    }

}
