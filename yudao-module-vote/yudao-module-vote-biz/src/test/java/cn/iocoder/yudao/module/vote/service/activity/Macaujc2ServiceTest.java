package cn.iocoder.yudao.module.vote.service.activity;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.lang.WeightRandom;
import cn.hutool.core.text.csv.CsvUtil;
import cn.hutool.core.text.csv.CsvWriter;
import cn.hutool.core.thread.ThreadUtil;
import cn.hutool.core.util.*;
import cn.hutool.http.HttpUtil;
import cn.hutool.json.JSONArray;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import cn.iocoder.yudao.framework.common.core.ArrayMap;
import cn.iocoder.yudao.framework.common.core.CounterMap;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.io.PrintStream;
import java.util.*;
import java.util.function.Consumer;
import java.util.stream.Collectors;

// 旧版澳门六合彩,用于分析就行。
// https://macaujc.com/api/

/**
 * 大小
 * 以指定出现位置的号码大于或等于25为大，小于或等于24为小，开出49为和。
 * <p>
 * 单双
 * 以指定出现位置的号码为单数或双数下注，开出49为和。
 * <p>
 * 合大合小
 * 以指定出现位置的号码个位和十位数字总和来判断胜负，和数大于或等于7为合大，小于或等于6为合小，开出49为和。
 * <p>
 * 合单合双以指定出现位置的号码个位和十位数字总和来判断单双，开出49为和。
 * <p>
 * 大单小单大双小双
 * 以指定出现位置的号码大小单双混合判断，开出49为和。
 * <p>
 * 尾大尾小
 * 以指定出现位置的号码末尾数来判断大小，0尾~4尾为小、5尾~9尾为大，开出49为和。
 * <p>
 * 家禽野兽
 * 开出的特码属于十二生肖中的牛、马、羊、鸡、狗、猪号码为家禽，属于十二生肖中的鼠、虎、龙、蛇、兔、猴号码为野兽；
 size: 1-大
 oddEven: 2-单
 sumSize: 3-合大
 sumOddEven: 4-合双
 combined: 大单
 tailSize: 6-尾大
 animal: 7-野兽
 12Animal: 牛
 color: 红波




 */
@Slf4j
@Service
public class Macaujc2ServiceTest {
    public static final String KIND = "macaujc2";
    public static final String URL = "https://api.macaumarksix.com/history/"+KIND+"/y/";
    /** 当前遍历的计数 */public static final CounterMap<String> CURRENT_MISS_COUNT = new CounterMap(new HashMap<String, Integer>(), 0);
    /** 最长时间的计数 */public static final CounterMap<String> MAX_MISS_COUNT = new CounterMap(new HashMap<String, Integer>(), 0);
    /** 最近没出现的计数 */ public static final CounterMap<String> LAST_MISS_COUNT = new CounterMap(new HashMap<String, Integer>(), 0);
    /** 权重随机的计数 */ public static final CounterMap<Integer> WEIGHT_OBJ_COUNT = new CounterMap(new HashMap<Integer, Integer>(), 1);
    /** 所有没出现的计数 */ public static final ArrayMap<String, Integer> ALL_MISS_COUNT = new ArrayMap(new HashMap<String, List<Integer>>());
    public static String [] fields = new String[]{
            "size","oddEven","sumSize","sumOddEven","combined","tailSize" , "animal" , "12Animal", "color", "missCounts" , "sumCount" , "code"
    };
    public static int j = 0;
    public static int maxIndex = 0;
    // {head-1=42, head-2=20, head-3=33, head-4=23, head-0=32}
    //2: {head-1=42, head-2=33, head-3=33, head-4=25, head-0=32}
    public static void main(String[] args) throws InterruptedException {
        System.out.println( DateUtil.now() + " ;类型：" + ( "macaujc".equals(KIND) ? "澳門六合彩" : "新澳門六合彩" )  );
        JSONArray dataArray = new JSONArray();
        String years[] = {"2024", "2023", "2022", "2021", "2020"};
        for (String year : years) {
            String body = HttpUtil.get(URL + year);
            JSONObject jsonObject = JSONUtil.parseObj(body);
            JSONArray jsonArray = jsonObject.getJSONArray("data");
            dataArray.addAll(jsonArray);
        }

        CsvWriter csvWriter = CsvUtil.getWriter("/Users/<USER>/WebstormProjects/proxy-chrom-plun/html/" + KIND + ".csv", CharsetUtil.CHARSET_UTF_8);
        csvWriter.writeHeaderLine(fields);

        maxIndex = dataArray.size() - 1;
        for (; j <= Macaujc2Info.SPECIAL_INDEX; j++) {
            for (int i = 0; i <= maxIndex; i++) {
                fields = new String[fields.length];
                Macaujc2Info info = dataArray.getBean(i, Macaujc2Info.class);
                int specialCode = info.getCode(j);
                String specialWave = info.getWave(j);
                String specialZodiac = info.getZodiac(j);
                print("期号: " + info.getExpect() + " 码数: " + NumberUtil.decimalFormat("00", specialCode) + "\t");

                // 大小分析
                analyzeSize(specialCode);
                print(" ");

                // 单双分析
                analyzeOddEven(specialCode);
                print(" ");
                // 合大合小分析
                analyzeSumSize(specialCode);
                print(" ");
                // 合单合双分析
                analyzeSumOddEven(specialCode);
                print(" ");
                // 大单、小单、大双、小双分析
                analyzeCombined(specialCode);
                print(" ");
                // 尾大尾小分析
                analyzeTailSize(specialCode);
                print(" ");
                // 家禽野兽分析
                analyzeAnimal(specialZodiac);
                print(" ");
                //
                analyze12Animal(specialZodiac);
                print(" ");
                // 色波分析
                analyzeColor(specialWave);

                // 计算特码的头数
                updateMissCounts(info);

                // 分析所有的合数内容。
                analyzeSumCount(specialCode);

                if(j == Macaujc2Info.SPECIAL_INDEX){
                    fields[11] = specialCode + "";
                    csvWriter.writeLine(fields);
                }
            }




            // 多维度分析没有出得内容。
            System.err.println("\n\n-----多维度分析------第几个码位：" + (j+1) );
//            log.warn("历史最长记录:{}", MAX_MISS_COUNT);
//            log.error("最近没出的内容:{}", LAST_MISS_COUNT);

            Set<String> set = ALL_MISS_COUNT.keySet();
            // 使用Stream排序并收集回List
            List<String> sortedList = set.stream()
                    .sorted(Comparator.comparingInt(s -> Integer.parseInt(s.split("-")[0])))
                    .collect(Collectors.toList());

            for (String key : sortedList) {
//            String key = entry.getKey();
                List<Integer> list = ALL_MISS_COUNT.get(key);
                list = list.stream().filter(n -> n > 4).collect(Collectors.toList()); // 过滤掉小于0的元素
                // 计算平均值
                double average = list.stream()
                        .mapToInt(Integer::intValue)
                        .average().orElse(Double.NaN);


                Integer lastCount = LAST_MISS_COUNT.get(key);
                Integer maxCount = MAX_MISS_COUNT.get(key);
                String outStr = StrUtil.format("{}:历史中总共没出现的长庄情况分别是{}期。平均出现期数：{}；\n历史最长记录:{}；最近没出的内容:{}\n"
                        , key.split("-")[1], StrUtil.join(",", list), NumberUtil.roundStr(average, 1)
                        , maxCount, lastCount);
                if( lastCount >  maxCount/2 ){
                    System.err.println(outStr);
                }else{
                    System.out.println(outStr);
                }

            }

            prediction(40,System.err);
            prediction(30,System.err);

            // clear
            CURRENT_MISS_COUNT.clear();
            MAX_MISS_COUNT.clear();
            LAST_MISS_COUNT.clear();
            ALL_MISS_COUNT.clear();
            WEIGHT_OBJ_COUNT.clear();
//            ServletUtil.getClientIPByHeader()
        }


    }
    public static void prediction(int count , PrintStream printStream){
        Set<Integer> randomSet = new HashSet<>();
        // 模拟随机的内容。
        do{
            WeightRandom.WeightObj<Integer>[] weightObjs = new WeightRandom.WeightObj[49];
            for (int k = 0; k < weightObjs.length; k++) {
                weightObjs [k] = new WeightRandom.WeightObj<>(k + 1 , maxIndex - WEIGHT_OBJ_COUNT.getOrDefault(k + 1) );
            }
            WeightRandom<Integer> weightedRandom = RandomUtil.weightRandom(weightObjs);
            ThreadUtil.safeSleep(RandomUtil.randomInt(1,30));
            Integer next = weightedRandom.next();
            randomSet.add(next);
        }while(randomSet.size()<count);

        if( "macaujc2".equals(KIND) && count == 30 ){
            Set<Integer> largeSet = new HashSet<>();
            for (int i = 1; i <= 49; i++) {
                largeSet.add(i);
            }
            largeSet.removeAll(randomSet);
//            randomSet = largeSet;
        }
        for (Integer next : randomSet) {
            printStream.print(next + " ");
        }
        printStream.println();

    }

    public static void for49(Consumer<Integer> consumer){
        for (int i = 1; i <= 49; i++) {
            consumer.accept(i);
        }
    }
    public static void print(String str) {
//        System.out.print(str);
    }

    // 大小分析
    public static void analyzeSize(int num) {
        String aKey = "1-大";
        String bKey = "1-小";
        String result = null;
        if (num >= 25) {
            result = aKey;
            int oldCount = CURRENT_MISS_COUNT.setDefaultValues(result);
            Integer ifAbsent = LAST_MISS_COUNT.putIfAbsent(result, oldCount);
            if(ifAbsent == null){
                for49(code->{ if(code >= 25) WEIGHT_OBJ_COUNT.addAndGet(code,oldCount); });
            }
            ALL_MISS_COUNT.add(result, oldCount);

            String key = bKey;
            // 未出现的头数 +1 。
            int current_count = CURRENT_MISS_COUNT.incrementAndGet(key);
            // 之后再 更新最长未出现期数
            if (current_count > MAX_MISS_COUNT.getOrDefault(key)) {
                MAX_MISS_COUNT.put(key, current_count);

            }
        } else {
            result = bKey;
            int oldCount = CURRENT_MISS_COUNT.setDefaultValues(result);
            Integer ifAbsent = LAST_MISS_COUNT.putIfAbsent(result, oldCount);
            if(ifAbsent == null){
                for49(code->{ if(code < 25) WEIGHT_OBJ_COUNT.addAndGet(code,oldCount); });
            }
            ALL_MISS_COUNT.add(result, oldCount);

            String key = aKey;
            // 未出现的头数 +1 。
            int current_count = CURRENT_MISS_COUNT.incrementAndGet(key);
            // 之后再 更新最长未出现期数
            if (current_count > MAX_MISS_COUNT.getOrDefault(key)) {
                MAX_MISS_COUNT.put(key, current_count);
            }

        }
        print("结果: " + result);
        fields[0] = result;
    }

    // 单双分析
    public static void analyzeOddEven(int num) {
        String aKey = "2-双";
        String bKey = "2-单";
        String result = null;
        if (num % 2 == 0) {
            result = aKey;
            int oldCount = CURRENT_MISS_COUNT.setDefaultValues(result);
            Integer ifAbsent = LAST_MISS_COUNT.putIfAbsent(result, oldCount);
            if(ifAbsent == null){
                for49(code->{ if(code % 2 == 0) WEIGHT_OBJ_COUNT.addAndGet(code,oldCount); });
            }
            ALL_MISS_COUNT.add(result, oldCount);

            String key = bKey;
            // 未出现的头数 +1 。
            int current_count = CURRENT_MISS_COUNT.incrementAndGet(key);
            // 之后再 更新最长未出现期数
            if (current_count > MAX_MISS_COUNT.getOrDefault(key)) {
                MAX_MISS_COUNT.put(key, current_count);
            }
        } else {
            result = bKey;
            int oldCount = CURRENT_MISS_COUNT.setDefaultValues(result);
            Integer ifAbsent = LAST_MISS_COUNT.putIfAbsent(result, oldCount);
            if(ifAbsent == null){
                for49(code->{ if(code % 2 != 0) WEIGHT_OBJ_COUNT.addAndGet(code,oldCount); });
            }
            ALL_MISS_COUNT.add(result, oldCount);

            String key = aKey;
            // 未出现的头数 +1 。
            int current_count = CURRENT_MISS_COUNT.incrementAndGet(key);
            // 之后再 更新最长未出现期数
            if (current_count > MAX_MISS_COUNT.getOrDefault(key)) {
                MAX_MISS_COUNT.put(key, current_count);
            }

        }
        print("结果: " + result);
        fields[1] = result;
    }

    // 合大合小分析
    public static void analyzeSumSize(int num) {
        {
            String aKey = "3-合大";
            String bKey = "3-合小";
            String result = null;
            int sum = (num / 10) + (num % 10);
            if (sum >= 7) {
                result = aKey;
                int oldCount = CURRENT_MISS_COUNT.setDefaultValues(result);
                Integer ifAbsent = LAST_MISS_COUNT.putIfAbsent(result, oldCount);
                if(ifAbsent == null){
                    for49(code->{ if( ((code / 10) + (code % 10))  >= 7) WEIGHT_OBJ_COUNT.addAndGet(code,oldCount); });
                }
                ALL_MISS_COUNT.add(result, oldCount);

                String key = bKey;
                // 未出现的头数 +1 。
                int current_count = CURRENT_MISS_COUNT.incrementAndGet(key);
                // 之后再 更新最长未出现期数
                if (current_count > MAX_MISS_COUNT.getOrDefault(key)) {
                    MAX_MISS_COUNT.put(key, current_count);
                }
            } else {
                result = bKey;
                int oldCount = CURRENT_MISS_COUNT.setDefaultValues(result);
                Integer ifAbsent = LAST_MISS_COUNT.putIfAbsent(result, oldCount);
                if(ifAbsent == null){
                    for49(code->{ if( ((code / 10) + (code % 10))  < 7) WEIGHT_OBJ_COUNT.addAndGet(code,oldCount); });
                }
                ALL_MISS_COUNT.add(result, oldCount);

                String key = aKey;
                // 未出现的头数 +1 。
                int current_count = CURRENT_MISS_COUNT.incrementAndGet(key);
                // 之后再 更新最长未出现期数
                if (current_count > MAX_MISS_COUNT.getOrDefault(key)) {
                    MAX_MISS_COUNT.put(key, current_count);
                }

            }
            print("结果: " + result);
            fields[2] = result;
        }
    }

    // 合单合双分析
    public static void analyzeSumOddEven(int num) {
        {
            String aKey = "4-合双";
            String bKey = "4-合单";
            String result = null;
            int sum = (num / 10) + (num % 10);
            if (sum % 2 == 0) {
                result = aKey;
                int oldCount = CURRENT_MISS_COUNT.setDefaultValues(result);
                Integer ifAbsent = LAST_MISS_COUNT.putIfAbsent(result, oldCount);
                if(ifAbsent == null){
                    for49(code->{ if( ((code / 10) + (code % 10))  == 0 ) WEIGHT_OBJ_COUNT.addAndGet(code,oldCount); });
                }
                ALL_MISS_COUNT.add(result, oldCount);

                String key = bKey;
                // 未出现的头数 +1 。
                int current_count = CURRENT_MISS_COUNT.incrementAndGet(key);
                // 之后再 更新最长未出现期数
                if (current_count > MAX_MISS_COUNT.getOrDefault(key)) {
                    MAX_MISS_COUNT.put(key, current_count);
                }

            } else {

                result = bKey;
                int oldCount = CURRENT_MISS_COUNT.setDefaultValues(result);
                Integer ifAbsent = LAST_MISS_COUNT.putIfAbsent(result, oldCount);
                if(ifAbsent == null){
                    for49(code->{ if( ((code / 10) + (code % 10))  != 0 ) WEIGHT_OBJ_COUNT.addAndGet(code,oldCount); });
                }
                ALL_MISS_COUNT.add(result, oldCount);

                String key = aKey;
                // 未出现的头数 +1 。
                int current_count = CURRENT_MISS_COUNT.incrementAndGet(key);
                // 之后再 更新最长未出现期数
                if (current_count > MAX_MISS_COUNT.getOrDefault(key)) {
                    MAX_MISS_COUNT.put(key, current_count);
                }
            }
            print("结果: " + result);
            fields[3] = result;
        }
    }

    // 大单、小单、大双、小双分析
    public static void analyzeCombined(int num) {
        int resulteIndex = -1;
        if (num >= 25 && num % 2 != 0) {
            resulteIndex = 0;
        } else if (num >= 25) {
            resulteIndex = 1;
        } else if (num % 2 != 0) {
            resulteIndex = 2;
        } else {
            resulteIndex = 3;
        }

        String[] allArray = {"大单", "大双", "小单", "小双"};
        // 遍历所有头数
        for (int head = 0; head < allArray.length; head++) {
            String key = "5-" + allArray[head];
            if (head == resulteIndex) {
                // 如果当前头数出现，重置未出现期数
//                currentMissCount.put(head, 0);
                int oldCount = CURRENT_MISS_COUNT.setDefaultValues(key);
                LAST_MISS_COUNT.putIfAbsent(key, oldCount);
                ALL_MISS_COUNT.add(key, oldCount);
            } else {
                // 未出现的头数 +1 。
                int current_count = CURRENT_MISS_COUNT.incrementAndGet(key);
                // 之后再 更新最长未出现期数
                if (current_count > MAX_MISS_COUNT.getOrDefault(key)) {
                    MAX_MISS_COUNT.put(key, current_count);
                }
            }
        }


        print("结果: " + allArray[resulteIndex]);
        fields[4] = allArray[resulteIndex];
    }

    // 尾大尾小分析
    public static void analyzeTailSize(int num) {
        {
            int tail = num % 10;
            String aKey = "6-尾大";
            String bKey = "6-尾小";
            String result = null;
            if (tail >= 5) {
                result = aKey;
                int oldCount = CURRENT_MISS_COUNT.setDefaultValues(result);
                Integer ifAbsent = LAST_MISS_COUNT.putIfAbsent(result, oldCount);
                if(ifAbsent == null){
                    for49(code->{ if( (code % 10)  >= 5 ) WEIGHT_OBJ_COUNT.addAndGet(code,oldCount); });
                }
                ALL_MISS_COUNT.add(result, oldCount);

                String key = bKey;
                // 未出现的头数 +1 。
                int current_count = CURRENT_MISS_COUNT.incrementAndGet(key);
                // 之后再 更新最长未出现期数
                if (current_count > MAX_MISS_COUNT.getOrDefault(key)) {
                    MAX_MISS_COUNT.put(key, current_count);
                }

            } else {
                result = bKey;
                int oldCount = CURRENT_MISS_COUNT.setDefaultValues(result);
                Integer ifAbsent = LAST_MISS_COUNT.putIfAbsent(result, oldCount);
                if(ifAbsent == null){
                    for49(code->{ if( (code % 10)  < 5 ) WEIGHT_OBJ_COUNT.addAndGet(code,oldCount); });
                }
                ALL_MISS_COUNT.add(result, oldCount);

                String key = aKey;
                // 未出现的头数 +1 。
                int current_count = CURRENT_MISS_COUNT.incrementAndGet(key);
                // 之后再 更新最长未出现期数
                if (current_count > MAX_MISS_COUNT.getOrDefault(key)) {
                    MAX_MISS_COUNT.put(key, current_count);
                }

            }
            print("结果: " + result);
            fields[5] = result;
        }
    }

    // 家禽野兽分析
    public static void analyzeAnimal(String zodiac) {
        // 家禽：牛、马、羊、鸡、狗、猪
        // 野兽：鼠、虎、龙、蛇、兔、猴
        List<String> domesticAnimals = Arrays.asList("牛", "馬", "羊", "雞", "狗", "豬");
        List<String> wildAnimals = Arrays.asList("鼠", "虎", "龍", "蛇", "兔", "猴");
        String jKey = "7-家禽";
        String yKey = "7-野兽";
        String result = null;
        if (domesticAnimals.contains(zodiac)) {
            result = jKey;
            int oldCount = CURRENT_MISS_COUNT.setDefaultValues(result);
            LAST_MISS_COUNT.putIfAbsent(result, oldCount);
            ALL_MISS_COUNT.add(result, oldCount);
            String key = yKey;
            // 未出现的头数 +1 。
            int current_count = CURRENT_MISS_COUNT.incrementAndGet(key);
            // 之后再 更新最长未出现期数
            if (current_count > MAX_MISS_COUNT.getOrDefault(key)) {
                MAX_MISS_COUNT.put(key, current_count);
            }
        } else if (wildAnimals.contains(zodiac)) {
            result = yKey;
            int oldCount = CURRENT_MISS_COUNT.setDefaultValues(result);
            LAST_MISS_COUNT.putIfAbsent(result, oldCount);
            ALL_MISS_COUNT.add(result, oldCount);
            String key = jKey;
            // 未出现的头数 +1 。
            int current_count = CURRENT_MISS_COUNT.incrementAndGet(key);
            // 之后再 更新最长未出现期数
            if (current_count > MAX_MISS_COUNT.getOrDefault(key)) {
                MAX_MISS_COUNT.put(key, current_count);
            }
        }
        print("结果: " + result);
        fields[6] = result;
    }

    public static void analyze12Animal(String zodiac) {
        String[] allArray = {"牛", "馬", "羊", "雞", "狗", "豬", "鼠", "虎", "龍", "蛇", "兔", "猴"};
        int resulteIndex = ArrayUtil.indexOf(allArray, zodiac);
        // 遍历所有头数
        for (int head = 0; head < allArray.length; head++) {
            String key = "8-" + allArray[head];
            if (head == resulteIndex) {
                // 如果当前头数出现，重置未出现期数
//                currentMissCount.put(head, 0);
                int oldCount = CURRENT_MISS_COUNT.setDefaultValues(key);
                LAST_MISS_COUNT.putIfAbsent(key, oldCount);
                ALL_MISS_COUNT.add(key, oldCount);
            } else {
                // 未出现的头数 +1 。
                int current_count = CURRENT_MISS_COUNT.incrementAndGet(key);
                // 之后再 更新最长未出现期数
                if (current_count > MAX_MISS_COUNT.getOrDefault(key)) {
                    MAX_MISS_COUNT.put(key, current_count);
                }
            }
        }
        print("生肖: " + allArray[resulteIndex]);
        fields[7] = allArray[resulteIndex];
    }

    // 色波分析
    public static void analyzeColor(String wave) {
        int resulteIndex = -1;
        switch (wave) {
            case "red":
                resulteIndex = 0;
                break;
            case "green":
                resulteIndex = 1;
                break;
            case "blue":
                resulteIndex = 2;
                break;
        }

        String[] waves = {"红波", "绿波", "蓝波"};
        // 遍历所有头数
        for (int head = 0; head < waves.length; head++) {
            String key = "9-" + waves[head];
            if (head == resulteIndex) {
                // 如果当前头数出现，重置未出现期数
//                currentMissCount.put(head, 0);
                int oldCount = CURRENT_MISS_COUNT.setDefaultValues(key);
                LAST_MISS_COUNT.putIfAbsent(key, oldCount);
                ALL_MISS_COUNT.add(key, oldCount);
            } else {
                // 未出现的头数 +1 。
                int current_count = CURRENT_MISS_COUNT.incrementAndGet(key);
                // 之后再 更新最长未出现期数
                if (current_count > MAX_MISS_COUNT.getOrDefault(key)) {
                    MAX_MISS_COUNT.put(key, current_count);
                }
            }
        }


        print("结果: " + waves[resulteIndex] + "\n");

        fields[8] = waves[resulteIndex];
    }


    // 更新头数的未出现期数
    public static void updateMissCounts(Macaujc2Info info) {
        int specialCode = info.getSpecialCode();
        int currentHead = specialCode / 10;
        // 遍历所有头数
        for (int head = 0; head <= 4; head++) {
            String key = "10-" + head + "头数";
            if (head == currentHead) {
                // 如果当前头数出现，重置未出现期数
//                currentMissCount.put(head, 0);
                int oldCount = CURRENT_MISS_COUNT.setDefaultValues(key);
                Integer ifAbsent = LAST_MISS_COUNT.putIfAbsent(key, oldCount);
                if(ifAbsent == null){
                    int finalHead = head;
                    for49(code->{ if( (code / 10)  == finalHead) WEIGHT_OBJ_COUNT.addAndGet(code,oldCount); });
                }
                ALL_MISS_COUNT.add(key, oldCount);
            } else {
                // 未出现的头数 +1 。
                int current_count = CURRENT_MISS_COUNT.incrementAndGet(key);
                // 之后再 更新最长未出现期数
                if (current_count > MAX_MISS_COUNT.getOrDefault(key)) {
                    MAX_MISS_COUNT.put(key, current_count);
//                    System.err.println( key + ":" + info.expect + ":" + current_count);
                }
            }
        }
        fields[9] = currentHead + "";
    }


    // 更新头数的未出现期数
    public static void analyzeSumCount(int num) {
        int sum = (num / 10) + (num % 10);
        // 遍历所有头数
        for (int head = 1; head <= 13; head++) {
            String key = "11-" + head + "合数";
            if (head == sum) {
                // 如果当前头数出现，重置未出现期数
//                currentMissCount.put(head, 0);
                int oldCount = CURRENT_MISS_COUNT.setDefaultValues(key);
                Integer ifAbsent = LAST_MISS_COUNT.putIfAbsent(key, oldCount);
                if(ifAbsent == null){
                    int finalHead = head;
                    for49(code->{ if( ( (code / 10) + (code % 10) )  == finalHead) WEIGHT_OBJ_COUNT.addAndGet(code,oldCount); });
                }
                ALL_MISS_COUNT.add(key, oldCount);
            } else {
                // 未出现的头数 +1 。
                int current_count = CURRENT_MISS_COUNT.incrementAndGet(key);
                // 之后再 更新最长未出现期数
                if (current_count > MAX_MISS_COUNT.getOrDefault(key)) {
                    MAX_MISS_COUNT.put(key, current_count);
                }
            }
        }
        fields[10] = sum + "";
    }
    @Data
    public class Macaujc2Info {
        public static final int SPECIAL_INDEX = 6;
        private String expect;
        private String openCode;
        private String zodiac;
        private String openTime;
        private String wave;

        public String split(String str, int index) {
            String[] split = StrUtil.splitToArray(str, ",");
            return split[index];
        }

        public int getCode(int index) {
            String split = this.split(openCode, index);
            return Integer.parseInt(split);
        }

        public String getZodiac(int index) {
            return this.split(zodiac, index);
        }

        public String getWave(int index) {
            return this.split(wave, index);
        }

        //////////////
        //  获得特码
        ////////////
        public int getSpecialCode() {
            return getCode(SPECIAL_INDEX);
        }

        public String getSpecialZodiac() {
            return this.getZodiac(SPECIAL_INDEX);
        }

        public String getSpecialWave() {
            return this.getWave(SPECIAL_INDEX);
        }
    }

}


/*


余兄，帮我买一个不记名的google账号呗。


-----------------  全部没有实名的账号记录情况。
tomDogTool
<EMAIL>
Uu5-l*%$



965241016212051740



<EMAIL>----Uu5-l*%$----<EMAIL>

https://x.com/HandyHelperMax
Uu5-l*%$x


1. 大卖app
2. 六合
3. 竞技
4. 云南的报销。
5. solana 交易

 */