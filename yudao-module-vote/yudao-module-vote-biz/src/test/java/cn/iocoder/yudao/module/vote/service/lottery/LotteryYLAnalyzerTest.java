package cn.iocoder.yudao.module.vote.service.lottery;

import cn.hutool.core.io.FileUtil;
import cn.hutool.core.io.file.FileWriter;
import cn.hutool.core.io.file.LineSeparator;
import cn.hutool.core.io.resource.ResourceUtil;
import cn.hutool.core.text.csv.CsvReader;
import cn.hutool.core.text.csv.CsvUtil;
import cn.hutool.core.util.CharsetUtil;
import cn.iocoder.yudao.module.vote.dal.dataobject.lottery.LotteryDO;
import cn.iocoder.yudao.module.vote.service.lottery.bo.JiLvBo;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Test;

import java.io.File;
import java.net.URL;
import java.util.*;
import java.util.stream.Collectors;

import static cn.iocoder.yudao.framework.common.util.collection.ListCountUtils.countConsecutiveLessThanLength;
import static cn.iocoder.yudao.framework.common.util.collection.ListCountUtils.countConsecutiveThanLength;

@Slf4j
public class LotteryYLAnalyzerTest {

    LotteryYLAnalyzer lotteryYLAnalyzer = new LotteryYLAnalyzer();
    @Test
    public void queue(){
        Queue<String> typeList = new LinkedList<>();
        typeList.offer("1");
        typeList.offer("2");
        typeList.offer("3");
        typeList.poll();
        typeList.offer("4");
        typeList.offer("5");
        typeList.poll();
        log.debug(""+typeList);
    }

    @Test
    public void testOutText(){
        List<String> typeList = new ArrayList<>();
        // 从文件中读取CSV数据.
        CsvReader reader = CsvUtil.getReader();
        List<LotteryDO> historyData = reader.read(ResourceUtil.getUtf8Reader("lottery/vote_lottery.csv") , LotteryDO.class);
        List<JiLvBo> jilvList = new ArrayList<>();
        // 按照时间从远到近，开始排序操作。   不需要有：.reversed() 操作内容。
        historyData = historyData.stream().sorted(Comparator.comparing(LotteryDO::getOpenTime)).collect(Collectors.toList());
        int length = 1000;
        for (int i = 0; i < historyData.size() - length; i++) {
            List<LotteryDO> subList = historyData.subList(i, length + i);
            jilvList.add(showMissCount(subList));
        }
        String path = "lottery/vote_lottery.log";
        File file = new File(path);
        FileUtil.del(path);
//        FileWriter.create(file, CharsetUtil.CHARSET_UTF_8).writeLines(jilvList, LineSeparator.MAC,true);
        FileUtil.appendUtf8Lines(jilvList, path);
    }

    @Test
    public void testFollow(){
        // 从文件中读取CSV数据.
        CsvReader reader = CsvUtil.getReader();
        List<LotteryDO> historyData = reader.read(ResourceUtil.getUtf8Reader("lottery/vote_lottery.csv") , LotteryDO.class);
        // 按照时间从远到近，开始排序操作。   不需要有：.reversed() 操作内容。
        historyData = historyData.stream().sorted(Comparator.comparing(LotteryDO::getOpenTime)).collect(Collectors.toList());
        int length = 1000;
        int followCount = 0;
        int bigWheel = 3;
        String eventType = null;
        List<Integer> followList = new ArrayList<>();
        List<JiLvBo> jiLvBoList = new ArrayList<>();
        boolean syn = true;
        for (int i = 0; i < historyData.size() - length; i++) {
            List<LotteryDO> subList = historyData.subList(i, length + i);
            if (followCount > 0) {
                // 正在跟注某个 eventType
                JiLvBo jiLvBo = getJiLvBoByMissCount(subList, eventType);

                if (jiLvBo.getProbability() > 0.001) {
                    // 表示该事件已出现，可重置跟注判断
                    jiLvBo.setFollowCount(followCount);
                    followList.add(followCount);
                    jiLvBoList.add(jiLvBo);
                    followCount = 0;
                    eventType = null;
                    bigWheel = 3;
                } else {
                    // 跟注失败，递减跟注次数
                    followCount--;
                    if (followCount == 0) {
                        bigWheel--;
                        jiLvBo.setFollowCount(0);
                        followList.add(0);
                        jiLvBoList.add(jiLvBo);
                        // 如果还没达到最大失败轮数，继续追这个 eventType
                        if (bigWheel > 0) {
                            // 等下一轮数据继续判断是否出现
                            continue;
                        } else {
                            // 超过失败轮次，放弃当前特征
                            eventType = null;
                        }
                    }
                }
            } else {
                // 当前没有跟注动作
                JiLvBo jiLvBo;
                if (false) {
                    // 专注等待之前的 eventType 再次出现
                    jiLvBo = getJiLvBoByMissCount(subList, eventType);
                    if (jiLvBo.getProbability() < 0.001) {
                        followCount = jiLvBo.getFollowCount(); // 重新开始追注
                    }
                } else {
                    // 没有当前特征，选择概率最小的开始追注
                    jiLvBo = showMissCount(subList);
                    if (jiLvBo.getProbability() < 0.001) {
                        followCount = jiLvBo.getFollowCount();
                        eventType = jiLvBo.getEventType();
                    }
                }
            }
        }
//        System.out.println(followList.size() + ";;;" + followList );
        System.out.println(jiLvBoList.size() + ";;;" + jiLvBoList );
        List<Integer> countConsecutiveLessThanLength = countConsecutiveLessThanLength(followList, 1);
        System.out.println(countConsecutiveLessThanLength);
        System.out.println(countConsecutiveThanLength(countConsecutiveLessThanLength, 2));
    }



    @Test
    public void testFollow0_5(){
        // 从文件中读取CSV数据.
        CsvReader reader = CsvUtil.getReader();
        List<LotteryDO> historyData = reader.read(ResourceUtil.getUtf8Reader("lottery/vote_lottery.csv") , LotteryDO.class);
        // 按照时间从远到近，开始排序操作。   不需要有：.reversed() 操作内容。
        historyData = historyData.stream().sorted(Comparator.comparing(LotteryDO::getOpenTime)).collect(Collectors.toList());
        int length = 1000;
        int followCount = 0;
        int bigWheel = 3;
        String eventType = null;
        List<Integer> followList = new ArrayList<>();
        List<JiLvBo> jiLvBoList = new ArrayList<>();
        boolean syn = true;
        for (int i = 0; i < historyData.size() - length; i++) {
            List<LotteryDO> subList = historyData.subList(i, length + i);
            if (followCount > 0) {
                // 正在跟注某个 eventType
                JiLvBo jiLvBo = getJiLvBoByMissCount(subList, eventType);

                if (jiLvBo.getMissCount() == 0) {
                    // 表示该事件已出现，可重置跟注判断
                    jiLvBo.setFollowCount(followCount);
                    followList.add(followCount);
                    jiLvBoList.add(jiLvBo);
                    followCount = 0;
                    eventType = null;
                    bigWheel = 3;
                } else {
                    // 跟注失败，递减跟注次数
                    followCount--;
                    if (followCount == 0) {
                        bigWheel--;
                        jiLvBo.setFollowCount(0);
                        followList.add(0);
                        jiLvBoList.add(jiLvBo);
                        // 如果还没达到最大失败轮数，继续追这个 eventType
                        if (bigWheel > 0) {
                            // 等下一轮数据继续判断是否出现
                            continue;
                        } else {
                            // 超过失败轮次，放弃当前特征
                            eventType = null;
                        }
                    }
                }
            } else {
                // 当前没有跟注动作
                JiLvBo jiLvBo;
                if (false) {
                    // 专注等待之前的 eventType 再次出现
                    jiLvBo = getJiLvBoByMissCount(subList, eventType);
                    if (jiLvBo.getMissCount() > 8) {
                        followCount = jiLvBo.getFollowCount(); // 重新开始追注
                    }
                } else {
                    // 没有当前特征，选择概率最小的开始追注
                    jiLvBo = showMissCount(subList);
                    if (jiLvBo.getMissCount() > 8) {
                        followCount = jiLvBo.getFollowCount();
                        eventType = jiLvBo.getEventType();
                    }
                }
            }
        }
//        System.out.println(followList.size() + ";;;" + followList );
        System.out.println(jiLvBoList.size() + ";;;" + jiLvBoList );
        List<Integer> countConsecutiveLessThanLength = countConsecutiveLessThanLength(followList, 1);
        System.out.println(countConsecutiveLessThanLength);
        System.out.println(countConsecutiveThanLength(countConsecutiveLessThanLength, 2));
    }

    private JiLvBo getJiLvBoByMissCount(List<LotteryDO> historyData,String eventType) {
        Map<String, JiLvBo> results = lotteryYLAnalyzer.analyzeMissProbabilities(historyData);
        return results.get(eventType);
    }

    private JiLvBo showMissCount(List<LotteryDO> historyData) {
        Map<String, JiLvBo> results = lotteryYLAnalyzer.analyzeMissProbabilities(historyData);
        Optional<JiLvBo> min = results.values().stream().min(Comparator.comparingDouble(JiLvBo::getProbability));
        return min.get();
    }
}
