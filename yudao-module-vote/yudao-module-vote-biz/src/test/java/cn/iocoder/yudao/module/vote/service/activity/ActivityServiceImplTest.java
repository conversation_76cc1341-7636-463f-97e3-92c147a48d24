package cn.iocoder.yudao.module.vote.service.activity;

import org.junit.jupiter.api.Disabled;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.mock.mockito.MockBean;

import javax.annotation.Resource;

import cn.iocoder.yudao.framework.test.core.ut.BaseDbUnitTest;

import cn.iocoder.yudao.module.vote.controller.admin.activity.vo.*;
import cn.iocoder.yudao.module.vote.dal.dataobject.activity.ActivityDO;
import cn.iocoder.yudao.module.vote.dal.mysql.activity.ActivityMapper;
import cn.iocoder.yudao.framework.common.pojo.PageResult;

import javax.annotation.Resource;
import org.springframework.context.annotation.Import;
import java.util.*;
import java.time.LocalDateTime;

import static cn.hutool.core.util.RandomUtil.*;
import static cn.iocoder.yudao.module.vote.enums.ErrorCodeConstants.*;
import static cn.iocoder.yudao.framework.test.core.util.AssertUtils.*;
import static cn.iocoder.yudao.framework.test.core.util.RandomUtils.*;
import static cn.iocoder.yudao.framework.common.util.date.LocalDateTimeUtils.*;
import static cn.iocoder.yudao.framework.common.util.object.ObjectUtils.*;
import static cn.iocoder.yudao.framework.common.util.date.DateUtils.*;
import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;

/**
* {@link ActivityServiceImpl} 的单元测试类
*
* <AUTHOR>
*/
@Import(ActivityServiceImpl.class)
public class ActivityServiceImplTest extends BaseDbUnitTest {

    @Resource
    private ActivityServiceImpl activityService;

    @Resource
    private ActivityMapper activityMapper;

    @Test
    public void testCreateActivity_success() {
        // 准备参数
        ActivityCreateReqVO reqVO = randomPojo(ActivityCreateReqVO.class);

        // 调用
        Long activityId = activityService.createActivity(reqVO);
        // 断言
        assertNotNull(activityId);
        // 校验记录的属性是否正确
        ActivityDO activity = activityMapper.selectById(activityId);
        assertPojoEquals(reqVO, activity);
    }

    @Test
    public void testUpdateActivity_success() {
        // mock 数据
        ActivityDO dbActivity = randomPojo(ActivityDO.class);
        activityMapper.insert(dbActivity);// @Sql: 先插入出一条存在的数据
        // 准备参数
        ActivityUpdateReqVO reqVO = randomPojo(ActivityUpdateReqVO.class, o -> {
            o.setId(dbActivity.getId()); // 设置更新的 ID
        });

        // 调用
        activityService.updateActivity(reqVO);
        // 校验是否更新正确
        ActivityDO activity = activityMapper.selectById(reqVO.getId()); // 获取最新的
        assertPojoEquals(reqVO, activity);
    }

    @Test
    public void testUpdateActivity_notExists() {
        // 准备参数
        ActivityUpdateReqVO reqVO = randomPojo(ActivityUpdateReqVO.class);

        // 调用, 并断言异常
        assertServiceException(() -> activityService.updateActivity(reqVO), ACTIVITY_NOT_EXISTS);
    }

    @Test
    public void testDeleteActivity_success() {
        // mock 数据
        ActivityDO dbActivity = randomPojo(ActivityDO.class);
        activityMapper.insert(dbActivity);// @Sql: 先插入出一条存在的数据
        // 准备参数
        Long id = dbActivity.getId();

        // 调用
        activityService.deleteActivity(id);
       // 校验数据不存在了
       assertNull(activityMapper.selectById(id));
    }

    @Test
    public void testDeleteActivity_notExists() {
        // 准备参数
        Long id = randomLongId();

        // 调用, 并断言异常
        assertServiceException(() -> activityService.deleteActivity(id), ACTIVITY_NOT_EXISTS);
    }

    @Test
    @Disabled  // TODO 请修改 null 为需要的值，然后删除 @Disabled 注解
    public void testGetActivityPage() {
       // mock 数据
       ActivityDO dbActivity = randomPojo(ActivityDO.class, o -> { // 等会查询到
           o.setName(null);
           o.setBannerImage(null);
           o.setHomeImage(null);
           o.setPrizeImage(null);
           o.setIntroduceImage(null);
           o.setShareTitle(null);
           o.setNotice(null);
           o.setBackground(null);
           o.setStartTime(null);
           o.setEndTime(null);
           o.setViewNumber(null);
           o.setVoteNumber(null);
           o.setGiftShowNumber(null);
           o.setGoWxAppid(null);
           o.setStatus(null);
           o.setVotesPerPerson(null);
           o.setPlayerMaxCount(null);
           o.setTheme(null);
           o.setRevision(null);
           o.setDeptId(null);
           o.setUserId(null);
           o.setCreateTime(null);
       });
       activityMapper.insert(dbActivity);
       // 测试 name 不匹配
       activityMapper.insert(cloneIgnoreId(dbActivity, o -> o.setName(null)));
       // 测试 bannerImage 不匹配
       activityMapper.insert(cloneIgnoreId(dbActivity, o -> o.setBannerImage(null)));
       // 测试 homeImage 不匹配
       activityMapper.insert(cloneIgnoreId(dbActivity, o -> o.setHomeImage(null)));
       // 测试 prizeImage 不匹配
       activityMapper.insert(cloneIgnoreId(dbActivity, o -> o.setPrizeImage(null)));
       // 测试 introduceImage 不匹配
       activityMapper.insert(cloneIgnoreId(dbActivity, o -> o.setIntroduceImage(null)));
       // 测试 shareTitle 不匹配
       activityMapper.insert(cloneIgnoreId(dbActivity, o -> o.setShareTitle(null)));
       // 测试 notice 不匹配
       activityMapper.insert(cloneIgnoreId(dbActivity, o -> o.setNotice(null)));
       // 测试 background 不匹配
       activityMapper.insert(cloneIgnoreId(dbActivity, o -> o.setBackground(null)));
       // 测试 startTime 不匹配
       activityMapper.insert(cloneIgnoreId(dbActivity, o -> o.setStartTime(null)));
       // 测试 endTime 不匹配
       activityMapper.insert(cloneIgnoreId(dbActivity, o -> o.setEndTime(null)));
       // 测试 viewNumber 不匹配
       activityMapper.insert(cloneIgnoreId(dbActivity, o -> o.setViewNumber(null)));
       // 测试 voteNumber 不匹配
       activityMapper.insert(cloneIgnoreId(dbActivity, o -> o.setVoteNumber(null)));
       // 测试 giftShowNumber 不匹配
       activityMapper.insert(cloneIgnoreId(dbActivity, o -> o.setGiftShowNumber(null)));
       // 测试 goWxAppid 不匹配
       activityMapper.insert(cloneIgnoreId(dbActivity, o -> o.setGoWxAppid(null)));
       // 测试 status 不匹配
       activityMapper.insert(cloneIgnoreId(dbActivity, o -> o.setStatus(null)));
       // 测试 votesPerPerson 不匹配
       activityMapper.insert(cloneIgnoreId(dbActivity, o -> o.setVotesPerPerson(null)));
       // 测试 playerMaxCount 不匹配
       activityMapper.insert(cloneIgnoreId(dbActivity, o -> o.setPlayerMaxCount(null)));
       // 测试 theme 不匹配
       activityMapper.insert(cloneIgnoreId(dbActivity, o -> o.setTheme(null)));
       // 测试 revision 不匹配
       activityMapper.insert(cloneIgnoreId(dbActivity, o -> o.setRevision(null)));
       // 测试 deptId 不匹配
       activityMapper.insert(cloneIgnoreId(dbActivity, o -> o.setDeptId(null)));
       // 测试 userId 不匹配
       activityMapper.insert(cloneIgnoreId(dbActivity, o -> o.setUserId(null)));
       // 测试 createTime 不匹配
       activityMapper.insert(cloneIgnoreId(dbActivity, o -> o.setCreateTime(null)));
       // 准备参数
       ActivityPageReqVO reqVO = new ActivityPageReqVO();
       reqVO.setName(null);
       reqVO.setBannerImage(null);
       reqVO.setHomeImage(null);
       reqVO.setPrizeImage(null);
       reqVO.setIntroduceImage(null);
       reqVO.setShareTitle(null);
       reqVO.setNotice(null);
       reqVO.setBackground(null);
       reqVO.setStartTime(buildBetweenTime(2023, 2, 1, 2023, 2, 28));
       reqVO.setEndTime(buildBetweenTime(2023, 2, 1, 2023, 2, 28));
       reqVO.setViewNumber(null);
       reqVO.setVoteNumber(null);
       reqVO.setGiftShowNumber(null);
       reqVO.setGoWxAppid(null);
       reqVO.setStatus(null);
       reqVO.setVotesPerPerson(null);
       reqVO.setPlayerMaxCount(null);
       reqVO.setTheme(null);
       reqVO.setRevision(null);
       reqVO.setDeptId(null);
       reqVO.setUserId(null);
       reqVO.setCreateTime(buildBetweenTime(2023, 2, 1, 2023, 2, 28));

       // 调用
       PageResult<ActivityDO> pageResult = activityService.getActivityPage(reqVO);
       // 断言
       assertEquals(1, pageResult.getTotal());
       assertEquals(1, pageResult.getList().size());
       assertPojoEquals(dbActivity, pageResult.getList().get(0));
    }

    @Test
    @Disabled  // TODO 请修改 null 为需要的值，然后删除 @Disabled 注解
    public void testGetActivityList() {
       // mock 数据
       ActivityDO dbActivity = randomPojo(ActivityDO.class, o -> { // 等会查询到
           o.setName(null);
           o.setBannerImage(null);
           o.setHomeImage(null);
           o.setPrizeImage(null);
           o.setIntroduceImage(null);
           o.setShareTitle(null);
           o.setNotice(null);
           o.setBackground(null);
           o.setStartTime(null);
           o.setEndTime(null);
           o.setViewNumber(null);
           o.setVoteNumber(null);
           o.setGiftShowNumber(null);
           o.setGoWxAppid(null);
           o.setStatus(null);
           o.setVotesPerPerson(null);
           o.setPlayerMaxCount(null);
           o.setTheme(null);
           o.setRevision(null);
           o.setDeptId(null);
           o.setUserId(null);
           o.setCreateTime(null);
       });
       activityMapper.insert(dbActivity);
       // 测试 name 不匹配
       activityMapper.insert(cloneIgnoreId(dbActivity, o -> o.setName(null)));
       // 测试 bannerImage 不匹配
       activityMapper.insert(cloneIgnoreId(dbActivity, o -> o.setBannerImage(null)));
       // 测试 homeImage 不匹配
       activityMapper.insert(cloneIgnoreId(dbActivity, o -> o.setHomeImage(null)));
       // 测试 prizeImage 不匹配
       activityMapper.insert(cloneIgnoreId(dbActivity, o -> o.setPrizeImage(null)));
       // 测试 introduceImage 不匹配
       activityMapper.insert(cloneIgnoreId(dbActivity, o -> o.setIntroduceImage(null)));
       // 测试 shareTitle 不匹配
       activityMapper.insert(cloneIgnoreId(dbActivity, o -> o.setShareTitle(null)));
       // 测试 notice 不匹配
       activityMapper.insert(cloneIgnoreId(dbActivity, o -> o.setNotice(null)));
       // 测试 background 不匹配
       activityMapper.insert(cloneIgnoreId(dbActivity, o -> o.setBackground(null)));
       // 测试 startTime 不匹配
       activityMapper.insert(cloneIgnoreId(dbActivity, o -> o.setStartTime(null)));
       // 测试 endTime 不匹配
       activityMapper.insert(cloneIgnoreId(dbActivity, o -> o.setEndTime(null)));
       // 测试 viewNumber 不匹配
       activityMapper.insert(cloneIgnoreId(dbActivity, o -> o.setViewNumber(null)));
       // 测试 voteNumber 不匹配
       activityMapper.insert(cloneIgnoreId(dbActivity, o -> o.setVoteNumber(null)));
       // 测试 giftShowNumber 不匹配
       activityMapper.insert(cloneIgnoreId(dbActivity, o -> o.setGiftShowNumber(null)));
       // 测试 goWxAppid 不匹配
       activityMapper.insert(cloneIgnoreId(dbActivity, o -> o.setGoWxAppid(null)));
       // 测试 status 不匹配
       activityMapper.insert(cloneIgnoreId(dbActivity, o -> o.setStatus(null)));
       // 测试 votesPerPerson 不匹配
       activityMapper.insert(cloneIgnoreId(dbActivity, o -> o.setVotesPerPerson(null)));
       // 测试 playerMaxCount 不匹配
       activityMapper.insert(cloneIgnoreId(dbActivity, o -> o.setPlayerMaxCount(null)));
       // 测试 theme 不匹配
       activityMapper.insert(cloneIgnoreId(dbActivity, o -> o.setTheme(null)));
       // 测试 revision 不匹配
       activityMapper.insert(cloneIgnoreId(dbActivity, o -> o.setRevision(null)));
       // 测试 deptId 不匹配
       activityMapper.insert(cloneIgnoreId(dbActivity, o -> o.setDeptId(null)));
       // 测试 userId 不匹配
       activityMapper.insert(cloneIgnoreId(dbActivity, o -> o.setUserId(null)));
       // 测试 createTime 不匹配
       activityMapper.insert(cloneIgnoreId(dbActivity, o -> o.setCreateTime(null)));
       // 准备参数
       ActivityExportReqVO reqVO = new ActivityExportReqVO();
       reqVO.setName(null);
       reqVO.setBannerImage(null);
       reqVO.setHomeImage(null);
       reqVO.setPrizeImage(null);
       reqVO.setIntroduceImage(null);
       reqVO.setShareTitle(null);
       reqVO.setNotice(null);
       reqVO.setBackground(null);
       reqVO.setStartTime(buildBetweenTime(2023, 2, 1, 2023, 2, 28));
       reqVO.setEndTime(buildBetweenTime(2023, 2, 1, 2023, 2, 28));
       reqVO.setViewNumber(null);
       reqVO.setVoteNumber(null);
       reqVO.setGiftShowNumber(null);
       reqVO.setGoWxAppid(null);
       reqVO.setStatus(null);
       reqVO.setVotesPerPerson(null);
       reqVO.setPlayerMaxCount(null);
       reqVO.setTheme(null);
       reqVO.setRevision(null);
       reqVO.setDeptId(null);
       reqVO.setUserId(null);
       reqVO.setCreateTime(buildBetweenTime(2023, 2, 1, 2023, 2, 28));

       // 调用
       List<ActivityDO> list = activityService.getActivityList(reqVO);
       // 断言
       assertEquals(1, list.size());
       assertPojoEquals(dbActivity, list.get(0));
    }

}
