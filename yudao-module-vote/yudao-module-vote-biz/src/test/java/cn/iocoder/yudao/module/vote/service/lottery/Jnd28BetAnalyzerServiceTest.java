package cn.iocoder.yudao.module.vote.service.lottery;

import cn.hutool.core.collection.ListUtil;
import cn.hutool.core.io.resource.ResourceUtil;
import cn.hutool.core.text.csv.CsvReader;
import cn.hutool.core.text.csv.CsvUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONArray;
import cn.hutool.json.JSONObject;
import cn.iocoder.yudao.framework.common.core.CounterMap;
import cn.iocoder.yudao.module.vote.controller.admin.lottery.vo.LotteryPageReqVO;
import cn.iocoder.yudao.module.vote.dal.dataobject.lottery.LotteryDO;
import cn.iocoder.yudao.module.vote.service.jnd28.Jnd28BetAnalyzerService;
import cn.iocoder.yudao.module.vote.service.jnd28.bo.BetBo;
import cn.iocoder.yudao.module.vote.service.jnd28.counter.Jnd28BetCounter;
import cn.iocoder.yudao.module.vote.service.jnd28.counter.Jnd28BetCounterMemoryImpl;
import org.jetbrains.annotations.NotNull;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeAll;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

import java.util.Collections;
import java.util.Comparator;
import java.util.HashMap;
import java.util.List;
import java.util.concurrent.atomic.AtomicReference;
import java.util.function.BiConsumer;
import java.util.function.BiFunction;
import java.util.stream.Collectors;

public class Jnd28BetAnalyzerServiceTest {
    double profitSituation = 0;
    int totalBetAmount = 0;
    int totalBetCount = 0;
    int maxBetAmount = 0;
    Jnd28BetCounter jnd28BetCounter = null;
    public static final CounterMap<Integer> WEIGHT_OBJ_COUNT = new CounterMap(new HashMap<Integer, Integer>(), 1);
    // 重置
    @BeforeEach
    public  void reset(){
        profitSituation = 0;
        totalBetAmount = 0;
        totalBetCount = 0;
        maxBetAmount = 0;
        jnd28BetCounter = null;
    }
    @AfterEach
    public void println() {
        if (jnd28BetCounter != null) {


            // 具体投注情况
            JSONObject betSituation = jnd28BetCounter.betSituation();
            Integer sum = betSituation.getInt("sum");
            Integer max = betSituation.getInt("max");
            JSONArray betList = betSituation.getJSONArray("betList");
            if(max !=null && sum != null ){
                System.out.println("--------后台计算机统计分析记录情况-------");
                System.out.println(StrUtil.format("总数是:{} , 最大数:{} , 具体投注情况: {}",
                        sum,
                        max,
                        betList));

                int amountSum = jnd28BetCounter.getTotalBetAmount();
                System.out.println("总花费金额：" + amountSum);

                double profitSituationAmount = jnd28BetCounter.getProfitSituation();
                System.out.println("盈利情况：" + profitSituationAmount);

                double overAmount = profitSituationAmount + amountSum * 0.04;
                int needAmount = (int) (1 * Math.pow(2, max + 1));

                System.out.println(StrUtil.format("自带计算器总体情况带流水：{} ; 需要准备的金额: {}; 获得的倍数情况: {}",
                        overAmount,
                        needAmount,
                        String.format("%.2f", overAmount * 1.0 / needAmount)));

            }


            System.out.println("--------真实的运行统计记录-------");
            System.out.println(StrUtil.format("总体出手次数 {} , 总体下注金额 {} , 总体盈利情况 {} , 最大金额是: {} , 需要准备的金额: {} , 获得的倍数情况: {}",
                    totalBetCount,
                    totalBetAmount,
                    profitSituation,
                    maxBetAmount,
                    maxBetAmount * 2,
                    String.format("%.2f", profitSituation / (maxBetAmount * 2))));
        } // 2025-03-25 18:07:26.003 |  INFO 6912 | pool-12-thread-1 [TID: N/A] c.i.y.m.v.s.lottery.LotteryServiceImpl   | [2022/08/26]时间，有数据

        System.out.println(WEIGHT_OBJ_COUNT);
    }


    @NotNull
    private Jnd28BetAnalyzerService runJnd28BetAnalyzerMethod(int httpLimit , BiFunction<Jnd28BetAnalyzerService,Long, BetBo> biFunction) {
        AtomicReference<List<LotteryDO>> atomicReference = new AtomicReference<List<LotteryDO>>();
        Jnd28BetAnalyzerService jnd28BetAnalyzerService = new Jnd28BetAnalyzerService(){
            @Override
            protected List<LotteryDO> getLotteryDOS(String today, int limits) {
                return atomicReference.get();
            }
            @Override
            public Jnd28BetCounter getJnd28BetCounter(String redisKey) {
                return Jnd28BetCounterMemoryImpl.getInstance(redisKey);
            }
        };
        // 读数据
        CsvReader reader = CsvUtil.getReader();
        List<LotteryDO> historyData = reader.read(ResourceUtil.getUtf8Reader("lottery/vote_lottery.csv") , LotteryDO.class);
        // 按照时间从远到近，开始排序操作。   不需要有：.reversed() 操作内容。
        historyData = historyData.stream().sorted(Comparator.comparing(LotteryDO::getOpenTime)).collect(Collectors.toList());

        // 执行投注
        for (int i = httpLimit; i < historyData.size()-1 ; i++) {
            // 生成最新的投注数据。
            List<LotteryDO> subList =  ListUtil.sub(  historyData , i-httpLimit, i+1);
            Collections.reverse(subList);
            atomicReference.set(subList);
            //
            LotteryDO curLotteryDO = historyData.get(i);
            Long fullExpect = curLotteryDO.getFullExpect();
            Integer sum = curLotteryDO.getSum();
            WEIGHT_OBJ_COUNT.incrementAndGet(sum);
            BetBo apply = biFunction.apply(jnd28BetAnalyzerService, fullExpect);
            LotteryDO nextLotteryDO = historyData.get(i+1);
            String nextTypeName = Jnd28BetAnalyzerService.getTypeName(nextLotteryDO);
            if(apply !=null){
                String betType = apply.getType();
                int amount = apply.getAmount();
                if(StrUtil.isNotBlank(betType)){
                    totalBetAmount += amount;
                    profitSituation += amount * 0.04;
                    totalBetCount ++;
                    if(amount > maxBetAmount) maxBetAmount = amount;
                    //
                    if(StrUtil.equals(nextTypeName,betType)){
                        profitSituation += amount;
                    }else{
                        profitSituation -= amount;
                    }
                }
            }
        }

        return jnd28BetAnalyzerService;
    }

    @Test
    public void testGetDaLuDanTiao() {
        //
        Jnd28BetAnalyzerService jnd28BetAnalyzerService = runJnd28BetAnalyzerMethod(2 , (Jnd28BetAnalyzerService::betDaLuDanTiao));
        // 输出
        jnd28BetCounter = jnd28BetAnalyzerService.getJnd28BetCounter(Jnd28BetAnalyzerService.DA_LU_DAN_TIAO);
    }


    @Test
    public void testBetZhuiTouLongHu() {
        Jnd28BetAnalyzerService jnd28BetAnalyzerService = runJnd28BetAnalyzerMethod(100 , (Jnd28BetAnalyzerService::betZhuiTouLongHu));
        jnd28BetCounter = jnd28BetAnalyzerService.getJnd28BetCounter(Jnd28BetAnalyzerService.ZHUI_TOU_LONG_HU);
    }

    @Test
    public void testBetZhuiTouLongHuOfMemory() {
        Jnd28BetAnalyzerService jnd28BetAnalyzerService = runJnd28BetAnalyzerMethod(100 , (Jnd28BetAnalyzerService::betZhuiTouLongHuOfMemory));
        jnd28BetCounter = jnd28BetAnalyzerService.getJnd28BetCounter(Jnd28BetAnalyzerService.ZHUI_TOU_LONG_HU_MEMORY);
    }
}
