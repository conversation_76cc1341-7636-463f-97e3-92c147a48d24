package cn.iocoder.yudao.module.vote.service.activity;

import cn.hutool.core.io.FileUtil;
import cn.hutool.core.util.ArrayUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONArray;
import cn.hutool.json.JSONConfig;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import cn.hutool.log.Log;
import cn.hutool.log.dialect.console.ConsoleColorLog;
import cn.hutool.log.dialect.console.ConsoleLog;
import cn.hutool.log.level.Level;
import cn.iocoder.yudao.framework.common.core.CounterMap;
import cn.iocoder.yudao.module.vote.controller.app.vo.RaytwoTeamsOddEnum;
import org.junit.jupiter.api.Test;

import java.io.PrintStream;
import java.text.Collator;
import java.time.Duration;
import java.time.Instant;
import java.util.*;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.concurrent.atomic.AtomicReference;
import java.util.function.BiPredicate;

/**
测试策略的结果，并对策略结果做代码的数字分析。
 1.  一开始的1.10 以下。胜率 = 100%
 2.  一开始在1.7 -> 1.87（2边相等）的 对冲 稳赢的情况，获得20%利润。胜率 = 70%  【投注方式总的容易输，如果有自动化全覆盖的情况，还可以】
 3.  cs2看是否中途有到1.7以上的对冲。 注意要再之后是3.3以上，而不是前面的是3.3   ==> 胜率为：68.56%
 4.  一开始一边高啦，之后看他是否反转获胜。先看大额的，突然跳水到，另外一边到2附近就上手。  ===>  大于11目标的时候 : 王者荣耀赢跟随=3, 英雄联盟赢跟随=1 胜率：100% 。 其他都具有随机性质。
 5.  都在2.2左右纠缠的情况，2边都相同的金额。  ==> 不可行,50%。
 */
public class RayAnalysisTest {
    private static final Log log = new ConsoleColorLog(RayAnalysisTest.class);
    static {
        ConsoleLog.setLevel(Level.DEBUG);
    }
    /*
    赛前在1.7左右 ， 另一边在6以上：的情况
胜率74% ： 1赢起始-CS2=22, 1赢跟随-CS2=3, CS2-输=9  = 3*150 + 22 * 20 - 900
[98+50]440 + 450 -  9*100 = -10。
[60+31]220 + 98*3 - 9*60 = -20。
[115+25] 55*22 + 3*16 - 9*115 =  1210 + 48 - 1035 = 223
     */
    @Test
    public void preMatch17(){
        CounterMap<String> statistics = handleJson("all", (jsonObject, counterMap) -> {
            Double team1Odd = jsonObject.getDouble("team1Odd");
            Double team2Odd = jsonObject.getDouble("team2Odd");
            String remark = jsonObject.getStr("remark");
            String winner = jsonObject.getStr("winner");
            if(team1Odd.doubleValue() == team2Odd.doubleValue() || jsonObject.getBool("isFinal")){
                return false;
            }else{
                String follow = null;
                Double followMaxOdd = 0d;
                Double followReverseMaxOdd = 0d;
                if (team1Odd < team2Odd && team1Odd >= 1.7) {
                    followMaxOdd = jsonObject.getDouble("team2MaxOdd");
                    followReverseMaxOdd = jsonObject.getDouble("team1MaxOdd");
                    follow = RaytwoTeamsOddEnum.TWO.getTeam();
                }else if(team2Odd < team1Odd && team2Odd >= 1.7) {
                    followMaxOdd = jsonObject.getDouble("team1MaxOdd");
                    followReverseMaxOdd = jsonObject.getDouble("team2MaxOdd");
                    follow = RaytwoTeamsOddEnum.ONE.getTeam();
                }else if(team1Odd < 1.7 || team2Odd < 1.7){
                    return false;
                }else{
                    //
                    System.err.println(jsonObject.toString());
                }
                boolean followWin = StrUtil.equals(follow, winner);
                if(followWin){
                    incrementFollowWin(counterMap);
                }
                if("CS2".equals(remark)){
                    if(followReverseMaxOdd > 2 ){
                        counterMap.incrementAndGet("06起始最大值");
                        if(!followWin){
                            counterMap.incrementAndGet("07起始最大值并赢");
                        }else{
                            System.out.println(jsonObject.toString());
                        }
                    }
                }


                if(followWin){
                    if( followMaxOdd > 6.2){
                        counterMap.incrementAndGet("1赢跟随-"+ remark);
                    }else{
                        counterMap.incrementAndGet(remark+"-输");
                    }
                }else{
                    counterMap.incrementAndGet("1赢起始-"+ remark);
                }

                return true;
            }

        }, "CS2");
        System.out.println(statistics);

    }

    /**
     * 分析起始1.7以下的胜率情况。
     * CS2赢起始-11=2,
     * CS2赢起始-12=5      CS2赢跟随-12=1
     * CS2赢起始-13=8,     CS2赢跟随-13=2
     * CS2赢起始-14=16,    CS2赢跟随-14=7
     * CS2赢起始-15=14,    CS2赢跟随-15=8
     * CS2赢起始-16=18,    CS2赢跟随-16=20
     */
    @Test
    public void preMatch14(){
        CounterMap<String> statistics = handleJson("all", (jsonObject, counterMap) -> {
            Double team1Odd = jsonObject.getDouble("team1Odd");
            Double team2Odd = jsonObject.getDouble("team2Odd");
            String remark = jsonObject.getStr("remark");
            String winner = jsonObject.getStr("winner");
            int startOdds  = 0;
            if(jsonObject.getBool("isFinal")){
                return false;
            }else{
                String follow = null;
                Double followMaxOdd = 0d;
                Double followReverseMaxOdd = 0d;
                if ( team1Odd < 1.7) {
                    startOdds = (int)  ( team1Odd * 10) ;
                    followMaxOdd = jsonObject.getDouble("team2MaxOdd");
                    followReverseMaxOdd = jsonObject.getDouble("team1MaxOdd");
                    follow = RaytwoTeamsOddEnum.TWO.getTeam();
                }else if(team2Odd < 1.7) {
                    startOdds = (int)  ( team2Odd * 10) ;
                    followMaxOdd = jsonObject.getDouble("team1MaxOdd");
                    followReverseMaxOdd = jsonObject.getDouble("team2MaxOdd");
                    follow = RaytwoTeamsOddEnum.ONE.getTeam();
                }else {
                    return false;
                }
                boolean followWin = StrUtil.equals(follow, winner);
                if(followWin){
                    incrementFollowWin(counterMap);
                    counterMap.incrementAndGet(remark +"赢跟随-"+ startOdds);
                    if(startOdds < 14){
                        System.err.println(jsonObject.toString());
                    }
                }else{
                    counterMap.incrementAndGet(remark +"赢起始-"+ startOdds);
                    if(startOdds < 14){
                        System.out.println(jsonObject.toString());
                    }
                }
                return true;
            }

        }, "CS2");
        System.out.println(statistics);
    }


    @Test
    public void hedge(){
        CounterMap<String> statistics = handleJson("hedge", (jsonObject, counterMap) -> {
            Double team1Odd = jsonObject.getDouble("team1Odd");
            Double team2Odd = jsonObject.getDouble("team2Odd");
            String remark = jsonObject.getStr("remark");
            if(team1Odd.doubleValue() == team2Odd.doubleValue() || jsonObject.getBool("isFinal")){
                return false;
            }else{
                if (jsonObject.getInt("followOdd",0) > 0) {
                    counterMap.incrementAndGet("稳赢");
                    counterMap.incrementAndGet(remark+"-赢");
//                    System.out.println(jsonObject.toString());
                    printlnCount(System.out,jsonObject,"followWin","followOdd","followMaxOdd","followReverseMaxOdd" , "followMinOdd" , "followReverseMinOdd" );
                }else{
                    System.err.println(jsonObject.toString());
                    counterMap.incrementAndGet(remark+"-输");
                }
                // 查看最大值的分布情况。
                Double followMaxOdd = jsonObject.getDouble("followMaxOdd");
                if( followMaxOdd > 4){
                    counterMap.incrementAndGet(remark+"-大于4");
                }

                return true;
            }

        }, "CS2");
        System.out.println(statistics);

    }
     @Test  // 1.0x 到1.5以上 投入大额 。 否则投入小额资金。
    public void hedgeTimeDiff(){
        String json = FileUtil.readUtf8String("/Users/<USER>/IdeaProjects/ruoyi-vue-pro/yudao-module-vote/yudao-module-vote-biz/src/test/resources/ray/hedgeTimeDiff.json");
        List<String> matchSql = new ArrayList<>();
        JSONArray jsonArray = JSONUtil.parseArray(json);
        CounterMap<String> counterMap = new CounterMap(new HashMap<String, Integer>(), 0);
        int effectiveCount = 0; // 有效。
        int wynnCount = 0; // 稳赢

         int size = jsonArray.size();
         int finalMatchCount = 0; //

         for (int i = 0; i < size; i++) {
            JSONObject jsonObject = jsonArray.getJSONObject(i);
            String remark = jsonObject.getStr("remark");
            String matchId = jsonObject.getStr("matchId");
            String winner = jsonObject.getStr("winner");
            String follow = jsonObject.getStr("follow");
            matchSql.add( "'" + matchId+ "'" );
            String[] matchSplit = matchId.split("-");
            Double team1Odd = jsonObject.getDouble("team1Odd");
            Double team2Odd = jsonObject.getDouble("team2Odd");
            Double maxOdd = jsonObject.getDouble("maxOdd");
            Double followMinOdd = jsonObject.getDouble("followMinOdd");
            Double reverseMinOdd = jsonObject.getDouble("reverseMinOdd");
            Double reverseMaxOdd = jsonObject.getDouble("reverseMaxOdd");
            Double followOddFlag = jsonObject.getDouble("followOddFlag");
            Double reverseOddFlag = jsonObject.getDouble("reverseOddFlag");
            if(matchId.contains("-final-")){
                finalMatchCount++;
            }
            if (jsonObject.getInt("size") > 5  && (team1Odd < 1.1 || team2Odd< 1.1)) {
                counterMap.incrementAndGet("合法数");
                if(follow.equals(winner)){
                    counterMap.incrementAndGet("跟随者胜利次数");
                }else{
                    if(reverseMaxOdd <= 1.4){
                        System.err.println( reverseMaxOdd + " reverseMaxOdd:" + jsonObject );
                    }else{

                    }
                }
//                if(StrUtil.contains(matchSplit[1],"3")){
                if(followMinOdd.doubleValue() > 2.9  ){
//                    System.err.println(jsonObject.toString());
//                    counterMap.incrementAndGet(remark+"-3");
                }else{
                    effectiveCount++;

                    if (reverseMaxOdd.doubleValue() > 1.7) {
                        wynnCount++ ;
//                        System.err.println(jsonObject.toString());
                        counterMap.incrementAndGet(remark+"-赢");
                    }else{
                        System.out.println(jsonObject.toString());
                        counterMap.incrementAndGet(remark+"-输");
                    }
                }


            }
        }
        // 有效:96;稳赢:67;  0.69
        System.out.println(StrUtil.format("所有:{}；final:{}; 有效:{};稳赢:{};counterMap:{}" , size ,finalMatchCount, effectiveCount , wynnCount,counterMap));
        System.out.println(StrUtil.join(",",matchSql));

        // all : 有效:226;稳赢:170;

    }

    /**
     * 策略：都在2.2左右纠缠的情况，2边都相同的金额。
     * 大于11 : 王者荣耀赢跟随-0=3, 英雄联盟赢跟随-0=1  => 胜率：100%
     * 2.2 所有类型都是50%左右，有些甚至不到。  这个不可行。
     */
    @Test // 纠缠的次数操作情况。
    public void tangleCount(){
        CounterMap<String> statistics = handleJson("all", (jsonObject, counterMap) -> {
            Double team1Odd = jsonObject.getDouble("team1Odd");
            Double team2Odd = jsonObject.getDouble("team2Odd");
            int tangle = jsonObject.getInt("tangle",0);
            String remark = jsonObject.getStr("remark");
            String winner = jsonObject.getStr("winner");
            RaytwoTeamsOddEnum teamsOddEnum = null;
            int count = 0;
            //
            for (int i = 0; i < tangle; i++) {
                Double tangleMaxOdd = jsonObject.getDouble("tangle-tangleMaxOdd-"+i);
                if(tangleMaxOdd > 2.2 ){
                    String follow = jsonObject.getStr("tangle-follow-"+i);
                    if(teamsOddEnum == null || !teamsOddEnum.equals(follow)){
                        teamsOddEnum = RaytwoTeamsOddEnum.valueOfTeam(follow);
                        count ++;
                    }
                }
            }

            if(count == 0 || jsonObject.getBool("isFinal") ){
                return false;
            }

            if(count > 1){
                counterMap.incrementAndGet(remark +"赢");
                System.err.println(jsonObject.toString());
            }else{
                counterMap.incrementAndGet(remark +"输");
                System.out.println(jsonObject.toString());
            }


            return true;

        }, null);
        System.out.println(statistics);
    }
    /**
     * 策略：一开始一边高啦，之后看他是否反转获胜。先看大额的，突然跳水到，另外一边到2附近就上手。
     * 大于11 : 王者荣耀赢跟随-0=3, 英雄联盟赢跟随-0=1  => 胜率：100%
     */
    @Test
    public void tangleReverse(){
        CounterMap<String> statistics = handleJson("all", (jsonObject, counterMap) -> {
            Double team1Odd = jsonObject.getDouble("team1Odd");
            Double team2Odd = jsonObject.getDouble("team2Odd");
            int tangle = jsonObject.getInt("tangle",0);
            String remark = jsonObject.getStr("remark");
            String winner = jsonObject.getStr("winner");
            String follow = null;
            Double followOdd = null;
            //
            for (int i = 0; i < tangle; i++) {
                Double tangleMaxOdd = jsonObject.getDouble("tangle-tangleMaxOdd-"+i);
                if(tangleMaxOdd > 11 && follow == null){
                    follow = jsonObject.getStr("tangle-follow-"+i);
                    System.out.print( remark + ":" + tangleMaxOdd + ":");
                    continue;
                }
                // 下一次纠缠就投
                if(follow != null && followOdd == null){
                    followOdd = jsonObject.getDouble("tangle-tangleMaxOdd-"+i);
                    System.out.print(follow + ":" + followOdd + "\n");
                    break;
                }
            }

            if(followOdd == null || jsonObject.getBool("isFinal")){
                return false;
            }


            boolean followWin = StrUtil.equals(follow, winner);
            if(followWin){
                incrementFollowWin(counterMap);
                counterMap.incrementAndGet(remark +"赢跟随");
                System.err.println(jsonObject.toString());
            }else{
                counterMap.incrementAndGet(remark +"赢起始");
                System.out.println(jsonObject.toString());
            }
            return true;

        }, null);
        System.out.println(statistics);
    }
    @Test  // 纠缠的情况分析
    public void tangle(){
        AtomicInteger lossesCount = new AtomicInteger(); // 输掉的次数
        AtomicInteger winnerCount = new AtomicInteger(); // 胜利的次数
        //  133 / 194  = 67.17%
        CounterMap<String> statistics = handleJson("tangle", (jsonObject, counterMap) -> {
            Double team1Odd = jsonObject.getDouble("team1Odd");
            Double team2Odd = jsonObject.getDouble("team2Odd");
            String remark = jsonObject.getStr("remark");
            if( !jsonObject.containsKey("follow")  ){
                return false;
            }else{
                if (jsonObject.getDouble("followOdd",0d) > 0) {
                    winnerCount.incrementAndGet();
                    counterMap.incrementAndGet("稳赢");
                    counterMap.incrementAndGet(remark+"-赢");
                    printlnCount(System.out,jsonObject,"followOdd","followMaxOdd","followReverseMaxOdd" , "followMinOdd" , "followReverseMinOdd" );
                }else{
                    lossesCount.incrementAndGet();
                    System.err.println(jsonObject.toString());
                    counterMap.incrementAndGet(remark+"-输");
                }
                return true;
            }

        }, null);
        System.out.println(statistics);
        System.out.println( "胜率为：" + calculatePercentage(lossesCount, winnerCount));
    }
    @Test  // 纠缠的情况分析
    public void all(){
        AtomicInteger lossesCount = new AtomicInteger(); // 输掉的次数
        AtomicInteger winnerCount = new AtomicInteger(); // 胜利的次数

        CounterMap<String> statistics = handleJson("all", (jsonObject, counterMap) -> {
            Double team1Odd = jsonObject.getDouble("team1Odd");
            Double team2Odd = jsonObject.getDouble("team2Odd");
            Double team1MaxOdd = jsonObject.getDouble("team1MaxOdd");
            Double team2MaxOdd = jsonObject.getDouble("team2MaxOdd");
            String remark = jsonObject.getStr("remark");
            String winner = jsonObject.getStr("winner");

            Integer tangle = jsonObject.getInt("tangle", 0);
            // 单边投大于2.5的情况。 45%胜率，同时，你在比赛的时候分不清应该投那一边，这个几率更加小。
            /*if(RaytwoTeamsOddEnum.ONE.equals(winner) && team1MaxOdd > 2.5 ){
                winnerCount.incrementAndGet();
                counterMap.incrementAndGet("1-" + remark+"-赢");
                return true;
            } else if (RaytwoTeamsOddEnum.TWO.equals(winner) && team2MaxOdd > 2.5) {
                winnerCount.incrementAndGet();
                counterMap.incrementAndGet("1-" + remark+"-赢");
                return true;
            }else{
                counterMap.incrementAndGet("1-" + remark+"-输");
                lossesCount.incrementAndGet();
                return true;
            }*/
                


            //
            if( tangle == 0){
                // 这种单边胜利的情况占比1/3左右。
//                printlnCount(System.err,jsonObject,"tangle","team1Odd","team2Odd","team1MinOdd","team2MinOdd","team1MaxOdd" , "team2MaxOdd");
                return false;
            }else{
                if (tangle == 1 ) {
                    counterMap.incrementAndGet("1-" + remark+"-输");
                    lossesCount.incrementAndGet();
                } else if (team1MaxOdd < 3 || team2MaxOdd < 3) {
                    counterMap.incrementAndGet("1-" + remark+"-输");
                    lossesCount.incrementAndGet();
//                    printlnCount(System.out,jsonObject,"tangle", "winner","team1Odd","team2Odd","tangle-team1Odd-0","tangle-team2Odd-0","team1MaxOdd" , "team2MaxOdd");
                } else{
                    winnerCount.incrementAndGet();
                    counterMap.incrementAndGet("1-" + remark+"-赢");

                    if(team1MaxOdd > 9 && team2MaxOdd > 9){
                    }
                    printlnCount(System.out,jsonObject,"tangle","winner","team1Odd","team2Odd","tangle-team1Odd-0","tangle-team2Odd-0","team1MaxOdd" , "team2MaxOdd");

                }
                counterMap.incrementAndGet("9-纠缠-"+tangle);
                return true;
            }

        }, null);
        System.out.println(statistics);
        System.out.println( "胜率为：" + calculatePercentage(lossesCount, winnerCount));
    }

    /**
     *
     * @param fileNmae
     * @param bet
     * @return
     */
    public CounterMap<String> handleJson(String fileNmae  ,  BiPredicate<JSONObject,CounterMap<String>> bet , String... games){
        String json = FileUtil.readUtf8String("/Users/<USER>/IdeaProjects/ruoyi-vue-pro/yudao-module-vote/yudao-module-vote-biz/src/test/resources/ray/"+fileNmae+".json");
        JSONArray jsonArray = JSONUtil.parseArray(json);
        CounterMap<String> counterMap = new CounterMap(new TreeMap<String, Integer>(), 0);
        int size = jsonArray.size();
        counterMap.putIfAbsent("01总数",size);
        for (int i = 0; i < size; i++) {
            JSONObject jsonObject = jsonArray.getJSONObject(i);
            JSONConfig jsonConfig = jsonObject.getConfig();
            jsonConfig.setKeyComparator(Collator.getInstance(Locale.CHINESE)::compare);
            String remark = jsonObject.getStr("remark");

            String winner = jsonObject.getStr("winner");
            String matchId = jsonObject.getStr("matchId");
            String team1MinOdd = jsonObject.getStr("team1MinOdd");
            String team1MaxOdd = jsonObject.getStr("team1MaxOdd");
            String team2MinOdd = jsonObject.getStr("team2MinOdd");
            String team2MaxOdd = jsonObject.getStr("team2MaxOdd");
            String follow = jsonObject.getStr("follow");
            //
            if(RaytwoTeamsOddEnum.ONE.equals(follow)){
                jsonObject.putIfAbsent("followMinOdd",team1MinOdd);
                jsonObject.putIfAbsent("followMaxOdd",team1MaxOdd);
                jsonObject.putIfAbsent("followReverseMinOdd",team2MinOdd);
                jsonObject.putIfAbsent("followReverseMaxOdd",team2MaxOdd);
            }else if (RaytwoTeamsOddEnum.TWO.equals(follow)){
                jsonObject.putIfAbsent("followMinOdd",team2MinOdd);
                jsonObject.putIfAbsent("followMaxOdd",team2MaxOdd);
                jsonObject.putIfAbsent("followReverseMinOdd",team1MinOdd);
                jsonObject.putIfAbsent("followReverseMaxOdd",team1MaxOdd);
            }else{}
            //
            if (jsonObject.getInt("size") > 20 &&  (games == null || StrUtil.equalsAny(remark,games))  ) {
                counterMap.incrementAndGet("02合法数");
                boolean followWin = StrUtil.equals(follow, winner);
                if(follow != null) jsonObject.putIfAbsent("followWin",followWin);
                boolean isFinal = matchId.contains("-final-");
                jsonObject.putIfAbsent("isFinal",isFinal);
                // 进去执行，业务逻辑并且判断是否投注啦。
                if(bet.test(jsonObject,counterMap)){
                    counterMap.incrementAndGet("03有效投注比赛");
                    // 基于有效投注的查看才有意义
                    if(followWin){
                        incrementFollowWin(counterMap);
                    }
                    // 基于有效投注的查看才有意义
                    if(isFinal){
                        counterMap.incrementAndGet("05全局比赛的类型");
                    }
                }
            }else{
//                System.out.println(matchId);
            }
        }
        return counterMap;
    }
    public void incrementFollowWin(CounterMap<String> counterMap){
        counterMap.incrementAndGet("04跟随者胜利次数");
    }
    /**
     * 打印成csv形式的内容。
     * @param jsonArray
     * @param keys
     */
    public void printlnCsv(PrintStream printStream , JSONArray jsonArray , String... keys){
        // 打印表头
//        Arrays.stream(keys).max()
//        int maxLength = 0;
//        for (String key : keys) {
//            int length = StrUtil.length(key);
//            if (length > maxLength) {
//                maxLength = length;
//            }
//        }
        // 打印表头
        for (String key : keys) {
            // 使用 String.format 方法，将每个键值对格式化为固定宽度的字符串。%-<width>s 用于指定字段的最小宽度，- 表示左对齐。
            printStream.print(  key  + " ");
        }
        printStream.print(  "\n");
        // 打印内容
        int size = jsonArray.size();
        for (int i = 0; i < size; i++) {
            JSONObject jsonObject = jsonArray.getJSONObject(i);
            for (String key : keys) {
                // 使用 String.format 方法，将每个键值对格式化为固定宽度的字符串。%-<width>s 用于指定字段的最小宽度，- 表示左对齐。
                printStream.print( String.format("%-"+key.length()+"s",jsonObject.getStr(key))  + " ");
            }
            printStream.print(  "\n");
        }
    }


    /**
     * 只能打印带有计数的value，为啦保证对齐
     * @param jsonObject
     * @param keys
     */
    public void printlnCount(PrintStream printStream ,JSONObject jsonObject , String... keys){
        for (String key : keys) {
            // 使用 String.format 方法，将每个键值对格式化为固定宽度的字符串。%-<width>s 用于指定字段的最小宽度，- 表示左对齐。
            printStream.print(  key + ":" +  String.format("%-5s",jsonObject.getStr(key))  + " ");
        }
        printStream.print(  "\n");
    }

    public void calculatePercentage(String title,CounterMap<String> counterMap , String lossesKey, String winnerKey) {
        int numerator = counterMap.get(winnerKey) ;
        int denominator = numerator + counterMap.get(lossesKey);
        System.out.println( title + ":" + calculatePercentage(numerator,denominator) );
    }

    /**
     * 获得胜率情况。
     * @param lossesCount 输掉的次数
     * @param winnerCount 胜利的次数
     * @return
     */
    public String calculatePercentage(AtomicInteger lossesCount, AtomicInteger winnerCount) {
        int numerator = winnerCount.get() ;
        int denominator = numerator + lossesCount.get();
        return calculatePercentage(numerator,denominator);
    }

    /**
     * 获得百分比。
     * @param numerator
     * @param denominator
     * @return
     */
    public String calculatePercentage(int numerator, int denominator) {
        if (denominator == 0) {
            throw new IllegalArgumentException("分母不能为零");
        }
        double percentage = (double) numerator / denominator * 100;
        return String.format("%.2f%%", percentage); // 保留两位小数
    }
}
// 1.8 * 7 = 140
//

