package cn.iocoder.yudao.module.vote.controller.app;

/**
 * 在 Controller 的命名上，额外增加 App 作为前缀，一方面区分是管理后台还是用户 App 的 Controller，另一方面避免 Spring Bean 的名字冲突。
 *
 * 可能你会奇怪，这里我们定义了两个 /demo/test/get 接口，会不会存在重复导致冲突呢？答案，当然是并不会。原因是：
 *
 * controller.admin 包下的接口，默认会增加 /admin-api，即最终的访问地址是 /admin-api/demo/test/get
 * controller.app 包下的接口，默认会增加 /app-api，即最终的访问地址是 /app-api/demo/test/get
 *
 * <AUTHOR>
 * @version 1.0
 * @module Socialuni
 * @date 2023/4/29 10:25
 * @since 1.0
 */

import cn.iocoder.yudao.framework.common.pojo.CommonResult;
import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.module.vote.controller.admin.jobpositions.vo.JobPositionsPageReqVO;
import cn.iocoder.yudao.module.vote.controller.admin.jobpositions.vo.JobPositionsRespVO;
import cn.iocoder.yudao.module.vote.convert.jobpositions.JobPositionsConvert;
import cn.iocoder.yudao.module.vote.dal.dataobject.jobpositions.JobPositionsDO;
import cn.iocoder.yudao.module.vote.service.jobpositions.JobPositionsService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.Collection;
import java.util.List;

import static cn.iocoder.yudao.framework.common.pojo.CommonResult.success;

@Tag(name = "职递 App - 岗位信息")
@RestController
@RequestMapping("/vote/zhidi") // 需要忽略租户信息。
@Validated
@Slf4j
public class AppVoteZhidiController {
    @Resource
    private JobPositionsService jobPositionsService;

    @GetMapping("/get")
    @Operation(summary = "获得职位信息")
    @Parameter(name = "id", description = "编号", required = true, example = "1024")
    public CommonResult<JobPositionsRespVO> getJobPositions(@RequestParam("id") Integer id) {
        JobPositionsDO jobPositions = jobPositionsService.getJobPositions(id);
        return success(JobPositionsConvert.INSTANCE.convert(jobPositions));
    }


    @PostMapping("/page")
    @Operation(summary = "获得职位信息分页")
    public CommonResult<PageResult<JobPositionsRespVO>> getJobPositionsPage(@Valid @RequestBody JobPositionsPageReqVO pageVO) {
        PageResult<JobPositionsDO> pageResult = jobPositionsService.getJobPositionsPage(pageVO);
        return success(JobPositionsConvert.INSTANCE.convertPage(pageResult));
    }
}
