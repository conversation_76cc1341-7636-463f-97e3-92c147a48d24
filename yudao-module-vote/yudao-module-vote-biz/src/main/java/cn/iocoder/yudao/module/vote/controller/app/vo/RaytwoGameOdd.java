package cn.iocoder.yudao.module.vote.controller.app.vo;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.collection.ListUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.map.MapUtil;
import cn.hutool.core.util.NumberUtil;
import cn.hutool.core.util.StrUtil;
import lombok.Data;

import java.time.Duration;
import java.time.Instant;
import java.util.*;

// 一场比赛
@Data
public class RaytwoGameOdd {
    private Map<Instant,RaytwoTeamsOdd> map = new HashMap();
    private String team1_name;
    private String team2_name;
    public RaytwoGameOdd(String team1_name, String team2_name) {
        // 排序获得第一个和第二个的名称。
        List<String> list = Arrays.asList(team1_name,team2_name);
        List<String> keys = ListUtil.sortByPinyin(list);
        this.team1_name = keys.get(0);
        this.team2_name = keys.get(1);
    }

    /**
     * 正向的时间顺序排序
     * @return
     */
    public List<RaytwoTeamsOdd> sortInstants(){
        return sort( ( x, y) ->{ return x.getTime().compareTo(y.getTime());} );
    }

    public List<RaytwoTeamsOdd> sort(Comparator<RaytwoTeamsOdd> comparator){
        Collection<RaytwoTeamsOdd> values = map.values();
        List<RaytwoTeamsOdd> sortList = CollectionUtil.sort(values, comparator);
        // 补全数据。
        int size = sortList.size();
        for (int i = 1; i < size; i++) {
            RaytwoTeamsOdd raytwoTeamsOddPrevious = sortList.get(i - 1);
            RaytwoTeamsOdd raytwoTeamsOdd = sortList.get(i);

            // 补全是否是赛前的标识数据。
            Duration between = Duration.between(raytwoTeamsOddPrevious.getTime(), raytwoTeamsOdd.getTime());
            if (between.toMinutes() > 10) {
                setBeforeAttributeByList(sortList,i,"isPreMatch", true );
            }

            // 补全odds的数据
            if (!raytwoTeamsOdd.isTeam1Real()) {
                //
                Double aDouble = getRealOddsByList(sortList, i - 1, RaytwoTeamsOddEnum.ONE);
                if(aDouble != null){
                    raytwoTeamsOdd.setTeam1Odd(aDouble);
                }
            }
            if (!raytwoTeamsOdd.isTeam2Real()) {
                Double bDouble = getRealOddsByList(sortList, i - 1, RaytwoTeamsOddEnum.TWO);
                if(bDouble != null){
                    raytwoTeamsOdd.setTeam2Odd(bDouble);
                }
            }



        }
        return sortList;
    }
    //
    public Double getRealOddsByList(List<RaytwoTeamsOdd> sortList, int index, RaytwoTeamsOddEnum flag){
        if(index < 0) return null;
        RaytwoTeamsOdd raytwoTeamsOdd = CollectionUtil.get(sortList, index);
        Double odd = null;
        if( raytwoTeamsOdd != null){
            if (RaytwoTeamsOddEnum.ONE.equals(flag) && raytwoTeamsOdd.isTeam1Real()) {
                odd = raytwoTeamsOdd.getTeam1Odd();
            } else if (RaytwoTeamsOddEnum.TWO.equals(flag) && raytwoTeamsOdd.isTeam2Real()) {
                odd = raytwoTeamsOdd.getTeam2Odd();
            }else{
                // 向前递归的找数据。
                getRealOddsByList(sortList , index -1 ,flag );
            }
        }
        return odd;
    }

    /**
     * 设置前面所有的属性内容
     * @param sortList
     * @param index
     * @param fieldNameOrIndex
     * @param value
     * @return
     */
    public boolean setBeforeAttributeByList(List<RaytwoTeamsOdd> sortList, int index,  String fieldNameOrIndex, Object value  ){
        if(index < 0) return false;
        for (int i = 0; i < index; i++) { // index 不需要设置
            RaytwoTeamsOdd raytwoTeamsOdd = CollectionUtil.get(sortList, i);
            BeanUtil.setFieldValue(raytwoTeamsOdd , fieldNameOrIndex ,value  );
        }
        return true;
    }

    public static RaytwoGameOdd newInstanceInfluxMap(List<Map<String, Object>> maps){
        //
        Set<String> set = new HashSet<>();
        RaytwoGameOdd raytwoGameOdd = null;
        if (CollectionUtil.size(maps) > 1) {
            maps.forEach( map ->{
                String team = StrUtil.toString( map.get("team") ) ;
                set.add(team);
            });
            List<String> list = CollectionUtil.sortByPinyin(set);
            raytwoGameOdd = new RaytwoGameOdd(list.get(0),list.get(1));
            raytwoGameOdd.putInfluxMap(maps);
        }
        return raytwoGameOdd;
    }
    private void putInfluxMap( List<Map<String, Object>> maps){
//
        maps.forEach( map ->{
            Instant instant = (Instant) map.get("_time");
            String team = StrUtil.toString( map.get("team") ) ;
            String type = StrUtil.toString( map.get("type") ) ;
            Object statusObj = map.get("status");
            if(statusObj == null){
                System.out.println("--team--:"+team);
                statusObj = "1";
            }
            Integer status = Integer.parseInt( StrUtil.toString(statusObj) )  ;
            Double odds = NumberUtil.parseDouble(StrUtil.toString( map.get("odds") ));
            this.putMap(instant,team,status,type,odds);
        });
    }
    private void putMap( Instant time ,  String team, Integer status, String type,Double odd){
        // 从map里面获得对应的
        RaytwoTeamsOdd orDefault = map.getOrDefault(time, new RaytwoTeamsOdd(time));
        // 通过team 区分第一还是第二。
        if(StrUtil.contains(team,this.team1_name)){
            orDefault.setOdd1(status,type,odd);
        }else if(StrUtil.contains(team,this.team2_name)){
            orDefault.setOdd2(status,type,odd);
        }else{
            throw new RuntimeException( team+ "没有匹配的比赛名");
        }
        // 设置时间。
        orDefault.setTime(time);
        map.put(time,orDefault);
    }
}
