package cn.iocoder.yudao.module.vote.dal.dataobject.activity;

import cn.iocoder.yudao.framework.mybatis.core.dataobject.BaseDO;
import com.baomidou.mybatisplus.annotation.KeySequence;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.*;

import java.time.LocalDateTime;

/**
 * 投票活动 DO
 *
 * <AUTHOR>
 */
@TableName("vote_raybet_match")
@KeySequence("vote_raybet_match_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class RaybetMatchDO extends BaseDO {

    /**
     * 编号
     */
    @TableId
    private Long id;

    private Long gameId;

    private String gameName;

    private String matchName;

    private String matchShortName;

    private String round;

    private String tournamentName;

    private String tournamentShortName;

    private LocalDateTime startTime;

    private LocalDateTime endTime;

    private String score;
}
