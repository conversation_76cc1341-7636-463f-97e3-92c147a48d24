package cn.iocoder.yudao.module.vote.dal.dataobject.activity;

import cn.iocoder.yudao.framework.mybatis.core.dataobject.BaseDO;
import com.baomidou.mybatisplus.annotation.KeySequence;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.*;

import java.time.LocalDateTime;

/**
 * 投票活动 DO
 *
 * <AUTHOR>
 */
@TableName("vote_raybet_winner")
@KeySequence("vote_raybet_winner_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class RaybetWinnerDO extends BaseDO {
    @TableId
    private String matchId;

    private Long matchFork; // 外建

    private String team1Id;

    private String team2Id;

    private String winner;

    private String remark;
    /**
     * 状态标识
     */
    private Integer status;
}
