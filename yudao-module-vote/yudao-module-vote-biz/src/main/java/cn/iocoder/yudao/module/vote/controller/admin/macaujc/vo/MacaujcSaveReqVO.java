package cn.iocoder.yudao.module.vote.controller.admin.macaujc.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;
import java.util.*;
import javax.validation.constraints.*;
import org.springframework.format.annotation.DateTimeFormat;
import java.time.LocalDateTime;

@Schema(description = "管理后台 - 彩票新增/修改 Request VO")
@Data
public class MacaujcSaveReqVO {

    @Schema(description = "期数", requiredMode = Schema.RequiredMode.REQUIRED)
    private Integer expect;

    @Schema(description = "开奖号码", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "开奖号码不能为空")
    private String openCode;

    @Schema(description = "生肖", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotEmpty(message = "生肖不能为空")
    private String zodiac;

    @Schema(description = "波色", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotEmpty(message = "波色不能为空")
    private String wave;

    @Schema(description = "开奖时间")
    private LocalDateTime openTime;

    @Schema(description = "类型", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotEmpty(message = "类型不能为空")
    private String kind;

}