package cn.iocoder.yudao.module.vote.framework.datapermission.config;

import cn.iocoder.yudao.framework.datapermission.core.rule.dept.DeptDataPermissionRuleCustomizer;
import cn.iocoder.yudao.module.vote.dal.dataobject.activity.ActivityDO;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * system 模块的数据权限 Configuration
 *
 * <AUTHOR>
 */
@Configuration(proxyBeanMethods = false)
public class VoteDataPermissionConfiguration {

    @Bean
    public DeptDataPermissionRuleCustomizer voteDeptDataPermissionRuleCustomizer() {
        return rule -> {
            // dept
            rule.addDeptColumn(ActivityDO.class);
            // user
            rule.addUserColumn(ActivityDO.class, "id");
        };
    }

}
