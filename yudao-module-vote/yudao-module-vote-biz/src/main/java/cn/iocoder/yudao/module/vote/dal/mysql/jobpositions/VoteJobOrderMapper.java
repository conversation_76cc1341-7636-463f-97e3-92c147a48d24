package cn.iocoder.yudao.module.vote.dal.mysql.jobpositions;

import cn.iocoder.yudao.framework.common.pojo.PageParam;
import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.mybatis.core.mapper.BaseMapperX;
import cn.iocoder.yudao.framework.mybatis.core.query.LambdaQueryWrapperX;
import cn.iocoder.yudao.module.vote.dal.dataobject.jobpositions.VoteJobOrderDO;
import org.apache.ibatis.annotations.Mapper;

/**
 * 示例订单 Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface VoteJobOrderMapper extends BaseMapperX<VoteJobOrderDO> {

    default PageResult<VoteJobOrderDO> selectPage(PageParam reqVO) {
        return selectPage(reqVO, new LambdaQueryWrapperX<VoteJobOrderDO>()
                .orderByDesc(VoteJobOrderDO::getId));
    }

    default int updateByIdAndPayed(Long id, boolean wherePayed, VoteJobOrderDO updateObj) {
        return update(updateObj, new LambdaQueryWrapperX<VoteJobOrderDO>()
                .eq(VoteJobOrderDO::getId, id).eq(VoteJobOrderDO::getPayStatus, wherePayed));
    }

}
