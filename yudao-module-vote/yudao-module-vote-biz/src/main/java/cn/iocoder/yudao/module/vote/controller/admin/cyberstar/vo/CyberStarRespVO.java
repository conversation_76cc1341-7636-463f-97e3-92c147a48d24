package cn.iocoder.yudao.module.vote.controller.admin.cyberstar.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;
import java.util.*;
import org.springframework.format.annotation.DateTimeFormat;
import java.time.LocalDateTime;
import com.alibaba.excel.annotation.*;

@Schema(description = "管理后台 - 网红主播 Response VO")
@Data
@ExcelIgnoreUnannotated
public class CyberStarRespVO {

    @Schema(description = "文件编号", requiredMode = Schema.RequiredMode.REQUIRED, example = "12791")
    @ExcelProperty("文件编号")
    private Long id;

    @Schema(description = "昵称", requiredMode = Schema.RequiredMode.REQUIRED, example = "赵六")
    @ExcelProperty("昵称")
    private String nickname;

    @Schema(description = "用户ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "1718")
    @ExcelProperty("用户ID")
    private String userId;

    @Schema(description = "类型", requiredMode = Schema.RequiredMode.REQUIRED, example = "1718")
    @ExcelProperty("类型")
    private Integer type;

    @Schema(description = "头像图片URL", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("头像图片URL")
    private String img;

    @Schema(description = "Cookie信息")
    @ExcelProperty("Cookie信息")
    private String cookie;

    @Schema(description = "gmv当天金额")
    @ExcelProperty("gmv当天金额")
    private Double gmvTodayAmountDouble;

    @Schema(description = "跳水金额", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("跳水金额")
    private Integer diveAmount;

    @Schema(description = "gift当天金额")
    @ExcelProperty("gift当天金额")
    private Double giftTodayAmountDouble;

    @Schema(description = "创建时间", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("创建时间")
    private LocalDateTime createTime;

}