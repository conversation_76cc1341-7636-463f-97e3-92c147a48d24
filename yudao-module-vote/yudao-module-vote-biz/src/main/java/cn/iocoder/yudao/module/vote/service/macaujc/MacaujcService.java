package cn.iocoder.yudao.module.vote.service.macaujc;

import java.util.*;
import javax.validation.*;
import cn.iocoder.yudao.module.vote.controller.admin.macaujc.vo.*;
import cn.iocoder.yudao.module.vote.dal.dataobject.macaujc.MacaujcDO;
import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.common.pojo.PageParam;

/**
 * 彩票 Service 接口
 *
 * <AUTHOR>
 */
public interface MacaujcService {

    /**
     * 创建彩票
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    Integer createMacaujc(@Valid MacaujcSaveReqVO createReqVO);

    /**
     * 更新彩票
     *
     * @param updateReqVO 更新信息
     */
    void updateMacaujc(@Valid MacaujcSaveReqVO updateReqVO);

    /**
     * 删除彩票
     *
     * @param id 编号
     */
    void deleteMacaujc(Integer id);

    /**
     * 获得彩票
     *
     * @param id 编号
     * @return 彩票
     */
    MacaujcDO getMacaujc(Integer id);

    /**
     * 获得彩票分页
     *
     * @param pageReqVO 分页查询
     * @return 彩票分页
     */
    PageResult<MacaujcDO> getMacaujcPage(MacaujcPageReqVO pageReqVO);

    /**
     *  统计
     * @return
     */
    String statistics(String kind);
}