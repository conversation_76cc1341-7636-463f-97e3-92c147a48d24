package cn.iocoder.yudao.module.vote.convert.activity;

import java.util.*;

import cn.iocoder.yudao.framework.common.pojo.PageResult;

import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;
import cn.iocoder.yudao.module.vote.controller.admin.activity.vo.*;
import cn.iocoder.yudao.module.vote.dal.dataobject.activity.ActivityDO;

/**
 * 投票活动 Convert
 *
 * <AUTHOR>
 */
@Mapper
public interface ActivityConvert {

    ActivityConvert INSTANCE = Mappers.getMapper(ActivityConvert.class);

    ActivityDO convert(ActivityCreateReqVO bean);

    ActivityDO convert(ActivityUpdateReqVO bean);

    ActivityRespVO convert(ActivityDO bean);

    List<ActivityRespVO> convertList(List<ActivityDO> list);

    PageResult<ActivityRespVO> convertPage(PageResult<ActivityDO> page);

    List<ActivityExcelVO> convertList02(List<ActivityDO> list);

}
