package cn.iocoder.yudao.module.vote.service.activity;

import java.util.*;
import javax.validation.*;
import cn.iocoder.yudao.module.vote.controller.admin.activity.vo.*;
import cn.iocoder.yudao.module.vote.dal.dataobject.activity.ActivityDO;
import cn.iocoder.yudao.framework.common.pojo.PageResult;

/**
 * 投票活动 Service 接口
 *
 * <AUTHOR>
 */
public interface ActivityService {

    /**
     * 创建投票活动
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    Long createActivity(@Valid ActivityCreateReqVO createReqVO);

    /**
     * 更新投票活动
     *
     * @param updateReqVO 更新信息
     */
    void updateActivity(@Valid ActivityUpdateReqVO updateReqVO);

    /**
     * 删除投票活动
     *
     * @param id 编号
     */
    void deleteActivity(Long id);

    /**
     * 获得投票活动
     *
     * @param id 编号
     * @return 投票活动
     */
    ActivityDO getActivity(Long id);

    /**
     * 获得投票活动列表
     *
     * @param ids 编号
     * @return 投票活动列表
     */
    List<ActivityDO> getActivityList(Collection<Long> ids);

    /**
     * 获得投票活动分页
     *
     * @param pageReqVO 分页查询
     * @return 投票活动分页
     */
    PageResult<ActivityDO> getActivityPage(ActivityPageReqVO pageReqVO);

    /**
     * 获得投票活动列表, 用于 Excel 导出
     *
     * @param exportReqVO 查询条件
     * @return 投票活动列表
     */
    List<ActivityDO> getActivityList(ActivityExportReqVO exportReqVO);

}
