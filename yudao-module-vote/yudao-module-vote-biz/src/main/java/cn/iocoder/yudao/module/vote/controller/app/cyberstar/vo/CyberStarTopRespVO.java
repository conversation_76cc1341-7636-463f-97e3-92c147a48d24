package cn.iocoder.yudao.module.vote.controller.app.cyberstar.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDateTime;

@Schema(description = "管理后台 - 网红主播 Response VO")
@Data
@ExcelIgnoreUnannotated
public class CyberStarTopRespVO {

    @Schema(description = "文件编号", requiredMode = Schema.RequiredMode.REQUIRED, example = "12791")
    @ExcelProperty("文件编号")
    private Long id;

    @Schema(description = "金额")
    @ExcelProperty("金额")
    private Integer amountDouble = 0;

}