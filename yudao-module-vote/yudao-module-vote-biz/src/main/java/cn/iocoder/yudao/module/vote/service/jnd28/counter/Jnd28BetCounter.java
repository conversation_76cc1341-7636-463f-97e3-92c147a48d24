package cn.iocoder.yudao.module.vote.service.jnd28.counter;

import cn.hutool.json.JSONObject;

public interface Jnd28BetCounter {
    /**
     * @return 获得盈利情况
     */
    double getProfitSituation();
    /**
     * @return 获得投注总金额
     */
    int getTotalBetAmount();
    /**
     * 派奖
     */
    void awardGiving();

    /**
      * @return 投注分布统计
     */
    JSONObject betSituation();
    /**
     * 投注当前金额
     * @return 计算当前投注的金额
     */
    int betByCurAmount();
     /**
      * 获得[龙]的次数
      */
     int getChaseLongCount();
     /**
      * 获得[虎]的次数
      */
     int getChaseHuCount();
     /**
      * 获得[投注]的次数
      */
     int getBetAmountCount();

    // increment
     /**
      * 自增[龙]的次数
      */
     void incrementChaseLongCount();
     /**
      * 自增[虎]的次数
      */
     void incrementChaseHuCount();
     /**
      * 自增[投注]的次数
      */
     void incrementBetAmountCount();


     /**
      * 重置[龙]的次数
      */
     void resetChaseLongCount();
     /**
      * 重置[虎]的次数
      */
     void resetChaseHuCount();
     /**
      * 重置[投注]的次数
      */
     void resetBetAmountCount();
}
