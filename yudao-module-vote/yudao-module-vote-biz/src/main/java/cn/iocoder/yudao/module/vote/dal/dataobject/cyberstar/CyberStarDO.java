package cn.iocoder.yudao.module.vote.dal.dataobject.cyberstar;

import cn.iocoder.yudao.module.system.enums.permission.MenuTypeEnum;
import lombok.*;
import java.util.*;
import java.time.LocalDateTime;
import java.time.LocalDateTime;
import com.baomidou.mybatisplus.annotation.*;
import cn.iocoder.yudao.framework.mybatis.core.dataobject.BaseDO;

/**
 * 网红主播 DO
 *
 * <AUTHOR>
 */
@TableName("vote_cyber_star")
@KeySequence("vote_cyber_star_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class CyberStarDO extends BaseDO {

    /**
     * 文件编号
     */
    @TableId
    private Long id;
    /**
     * 昵称
     */
    private String nickname;
    /**
     * 用户ID
     */
    private String userId;
    /**
     * 用户类型
     * 枚举 :
     */
    private Integer type;
    /**
     * 头像图片URL
     */
    private String img;
    /**
     * Cookie信息
     */
    private String cookie;
    /**
     * gmv当天金额
     */
    private Double gmvTodayAmountDouble;
    /**
     * 跳水金额
     */
    private Integer diveAmount;
    /**
     * gift当天金额
     */
    private Double giftTodayAmountDouble;

}