package cn.iocoder.yudao.module.vote.controller.admin.cyberstar.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;
import java.util.*;
import javax.validation.constraints.*;

@Schema(description = "管理后台 - 网红主播新增/修改 Request VO")
@Data
public class CyberStarSaveReqVO {

    @Schema(description = "文件编号", requiredMode = Schema.RequiredMode.REQUIRED, example = "12791")
    private Long id;

    @Schema(description = "昵称", requiredMode = Schema.RequiredMode.REQUIRED, example = "赵六")
    @NotEmpty(message = "昵称不能为空")
    private String nickname;

    @Schema(description = "用户ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "1718")
    @NotEmpty(message = "用户ID不能为空")
    private String userId;

    @Schema(description = "头像图片URL", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotEmpty(message = "头像图片URL不能为空")
    private String img;

    @Schema(description = "Cookie信息")
    private String cookie;

    @Schema(description = "gmv当天金额")
    private Double gmvTodayAmountDouble;

    @Schema(description = "跳水金额", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "跳水金额不能为空")
    private Integer diveAmount;

    @Schema(description = "gift当天金额")
    private Double giftTodayAmountDouble;

}