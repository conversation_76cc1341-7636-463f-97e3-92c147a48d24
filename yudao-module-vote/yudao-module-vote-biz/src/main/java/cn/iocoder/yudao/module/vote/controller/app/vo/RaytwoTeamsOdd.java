package cn.iocoder.yudao.module.vote.controller.app.vo;

import cn.hutool.core.util.StrUtil;
import lombok.Data;
import lombok.ToString;

import java.time.Instant;

import static cn.iocoder.yudao.module.vote.controller.app.vo.RaytwoTeamsOddEnum.ONE;
import static cn.iocoder.yudao.module.vote.controller.app.vo.RaytwoTeamsOddEnum.TWO;

@Data
@ToString(callSuper = true)
public class RaytwoTeamsOdd{
    private boolean isPreMatch; // 是否是赛前


    private Instant time;
    private Double team1Odd;
    private boolean team1Real;
    private Integer team1Status;
    private String team1Type;


    private Double team2Odd;
    private boolean team2Real;
    private Integer team2Status;
    private String team2Type;

    public RaytwoTeamsOdd(Instant time) {
        this.time = time;
    }

    public void setOdd1(Integer status, String type,Double odd) {
        this.team1Odd = odd;
        this.team1Real = true;
        this.team1Status = status;
        this.team1Type = type;
        if(team2Odd == null){
            team2Odd = team1Odd;
        }
    }
    public void setOdd2(Integer status, String type,Double odd) {
        this.team2Odd = odd;
        this.team2Real = true;
        this.team2Status = status;
        this.team2Type = type;
        if(team1Odd == null){
            team1Odd = odd;
        }
    }

    /**
     * 获得真实的值，如果没有的话，就是-1。 如果不想用这个还可以自己用isTeamXReal判断
     * 其实就是语法糖，简化 isTeam1Real 和 获得数字的判断逻辑。
     * @param flag
     * @return
     */
    public double getRealOdd(RaytwoTeamsOddEnum flag){
        if(ONE.equals(flag)){
            return  this.isTeam1Real() ? this.getTeam1Odd() : -1;
        }else if(TWO.equals(flag)){
            return this.isTeam2Real()  ? this.getTeam2Odd() : -1;
        }else{
            throw new RuntimeException("没有匹配标识");
        }
    }
    /**
     * 针对 getRealOdd 的反向取值。
     * @param flag
     * @return
     * @see #getOdd(RaytwoTeamsOddEnum)
     */
    public double getRealReverseOdd(RaytwoTeamsOddEnum flag){
        if(ONE.equals(flag)){
            return this.isTeam2Real() ? this.getTeam2Odd() : -1;
        }else if(TWO.equals(flag)){
            return this.isTeam1Real() ? this.getTeam1Odd() : -1;
        }else{
            throw new RuntimeException("没有匹配标识");
        }
    }

    public double getOdd(RaytwoTeamsOddEnum flag){
        if(ONE.equals(flag)){
            return this.getTeam1Odd();
        }else if(TWO.equals(flag)){
            return this.getTeam2Odd();
        }else{
            throw new RuntimeException("没有匹配标识");
        }
    }

    /**
     * 针对 getOdd 的反向取值。
     * @param flag
     * @return
     * @see #getOdd(RaytwoTeamsOddEnum)
     */
    public double getReverseOdd(RaytwoTeamsOddEnum flag){
        if(ONE.equals(flag)){
            return this.getTeam2Odd();
        }else if(TWO.equals(flag)){
            return this.getTeam1Odd();
        }else{
            throw new RuntimeException("没有匹配标识");
        }
    }
}
