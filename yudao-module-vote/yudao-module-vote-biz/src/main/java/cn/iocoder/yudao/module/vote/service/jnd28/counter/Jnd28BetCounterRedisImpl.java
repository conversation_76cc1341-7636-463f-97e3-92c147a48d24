package cn.iocoder.yudao.module.vote.service.jnd28.counter;

import cn.hutool.core.util.StrUtil;
import cn.hutool.extra.spring.SpringUtil;
import cn.hutool.json.JSONObject;
import cn.iocoder.yudao.framework.common.util.collection.ListCountUtils;
import cn.iocoder.yudao.framework.redis.tool.RedisStringValueGetter;
import org.jetbrains.annotations.NotNull;
import org.springframework.data.redis.core.RedisTemplate;

import java.util.*;

public class Jnd28BetCounterRedisImpl implements Jnd28BetCounter {
    private static final Map<String, Jnd28BetCounterRedisImpl> map = new HashMap<>();

    public final String key;
    private RedisStringValueGetter redisStringValueGetter;
    private RedisTemplate<String,Integer> redisTemplate;

    public Jnd28BetCounterRedisImpl(String key) {
        this.key = key;
        this.redisStringValueGetter = SpringUtil.getBean(RedisStringValueGetter.class);
        this.redisTemplate = SpringUtil.getBean("integerRedisTemplate");
    }

    @Override
    public double getProfitSituation() {
        return redisStringValueGetter.getDouble(formatProfitSituationKey(),0d);
    }

    @Override
    public void awardGiving() {
        int curAmount = getCurAmount();
        redisStringValueGetter.opsForValue().increment(formatProfitSituationKey() ,(curAmount * 2 - curAmount));
    }

    @Override
    public JSONObject betSituation() {
        List<Integer> betList = redisTemplate.opsForList().range(formatBetListKey(), 0, -1);
        Integer max = null;
        int sum = 0;
        JSONObject jsonObject = new JSONObject();
        if (betList != null) {
            max = ListCountUtils.max(betList);
            sum= betList.stream().mapToInt(x -> x).sum();
            jsonObject.set("max",max);
            jsonObject.set("sum",sum);
            jsonObject.set("betList",betList);
        }
        return jsonObject;
//        return StrUtil.format("总数是:{},最大数:{},具体投注情况:{}",sum,max,Objects.toString(betList)) ;
    }

    @Override
    public int betByCurAmount() {
        int curAmount = getCurAmount();
        redisStringValueGetter.opsForValue().increment(formatProfitSituationKey() ,-curAmount);
        redisStringValueGetter.opsForValue().increment(formatTotalBetAmountKey() ,curAmount);
        return curAmount;
    }

    private int getCurAmount() {
        return 1 * (int) Math.pow(2, this.getBetAmountCount());
    }

    public int getTotalBetAmount() {
        return redisStringValueGetter.getInt(formatTotalBetAmountKey(),0);
    }




    public int getChaseLongCount(){
        return redisStringValueGetter.getInt(formatChaseLongCountKey(),0);
    }
    public int getChaseHuCount(){
        return redisStringValueGetter.getInt(formatChaseHuCountKey(),0);
    }
    public int getBetAmountCount(){
        return redisStringValueGetter.getInt(formatBetAmountCountKey(),0);
    }

    // increment

    public void incrementChaseLongCount(){
        redisStringValueGetter.opsForValue().increment(formatChaseLongCountKey());
    }
    public void incrementChaseHuCount(){
        redisStringValueGetter.opsForValue().increment(formatChaseHuCountKey());
    }
    public void incrementBetAmountCount(){
        redisStringValueGetter.opsForValue().increment(formatBetAmountCountKey());
    }

    //del
    public void resetChaseLongCount(){
        redisTemplate.delete(formatChaseLongCountKey());
    }
    public void resetChaseHuCount(){
        redisTemplate.delete(formatChaseHuCountKey());
    }
    public void resetBetAmountCount(){
        redisTemplate.opsForList().rightPush( formatBetListKey(), getBetAmountCount());
        redisTemplate.delete(formatBetAmountCountKey());
    }

    @NotNull
    private  String formatChaseHuCountKey() {
        return "vote:jnd28:chaseHuCount:" + key;
    }
    @NotNull
    private  String formatChaseLongCountKey() {
        return "vote:jnd28:chaseLongCount:" + key;
    }
    @NotNull
    private  String formatBetAmountCountKey() {
        return "vote:jnd28:betAmountCount:" + key;
    }
    @NotNull
    private  String formatTotalBetAmountKey() {
        return "vote:jnd28:totalBetAmount:" + key;
    }
    @NotNull
    private  String formatProfitSituationKey() {
        return "vote:jnd28:profitSituation:" + key;
    }
    @NotNull
    private  String formatBetListKey() {
        return "vote:jnd28:betList:" + key;
    }

}
