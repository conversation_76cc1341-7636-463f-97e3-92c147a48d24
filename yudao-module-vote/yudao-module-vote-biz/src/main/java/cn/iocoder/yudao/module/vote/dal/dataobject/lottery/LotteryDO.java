package cn.iocoder.yudao.module.vote.dal.dataobject.lottery;

import lombok.*;
import java.util.*;
import java.time.LocalDateTime;
import com.baomidou.mybatisplus.annotation.*;
import cn.iocoder.yudao.framework.mybatis.core.dataobject.BaseDO;

/**
 * kuai28.com DO
 *
 * <AUTHOR>
 */
@TableName("vote_lottery")
@KeySequence("vote_lottery_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class LotteryDO  {

    /**
     * 标识符ID
     */
    @TableId
    private Long id;
    /**
     * 效果标识，用于表示某种效果或状态的整数类型
     */
    private Integer efficacy;
    /**
     * 完整预期值，用于存储特定的大整数值，如期号等
     */
    private Long fullExpect;
    /**
     * 数字排序，用于表示开奖号码按某种规则排序后的结果，字符串类型
     */
    private String numSort;
    /**
     * 数字类型，例如单双、大小等分类，字符串类型
     */
    private String numType;
    /**
     * 开奖号码，实际开奖的号码组合，以逗号分隔的字符串形式存储
     */
    private String openCode;
    /**
     * 开奖时间，记录具体的开奖日期和时间
     */
    private LocalDateTime openTime;
    /**
     * 和数值，所有开奖号码相加得到的总和，整数类型
     */
    private Integer sum;
    /**
     * 类型名称，用于描述彩票类型的名称，字符串类型
     */
    private String typeName;
    /**
     * 品种类型，用于区分不同种类的游戏，例如jnd28, 字符串类型
     */
    private String varietyType;

}