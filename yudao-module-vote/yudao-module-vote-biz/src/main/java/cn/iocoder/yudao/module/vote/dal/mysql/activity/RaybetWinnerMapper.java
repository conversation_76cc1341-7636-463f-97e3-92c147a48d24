package cn.iocoder.yudao.module.vote.dal.mysql.activity;

import cn.iocoder.yudao.framework.mybatis.core.mapper.BaseMapperX;
import cn.iocoder.yudao.module.vote.dal.dataobject.activity.RaybetMatchDO;
import cn.iocoder.yudao.module.vote.dal.dataobject.activity.RaybetWinnerDO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Select;

import java.time.LocalDateTime;
import java.util.List;


/**
 * 投票活动 Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface RaybetWinnerMapper extends BaseMapperX<RaybetWinnerDO> {

    @Select("select w.match_id ,w.match_fork ,w.team1_id ,w.team2_id ,w.winner  ,w.status , w.creator ,w.create_time ,w.update_time , m.game_name as remark from vote_raybet_winner w , vote_raybet_match m where w.`match_fork` = m.id and w.status = 0 and w.update_time  >  STR_TO_DATE('2024-09-29', '%Y-%m-%d') and  m.game_name in ('DOTA2', '英雄联盟' , '王者荣耀' , 'CS2') " ) // 正常的查询。 因为这个时候是否判断输赢的方式已经更新啦。
//    @Select("select w.match_id ,w.match_fork ,w.team1_id ,w.team2_id ,w.winner  ,w.status , w.creator ,w.create_time ,w.update_time , m.game_name as remark from vote_raybet_winner w , vote_raybet_match m where w.`match_fork` = m.id and w.match_id = '38049192-final-' " ) // 过滤特定的id
    List<RaybetWinnerDO> selectAnalysis();
}
