package cn.iocoder.yudao.module.vote.controller.admin.lottery;

import org.springframework.web.bind.annotation.*;
import javax.annotation.Resource;
import org.springframework.validation.annotation.Validated;
import org.springframework.security.access.prepost.PreAuthorize;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.Operation;

import javax.validation.constraints.*;
import javax.validation.*;
import javax.servlet.http.*;
import java.util.*;
import java.io.IOException;

import cn.iocoder.yudao.framework.common.pojo.PageParam;
import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.common.pojo.CommonResult;
import cn.iocoder.yudao.framework.common.util.object.BeanUtils;
import static cn.iocoder.yudao.framework.common.pojo.CommonResult.success;

import cn.iocoder.yudao.framework.excel.core.util.ExcelUtils;

import cn.iocoder.yudao.framework.apilog.core.annotation.ApiAccessLog;
import static cn.iocoder.yudao.framework.apilog.core.enums.OperateTypeEnum.*;

import cn.iocoder.yudao.module.vote.controller.admin.lottery.vo.*;
import cn.iocoder.yudao.module.vote.dal.dataobject.lottery.LotteryDO;
import cn.iocoder.yudao.module.vote.service.lottery.LotteryService;

@Tag(name = "管理后台 - kuai28.com")
@RestController
@RequestMapping("/vote/lottery")
@Validated
public class LotteryController {

    @Resource
    private LotteryService lotteryService;

    @PostMapping("/create")
    @Operation(summary = "创建kuai28.com")
    @PreAuthorize("@ss.hasPermission('vote:lottery:create')")
    public CommonResult<Long> createLottery(@Valid @RequestBody LotterySaveReqVO createReqVO) {
        return success(lotteryService.createLottery(createReqVO));
    }

    @PutMapping("/update")
    @Operation(summary = "更新kuai28.com")
    @PreAuthorize("@ss.hasPermission('vote:lottery:update')")
    public CommonResult<Boolean> updateLottery(@Valid @RequestBody LotterySaveReqVO updateReqVO) {
        lotteryService.updateLottery(updateReqVO);
        return success(true);
    }

    @DeleteMapping("/delete")
    @Operation(summary = "删除kuai28.com")
    @Parameter(name = "id", description = "编号", required = true)
    @PreAuthorize("@ss.hasPermission('vote:lottery:delete')")
    public CommonResult<Boolean> deleteLottery(@RequestParam("id") Long id) {
        lotteryService.deleteLottery(id);
        return success(true);
    }

    @GetMapping("/get")
    @Operation(summary = "获得kuai28.com")
    @Parameter(name = "id", description = "编号", required = true, example = "1024")
    @PreAuthorize("@ss.hasPermission('vote:lottery:query')")
    public CommonResult<LotteryRespVO> getLottery(@RequestParam("id") Long id) {
        LotteryDO lottery = lotteryService.getLottery(id);
        return success(BeanUtils.toBean(lottery, LotteryRespVO.class));
    }

    @GetMapping("/page")
    @Operation(summary = "获得kuai28.com分页")
    @PreAuthorize("@ss.hasPermission('vote:lottery:query')")
    public CommonResult<PageResult<LotteryRespVO>> getLotteryPage(@Valid LotteryPageReqVO pageReqVO) {
        PageResult<LotteryDO> pageResult = lotteryService.getLotteryPage(pageReqVO);
        return success(BeanUtils.toBean(pageResult, LotteryRespVO.class));
    }

    @GetMapping("/export-excel")
    @Operation(summary = "导出kuai28.com Excel")
    @PreAuthorize("@ss.hasPermission('vote:lottery:export')")
    @ApiAccessLog(operateType = EXPORT)
    public void exportLotteryExcel(@Valid LotteryPageReqVO pageReqVO,
              HttpServletResponse response) throws IOException {
        pageReqVO.setPageSize(PageParam.PAGE_SIZE_NONE);
        List<LotteryDO> list = lotteryService.getLotteryPage(pageReqVO).getList();
        // 导出 Excel
        ExcelUtils.write(response, "kuai28.com.xls", "数据", LotteryRespVO.class,
                        BeanUtils.toBean(list, LotteryRespVO.class));
    }

}