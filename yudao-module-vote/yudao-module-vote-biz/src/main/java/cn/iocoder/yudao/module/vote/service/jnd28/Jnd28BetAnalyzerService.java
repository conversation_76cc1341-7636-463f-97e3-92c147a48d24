package cn.iocoder.yudao.module.vote.service.jnd28;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONArray;
import cn.iocoder.yudao.module.vote.controller.admin.lottery.vo.LotteryPageReqVO;
import cn.iocoder.yudao.module.vote.dal.dataobject.lottery.LotteryDO;
import cn.iocoder.yudao.module.vote.service.jnd28.bo.BetBo;
import cn.iocoder.yudao.module.vote.service.jnd28.counter.Jnd28BetCounter;
import cn.iocoder.yudao.module.vote.service.jnd28.counter.Jnd28BetCounterMemoryImpl;
import cn.iocoder.yudao.module.vote.service.jnd28.counter.Jnd28BetCounterRedisImpl;
import cn.iocoder.yudao.module.vote.service.lottery.LotteryServiceImpl;

import java.util.*;
import java.util.stream.Collectors;

public class Jnd28BetAnalyzerService {

    public static final String DA_LU_DAN_TIAO = "daLuDanTiao";
    public static final String ZHUI_TOU_LONG_HU = "zhuiTouLongHu";
    public static final String ZHUI_TOU_LONG_HU_MEMORY = "zhuiTouLongHuMemory";

    public BetBo betDaLuDanTiao(Long fullExpect) {
        // 查询对应的结果代码。
        //1. 获得数据内容。
        BetBo betBo =  null;// new BetBo();
        String today = DateUtil.today();
        int maxFollowCount = 3; // 最多连续追注次数
        int MAX_BET_AMOUNT_COUNT = 50; // 最大投注次数
        Jnd28BetCounter jnd28BetCounter = getJnd28BetCounter(DA_LU_DAN_TIAO);
        //
        List<LotteryDO> dataList = getLotteryDOS(today,3);
        List<String> typeHistory = new ArrayList<>();
        for (int i = 0; i < dataList.size(); i++) {
            LotteryDO lotteryDO = dataList.get(i);
            if(i==0 && lotteryDO.getFullExpect() != fullExpect){
                return betBo; // 如果最新期号不一致，说明被攻击啦。 不予理睬。
            }
            //2.1 获得 对应的结果内容。
            String openCode = lotteryDO.getOpenCode();
            int firstNum = Integer.parseInt(openCode.substring(0, 1));
            int lastNum = Integer.parseInt(openCode.substring(openCode.length() - 1));
            String typeName = "";
            if (firstNum > lastNum) {
                typeName  = "龙";
            } else if (firstNum < lastNum) {
                typeName = "虎";
            } else {
                typeName = "和";
            }
            typeHistory.add(typeName);
            //
        }

        // 判断追注龙的情况下。

        int chaseLongCount = jnd28BetCounter.getChaseLongCount();
        int chaseHuCount = jnd28BetCounter.getChaseHuCount();
        int bet_amount_count = jnd28BetCounter.getBetAmountCount();

        if(chaseLongCount > 0 ){ // 如果投注过啦的话，应该这样操作。
            if(chaseLongCount < maxFollowCount){ // 不需要止损
                if ("龙".equals(typeHistory.get(0))) { //中啦
                    jnd28BetCounter.resetBetAmountCount();
                    jnd28BetCounter.resetChaseLongCount();
                    jnd28BetCounter.awardGiving();
                }else{ // 还是继续没有出现的话。
                    betBo = this.betLong(jnd28BetCounter);
                }
            }else{ // 需要止损操作。
                jnd28BetCounter.resetChaseLongCount();
            }
        }else if (chaseHuCount>0){ //
            if(chaseHuCount < maxFollowCount){ // 不需要止损
                if ("虎".equals(typeHistory.get(0))) { //中啦
                    jnd28BetCounter.resetBetAmountCount();
                    jnd28BetCounter.resetChaseHuCount();
                    jnd28BetCounter.awardGiving();
                }else{ // 还是继续没有出现的话。
                    betBo = this.betHu(jnd28BetCounter);;
                }
            }else{ // 需要止损操作。
                jnd28BetCounter.resetChaseHuCount();
            }
        }else{ // 选择投注的方式。
            String allResult = StrUtil.join("", typeHistory);
            if("龙虎龙".equals(allResult)){
                if( bet_amount_count < MAX_BET_AMOUNT_COUNT){
                    betBo = this.betLong(jnd28BetCounter);
                }
            }else if("虎龙虎".equals(allResult)){
                if( bet_amount_count < MAX_BET_AMOUNT_COUNT){
                    betBo = this.betHu(jnd28BetCounter);
                }
            }else{
                // 其他情况不理睬。
            }

        }

        // 最后不管什么情况都开始判断为：
        if(bet_amount_count >= MAX_BET_AMOUNT_COUNT){ // 如果是到啦最大值，可以归零开始操作。 如果不归零就停止投注啦。
            jnd28BetCounter.resetBetAmountCount();
        }

        return betBo;
    }


    /**
     * 追注龙虎的情况操作。
     * @param fullExpect
     * @return
     */
    public BetBo betZhuiTouLongHu(Long fullExpect) {
        BetBo betBo =  null;// new BetBo();
        String today = DateUtil.today();
        int threshold = 8;
        int maxFollowCount = 3;

        Jnd28BetCounter jnd28BetCounter = getJnd28BetCounter(ZHUI_TOU_LONG_HU);
        List<LotteryDO> dataList = getLotteryDOS(today, 40);
        List<String> typeList = lotteryListToType(dataList);

        if (dataList.isEmpty() || !dataList.get(0).getFullExpect().equals(fullExpect)) {
            return betBo;
        }

        // 统计连续未出现次数
        int noLongStreak = calcNoStreak(dataList, "龙");
        int noHuStreak = calcNoStreak(dataList, "虎");

        // 最近一期的开奖类型
        String latestType = getTypeName(dataList.get(0).getOpenCode());

        // 判断是否追注“龙”
        if (shouldBet(noLongStreak, threshold, maxFollowCount)) {
            betBo = this.betLong(jnd28BetCounter);
        }


        // 判断是否追注“虎”
        if (shouldBet(noHuStreak, threshold, maxFollowCount)) {
            betBo = this.betHu(jnd28BetCounter);
        }

        // 失败投注情况处理
//        if( jnd28BetCounter.getChaseLongCount() > 0 && faildBet(noLongStreak, threshold, maxFollowCount)) {
//            jnd28BetCounter.resetChaseLongCount();
//        }
//        if( jnd28BetCounter.getChaseHuCount() > 0 && faildBet(noHuStreak, threshold, maxFollowCount)) {
//            jnd28BetCounter.resetChaseHuCount();
//        }
        int chaseLongCount = jnd28BetCounter.getChaseLongCount();
        int chaseHuCount = jnd28BetCounter.getChaseHuCount();
        // 如果上轮追“龙”并且中了
        if (   latestType.equals("龙")) {
            if(chaseLongCount > 0){
                jnd28BetCounter.awardGiving();
                jnd28BetCounter.resetBetAmountCount();
            }
            jnd28BetCounter.resetChaseLongCount();
        }

        // 如果上轮追“虎”并且中了
        if (  latestType.equals("虎")) {
            if(chaseHuCount > 0){
                jnd28BetCounter.awardGiving();
                jnd28BetCounter.resetBetAmountCount();
            }
            jnd28BetCounter.resetChaseHuCount();
        }

        return betBo;
    }


//    int noLongStreak = 0;
    int noHuStreak = 0;
    private int noLongStreak = 0;
    private int currentAmount = 10;

    int threshold = 7;
    int maxThreshold =  threshold + 3; // 11

    public BetBo betZhuiTouLongOfMemory(String curType) {

        // 命中“龙”
//        String betType = "虎"; // 40960
        String betType = "龙"; //   5120
        if (betType.equals(curType)) {
            if( noLongStreak >= threshold && noLongStreak < maxThreshold){
                currentAmount = 10; // 重置初始金额
            }
            noLongStreak = 0;

            return null; // 必定，不往下执行啦。
        }

        // 当前不是“龙”
        noLongStreak++;

        // 没达到触发追注的阈值
        if (noLongStreak < threshold) {
            return null;
        }else{ // 达到阈值
            BetBo betBo = null;
            if(noLongStreak < maxThreshold){ // 投递3次
                betBo = new BetBo(betType, currentAmount);
                currentAmount *= 2;
            }

            return betBo;
        }
    }

    public BetBo betZhuiTouHuOfMemory(String curType) {


        // 命中“龙”
        String betType = "虎"; // 40960
        if (betType.equals(curType)) {
            if( noHuStreak >= threshold && noHuStreak < maxThreshold){
                currentAmount = 10; // 重置初始金额
            }
            noHuStreak = 0;

            return null; // 必定，不往下执行啦。
        }

        // 当前不是“龙”
        noHuStreak++;

        // 没达到触发追注的阈值
        if (noHuStreak < threshold) {
            return null;
        }else{ // 达到阈值
            BetBo betBo = null;
            if(noHuStreak < maxThreshold){ // 投递3次
                betBo = new BetBo(betType, currentAmount);
                currentAmount *= 2;
            }
            return betBo;
        }
    }

    Queue<String> typeList = new LinkedList<>();
    int longTaio = 0;
    public BetBo betDaLuDanTiaoLongOfMemory(String curType) {
        typeList.offer(curType);
        if(typeList.size() > 5){
            typeList.poll();
        }


        BetBo betBo = null;
        //
        final String betType = "龙";
        if(longTaio>0 ){ // 有跳过的情况啦。
            if(longTaio < 3){
                if(betType.equals(curType)){
                    currentAmount = 10;
                    longTaio = 0;
                    return null;
                }else{
                    betBo = new BetBo(betType,currentAmount);
                    currentAmount *= 2;
                    longTaio += 1;
                    return betBo;
                }
            }else{
                longTaio = 0;
                return null;
            }
        }
        //
        String allResult = StrUtil.join("", typeList);
        if("龙虎龙虎龙".equals(allResult)){
            betBo = new BetBo(betType,currentAmount);
            currentAmount *= 2;
            longTaio += 1;
        }

        return betBo;
    }

    /**
     * 追注龙虎的情况操作。
     * @param fullExpect
     * @return
     */
    public BetBo betZhuiTouLongHuOfMemory(Long fullExpect) {
        // 查询对应的结果代码。
        //1. 获得数据内容。
        BetBo betBo =  null;// new BetBo();
        String today = DateUtil.today();
        int maxFollowCount = 2; // 最多连续追注次数
        int MAX_BET_AMOUNT_COUNT = 9; // 最大投注次数
        Jnd28BetCounter jnd28BetCounter = getJnd28BetCounter(ZHUI_TOU_LONG_HU_MEMORY);
        //
        List<LotteryDO> dataList = getLotteryDOS(today,7);

        LotteryDO lotteryDO = dataList.get(0);
        if(lotteryDO.getFullExpect() != fullExpect){
            return betBo; // 如果最新期号不一致，说明被攻击啦。 不予理睬。
        }
        //2.1 获得 对应的结果内容。
        String typeName = getTypeName(lotteryDO);

        if(true){
            BetBo longBetBo =   betZhuiTouLongOfMemory( typeName);
            BetBo huBetBo =   betZhuiTouHuOfMemory( typeName);
            if(noHuStreak>=threshold && noLongStreak >= threshold){
                System.out.println("出现同时");
            }
            return Optional.ofNullable(longBetBo).orElse(huBetBo);
        }

        // 计算不出龙的连续次数
        if (!typeName.equals("龙")) {
            noLongStreak++;
            if (noLongStreak >= threshold && noLongStreak <= maxFollowCount + threshold) {
                betBo = this.betLong(jnd28BetCounter); //
            }
        } else {
            if(  noLongStreak >= threshold && noLongStreak <= maxFollowCount + threshold){  // 如果中啦
                jnd28BetCounter.awardGiving();
                jnd28BetCounter.resetBetAmountCount();
            }
            noLongStreak = 0;
            jnd28BetCounter.resetChaseLongCount();
        }

        // 计算不出虎的连续次数
        if (!typeName.equals("虎")) {
            noHuStreak++;
            if (noHuStreak >= threshold && noHuStreak <= maxFollowCount + threshold) {
                betBo = this.betHu(jnd28BetCounter); //
            }
        } else {
            if( noHuStreak >= threshold && noHuStreak <= maxFollowCount + threshold ){  // 如果中啦
                jnd28BetCounter.awardGiving();
                jnd28BetCounter.resetBetAmountCount();
            }
            noHuStreak = 0;
            jnd28BetCounter.resetChaseHuCount();
        }
        return betBo;
    }

    public List<String> lotteryListToType(List<LotteryDO> dataList){
        return dataList.stream().map(lotteryDO->getTypeName(lotteryDO.getOpenCode())).collect(Collectors.toList());
    }

    /**
     * 计算某类型（“龙”或“虎”）连续未出现的次数
     */
    private int calcNoStreak(List<LotteryDO> dataList, String targetType) {
        int count = 0;
        for (LotteryDO lotteryDO : dataList) {
            String type = getTypeName(lotteryDO.getOpenCode());
            if (!type.equals(targetType)) {
                count++;
            } else {
                break;
            }
        }
        return count;
    }

    public static String getTypeName(LotteryDO nextLotteryDO) {
        return getTypeName(nextLotteryDO.getOpenCode());
    }
    /**
     * 根据开奖号返回龙虎和类型
     */
    public static String getTypeName(String openCode) {
        int firstNum = Integer.parseInt(openCode.substring(0, 1));
        int lastNum = Integer.parseInt(openCode.substring(openCode.length() - 1));
        if (firstNum > lastNum) return "龙";
        if (firstNum < lastNum) return "虎";
        return "和";
    }

    /**
     * 是否符合追注条件
     */
    private boolean shouldBet(int streak, int threshold, int maxFollowCount) {
        return streak >= threshold && streak <= threshold + maxFollowCount;
    }
    /**
     * 失败投注的情况
     */
    private boolean faildBet(int streak, int threshold, int maxFollowCount) {
        return streak > threshold + maxFollowCount;
    }



    protected  List<LotteryDO> getLotteryDOS(String today , int limits) {
        return LotteryServiceImpl.doKuaiHttp(today, limits);
    }

    public Jnd28BetCounter getJnd28BetCounter(String redisKey) {
        return new Jnd28BetCounterRedisImpl(redisKey);
    }

    public BetBo betHu(Jnd28BetCounter jnd28BetCounter){
        int amount = jnd28BetCounter.betByCurAmount();
        BetBo betBo =  new BetBo();
        betBo.setType("虎");
        betBo.setAmount(amount);
        jnd28BetCounter.incrementBetAmountCount();
        jnd28BetCounter.incrementChaseHuCount();
        return betBo;
    }
    public BetBo betLong(Jnd28BetCounter jnd28BetCounter){
        int amount = jnd28BetCounter.betByCurAmount();
        BetBo betBo =  new BetBo();
        betBo.setType("龙");
        betBo.setAmount(amount);
        jnd28BetCounter.incrementBetAmountCount();
        jnd28BetCounter.incrementChaseLongCount();
        return betBo;
    }

}
