package cn.iocoder.yudao.module.vote.controller.admin.activity;

import org.springframework.web.bind.annotation.*;
import javax.annotation.Resource;
import org.springframework.validation.annotation.Validated;
import org.springframework.security.access.prepost.PreAuthorize;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.Operation;

import javax.validation.constraints.*;
import javax.validation.*;
import javax.servlet.http.*;
import java.util.*;
import java.io.IOException;

import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.common.pojo.CommonResult;
import static cn.iocoder.yudao.framework.common.pojo.CommonResult.success;

import cn.iocoder.yudao.framework.excel.core.util.ExcelUtils;


import cn.iocoder.yudao.module.vote.controller.admin.activity.vo.*;
import cn.iocoder.yudao.module.vote.dal.dataobject.activity.ActivityDO;
import cn.iocoder.yudao.module.vote.convert.activity.ActivityConvert;
import cn.iocoder.yudao.module.vote.service.activity.ActivityService;

@Tag(name = "管理后台 - 投票活动")
@RestController
@RequestMapping("/vote/activity")
@Validated
public class ActivityController {

    @Resource
    private ActivityService activityService;

    @PostMapping("/create")
    @Operation(summary = "创建投票活动")
    @PreAuthorize("@ss.hasPermission('vote:activity:create')")
    public CommonResult<Long> createActivity(@Valid @RequestBody ActivityCreateReqVO createReqVO) {
        return success(activityService.createActivity(createReqVO));
    }

    @PutMapping("/update")
    @Operation(summary = "更新投票活动")
    @PreAuthorize("@ss.hasPermission('vote:activity:update')")
    public CommonResult<Boolean> updateActivity(@Valid @RequestBody ActivityUpdateReqVO updateReqVO) {
        activityService.updateActivity(updateReqVO);
        return success(true);
    }

    @DeleteMapping("/delete")
    @Operation(summary = "删除投票活动")
    @Parameter(name = "id", description = "编号", required = true)
    @PreAuthorize("@ss.hasPermission('vote:activity:delete')")
    public CommonResult<Boolean> deleteActivity(@RequestParam("id") Long id) {
        activityService.deleteActivity(id);
        return success(true);
    }

    @GetMapping("/get")
    @Operation(summary = "获得投票活动")
    @Parameter(name = "id", description = "编号", required = true, example = "1024")
    @PreAuthorize("@ss.hasPermission('vote:activity:query')")
    public CommonResult<ActivityRespVO> getActivity(@RequestParam("id") Long id) {
        ActivityDO activity = activityService.getActivity(id);
        return success(ActivityConvert.INSTANCE.convert(activity));
    }

    @GetMapping("/list")
    @Operation(summary = "获得投票活动列表")
    @Parameter(name = "ids", description = "编号列表", required = true, example = "1024,2048")
    @PreAuthorize("@ss.hasPermission('vote:activity:query')")
    public CommonResult<List<ActivityRespVO>> getActivityList(@RequestParam("ids") Collection<Long> ids) {
        List<ActivityDO> list = activityService.getActivityList(ids);
        return success(ActivityConvert.INSTANCE.convertList(list));
    }

    @GetMapping("/page")
    @Operation(summary = "获得投票活动分页")
    @PreAuthorize("@ss.hasPermission('vote:activity:query')")
    public CommonResult<PageResult<ActivityRespVO>> getActivityPage(@Valid ActivityPageReqVO pageVO) {
        PageResult<ActivityDO> pageResult = activityService.getActivityPage(pageVO);
        return success(ActivityConvert.INSTANCE.convertPage(pageResult));
    }

    @GetMapping("/export-excel")
    @Operation(summary = "导出投票活动 Excel")
    @PreAuthorize("@ss.hasPermission('vote:activity:export')")
    public void exportActivityExcel(@Valid ActivityExportReqVO exportReqVO,
              HttpServletResponse response) throws IOException {
        List<ActivityDO> list = activityService.getActivityList(exportReqVO);
        // 导出 Excel
        List<ActivityExcelVO> datas = ActivityConvert.INSTANCE.convertList02(list);
        ExcelUtils.write(response, "投票活动.xls", "数据", ActivityExcelVO.class, datas);
    }

}
