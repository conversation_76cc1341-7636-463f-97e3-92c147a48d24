package cn.iocoder.yudao.module.vote.controller.admin.activity.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;
import java.util.*;
import java.time.LocalDateTime;
import java.time.LocalDateTime;
import java.time.LocalDateTime;
import java.time.LocalDateTime;

import com.alibaba.excel.annotation.ExcelProperty;

/**
 * 投票活动 Excel VO
 *
 * <AUTHOR>
 */
@Data
public class ActivityExcelVO {

    @ExcelProperty("编号")
    private Long id;

    @ExcelProperty("名称")
    private String name;

    @ExcelProperty("横幅图片")
    private String bannerImage;

    @ExcelProperty("首页轮播图片")
    private String homeImage;

    @ExcelProperty("奖品图片")
    private String prizeImage;

    @ExcelProperty("介绍图片")
    private String introduceImage;

    @ExcelProperty("分享标题")
    private String shareTitle;

    @ExcelProperty("活动公告")
    private String notice;

    @ExcelProperty("背景图")
    private String background;

    @ExcelProperty("开始时间")
    private LocalDateTime startTime;

    @ExcelProperty("结束时间")
    private LocalDateTime endTime;

    @ExcelProperty("浏览数")
    private Integer viewNumber;

    @ExcelProperty("票数总计")
    private Integer voteNumber;

    @ExcelProperty("礼物展示条数")
    private Integer giftShowNumber;

    @ExcelProperty("去其他的微信小程序appId")
    private String goWxAppid;

    @ExcelProperty("状态标识")
    private Byte status;

    @ExcelProperty("每天可为每个选手投几票")
    private Integer votesPerPerson;

    @ExcelProperty("可为最多几名选手投票")
    private Integer playerMaxCount;

    @ExcelProperty("主题")
    private String theme;

    @ExcelProperty("乐观锁")
    private Integer revision;

    @ExcelProperty("部门ID")
    private Long deptId;

    @ExcelProperty("用户ID")
    private Long userId;

    @ExcelProperty("创建时间")
    private LocalDateTime createTime;

}
