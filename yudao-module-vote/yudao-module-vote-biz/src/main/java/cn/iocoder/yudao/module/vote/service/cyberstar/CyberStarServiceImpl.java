package cn.iocoder.yudao.module.vote.service.cyberstar;

import cn.iocoder.yudao.module.vote.dal.redis.RedisKeyConstants;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Service;
import javax.annotation.Resource;
import org.springframework.validation.annotation.Validated;

import java.util.*;
import cn.iocoder.yudao.module.vote.controller.admin.cyberstar.vo.*;
import cn.iocoder.yudao.module.vote.dal.dataobject.cyberstar.CyberStarDO;
import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.common.util.object.BeanUtils;

import cn.iocoder.yudao.module.vote.dal.mysql.cyberstar.CyberStarMapper;

import static cn.iocoder.yudao.framework.common.exception.util.ServiceExceptionUtil.exception;
import static cn.iocoder.yudao.module.vote.enums.ErrorCodeConstants.*;

/**
 * 网红主播 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class CyberStarServiceImpl implements CyberStarService {

    @Resource
    private CyberStarMapper cyberStarMapper;
    @Override
    public Boolean updateBatch(List<CyberStarSaveReqVO> entities) {
        List<CyberStarDO> starDOList = BeanUtils.toBean(entities, CyberStarDO.class);
        return cyberStarMapper.updateBatch(starDOList);
    }

    @Override
    public Long createCyberStar(CyberStarSaveReqVO createReqVO) {
        // 插入
        CyberStarDO cyberStar = BeanUtils.toBean(createReqVO, CyberStarDO.class);

        cyberStarMapper.insert(cyberStar);
        // 返回
        return cyberStar.getId();
    }

    @Override
    public void updateCyberStar(CyberStarSaveReqVO updateReqVO) {
        // 校验存在
        validateCyberStarExists(updateReqVO.getId());
        // 更新
        CyberStarDO updateObj = BeanUtils.toBean(updateReqVO, CyberStarDO.class);
        cyberStarMapper.updateById(updateObj);
    }

    @Override
    public void deleteCyberStar(Long id) {
        // 校验存在
        validateCyberStarExists(id);
        // 删除
        cyberStarMapper.deleteById(id);
    }

    private void validateCyberStarExists(Long id) {
        if (cyberStarMapper.selectById(id) == null) {
            throw exception(CYBER_STAR_NOT_EXISTS);
        }
    }

    @Override
    public CyberStarDO getCyberStar(Long id) {
        return cyberStarMapper.selectById(id);
    }

    @Override
    public PageResult<CyberStarDO> getCyberStarPage(CyberStarPageReqVO pageReqVO) {
        return cyberStarMapper.selectPage(pageReqVO);
    }

    @Override
    public List<CyberStarDO> getAll() {
        return cyberStarMapper.selectList();
    }

    @Override
    @Cacheable(cacheNames = RedisKeyConstants.CYBER_STAR_TYPE, key = "#type")
    public List<CyberStarDO> getListByType(Integer type) {
        return cyberStarMapper.selectList(CyberStarDO::getType,type);
    }

    @Override
    public List<CyberStarDO> getTopGmv(int limit) {
        return cyberStarMapper.selectTopGmv(limit);
    }

    @Override
    public List<CyberStarDO> getTopGift(int limit) {
        return cyberStarMapper.selectTopGift(limit);
    }

}