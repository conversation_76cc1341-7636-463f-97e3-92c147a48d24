package cn.iocoder.yudao.module.vote.dal.mysql.activity;

import java.util.*;

import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.mybatis.core.query.LambdaQueryWrapperX;
import cn.iocoder.yudao.framework.mybatis.core.mapper.BaseMapperX;
import cn.iocoder.yudao.module.vote.dal.dataobject.activity.ActivityDO;
import org.apache.ibatis.annotations.Mapper;
import cn.iocoder.yudao.module.vote.controller.admin.activity.vo.*;

/**
 * 投票活动 Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface ActivityMapper extends BaseMapperX<ActivityDO> {

    default PageResult<ActivityDO> selectPage(ActivityPageReqVO reqVO) {
        return selectPage(reqVO, new LambdaQueryWrapperX<ActivityDO>()
                .likeIfPresent(ActivityDO::getName, reqVO.getName())
                .eqIfPresent(ActivityDO::getBannerImage, reqVO.getBannerImage())
                .eqIfPresent(ActivityDO::getHomeImage, reqVO.getHomeImage())
                .eqIfPresent(ActivityDO::getPrizeImage, reqVO.getPrizeImage())
                .eqIfPresent(ActivityDO::getIntroduceImage, reqVO.getIntroduceImage())
                .eqIfPresent(ActivityDO::getShareTitle, reqVO.getShareTitle())
                .eqIfPresent(ActivityDO::getNotice, reqVO.getNotice())
                .eqIfPresent(ActivityDO::getBackground, reqVO.getBackground())
                .betweenIfPresent(ActivityDO::getStartTime, reqVO.getStartTime())
                .betweenIfPresent(ActivityDO::getEndTime, reqVO.getEndTime())
                .eqIfPresent(ActivityDO::getViewNumber, reqVO.getViewNumber())
                .eqIfPresent(ActivityDO::getVoteNumber, reqVO.getVoteNumber())
                .eqIfPresent(ActivityDO::getGiftShowNumber, reqVO.getGiftShowNumber())
                .eqIfPresent(ActivityDO::getGoWxAppid, reqVO.getGoWxAppid())
                .eqIfPresent(ActivityDO::getStatus, reqVO.getStatus())
                .eqIfPresent(ActivityDO::getVotesPerPerson, reqVO.getVotesPerPerson())
                .eqIfPresent(ActivityDO::getPlayerMaxCount, reqVO.getPlayerMaxCount())
                .eqIfPresent(ActivityDO::getTheme, reqVO.getTheme())
                .eqIfPresent(ActivityDO::getRevision, reqVO.getRevision())
                .eqIfPresent(ActivityDO::getDeptId, reqVO.getDeptId())
                .eqIfPresent(ActivityDO::getUserId, reqVO.getUserId())
                .betweenIfPresent(ActivityDO::getCreateTime, reqVO.getCreateTime())
                .orderByDesc(ActivityDO::getId));
    }

    default List<ActivityDO> selectList(ActivityExportReqVO reqVO) {
        return selectList(new LambdaQueryWrapperX<ActivityDO>()
                .likeIfPresent(ActivityDO::getName, reqVO.getName())
                .eqIfPresent(ActivityDO::getBannerImage, reqVO.getBannerImage())
                .eqIfPresent(ActivityDO::getHomeImage, reqVO.getHomeImage())
                .eqIfPresent(ActivityDO::getPrizeImage, reqVO.getPrizeImage())
                .eqIfPresent(ActivityDO::getIntroduceImage, reqVO.getIntroduceImage())
                .eqIfPresent(ActivityDO::getShareTitle, reqVO.getShareTitle())
                .eqIfPresent(ActivityDO::getNotice, reqVO.getNotice())
                .eqIfPresent(ActivityDO::getBackground, reqVO.getBackground())
                .betweenIfPresent(ActivityDO::getStartTime, reqVO.getStartTime())
                .betweenIfPresent(ActivityDO::getEndTime, reqVO.getEndTime())
                .eqIfPresent(ActivityDO::getViewNumber, reqVO.getViewNumber())
                .eqIfPresent(ActivityDO::getVoteNumber, reqVO.getVoteNumber())
                .eqIfPresent(ActivityDO::getGiftShowNumber, reqVO.getGiftShowNumber())
                .eqIfPresent(ActivityDO::getGoWxAppid, reqVO.getGoWxAppid())
                .eqIfPresent(ActivityDO::getStatus, reqVO.getStatus())
                .eqIfPresent(ActivityDO::getVotesPerPerson, reqVO.getVotesPerPerson())
                .eqIfPresent(ActivityDO::getPlayerMaxCount, reqVO.getPlayerMaxCount())
                .eqIfPresent(ActivityDO::getTheme, reqVO.getTheme())
                .eqIfPresent(ActivityDO::getRevision, reqVO.getRevision())
                .eqIfPresent(ActivityDO::getDeptId, reqVO.getDeptId())
                .eqIfPresent(ActivityDO::getUserId, reqVO.getUserId())
                .betweenIfPresent(ActivityDO::getCreateTime, reqVO.getCreateTime())
                .orderByDesc(ActivityDO::getId));
    }

}
