package cn.iocoder.yudao.module.vote.controller.admin.macaujc;

import org.springframework.web.bind.annotation.*;
import javax.annotation.Resource;
import org.springframework.validation.annotation.Validated;
import org.springframework.security.access.prepost.PreAuthorize;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.Operation;

import javax.validation.constraints.*;
import javax.validation.*;
import javax.servlet.http.*;
import java.util.*;
import java.io.IOException;

import cn.iocoder.yudao.framework.common.pojo.PageParam;
import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.common.pojo.CommonResult;
import cn.iocoder.yudao.framework.common.util.object.BeanUtils;
import static cn.iocoder.yudao.framework.common.pojo.CommonResult.success;

import cn.iocoder.yudao.framework.excel.core.util.ExcelUtils;

import cn.iocoder.yudao.framework.apilog.core.annotation.ApiAccessLog;
import static cn.iocoder.yudao.framework.apilog.core.enums.OperateTypeEnum.*;

import cn.iocoder.yudao.module.vote.controller.admin.macaujc.vo.*;
import cn.iocoder.yudao.module.vote.dal.dataobject.macaujc.MacaujcDO;
import cn.iocoder.yudao.module.vote.service.macaujc.MacaujcService;

@Tag(name = "管理后台 - 彩票")
@RestController
@RequestMapping("/vote/macaujc")
@Validated
public class MacaujcController {

    @Resource
    private MacaujcService macaujcService;

    @PostMapping("/create")
    @Operation(summary = "创建彩票")
    @PreAuthorize("@ss.hasPermission('vote:macaujc:create')")
    public CommonResult<Integer> createMacaujc(@Valid @RequestBody MacaujcSaveReqVO createReqVO) {
        return success(macaujcService.createMacaujc(createReqVO));
    }

    @PutMapping("/update")
    @Operation(summary = "更新彩票")
    @PreAuthorize("@ss.hasPermission('vote:macaujc:update')")
    public CommonResult<Boolean> updateMacaujc(@Valid @RequestBody MacaujcSaveReqVO updateReqVO) {
        macaujcService.updateMacaujc(updateReqVO);
        return success(true);
    }

    @DeleteMapping("/delete")
    @Operation(summary = "删除彩票")
    @Parameter(name = "id", description = "编号", required = true)
    @PreAuthorize("@ss.hasPermission('vote:macaujc:delete')")
    public CommonResult<Boolean> deleteMacaujc(@RequestParam("id") Integer id) {
        macaujcService.deleteMacaujc(id);
        return success(true);
    }

    @GetMapping("/get")
    @Operation(summary = "获得彩票")
    @Parameter(name = "id", description = "编号", required = true, example = "1024")
    @PreAuthorize("@ss.hasPermission('vote:macaujc:query')")
    public CommonResult<MacaujcRespVO> getMacaujc(@RequestParam("id") Integer id) {
        MacaujcDO macaujc = macaujcService.getMacaujc(id);
        return success(BeanUtils.toBean(macaujc, MacaujcRespVO.class));
    }

    @GetMapping("/page")
    @Operation(summary = "获得彩票分页")
    @PreAuthorize("@ss.hasPermission('vote:macaujc:query')")
    public CommonResult<PageResult<MacaujcRespVO>> getMacaujcPage(@Valid MacaujcPageReqVO pageReqVO) {
        PageResult<MacaujcDO> pageResult = macaujcService.getMacaujcPage(pageReqVO);
        return success(BeanUtils.toBean(pageResult, MacaujcRespVO.class));
    }

    @GetMapping("/export-excel")
    @Operation(summary = "导出彩票 Excel")
    @PreAuthorize("@ss.hasPermission('vote:macaujc:export')")
    @ApiAccessLog(operateType = EXPORT)
    public void exportMacaujcExcel(@Valid MacaujcPageReqVO pageReqVO,
              HttpServletResponse response) throws IOException {
        pageReqVO.setPageSize(PageParam.PAGE_SIZE_NONE);
        List<MacaujcDO> list = macaujcService.getMacaujcPage(pageReqVO).getList();
        // 导出 Excel
        ExcelUtils.write(response, "彩票.xls", "数据", MacaujcRespVO.class,
                        BeanUtils.toBean(list, MacaujcRespVO.class));
    }

}