package cn.iocoder.yudao.module.vote.convert.cyberstar;

import cn.iocoder.yudao.module.vote.controller.admin.cyberstar.vo.CyberStarRespVO;
import cn.iocoder.yudao.module.vote.controller.app.cyberstar.vo.CyberStarTopRespVO;
import cn.iocoder.yudao.module.vote.dal.dataobject.cyberstar.CyberStarDO;
import org.mapstruct.IterableMapping;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.Named;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * 网红主播 Convert
 *
 * <AUTHOR>
 */
@Mapper
public interface CyberStarConvert {

    CyberStarConvert INSTANCE = Mappers.getMapper(CyberStarConvert.class);

    /**
     * 将 CyberStarDO 转换为 CyberStarRespVO
     */
    CyberStarRespVO convert(CyberStarDO bean);

    /**
     * 将 CyberStarDO 列表转换为 CyberStarRespVO 列表
     */
    List<CyberStarRespVO> convertList(List<CyberStarDO> list);

    /**
     * 将 CyberStarDO 转换为 CyberStarTopRespVO，用于 GMV 排行
     */
    @Named("convertToTopGmvVO")
    @Mapping(source = "gmvTodayAmountDouble", target = "amountDouble")
    CyberStarTopRespVO convertToTopGmvVO(CyberStarDO bean);

    /**
     * 将 CyberStarDO 列表转换为 CyberStarTopRespVO 列表，用于 GMV 排行
     */
    @IterableMapping(qualifiedByName = "convertToTopGmvVO")
    List<CyberStarTopRespVO> convertToTopGmvVOList(List<CyberStarDO> list);

    /**
     * 将 CyberStarDO 转换为 CyberStarTopRespVO，用于 Gift 排行
     */
    @Named("convertToTopGiftVO")
    @Mapping(source = "giftTodayAmountDouble", target = "amountDouble")
    CyberStarTopRespVO convertToTopGiftVO(CyberStarDO bean);

    /**
     * 将 CyberStarDO 列表转换为 CyberStarTopRespVO 列表，用于 Gift 排行
     */
    @IterableMapping(qualifiedByName = "convertToTopGiftVO")
    List<CyberStarTopRespVO> convertToTopGiftVOList(List<CyberStarDO> list);
}
