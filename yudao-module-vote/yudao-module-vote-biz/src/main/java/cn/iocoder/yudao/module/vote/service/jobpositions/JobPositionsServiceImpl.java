package cn.iocoder.yudao.module.vote.service.jobpositions;

import cn.iocoder.yudao.framework.security.core.util.SecurityFrameworkUtils;
import cn.iocoder.yudao.module.vote.service.JobPayOrderService;
import org.springframework.stereotype.Service;
import javax.annotation.Resource;
import org.springframework.validation.annotation.Validated;

import java.util.*;
import cn.iocoder.yudao.module.vote.controller.admin.jobpositions.vo.*;
import cn.iocoder.yudao.module.vote.dal.dataobject.jobpositions.JobPositionsDO;
import cn.iocoder.yudao.framework.common.pojo.PageResult;

import cn.iocoder.yudao.module.vote.convert.jobpositions.JobPositionsConvert;
import cn.iocoder.yudao.module.vote.dal.mysql.jobpositions.JobPositionsMapper;

import static cn.iocoder.yudao.framework.common.exception.util.ServiceExceptionUtil.exception;
import static cn.iocoder.yudao.module.vote.enums.ErrorCodeConstants.*;

/**
 * 职位信息 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class JobPositionsServiceImpl implements JobPositionsService {

    @Resource
    private JobPositionsMapper jobPositionsMapper;
    @Resource
    private JobPayOrderService jobPayOrderService;

    @Override
    public Integer createJobPositions(JobPositionsCreateReqVO createReqVO) {
        // 插入
        JobPositionsDO jobPositions = JobPositionsConvert.INSTANCE.convert(createReqVO);
        jobPositionsMapper.insert(jobPositions);
        // 返回
        return jobPositions.getPositionId();
    }

    @Override
    public void updateJobPositions(JobPositionsUpdateReqVO updateReqVO) {
        // 校验存在
        validateJobPositionsExists(updateReqVO.getPositionId());
        // 更新
        JobPositionsDO updateObj = JobPositionsConvert.INSTANCE.convert(updateReqVO);
        jobPositionsMapper.updateById(updateObj);
    }

    @Override
    public void deleteJobPositions(Integer id) {
        // 校验存在
        validateJobPositionsExists(id);
        // 删除
        jobPositionsMapper.deleteById(id);
    }

    private void validateJobPositionsExists(Integer id) {
        if (jobPositionsMapper.selectById(id) == null) {
            throw exception(JOB_POSITIONS_NOT_EXISTS);
        }
    }

    @Override
    public JobPositionsDO getJobPositions(Integer id) {
        JobPositionsDO jobPositionsDO = jobPositionsMapper.selectById(id);
        // 如果没有充值会员就不展示对应的内容。
        Long userId = SecurityFrameworkUtils.getLoginUserId();
        if(!jobPayOrderService.isPay(userId)){
            jobPositionsDO.setLink(null);
        }
        return jobPositionsDO;
    }

    @Override
    public List<JobPositionsDO> getJobPositionsList(Collection<Integer> ids) {
        return jobPositionsMapper.selectBatchIds(ids);
    }

    @Override
    public PageResult<JobPositionsDO> getJobPositionsPage(JobPositionsPageReqVO pageReqVO) {
        // TODO 这里也需要过滤一下，里面展示的字段内容。
        Long userId = SecurityFrameworkUtils.getLoginUserId();
        boolean pay = jobPayOrderService.isPay(userId);
        return jobPositionsMapper.selectPage(pageReqVO,pay);
    }

    @Override
    public List<JobPositionsDO> getJobPositionsList(JobPositionsExportReqVO exportReqVO) {
        return jobPositionsMapper.selectList(exportReqVO);
    }

}
