package cn.iocoder.yudao.module.vote.service.cyberstar;

import java.util.*;
import javax.validation.*;
import cn.iocoder.yudao.module.vote.controller.admin.cyberstar.vo.*;
import cn.iocoder.yudao.module.vote.dal.dataobject.cyberstar.CyberStarDO;
import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.common.pojo.PageParam;

/**
 * 网红主播 Service 接口
 *
 * <AUTHOR>
 */
public interface CyberStarService {

    Boolean updateBatch(List<CyberStarSaveReqVO> entities);

    /**
     * 创建网红主播
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    Long createCyberStar(@Valid CyberStarSaveReqVO createReqVO);

    /**
     * 更新网红主播
     *
     * @param updateReqVO 更新信息
     */
    void updateCyberStar(@Valid CyberStarSaveReqVO updateReqVO);

    /**
     * 删除网红主播
     *
     * @param id 编号
     */
    void deleteCyberStar(Long id);

    /**
     * 获得网红主播
     *
     * @param id 编号
     * @return 网红主播
     */
    CyberStarDO getCyberStar(Long id);

    /**
     * 获得网红主播分页
     *
     * @param pageReqVO 分页查询
     * @return 网红主播分页
     */
    PageResult<CyberStarDO> getCyberStarPage(CyberStarPageReqVO pageReqVO);

    /**
     * 获得所有网红主播
     *
     * @return 网红主播列表
     */
    List<CyberStarDO> getAll();


    List<CyberStarDO> getListByType(Integer type);

    /**
     * 获取前 N 名 GMV 的网红主播数据
     *
     * @param limit 限制数量
     * @return 网红主播列表，只包含 id 和 gmvTodayAmountDouble 字段
     */
    List<CyberStarDO> getTopGmv(int limit);

    /**
     * 获取前 N 名 Gift 的网红主播数据
     *
     * @param limit 限制数量
     * @return 网红主播列表，只包含 id 和 giftTodayAmountDouble 字段
     */
    List<CyberStarDO> getTopGift(int limit);
}