package cn.iocoder.yudao.module.vote.service.jobpositions;

import java.util.*;
import javax.validation.*;
import cn.iocoder.yudao.module.vote.controller.admin.jobpositions.vo.*;
import cn.iocoder.yudao.module.vote.dal.dataobject.jobpositions.JobPositionsDO;
import cn.iocoder.yudao.framework.common.pojo.PageResult;

/**
 * 职位信息 Service 接口
 *
 * <AUTHOR>
 */
public interface JobPositionsService {

    /**
     * 创建职位信息
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    Integer createJobPositions(@Valid JobPositionsCreateReqVO createReqVO);

    /**
     * 更新职位信息
     *
     * @param updateReqVO 更新信息
     */
    void updateJobPositions(@Valid JobPositionsUpdateReqVO updateReqVO);

    /**
     * 删除职位信息
     *
     * @param id 编号
     */
    void deleteJobPositions(Integer id);

    /**
     * 获得职位信息
     *
     * @param id 编号
     * @return 职位信息
     */
    JobPositionsDO getJobPositions(Integer id);

    /**
     * 获得职位信息列表
     *
     * @param ids 编号
     * @return 职位信息列表
     */
    List<JobPositionsDO> getJobPositionsList(Collection<Integer> ids);

    /**
     * 获得职位信息分页
     *
     * @param pageReqVO 分页查询
     * @return 职位信息分页
     */
    PageResult<JobPositionsDO> getJobPositionsPage(JobPositionsPageReqVO pageReqVO);

    /**
     * 获得职位信息列表, 用于 Excel 导出
     *
     * @param exportReqVO 查询条件
     * @return 职位信息列表
     */
    List<JobPositionsDO> getJobPositionsList(JobPositionsExportReqVO exportReqVO);

}
