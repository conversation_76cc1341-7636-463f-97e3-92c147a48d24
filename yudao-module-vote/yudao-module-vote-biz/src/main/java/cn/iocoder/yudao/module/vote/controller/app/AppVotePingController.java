package cn.iocoder.yudao.module.vote.controller.app;

/**
 * 在 Controller 的命名上，额外增加 App 作为前缀，一方面区分是管理后台还是用户 App 的 Controller，另一方面避免 Spring Bean 的名字冲突。
 *
 * 可能你会奇怪，这里我们定义了两个 /demo/test/get 接口，会不会存在重复导致冲突呢？答案，当然是并不会。原因是：
 *
 * controller.admin 包下的接口，默认会增加 /admin-api，即最终的访问地址是 /admin-api/demo/test/get
 * controller.app 包下的接口，默认会增加 /app-api，即最终的访问地址是 /app-api/demo/test/get
 *
 *  比赛投屏编程: 临时代码内容的使用
 * <AUTHOR>
 * @version 1.0
 * @module Socialuni
 * @date 2023/4/29 10:25
 * @since 1.0
 */

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.collection.ListUtil;
import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.date.LocalDateTimeUtil;
import cn.hutool.core.lang.Filter;
import cn.hutool.core.map.MapUtil;
import cn.hutool.core.thread.ThreadUtil;
import cn.hutool.core.util.NumberUtil;
import cn.hutool.core.util.RandomUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.http.HttpRequest;
import cn.hutool.http.HttpUtil;
import cn.hutool.json.JSONArray;
import cn.hutool.json.JSONObject;
import cn.iocoder.yudao.framework.common.core.CounterMap;
import cn.iocoder.yudao.framework.common.pojo.CommonResult;
import cn.iocoder.yudao.framework.common.util.collection.CollectionUtils;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.CommandLineRunner;
import org.springframework.data.redis.core.BoundHashOperations;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.messaging.simp.SimpMessagingTemplate;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.util.StringUtils;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.time.format.DateTimeParseException;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.function.BiConsumer;
import java.util.function.Consumer;
import java.util.function.Predicate;

import static cn.iocoder.yudao.framework.common.pojo.CommonResult.success;

@Tag(name = "比赛投屏编程 - 比赛投屏")
//@RestController
//@RequestMapping("/vote/ping")
@Validated
@Slf4j
public class AppVotePingController /*implements CommandLineRunner*/ {
  public static final int YESTERDAY_OFFSET = -1;
  @Resource // 保证 aj-captcha 的 SPI 创建时的注入
  private StringRedisTemplate stringRedisTemplate;
  @Autowired
  private SimpMessagingTemplate simpMessagingTemplate;
  @GetMapping("/getLive/{id}/{liveStreamId}")
  @Operation(summary = "获得直播场次的成交数")
  public CommonResult<String> getLive(@PathVariable("id") Integer id,@PathVariable("liveStreamId") String liveStreamId) {
    JSONObject object = doHttp(CyberStar.values()[id - 1], "https://zs.kwaixiaodian.com/gateway/live/op/board/main/overview", "{\"liveStreamId\": "+liveStreamId+"}");
    String str = object.getByPath("data.overview[10].value.value", String.class);
    return success(str);
  }

  /**
   *
   * @param reidsKey
   * @param startTime 包含07-01号
   * @param endTime  不包含07-03号
   * @return
   */
  @GetMapping("/getData")
  @Operation(summary = "获得单个数据")
  public CommonResult<String> getData(@RequestParam("reidsKey") String reidsKey,
                                      @RequestParam("startTime") String startTime,@RequestParam("endTime") String endTime) {
    // 获得数据
    double sumMap = getSumMap(reidsKey, startTime, endTime);
    return success(NumberUtil.decimalFormat("0.000",sumMap));
  }



  /**
   * 聚合多个数据内容
   * @param prefix
   * @param keys
   * @param startTime 包含07-01号
   * @param endTime  不包含07-03号
   * @return
   */
  @GetMapping("/getMultipleData")
  @Operation(summary = "获得多个数据")
  public CommonResult<String> getMultipleData(@RequestParam("prefix") String prefix,@RequestParam("keys") String keys,
                                      @RequestParam("startTime") String startTime,@RequestParam("endTime") String endTime) {
    // 获得数据
    double sumMap = 0;
    List<String> keyArr = StrUtil.split(keys, ',');
    for (String key : keyArr) {
      sumMap += getSumMap( prefix + key, startTime, endTime);
    }
    return success(NumberUtil.decimalFormat("0.000",sumMap));
  }

  private double getSumMap(String reidsKey, String startTime, String endTime) {
    BoundHashOperations<String, String, String> redis = stringRedisTemplate.boundHashOps(reidsKey);
    double sumMap = sumMap(redis.entries(), (entry)->{return isTimeBetween(entry.getKey(), startTime, endTime);});
    return sumMap;
  }
  /**
   * 输出应为true如果a在b和c之间
   * @param timeA
   * @param timeB
   * @param timeC
   * @return
   */
  public static boolean isTimeBetween(String timeA, String timeB, String timeC) {
    DateTimeFormatter dateFormatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");
    try {
      LocalDateTime dateTimeA = LocalDate.parse(timeA, dateFormatter).atStartOfDay();
      LocalDateTime dateTimeB = LocalDate.parse(timeB, dateFormatter).atStartOfDay();
      LocalDateTime dateTimeC = LocalDate.parse(timeC, dateFormatter).atStartOfDay();
      return !dateTimeA.isBefore(dateTimeB) && dateTimeA.isBefore(dateTimeC);
    } catch (DateTimeParseException e) {
      throw new IllegalArgumentException("Invalid date format.", e);
    }
  }
  @GetMapping("/get")
  @Operation(summary = "大屏累计")
  public CommonResult<List<Map<String,Object>>> get() {
    return get(null,null);
  }

  @GetMapping("/getListTimeBetween")
  @Operation(summary = "获得时间段的list列表")
  public CommonResult<List<Map<String,Object>>> getListTimeBetween(@RequestParam("keys") String keys,@RequestParam("startTime") String startTime,@RequestParam("endTime") String endTime) {
    if("2024-11-10".equals(startTime)){  // 特殊处理内容。
      CommonResult<List<Map<String,Object>>> list22CommonResult = get(x -> StrUtil.equalsAny(x.getKey(),"2024-11-10")
              , x -> StrUtil.contains(keys, x.name()), null, false, false);

      CommonResult<List<Map<String,Object>>> listCommonResult = get(x -> StrUtil.equalsAny(x.getKey(),"2024-11-11")
              , x -> StrUtil.contains(keys, x.name()), null, false, false);
      List<Map<String,Object>> list = listCommonResult.getData();
      list.forEach(x->{
        Map<String,Object> json = (Map<String, Object>) x;
        Object nickname = json.get("nickname");
        Integer total =   (Integer) json.get("total");
        Object name =    json.get("name");
        json.put("imgUrl",StrUtil.format("https://yunlvltd.com/touping-img/{}.pic.jpg",name));

        CounterMap<String> counterMap = new CounterMap(new HashMap(),0);
        counterMap.getAndAdd("娇妹",19596);
        counterMap.getAndAdd("雅儿",45438);
        if(new Date().getTime() < 1731297600000l){
          System.out.println(StrUtil.format("counterMap.getAndAdd(\"{}\",{});" , nickname, total));
//        json.put("total",0); // 这里不能是0 ， 应该要减去前几天的数据。 否则页面看起来，数据会突然少一下。
          Integer subValue = counterMap.getOrDefault(nickname);
          json.put("total",0 - subValue);
        }else{
          counterMap.getAndAdd("娇妹",64355);
          counterMap.getAndAdd("雅儿",171783);
          Integer subValue = counterMap.getOrDefault(nickname);
          json.put("total",total - subValue);
        }
      });

      return CommonResult.success(sumList(list22CommonResult.getData(),list));
    }else{ //
      return get(x -> isTimeBetween(x.getKey(), startTime, endTime)
              , x -> StrUtil.contains(keys, x.name()), null, false, false);
    }
  }

  @GetMapping("/getMonth")
  @Operation(summary = "大屏当月")
  public CommonResult<List<Map<String,Object>>> getMonth() {
    String format = DateUtil.format(new Date(), "yyyy-MM");
    return get(x->{
      return StrUtil.startWith(x.getKey(),format);
    },null);
  }
  @GetMapping("/getMonth6")
  @Operation(summary = "大屏6月")
  public CommonResult<List<Map<String,Object>>> getMonth6() {
    if(new Date().getTime() > 1718507986000l){ // 2024-06-16 11:19:46
      return success(null);
    }
    String format = DateUtil.format(new Date(), "yyyy-MM");
    return get(x->{

      return StrUtil.startWith(x.getKey(),format);
    },x-> !StrUtil.equalsAny(x.nickname,"娜娜","萌宝"),null,true,false);
  }
  @GetMapping("/getMonth6/{id}")
  @Operation(summary = "大屏6月")
  public CommonResult<Object> getMonth6Id(@PathVariable("id") Integer id) {
    Object o = null;
    if(new Date().getTime() > 1718507986000l || id == null ){ // 2024-06-16 11:19:46
      return success(null);
    }else{
      String format = DateUtil.format(new Date(), "yyyy-MM");
      CommonResult<List<Map<String,Object>>> listCommonResult = get(x -> {
        return StrUtil.startWith(x.getKey(), format);
      }, x -> !StrUtil.equalsAny(x.nickname,"娜娜","萌宝") && x.ordinal() == id-1, null,true, false);
      o = CollectionUtil.get(listCommonResult.getData(), 0);
    }
    return success(o);
  }
  /**
   * 过滤一下内容。
   * @param filter
   * @return
   */
  public CommonResult<List<Map<String,Object>>> get(Filter<Map.Entry<String, String>> filter, BiConsumer<CyberStar,Double> consumer) {
      return get(filter,consumer,false);
  }
  public CommonResult<List<Map<String,Object>>> get(Filter<Map.Entry<String, String>> filter, BiConsumer<CyberStar,Double> consumer, boolean pushFlag) {
    return get(filter,x -> !StrUtil.equalsAny(x.nickname,"小伊伊123"), consumer,false,pushFlag);
  }
  public CommonResult<List<Map<String,Object>>> get(Filter<Map.Entry<String, String>> filter, Predicate<CyberStar> cyberStarPredicate, BiConsumer<CyberStar,Double> consumer,boolean needOther, boolean pushFlag) {
    return get(filter,cyberStarPredicate,consumer,needOther,pushFlag,false); // 默认不只展示明细
  }
  //
  public CommonResult<List<Map<String,Object>>> get(Filter<Map.Entry<String, String>> filter, Predicate<CyberStar> cyberStarPredicate, BiConsumer<CyberStar,Double> consumer,boolean needOther, boolean pushFlag, boolean details) {
    List<Map<String,Object>> list = new ArrayList<>();

    for (CyberStar cyberStar : CyberStar.values()) {
      if (cyberStarPredicate.test(cyberStar)) {
        // 基础信息
        Map<String,Object> json = new HashMap<>();
        json.put("nickname",cyberStar.nickname);
        json.put("id",cyberStar.ordinal()+1);
        json.put("name",cyberStar.name());
        json.put("imgUrl",cyberStar.img);
        double total = 0;
        // 获得数据
        BoundHashOperations<String, String, String> redis = stringRedisTemplate.boundHashOps(cyberStar.getReidsKey());
        Map<String, String> amountMap = redis.entries();
        total += sumMap(amountMap,filter);
        if(needOther){
          BoundHashOperations<String, String, String> otherRedis = stringRedisTemplate.boundHashOps(cyberStar.getOtherReidsKey());
          total += sumMap(otherRedis.entries(),filter);
        }
        if(details) json.put("date", amountMap);
        putTotal(json,total);
        if(consumer != null){
          consumer.accept(cyberStar,total);
        }
        if(pushFlag){
          // 当天的数据变化的处理方式。
          convertAndSend(cyberStar);
        }
        // 放回
        list.add(json);
      }
    }
    //
    return success(list);
  }
  public double sumMap(Map<String, String> amountMap,Filter<Map.Entry<String, String>> filter){
    amountMap = MapUtil.filter(amountMap, filter);
    double total = 0;
    if(MapUtil.isNotEmpty(amountMap)){
      total = amountMap.values().stream()
              .mapToDouble(o -> NumberUtil.parseDouble(StrUtil.toStringOrNull(o)))
              .sum();
    }
    return total;
  }

  /**
   * 将2个对应的数组的总数进行合并计算。
   * @param one
   * @param two
   * @return
   */
  public List<Map<String,Object>> sumList(List<Map<String,Object>> one , List<Map<String,Object>> two){
    int size = CollectionUtil.size(one);
    if (size != CollectionUtil.size(two))  return null;
    //
    List<Map<String,Object>> list = new ArrayList<>(size);
    for (int i = 0; i < size; i++) {
      Map<String,Object> oneMap =  one.get(i);
      Integer oneTotal = (Integer) oneMap.get("total");
      Map<String,Object> sumMap = new HashMap(two.get(i));
      Integer twoTotal = (Integer) sumMap.get("total");
      sumMap.put("total", oneTotal + twoTotal);
      list.add(sumMap);
    }
    return list;
  }

  /**
   * 通过定时任务轮询，刷新缓存
   * 获取快手小店的内容。
   */
//  @Scheduled(initialDelay = 60, fixedRate = 60, timeUnit = TimeUnit.SECONDS)
//  public void refreshLocalCache() {
//    System.err.println("xxxxxxxxx"); 这个也可以实现。
//  }

  
  public void run(String... args) throws Exception {
//    this.reconciliation();
//    if(true) return;

    ThreadUtil.execute(()->{
      // 排除当天的数据。
      get(x->{
        return !StrUtil.equals(x.getKey(),DateUtil.today());
      },(obj,x)->  obj.total = x );

      // 累计当月的数据内容。
      String monthDay = DateUtil.format(new Date(), "yyyy-MM");
      String finalMonthDay = monthDay;
      get(x->{
        return StrUtil.startWith(x.getKey(), finalMonthDay) && !StrUtil.equals(x.getKey(),DateUtil.today());
      },(obj,x)->  obj.totalMonth = x );


      //


      // 循环的实时获取最新的内容。
      while(true){
        if(new Date().getTime() > 1735660817000l){ // 2025-01-01 00:00:17
            return;
        }
        String today = DateUtil.today(); // 今天的日期。
        String exclude = stringRedisTemplate.opsForValue().get("vote:touping:exclude");
        for (CyberStar cyberStar : CyberStar.values()) {
          //
          String name = cyberStar.name();
          if(StrUtil.contains(exclude, name)){
            log.warn(name + " : userId is exclude"  );
            continue;
          }
          ThreadUtil.sleep(RandomUtil.randomInt(0,3),TimeUnit.SECONDS);
          // 添加校验userId的校验。
          if(cyberStar.cookie.isEmpty()){
            log.error(name + " : userId isEmpty  "  );
            continue;
          }
          if(!cyberStar.cookie.contains(cyberStar.userId)){
            log.error(name + " : userId is error , please update "  );
            continue;
          }
          // http获取成交金额
          try {
            JSONObject resultJson =  httpTodayAmount(cyberStar.cookie);
            // 采集其他的数据内容
            Map<String,String> customizedReids = new HashMap<>();
            customizedReids.put("completedOrders","data.orderData[2].value.value"); // 成交订单数
            customizedReids.forEach((k,v)->{
              BoundHashOperations<String, Object, Object> redis = stringRedisTemplate.boundHashOps( cyberStar.getCustomizedReidsKey(k) );
              String todayAmount = resultJson.getByPath(v, String.class);
              Object todayOld = redis.get(today);
              // 不要没变化就存库，快手跳低的数据就崩啦。
              if(amountIsOk(todayAmount) &&  NumberUtil.parseInt(todayAmount) >  NumberUtil.parseInt(StrUtil.utf8Str(todayOld)) ){
                redis.put(today,todayAmount);
              }
            });
            // 旧版的逻辑代码.
            String todayAmount = resultJson.getByPath("data.payData[0].value.value", String.class);
            // 存入redis中
            BoundHashOperations<String, Object, Object> touping = stringRedisTemplate.boundHashOps(cyberStar.getReidsKey());
            if(amountIsOk(todayAmount)){
              boolean pushFlag = false;
              double todayAmountDouble = Double.parseDouble(todayAmount);
              // 添加 如果数据突然跳水，就需要添加处理一下。
              double amountDifference = todayAmountDouble - cyberStar.todayAmountDouble;
              if( ( amountDifference > cyberStar.dive_amount || todayAmountDouble / cyberStar.todayAmountDouble > 100 )   && cyberStar.dive_count < 10 && cyberStar.todayAmountDouble > 0 ){ // >0 防止第一次重启特别慢。
                  cyberStar.dive_count ++;
                  log.error("{}的数据第{}次跳水,原数据{},现数据{},差值{}",cyberStar.nickname,cyberStar.dive_count,cyberStar.todayAmountDouble,todayAmountDouble,amountDifference);
                  continue; //
              }else{
                  cyberStar.dive_count = 0;
              }
              // 第二天的处理方式。
              if (cyberStar.today.equals(today)) {
                // 数据有变化。
                if(cyberStar.todayAmountDouble < todayAmountDouble){
                  cyberStar.total = cyberStar.total - cyberStar.todayAmountDouble + todayAmountDouble;
                  cyberStar.totalMonth = cyberStar.totalMonth - cyberStar.todayAmountDouble + todayAmountDouble;
                  cyberStar.todayAmountDouble = todayAmountDouble;
                  pushFlag = true;
                }
              }else{ // 第二天，数据一定会变化。

                // 判断是否是第二个月
                monthDay = DateUtil.format(new Date(), "yyyy-MM");
                if (!StrUtil.startWith(cyberStar.today,monthDay)) {
                  cyberStar.totalMonth =  todayAmountDouble; // 不同的月份开始。
                }else{
                  cyberStar.totalMonth = cyberStar.totalMonth + todayAmountDouble; // 相同的月份
                }
                //
                cyberStar.today = today;
                cyberStar.total = cyberStar.total + todayAmountDouble;
                cyberStar.todayAmountDouble = todayAmountDouble;
                pushFlag = true;
              }
              // 变化就推送出去。
              if(pushFlag){
                touping.put(today,todayAmount); // 不要没变化就存库，快手跳低的数据就崩啦。
                // 当天的数据变化的处理方式。
                convertAndSend(cyberStar);
              }
            }else{
              log.error( " amount-is-error : {}, {}", cyberStar,todayAmount );
            }
          } catch (Exception e) {
            log.error(name +" store-value is error ",e);
          }
        }
        // 临时的数据添加
        try{
          String result = HttpRequest.post("https://zs.kwaixiaodian.com/rest/pc/live/assistant/dynamic/onsale")
                  .header("Cookie", "_did=web_761475927458F83F; did=web_karptclwgso1c66vkkmqe1eppjr4a1p0; userId=103432682; sellerId=103432682; kuaishou.shop.b_st=ChJrdWFpc2hvdS5zaG9wLmIuc3QSoAFCJ2daoG6age-KTIYSfaSDtbuOlLADXCccfwkKejfoVOk0FqK1cDRGp9Fv1HarpKRa_mlP2_uszCCWu-ysDHH5fvVpC9BuTgYFLherSz1fdqNpbUf2PvPQMBjZA1UarUkT22NyfbPzAPyqn0a3P-Z-T2b2UJ9dAMBhET975ssPe0csvacKD12Gj8z5gpTxdEY7xT_txnTMFLOxzAn3V513GhJx70cBBaGVtEpnSlNxog9ZoqoiIF1-VQAlDd3DONQF6hZP1g3F5jlDiZISL_cRRM98XhleKAUwAQ; kuaishou.shop.b_ph=f9b4c4e14d75244174da529e85b142b062cb")//头信息，多个头信息多次调用此方法即可
                  .body("{\"version\":2}")//表单内容
                  .timeout(20000)//超时，毫秒
                  .execute().body();
          JSONObject resultJson = new JSONObject(result);
          JSONArray otherByPath = resultJson.getByPath("data.commodityCardList", JSONArray.class);
          if( otherByPath != null ) {
            for (int i = 0; i < otherByPath.size(); i++) {
              JSONObject otherJsonObject = otherByPath.getJSONObject(i);
              String title = otherJsonObject.getStr("title");
              String commodityValue = otherJsonObject.getByPath("dataIndexAreaList[1].value", String.class);
              // 存入redis中
              String nickname = StrUtil.subBetween(title, "【", "专属】");
              CyberStar cyberStar = CyberStar.searchByName(nickname);
              BoundHashOperations<String, Object, Object> touping = stringRedisTemplate.boundHashOps(cyberStar.getOtherReidsKey());
              if (amountIsOk(commodityValue)) {
                touping.put(today, commodityValue);
              }else{
                log.error( " amount-is-error : {}, {}", cyberStar,commodityValue );
              }
            }
          }

        } catch (Exception e) {
            log.error(" store-value is error ",e);
        }

      }
    });
  }
  /**
   * 每3个小时获得对应的数据。 用于对账
   */
//  @Scheduled(cron = "35 * * * * ?")
//  @Scheduled(cron = "0 0 3,5,7 * * ?")
  public void reconciliation() {
      //
      for (CyberStar cyberStar : CyberStar.values()) {
        try{
            if(StrUtil.isBlank(cyberStar.cookie)) continue;
            String todayAmount =  httpTodayAmount(cyberStar.cookie,"data[7].value.sourceValue");
            // 存入redis中
            BoundHashOperations<String, Object, Object> touping = stringRedisTemplate.boundHashOps(cyberStar.getReidsKey());
            if(amountIsOk(todayAmount)) {
                Date now = new Date();
                Date lastNow =  DateUtil.offsetDay(now,YESTERDAY_OFFSET);
                String lastNowDate = formatDate(lastNow);
                Object oldAmount = touping.get(lastNowDate);
                if (!todayAmount.equals(oldAmount)) {
                    log.warn("[{}] was incorrect old [{}] , reconciliation [{}] ", cyberStar.name() , oldAmount , todayAmount );
                    touping.put(lastNowDate, todayAmount);
                }
            }else{
                log.error( " amount-is-error : {}, {}", cyberStar,todayAmount );
            }
        }catch (Exception e) {
            log.error("reconciliation----value is error ",e);
        }

      }

      // 累计当年的数据内容
      get(x->{
        return true;
      },(obj,x)->  obj.total = x  , true);

      // 累计当月的数据内容。
      String monthDay = DateUtil.format(new Date(), "yyyy-MM");
      String finalMonthDay = monthDay;
      get(x->{
        return StrUtil.startWith(x.getKey(), finalMonthDay) ;
      },(obj,x)->  obj.totalMonth = x  , true );
  }
  /**
   * 推送消息内容
   * @param cyberStar
   */
  public void convertAndSend(CyberStar cyberStar){
    Map<String,Object> pushObj = new HashMap<>();
    pushObj.put("id",cyberStar.ordinal()+1);
    putTotal(pushObj,cyberStar.total);
    simpMessagingTemplate.convertAndSend("/topic/votePing/total", pushObj );
    putTotal(pushObj,cyberStar.totalMonth);
    simpMessagingTemplate.convertAndSend("/topic/votePing/totalMonth", pushObj );
  }
  public void putTotal(Map<String,Object> map,Double total){
    map.put("total", total.intValue() );
  }

  public boolean amountIsOk(String todayAmount){
      double amount = 0;
      try {
        amount =Double.parseDouble( todayAmount);
      }catch (Exception e){

      }
      return amount > 0;
  }

  /**
   * @param cookie
   * @param expression 获取值的表达式。
   * @return
   */
  public String httpTodayAmount(String cookie,String expression){
    // 获得 今日成交金额
    if(StrUtil.isBlank(cookie)) return null;

    JSONObject jsonObject = new JSONObject("{\"timeRange\":\"CUSTOMIZED_DAY\",\"currentStartDay\":\"2024-11-06\",\"currentEndDay\":\"2024-11-06\",\"compareStartDay\":\"2024-11-05\",\"compareEndDay\":\"2024-11-05\",\"saleType\":\"ALL\",\"serviceCode\":\"sytTradeOverview\"}");
    Date now = new Date();
    Date lastNow =  DateUtil.offsetDay(now,YESTERDAY_OFFSET);
    Date lastNow2 =  DateUtil.offsetDay(now,YESTERDAY_OFFSET-1);
    String nowDate = formatDate(now);
    String lastNowDate = formatDate(lastNow);
    String lastNowDate2 = formatDate(lastNow2);
    //
    jsonObject.set("currentStartDay", lastNowDate2);
    jsonObject.set("currentEndDay", lastNowDate2);
    jsonObject.set("compareStartDay", lastNowDate);
    jsonObject.set("compareEndDay", lastNowDate);
    //链式构建请求
    String result = HttpRequest.post("https://syt.kwaixiaodian.com/rest/business/gateway/trade/data/fetch")
            .header("Cookie", cookie)//头信息，多个头信息多次调用此方法即可
            .body(jsonObject.toString())//表单内容
            .timeout(20000)//超时，毫秒
            .execute().body();
    // 获得 今日成交金额
    JSONObject resultJson = new JSONObject(result);
    if(resultJson.getInt("result") != 200){
      throw new RuntimeException(resultJson.getStr("error_msg"));
    }
    String todayAmount = resultJson.getByPath(expression, String.class);
    return todayAmount;
  }
  public JSONObject httpTodayAmount(String cookie){
    if(StrUtil.isBlank(cookie)) return null;

    JSONObject jsonObject = new JSONObject("{\n" +
            "    \"timeRange\": \"CUSTOMIZED_DAY\",\n" +
            "    \"currentStartDay\": \"2023-12-07\",\n" +
            "    \"currentEndDay\": \"2023-12-07\",\n" +
            "    \"compareStartDay\": \"2023-12-06\",\n" +
            "    \"compareEndDay\": \"2023-12-06\",\n" +
            "    \"sellerRole\": 0,\n" +
            "    \"modelType\": \"INDEX\"\n" +
            "}");
    Date now = new Date();
    Date lastNow =  DateUtil.offsetDay(now,YESTERDAY_OFFSET);
    String nowDate = formatDate(now);
    String lastNowDate = formatDate(lastNow);
    //
    jsonObject.set("currentStartDay", nowDate);
    jsonObject.set("currentEndDay", nowDate);
    jsonObject.set("compareStartDay", lastNowDate);
    jsonObject.set("compareEndDay", lastNowDate);
    //链式构建请求
    String result = HttpRequest.post("https://syt.kwaixiaodian.com/rest/business/gateway/index/realtime/overview/v2")
            .header("Cookie", cookie)//头信息，多个头信息多次调用此方法即可
            .body(jsonObject.toString())//表单内容
            .timeout(20000)//超时，毫秒
            .execute().body();
    // 获得 今日成交金额
    JSONObject resultJson = new JSONObject(result);
    if(resultJson.getInt("result") != 200){
      throw new RuntimeException(resultJson.getStr("error_msg"));
    }
    return resultJson;
  }

  /**
   * @return
   */
  public static JSONObject doHttp(CyberStar cyberStar,String url,String body){
    //链式构建请求
    String result = HttpRequest.post(url)
            .header("Cookie", cyberStar.cookie)//头信息，多个头信息多次调用此方法即可
            .body(body)//表单内容
            .timeout(20000)//超时，毫秒
            .execute().body();
    // 获得 今日成交金额
    JSONObject resultJson = new JSONObject(result);
    return resultJson;
  }

  private String formatDate(Date date){
    return DateUtil.format(date, "yyyy-MM-dd");
  }

  public static void main(String[] args) {
    log.error("{}的数据第{}次跳水,原数据{},现数据{},差值{}",1,2,3,4,5);
    for (CyberStar cyberStar : CyberStar.values()) {
      System.out.println(StrUtil.format("('{}','{}','{}','{}',{},1.0,1.0,'1','2025-04-15 21:50:47','','2025-04-15 21:50:47',0,0),"
       ,  cyberStar.nickname , StrUtil.removePrefix(cyberStar.userId,"userId=")  ,
              cyberStar.img,  cyberStar.cookie, cyberStar.dive_amount
              ));
//      System.out.print(cyberStar.name()+" "+ cyberStar.nickname + "\n");
//      System.out.println( StrUtil.format("name:{};id:{};名称:{};",cyberStar.name(),cyberStar.ordinal()+1 , cyberStar.nickname ));
//      System.out.println( StrUtil.format("名称:{} ； 网址:\n https://yunlvltd.com/touping/#/page-detail-n-16?reidsKey=vote:touping:{}&startTime=2024-10-15&endTime=2024-10-18 \n", cyberStar.nickname , cyberStar.name() ));
    }

  }

  public enum CyberStar{
    jiaomei( "娇妹" ,"userId=2131929203","https://www.yunlvltd.com/pk-jiaomei.jpg",
            "did=web_hchi4ir4zzogskj9o0q7v5cltgk99f1x; bUserId=1000015505141; userId=2131929203; kuaishou.ad.analysis_st=ChdrdWFpc2hvdS5hZC5hbmFseXNpcy5zdBKwAU1BF_uHWR2gfDGZYw0oTc-bPyjoXJv7jdr-VdBnJ5075OtA-n8OWe4Q1HIm_cQK3W8q--VdQp_jX_e1SQYX2Tgp1D6QUhL_KMh0wvPcssPClEf4g4PwU2Zc69ZghkQnMFLY5oFeaZDIHUOqSDaJAf7XebTdHdrJ06vw0y8Zq0DkiXHYpCwq7qhUqgTdemjGTJv7vG5YCZ-qZh9YC4zm4CJK0VdGmnyj849oieXvGT6yGhIQN0ywTnpD2S-JIHMxDcdXAQ0iIMq6_BJ-frmWNWxBFCsd_grNmHlIilYMYk-e9W1PwljlKAUwAQ; kuaishou.ad.analysis_ph=8e3197097747bb9d23768797ba0cd50618e0" ),
    jiawen( "嘉文" ,"userId=371800776","https://p2-pro.a.yximgs.com/uhead/AB/2023/10/07/05/BMjAyMzEwMDcwNTQyMTlfMzcxODAwNzc2XzFfaGQxNTZfNDM2_s.jpg",
            "did=web_hchi4ir4zzogskj9o0q7v5cltgk99f1x; bUserId=1000015505412; userId=371800776; kuaishou.shop.b_st=ChJrdWFpc2hvdS5zaG9wLmIuc3QSoAEUO0RUtXJGNaTCeBhG7YuHSlqujPN9qqJ6-DnJpkkE_v6stgIvfCQO2_Waha156acjow64XeYCu2tDDiPoEe69nEqENYCAWyC0xdCIYzmG2d7SWzDhZHHzZ94ulZH0dVVOfnZ6zWWc8_joTc6juMsWpOEyDKuVyMcCXMTOc2le6SIAIVh3cQnJwkWRHdWSEdKRAioLSAAsED9YOmGLMz1DGhJHLt8Pt7EFLsNXpPl1vIIF_g4iIDn-g7axGjeyd3zGU07txNeIsmKt5vZ9EFBoiGnh9bKPKAUwAQ; kuaishou.shop.b_ph=e612ab579b075b457146f98ed42f824de4d1; kuaishou.ad.analysis_st=ChdrdWFpc2hvdS5hZC5hbmFseXNpcy5zdBKwARBucM5aZ8o0CAWAMcfhkT_3wc7ISR10Tla5u-EMVf-2thnWi0TihyhWO8OQOeAJRCoHQo3lISXxRKdNDUgMg719yJrnGY5qDf2ZdhfBEGI0zVc2kV1UOcVAwKRfLe5pHEnU8gAY8PpNMNWIp6rfUUGSrLOr5v0FwhJ9ORkQKzX4KRPwywdwrzstSxu-73Gihp729st3WwSidFmuwCvVXUkaOh2QwH3Uddyvu7JcL5O8GhKn8Lgzxz2mhe-DKrQUXPCtYAMiINmviJqpcPtDhuCqn-EIPJGf8uAlya7wPfpxKsYKheGTKAUwAQ; kuaishou.ad.analysis_ph=0e58a9fa6be9e2dba84f76d362cc92d5184b" ),
    zhihu( "志虎" ,"userId=8427947","https://p5-pro.a.yximgs.com/uhead/AB/2022/04/19/17/BMjAyMjA0MTkxNzU0NTRfODQyNzk0N18xX2hkNjU5XzcwNA==_s.jpg",
            "did=web_hchi4ir4zzogskj9o0q7v5cltgk99f1x; bUserId=1000010986245; userId=8427947; kuaishou.shop.b_st=ChJrdWFpc2hvdS5zaG9wLmIuc3QSoAGmg30Om2Up-lIEGppSGrOw-uxvA7xq7gec5A_SJiUjfSDwpHoTjMrVwWoaZG2C1JukLUr4Dp_RIROVPfMO_9Dbeadxnm5uwL_DS1SoMt01dhZjrDj0TIIIKKOocT8yjL20xvfED6hZT2n2WT38SOknHq6HVxt_UY8MIQdFuJtacUsWx8iup30UgLeW_wRyvk-8z8h69dl-d7rju_Hf3qnFGhJYdbi6PPwH9SoNeq6w4HpOq_wiIDxd_kTVJ2pEEDSqiMfp2MszYI5x5-krb6piq2W5sxnyKAUwAQ; kuaishou.shop.b_ph=a0a67f1a6334d47caece77c163e92616320d; kuaishou.ad.analysis_st=ChdrdWFpc2hvdS5hZC5hbmFseXNpcy5zdBKwARsbqcLkxkLbY_t6ejyHKI5DUUYG3DHmGXv9HA66eUHnMGNhEOqe7BGXjY4Nhx-XMxIZ_21SCHJQAdLCklBH1DNOE1DPHfEzXBmUG_n2t0sWnM-Y_QCWsdB-TaUHGE9p5zUB8SYH9F-IMuGv39T9eIClDg0ADxsumxCl0VWo83K8c3-G-gpaHTWCMka8223fmXEars5FeBOEBO8T8d-D-_43EbS3YAdduDKLrPHDlWlyGhLbSDb7aGsw7p2CxsASTOzcpTEiIPjyk-betZDBCKKXr298CadWNCokfyn8v7IvZeqhn3y8KAUwAQ; kuaishou.ad.analysis_ph=2fad77a386c9d4c06f05d7726aa3118baf97" ),
    tianqi( "天启" ,"userId=331833119","https://p2-pro.a.yximgs.com/uhead/AB/2023/10/23/20/BMjAyMzEwMjMyMDAzMzRfMzMxODMzMTE5XzFfaGQ5NjlfNTYy_s.jpg",
            "did=web_hchi4ir4zzogskj9o0q7v5cltgk99f1x; bUserId=1000004423636; userId=331833119; kuaishou.shop.b_st=ChJrdWFpc2hvdS5zaG9wLmIuc3QSoAEvmOVzpcHBVQw9Vz-LkzUhW1AQC_dbqUVUxJROqv849luFh-lh0Md8nYCionA2lJ2Qli4orMyUOwBCSci6FRAUfIvGXNfT8tLHN2JlzPxc7tya8UHn4p67VSBhBd8snpweLUXjN1-UCm7ua__1KSxiws5ZpMf8w8DByMa6Qnm75kw9bmMqa9q33UpDTP_AW7sbE1UPwTla1I0PWezB85FJGhIlYdGjF9dJg7vA1C954KBAdRAiIPX1gnqUKTmbY71MoxbZID6y5q-VmN_qu4J3Hny8ykSCKAUwAQ; kuaishou.shop.b_ph=72b5422d0e4a849f5fdae7c9c465a635cf76; kuaishou.ad.analysis_st=ChdrdWFpc2hvdS5hZC5hbmFseXNpcy5zdBKwAUdu2Z9Q22os1gofYcYM6vyW56ln0v1y0oJoYMUPM5deFRRzCDlPCq7xU774PJJuDcDQUh9_MZ9EpwosoETEOKEpnToNxbC9of4mMIEAEAw7PI7jpLTPTy8VzUhhZbfyvJtZF4aTe1zC2pXv0xeI8ne-xWL3O9c1rHog205YAq_Ylm412Of5GRpVDdzIEzihsQvCH-WZ1Ww9jf2LUBdnThSxZyvHjlewhJxJBw5C6YvdGhKfnYlfKFPaF-z---LLO1WfrxQiIMsSdbFNxGCPzDDiPf7H43Ogtf_tLfA6IDvlF2O6fcPpKAUwAQ; kuaishou.ad.analysis_ph=a0c6007e5c9afac855dcac4a2c5815c35f08" ),
    mengquan( "懵圈" ,"userId=16194648","https://p2-pro.a.yximgs.com/uhead/AB/2023/10/07/03/BMjAyMzEwMDcwMzAyNTVfMTYxOTQ2NDhfMV9oZDk0Xzk4Mw==_s.jpg"
            ,"did=web_hchi4ir4zzogskj9o0q7v5cltgk99f1x; bUserId=1000002415749; userId=16194648; kuaishou.shop.b_st=ChJrdWFpc2hvdS5zaG9wLmIuc3QSoAEqNXxPdTgolEpQt01xu1dxesE_pbbXCb9gc4u2HadfLLC__K33H_AZ6Kwr96fe7pSH9GOBvkkUM8INgauAcx6tZ6I37OOkxk54llNpHBBT0u3mWwzD5CACvZn79q27mcWTSPum3wHcYikgy8L_aGoO1H5WlQQVK9Pld15uVuqcORiSYmB7FgUnobE14ZfrYajp5pjn_ANwhvbPuYiGc6cTGhLo_Z2VG4WxceKArz4eXu24hPEiILO37DAv4xKxZCFVT1G6Jt9UinmWWVLtRxuhSrtmEed8KAUwAQ; kuaishou.shop.b_ph=82c091ee2e1742c40287fa372589d7a6fda7; kuaishou.ad.analysis_st=ChdrdWFpc2hvdS5hZC5hbmFseXNpcy5zdBKwAd_CwbOdRq8TZ1cbRt-oFv0vGfi6NSMFJoJxxCWPQlofg99AmXo0_QtBALvlpzBaxIqcdUm1XpWMNcuPjU8Cj3SUns8cKm0Yp57wnPGF8FyrXqHJXrmondlVYfOPgvG0YZGtoDousEQz7aFX0wmm-pm12aDcjXupOMJdUlbngJ56hFef3kR0Ne2cgWSeCJ0i2zslgyx0JuawHcT8Xx9t3hBvrDqGFd76jX_CkCsaUHCeGhKfnYlfKFPaF-z---LLO1WfrxQiIHeMUg_TwVhM-IbS9Z_vFe-bvucv14YuatD1PAOJL2MTKAUwAQ; kuaishou.ad.analysis_ph=00b01424c67206241f78c510155001192e46" ),
    yaer( "雅儿" ,"userId=73911771","https://p2-pro.a.yximgs.com/uhead/AB/2022/08/23/21/BMjAyMjA4MjMyMTU3MzBfNzM5MTE3NzFfMV9oZDU3OV85MTY=_s.jpg",
            "did=web_hchi4ir4zzogskj9o0q7v5cltgk99f1x; bUserId=1000011566265; userId=73911771; kuaishou.shop.b_st=ChJrdWFpc2hvdS5zaG9wLmIuc3QSoAEK_vJDK9TD0JRAPqg2hN8nwiNS_FbZBPD1P0RaYk7FpPe8X65OlGuiYXkAJhaQnMHgPM-KQtxzz37SluGfQnkzqhibSMlgDHVWv4H-IkozHtzNtERFKVye_IJKq0mLJYTM0GLyCccOYbx8X8mP-RutCgCLa_5FyIHZwF9yGBFIc7tpNpv49_HC8ojzDL3sdDsHVtIlY5uyiDeAMHPYo5YoGhJpyITJotQHlukZ_tQtSKOqv5QiIGaZ7Tyz7VTYCQyl1lfHzv0NvbjhFCoXNi4XkXqmlhugKAUwAQ; kuaishou.shop.b_ph=f258f58092ad1de9b5df259441f882897bf8; kuaishou.ad.analysis_st=ChdrdWFpc2hvdS5hZC5hbmFseXNpcy5zdBKwAeoAhcXlfwREmuCPV0Ljb7q0bJ0QnrMca6s7Dk6w6WRFlJ7uYExh2DWmuG2k6ApUMrIDqOpGzTE_PGicQN4X0z1vMEmI9EIpvyVUSvl7TVd07ky1wRl8PKwGti3WJg3wfQni_skJYi9lgk6UKHXqgTQuic9hMiWu4BvkPftfgv7wpQ87YBTM80__O7lVRrfC4a8sfokkf7MiGdQ66SzXlU_Ly-W0P5fHl5M5DlREARBYGhLUDbQSvVB4bdfEWaIuXM0UUl4iINJoKGmLXHna55eX83XsBgdOzKAisKwBfP7gGkerRGsFKAUwAQ; kuaishou.ad.analysis_ph=f632ff09ab53b4c917cdca69699b03301ab2" ),
    chenxiao( "陈小" ,"userId=29982331","https://p4-pro.a.yximgs.com/uhead/AB/2023/09/17/02/BMjAyMzA5MTcwMjM0NTdfMjk5ODIzMzFfMV9oZDY3NF8xMDM=_s.jpg",
            "did=web_hchi4ir4zzogskj9o0q7v5cltgk99f1x; bUserId=1000012931692; userId=29982331; kuaishou.shop.b_st=ChJrdWFpc2hvdS5zaG9wLmIuc3QSoAFoD3X1xd-eI_Qs6v0iQC10uJ-TYDa49lozNGnNmXzGpLRSHYAriAEyVBSQJmmWHiyHnMN7IvFwjTuNvGj71Iz95xsM8qmWPrnWas4707z2x681AMUReRJMNBxjv8hmkzroKUuMkIBiKfCOqR80S4d4JXq_v1FwZfMgHdHPpq2eOQ8sV1V6FNMa32Pf2UgIQq6e0PyGBMdf5jtAkJWD7I2dGhJYdbi6PPwH9SoNeq6w4HpOq_wiIGUw1G7Kxy-Wy3r_OlxexNrqf27EPMKtEX53GTMPXkFVKAUwAQ; kuaishou.shop.b_ph=a16ae8e44b74d0b4a0f36ae995a9a9a4c3b4; kuaishou.ad.analysis_st=ChdrdWFpc2hvdS5hZC5hbmFseXNpcy5zdBKgAUSnEk-UcamOeUPZSqwhDMzxy53qneBYOEywGd2tO1IZrBA04M6JlJyhCWTm3t0bYZHgscpc4VcFcEciPonrrNYAPd4W2EJvN52s3evvKASE_8sGjuBnh23K05xkcp3I3EwfxRQuU7Yq54ATMfhAEsMuKstPlSV39iWg3V6tQOt9h61V3PYgsecVGPmvjaKuza9nxmYAu6Jic_0jZKduxcQaEp-diV8oU9oX7P774ss7VZ-vFCIg12gy-5tqmhAR2MDaYbwxc7zXBGIR_UnOC-rdVLfSTf4oBTAB; kuaishou.ad.analysis_ph=c49fe112bdaa2dbcd6d89a46c1816870b9b5" ),
//    linmu( "林木" ,"userId=12815087","https://p2-pro.a.yximgs.com/uhead/AB/2023/10/06/08/BMjAyMzEwMDYwODUwMzlfMTI4MTUwODdfMV9oZDgwNV8xNDc=_s.jpg",
//            "" ),
    xueer( "雪儿" ,"userId=11569700","https://v1.ecukwai.com/uhead/AB/2023/10/30/14/BMjAyMzEwMzAxNDUwMTFfMTE1Njk3MDBfMV9oZDE2OF8zNzI=_s.jpg",
            "did=web_hchi4ir4zzogskj9o0q7v5cltgk99f1x; bUserId=1000011505740; userId=11569700; kuaishou.shop.b_st=ChJrdWFpc2hvdS5zaG9wLmIuc3QSoAFnXf5yGn1D5vDQtrU_vNHRfh2aI20fzS1yCqDUXEekSaWVoGpBCMtPD8dMZBvLZ_VjiN2G7xZzsSKLnD5PsxG4pdHbJ0W9IwTMrcSe-hMt8Qj15T9qMhDoucoomG1QHafIiQeJsxjXCV_wDD2hkrLZx0OzFzlIMV-qP1Vr-dME_dp8_P07vwWWea90neTJe0TDaw5kVm1vaXhN4BqK45kZGhKWjgR0Z5KY20AASC-Mzxd_Ru4iIGuGgN5uWHXiDzIK50kCcHcijmAhPfjZS_5V98sJIGBDKAUwAQ; kuaishou.shop.b_ph=72991c25388447aecb5d8f4d103a0bce5821; kuaishou.ad.analysis_st=ChdrdWFpc2hvdS5hZC5hbmFseXNpcy5zdBKgAex7yVoMnOvNzkRqMfCSx2K_9vcVsNdYaMfWIzQCtwsdNwtBQB5nOIODRn1bvZ5ScxEWqj9mCfUJ2_wdM3TFoXvGswmzyGXzGWHFK0XNcZn8t7kEuwY5-SZam6KHaeDnDW4l7IY-p90xYp9_b99aAeD4FiYUpZ2AhW3zhMcfNJXFeaNbZ2O2OVucLY0F37NAXq6cywVM-7cbNQ_RE6yktr0aEmNaaUr6fDBnQqyQ1M9-jzooICIgUbUHhdxAqWGTYnM4d7X0ngWWrywKXWaId2ZFihFC45QoBTAB; kuaishou.ad.analysis_ph=ceea2083ded2fc6ac1772c6dc76efb149796" ),
    yiyue( "依玥" ,"userId=228816049","https://v1.ecukwai.com/uhead/AB/2024/02/03/19/BMjAyNDAyMDMxOTA5MzBfMjI4ODE2MDQ5XzFfaGQ1MTVfMg==_s.jpg",
            "did=web_hchi4ir4zzogskj9o0q7v5cltgk99f1x; bUserId=1000012466271; userId=228816049; kuaishou.shop.b_st=ChJrdWFpc2hvdS5zaG9wLmIuc3QSoAHW3w1XiXbZDgSvXTdFEESuaGJUSoIEdbP2DKYulpxPDfnuip0fnQMA63K1MzjEMPLThOX3QlLe8HqN8HHjsWj63u6XyvtoczGByM_WEplQucnSr_GPeyGDib87CGpGVG-Cv8_PevIaq0LNw_ImUnEggEjmkHmIvVBpvl4Ct5sV3HbkYWU0h4GerZ15bifLM1_eAtwYkwp1x2cjHau11kTdGhKbz3TbRj8_GqdtJns0FBl2yz4iII9Hi-ztLahW1vMShUBssg6c5byFfW4owylGSHKQ3rMJKAUwAQ; kuaishou.shop.b_ph=4fa9ba096d3deb1b860901a9911b7d99176a; kuaishou.ad.analysis_st=ChdrdWFpc2hvdS5hZC5hbmFseXNpcy5zdBKwAf9zAy7BFZbaDOcNPxsp7ru2W3U3WlUgDrUUAh3Sgh6syW1abGALQS8Hl568_0rHRCXiYLWJlxjrH8LzWClsWM5OCxk3iIA0TiWfJefLFOo5Eb92TC8SUtZ759sYQz9zUoZ_q6UkyT0A_H5-OQSPsZz9ecLf4JV8ev1Fi3v3LMoI5xGRCg6R-mftcwNEu8dh-jw0qrpmigOKUcNeRr3nh7bo22wK9fnhyXk4NNuOCwQkGhJjWmlK-nwwZ0KskNTPfo86KCAiICh2sMIHJahxxFUFdvdUjibUAn9zgoCtFyaQ-Pbn2jMVKAUwAQ; kuaishou.ad.analysis_ph=f480e78d9bc8a5116d3d9098cc0f2d4635c3" ),
    hongmei( "红妹" ,"userId=511012459","https://v2.ecukwai.com/uhead/AB/2020/05/13/00/BMjAyMDA1MTMwMDA1MjdfNTExMDEyNDU5XzFfaGQ1ODRfMTg3_s.jpg",
            "did=web_hchi4ir4zzogskj9o0q7v5cltgk99f1x; bUserId=1000011283039; userId=511012459; kuaishou.shop.b_st=ChJrdWFpc2hvdS5zaG9wLmIuc3QSoAFN6sSm1vDsKBx4X-OrM_Xq_kJIMb3nt5X7oyLBh38UwNozQJl6WDtW1hzs2Mmk6oaJJHRuz7CDyrJhr_ITHXyoeB46GZ4EcfQ1Jo6hfkzL9E2qxlEiKEfAXxQfTRxJpkQNTqJheyQC0u6p5R3HMdDX6tBM3iQjSor5eYR4b4j5HPaHNbJYvOA6gDmsZOVd5GeFxGDTnyJfgEKuVh_KB4z_GhJxLmZe4hrNJlIURRIhhs2U8_MiIJH38JBk2xSEfM8aFpIgiAYeM8qUOPk-Z5sMntTfDGSOKAUwAQ; kuaishou.shop.b_ph=9be2c00f3aa9192691af7d47e7b920e3a747; kuaishou.ad.analysis_st=ChdrdWFpc2hvdS5hZC5hbmFseXNpcy5zdBKwATGnKdILAuW87Kzr4yb6XQaJOxJCfa1kIDSO2JO0uPl14sEaESdPWhCMwcpfI_rq05jlo4ZKr9JmDv0bMVVXgRyGkYTuIURueCCsav5H8j36zXrXnWlGZtHD9Xj4nIEWrdbS7v3mEeZGC-84DRQZjeYmdTk6p6C-IDe5rckmVXf7zRoHQ_KNmRd6n8v3YiTF3kbM80HuL20hAekGf30Hn62qvymdCQxC_x64zY1Cqj63GhLbwz68ZkiTB3sY69L_Qrle9OgiIEafBqab3jqMqZSOP_pXO-4xB-2ClRNjWtFhyE4VhHjpKAUwAQ; kuaishou.ad.analysis_ph=4efeefea9d9375f59451f011792ddd7b8d1b" ),
    // xiaoyiyi( "小伊伊" ,"userId=103432682","https://v2.ecukwai.com/uhead/AB/2023/09/17/23/BMjAyMzA5MTcyMzA5MjJfMTAzNDMyNjgyXzFfaGQzNThfMTM1_s.jpg",
    //        "did=web_bdd68fdad0c089286240973ff7114b037984; userId=103432682; kuaishou.ad.analysis_st=ChdrdWFpc2hvdS5hZC5hbmFseXNpcy5zdBKwAcEw5x1gDYF4OQg7Vt7GO_RI--xUziDrJzHdUNT0PXBXWpQ5zEOMUIexfmvO3F5b-ZxFzV_iwUgeMk0ryeiRmoDEYb3WpuCobZbU4vY1TXxVpSG7sxlLHrOp-JQ3QWeQuPPmuMwsXNrp198y3cSnVS9uTRKCpFjX441-Xk2_npHFDqpHCIljzpNxHA5P7f4E9ZgmRk9RGp05rkQDKTlkYeXXXt000wQqOivEnz_YyMEoGhKn8Lgzxz2mhe-DKrQUXPCtYAMiIOIDCOpth84PAwA0oeFzu40w9y1ZycqV99TVmwDUBpe5KAUwAQ; kuaishou.ad.analysis_ph=7d3175340815079c7fa959b2f26d4dfd8a79" ),
//    yumeizhi( "美之" ,"userId=2261899","https://v2.ecukwai.com/uhead/AB/2024/03/03/21/BMjAyNDAzMDMyMTI2MjlfMjI2MTg5OV8xX2hkMTQ5XzEyMg==_s.jpg",
//            "did=web_hchi4ir4zzogskj9o0q7v5cltgk99f1x; bUserId=1000006982035; userId=2261899; kuaishou.shop.b_st=ChJrdWFpc2hvdS5zaG9wLmIuc3QSoAHaNzMTKFdRSjdmZIs7PfasixgaD5Sx80a3pEwup4gbjOI2VsOestp9qDS-ax1wuyS0bj82hwIVAZju9fVtCg8Ed3h2J5ufYA64_20oAicxkftsj5Pnru8_pMWVuY7LuVK-azepd0hJBv3zyUvXZA56UbdaywfTldVr42nOu3cQfAJtuIJTqCnncFlT0TvybuP1wr7F6d647PXsJBLFjcm9GhJx70cBBaGVtEpnSlNxog9ZoqoiIIXL0Ry_9wQJ0HdmMRrp0Rs2acVDaxlKXREEuJfo9M-JKAUwAQ; kuaishou.shop.b_ph=5483e080f402c53fa37eec4f5daacd23e79e; kuaishou.ad.analysis_st=ChdrdWFpc2hvdS5hZC5hbmFseXNpcy5zdBKwAfDeDcRlqaImV4fYPIvfvkq8so8OlxOhxhBOIsm0qi5q1atqka8s50PojL_19aPq9Q_UTxCQpi1RqSOnpLroums9inntIOJfi-pAB9WRQOmjf8EHZEj7hzia-aXciTmofVGq9Kw0cnSfbnp5nfhloWJnA2bnBF_jRaDkvkt8DHfvUz1db9VoQhtU2UExaW64jQuLN_pd32iPXRyccQSdUlsL0ZaUftxAlm68KrGiSCspGhLJQvUwrfHcDOIi8qefbDJk8MYiICe3Lo32BySfrUDZzjbg_anupZkZTO561MpgTJkzTwKwKAUwAQ; kuaishou.ad.analysis_ph=5df7a1045860151aa38681e72a3ac6f2f657" ),
    feibao( "菲宝" ,"userId=50913379","https://v2.ecukwai.com/uhead/AB/2023/07/18/19/BMjAyMzA3MTgxOTI1MTdfNTA5MTMzNzlfMV9oZDUzOV8xMDA=_s.jpg",
            "did=web_hchi4ir4zzogskj9o0q7v5cltgk99f1x; bUserId=1000006868736; userId=50913379; kuaishou.shop.b_st=ChJrdWFpc2hvdS5zaG9wLmIuc3QSoAFE1h3lt8iZZlUr0_HCB55WVXxRBCnPZPlcMfYnj5lQyaqeHDIIinhu4_PP-MmnPvFmH_yCzTo8Yne9AeEmfqhRAFZnJG8b29mj6yaWQ__Mbzaiseg_rTf1etnUX-D-gb04wf18N9YrWE2VKiP6F6YDbdi8D8OVgoeTHYx6pjrCzU0jWKT8JohvNdlyeAcXQiK3YbZIuW9-XENP5ME4qCPwGhKd_ML3fWEjTvpP6f9oisfIpAIiIPLGI1QISJZE63CE8KSImMsSq1Kn4-u16Hh7j4fdlcLMKAUwAQ; kuaishou.shop.b_ph=d915ea642ee5fbf7f49338fa21d2463ef6c6; kuaishou.ad.analysis_st=ChdrdWFpc2hvdS5hZC5hbmFseXNpcy5zdBKwAQt8vk7ch2P2wGpxmYrGpFgVQARHYWdaZXKY_wiHi4lcRk60GAlEUyLmk66ZOrZgsuk-Td50TK5VElXVdnmByJAjO8fQiWkwC9Zk57AsqIl59xG526dMfrEOodjnwR-hOclkK9rvTKOoGeUTunYNwpuhadKqI3fCVzOS6FArmyVag6avnbHECENUREtfUR-eYdiZ1wEtngNcCvbdaTN5Z_7ni6uPvxMZWgtlH20A8jt8GhKeNFIlcbXS9CWSbj9ULtqm_ikiICww6ElRNwtYSAYUGvUXYxdShUkxkA974PjrtLY5FyX1KAUwAQ; kuaishou.ad.analysis_ph=9c4a6880f06e46aa6a43ac171e59a8c49511" ),
    wenrouxi( "温柔曦" ,"userId=1256295378","https://v1.ecukwai.com/uhead/AB/2023/06/25/16/BMjAyMzA2MjUxNjE4NTdfMTI1NjI5NTM3OF8xX2hkMzk2XzU4NQ==_s.jpg",
            "did=web_hchi4ir4zzogskj9o0q7v5cltgk99f1x; bUserId=1000014891048; userId=1256295378; kuaishou.shop.b_st=ChJrdWFpc2hvdS5zaG9wLmIuc3QSoAGI-3VAujQ9QoWEOkCph3VPp4RgCXcRNvZ-iS2suR9nEL9hLstJ7FjNx-OTfimMAGmdjUuoa-da2MVgTRNH9umJvUy80mZWZ0y0KTNQ5dJYj6Chk_zmMGfUAl3C1wJbdIpL6ABARNtKw2hU0hIX4cCVrd42Gaz-1x4SU-l12NTiUBeO4LifRRxaZA17pFvnByl6lH4Dj0ykh2h2vMZh0bqaGhJpyITJotQHlukZ_tQtSKOqv5QiIF53edwz9MR8SIUOWu9Hvjefk6L2WeTyA_RyJNLD8x6BKAUwAQ; kuaishou.shop.b_ph=13fc41190a906cb2259d2ee9612408903b21; kuaishou.ad.analysis_st=ChdrdWFpc2hvdS5hZC5hbmFseXNpcy5zdBKwAY4-ptT0k_ZQPjn-0SSlFH5spQZ8Hmp7YwjgLparZm6qYRRhC0OrSJXJWz1NbqfB904OfrNg8FqnUolczicq83JMBIggKaCafUYfg61SlAdMbfY2u49u9zMbVpvBm9ndTaVAgGDJuA5JeTfJGlVJeu4JjccNxKXbIfITPoY6LqAX_GwUifbjTcN3n5fZyuKuSXcy7khSTQbE4VHziiQ1Wq5wxxeacOPnns2E8jpQm4gJGhIQN0ywTnpD2S-JIHMxDcdXAQ0iIL9hK2Q_AZGPXjEu_hQ-b2s2xPXfyfPo1WDkFBl4xtxjKAUwAQ; kuaishou.ad.analysis_ph=39277bbf721716fe144b1b7f63a10fba0e44" ),
    qimiaobing( "冰冰" ,"userId=47532992","https://v1.ecukwai.com/uhead/AB/2021/12/06/08/BMjAyMTEyMDYwODE2MTNfNDc1MzI5OTJfMV9oZDcwMl82NTQ=_s.jpg",
            "did=web_hchi4ir4zzogskj9o0q7v5cltgk99f1x; bUserId=1000015113075; userId=47532992; kuaishou.shop.b_st=ChJrdWFpc2hvdS5zaG9wLmIuc3QSoAEc2KNVVEVMPqBmplmQSADIRH6-_WX_hHjeNp2LXNEM_MAvPEnTfsaI_CqW-1n_s9Bpp9RQmxSQPcgwX4dd96c5EAF-SH-in7oYc1PAOGuemmhX5AievvzZIiKR6KuffvBhIwfFVl2uE5cByXDpgBZt20luHfLGfWBHGthCg3fDUXedX_vD5xC7KYq7PjQ-ptze-EulY0-09uHxKULlWhgfGhJpyITJotQHlukZ_tQtSKOqv5QiIPvYH0VB-m-Un6h1Fl2o1Pm1dXQUEtrbxWGir760jwaYKAUwAQ; kuaishou.shop.b_ph=ba3701b6ac7e03a646ba0ba2f95b2e419fc4; kuaishou.ad.analysis_st=ChdrdWFpc2hvdS5hZC5hbmFseXNpcy5zdBKwARUaU9-2TY6Tbn-sKKVYheII0_Ny3gGvw9TCexwiE0aSrvWR98uwqMdlKCXj1cGAdt052gSw6j9Ti7xmcKT-vEGLkkffKUVxr5IH_7DOcCnHMZ078RTEzuI2xkcNYX7VVy1oY3ke2KOdqxzGPQ78YDYk6maKxsqccy-tlzJYYOEsxe4nFn9W0bhwoif8bfoHzjEbKKaPfd0WpJ-sf0V_MQtdy6APqkO8Ree8B4fOXj7SGhJjWmlK-nwwZ0KskNTPfo86KCAiIBxqiM8nNhdSvnnP_5Nb7wHHsE-fyXwQfgdziso0Sh6aKAUwAQ; kuaishou.ad.analysis_ph=806507b4de20c4bc580852d9c846bf3a18af" ),
    nana( "娜娜" ,"userId=2454678977","https://v2.ecukwai.com/uhead/AB/2024/01/17/14/BMjAyNDAxMTcxNDM0MjRfMjQ1NDY3ODk3N18xX2hkNjg4XzY3MQ==_s.jpg",
            "did=web_hchi4ir4zzogskj9o0q7v5cltgk99f1x; bUserId=1000012465986; userId=2454678977; kuaishou.shop.b_st=ChJrdWFpc2hvdS5zaG9wLmIuc3QSoAEamDUDi85FPQowz4rDa7BsoQnRTfEqbdUwMHSW_mOy7o2tgi_V2KCwhlckMKfT6Zzd0wloievn_vM1FP0l9EbKm9dKn0qwyTPi1WOHsxHtqUP2-pKHS1u2YgZdtSdC5JNaoPh5lft7tXMlGGT6ta_5UaeF8qfDc1cw8M2CoPIKXFl6LCPcUWvAQvap6wARP6qbujSNLPdY4M4kQkvVEOMnGhIDc-qPTdpdt2c1Eu3bbve-xL4iIF4uLakKwhcHMg2rNaXGBo3YLBvh-aFGmdF6oJOZayF8KAUwAQ; kuaishou.shop.b_ph=1bcc78388f2c8d87c0dd4fd3900f0c0ffab2; kuaishou.ad.analysis_st=ChdrdWFpc2hvdS5hZC5hbmFseXNpcy5zdBKwATFIsLHvTBR7A8OV3GuDMpZRJNRZavHGwxYEQ3o2qlVyDLjxFriyKBVx8UGecKV56APWzqyR2ToBLd7ZgQWpru_opgllLrlNNCU6WI7S_31dMPgCtENj97Txu4JHVboNdlDWvEXq58iYgeAqhGbOw87dREuUNgsPwifEnoSteNWQ6CW290NYyAPEF9Q9f88H66pPah7KIpgDF4q0k0isZdTbc0-5GeaZHg8CBrBR2pLlGhIm80DHvgIy6IQ3MfyK_o6Yi38iIA0gNj0nkltPNEhW09eOpFya7nZIDr8SF3CVNuzzDmzTKAUwAQ; kuaishou.ad.analysis_ph=42b17c3e3a9692ee066207aecc7076069d60" ),
    mengbao( "萌宝" ,"userId=5913072","https://v1.ecukwai.com/uhead/AB/2024/03/06/02/BMjAyNDAzMDYwMjAxMTBfNTkxMzA3Ml8xX2hkMzY3XzQ1_s.jpg",
            "did=web_hchi4ir4zzogskj9o0q7v5cltgk99f1x; bUserId=1000003531885; userId=5913072; kuaishou.shop.b_st=ChJrdWFpc2hvdS5zaG9wLmIuc3QSoAFnj6g7npPUpfTt4XwVLiGaGm8sMh2v0jVxtTGCSJbaqcJNes2PtZUr_OUmb_E5Bz5edw6FbaYiXt7FuiAid1fU3c3gVsKDwzuiRcAtyfhj3J-jU4-yHiMLe4SulQ9hNBFMqJbYlJ-vq8aD4HgISHM1p7vSGiHEPGoa3Zzqu8gISUCz-L8aKg-6drMsCSgNR-3fIMiGnvTrHeIxVZ1KaCY-GhJxLmZe4hrNJlIURRIhhs2U8_MiIPv2mWd4qSVg8Nu457p_Gd35D6wEf1vMZBaSwfPbxj1-KAUwAQ; kuaishou.shop.b_ph=3725a319bb5da98aaafad62d8f3e69d50c01; kuaishou.ad.analysis_st=ChdrdWFpc2hvdS5hZC5hbmFseXNpcy5zdBKwAQlPmXfCMbNHVQ6e9AAfUkkTD3MtRvgv15coFuFqN5gkBAkem2fv4qNRMca3-lRHhpVgvpHoiXSMs846UpB3KKFTn-5iX5UZIK4DrZutDvMtyZfX9YeHs6j3ziJloKo9W2ZPaRLGwVxIbTTedVSnYYLp5V7Hw1cmPUxVL_WButHx5aM9G1soqruibMthoF6Rg70P-I_g77n5kT21XHpyIH0o-cePNn--QScCEJcQ_e7QGhLUDbQSvVB4bdfEWaIuXM0UUl4iIGCNeVfM1FnRLWTcwUtv4GqPnxSGXhpsSc-cNxv_NtROKAUwAQ; kuaishou.ad.analysis_ph=401eff817a831488be025574c304c14aa828" ),
    taiyan( "太阳" ,"userId=502669779","https://v1.ecukwai.com/uhead/AB/2024/03/08/01/BMjAyNDAzMDgwMTE0MTVfNTAyNjY5Nzc5XzFfaGQ3OTlfMzA2_s.jpg?x-oss-process=image/resize,w_88,h_88,m=lfit,limit=1/quality,Q_100/format,png",
            "did=web_hchi4ir4zzogskj9o0q7v5cltgk99f1x; bUserId=1000006786643; userId=502669779; kuaishou.shop.b_st=ChJrdWFpc2hvdS5zaG9wLmIuc3QSoAGsCBgnlG8a3S8yZ4edgrve_DvBqpRwTubnknJ-9amLSj7MiUQ5vnfY-uq_gVMTyDSs4tqHO1NiajTy11itFqdK2Ii1HSk1Ssek7L70g9DKdE8goV2_Eg-t-ZfKgOW8CFmJLk5HkQ7PP35zdlmba-g7oK2NNJZfqwyQDYxJp8o7AtWPSLzEjWqxkYfX4FLX0-1ad5PRF_jww-RkZZ1TmIZrGhItLX8o8yblZLeUpJbxgb-W1KYiIOJkHwo9ROoTYOHsHp-Ly8t8YOhkt9uMpn7-l9SnQKLNKAUwAQ; kuaishou.shop.b_ph=cfb1780ad7a1c54b61ba3a0c8b380e2a6d15; kuaishou.ad.analysis_st=ChdrdWFpc2hvdS5hZC5hbmFseXNpcy5zdBKwASKyoZLNDIUHkm0LpMOe12s8kT9pYt9Gds5EXcZPgKI8DHONQCyFjZvW6u4RM57hwFK107hoCaXJUbeUUm6zZ-WGrt8Pq0qj2jL9yVkuwGq8NPesCt_Mvj4qM3NDh3xMG5iJzN35rJVnu1eIZd3UDQymN2MjDGF7sH11Gtkw1tywpaM4bvT_bUGXs0YV8jsob9Pa9fjy6bSpZV5okMT9uV1IYwMH3oZVJpvhfZRgCRjnGhLUDbQSvVB4bdfEWaIuXM0UUl4iIBzJdKEbY60j0p9TynOMYSvgfD1T7ZbqgD9H3khvDxHPKAUwAQ; kuaishou.ad.analysis_ph=47d715763fefb51937625703308bec8dddac" ),
      xuexue( "雪雪" ,"userId=8940989","https://v1.ecukwai.com/uhead/AB/2024/05/05/22/BMjAyNDA1MDUyMjUzMjJfODk0MDk4OV8xX2hkNjAxXzQzMw==_s.jpg",
              "did=web_hchi4ir4zzogskj9o0q7v5cltgk99f1x; bUserId=1000014891541; userId=8940989; kuaishou.shop.b_st=ChJrdWFpc2hvdS5zaG9wLmIuc3QSoAH0_VKnqCFT9mWkJf3NYF0IK3EA54np33UvXmAbj2ifCb-Rhpo1xhh3PXEjTetRi6CUBrzq0zHPy7nVrig1qKTkP7yTG4f5RZD5YXqOX4Mp888NURK7LqZeIk8wf4sVYDEERIQH6010Pmt2-YBAHEeklWa8WVMryDoNU_vDfffxJ-SI45tXqQ-RgZZjQ0c6QSe_WpzG7KZPQ8CyJL9pLbFuGhJbNLK1AM9bncnjnr5P1Ut3hAIiIDzUB3LHKGRsfWl4hs-ax2trOM1F9PI1X3jH9BCjHLjuKAUwAQ; kuaishou.shop.b_ph=aa8e61328b5f9b907302905d6b56772553c7; kuaishou.ad.analysis_st=ChdrdWFpc2hvdS5hZC5hbmFseXNpcy5zdBKwARmZ3Z210QwkR4CpnjGzBIkyiRJtt2442swtkvHP8X_jHRDqNs1zZbw5czBmr33NKrCF_DjB5cZ0DmRpdGJSdYIG-U7B5gEgUk14ZGKYFdh2LOU1iqMYDpnKS3EIIbzVVUm0KZL1JXw_RPqkJkEFfztI0v3qKGcsQxZrxta0CZwjjiRbGIvlpK2BCkG7p5K92v6wR6uTwoUO7z_0ygt8NpQsIEzfp_Ad7uTTV4IDJLHCGhLUDbQSvVB4bdfEWaIuXM0UUl4iIM2zxOMQC3apvKbhwXN5BqcNNqe__86DJe8H9llk81zjKAUwAQ; kuaishou.ad.analysis_ph=f40c7193c80b3eeda8dc2e5ea98a27dbb0b2" ),
      ximeier( "兮妹" ,"userId=*********","https://v1.ecukwai.com/uhead/AB/2024/07/10/16/BMjAyNDA3MTAxNjA1NTFfMjI3ODExMDcyXzFfaGQxNjVfMjk5_s.jpg?x-oss-process=image/resize,w_88,h_88,m=lfit,limit=1/quality,Q_100/format,png",
              "did=web_hchi4ir4zzogskj9o0q7v5cltgk99f1x; bUserId=1000011164851; userId=*********; kuaishou.shop.b_st=ChJrdWFpc2hvdS5zaG9wLmIuc3QSoAFkSwvcRrzbZp8tlMRX2NSbESz6ylvPtdpDZk3QGESW0yp7cp4Y8-UiggzZCHb1HmTrriH77UFOvDz5iAKMmTY_3dHrLjFpyT645LAdOtympBZ2iA5eEBnywJoFqjMuuZ84UNP6iwr340_qtD5X_62dDh7BPUMrZf2rggM8QtyNlfwHyb4vWQRo7FDV-HAGrRSo1WeVVZfv0nkv_SUL5w41GhKmF-W_lNyJTm2KnbtfN4jmymsiIMKHfFRB0WSONA3E6Kw10FgGldKAjQmj74zSFWzwQNPdKAUwAQ; kuaishou.shop.b_ph=0064a1f5367ef1b0cf829a340d70b5be3562; kuaishou.ad.analysis_st=ChdrdWFpc2hvdS5hZC5hbmFseXNpcy5zdBKwAUdpWVOlLon0VYrPQZInkEwdFtDmhVG8IDSnOm-EZD_Ue4WLgAHFmoHt3yszRjS_R6VH9USQqK6fk6cfE7yCT-LNU7KxVLtUZ45IafNkEDQyFGoBr_hDX5y1tauvbpUE8lEQRTbj6ZT-IfxdieDc0IWNAKhY_BQY76A_QipqHlulddW63JYHvRvD3lqUWU5wysnfKH6Rm_PwIwvEfDpdoJLlhXdAzQisviwvBZvKKHi1GhIszidpTUyKE-jZUURCKcG_ackiIGao9eMkr4MMxpOGpx8wf_HcLCVpLGetWYRWFDqV46-FKAUwAQ; kuaishou.ad.analysis_ph=d0b0baeab233c83262de3f4454592b4639c9" ),
      waner( "婉儿" ,"userId=12222334","https://v1.ecukwai.com/uhead/AB/2024/03/14/02/BMjAyNDAzMTQwMjI3MjdfMTIyMjIzMzRfMV9oZDgyOV83NDI=_s.jpg",
            "did=web_hchi4ir4zzogskj9o0q7v5cltgk99f1x; bUserId=1000014729242; userId=12222334; kuaishou.shop.b_st=ChJrdWFpc2hvdS5zaG9wLmIuc3QSoAGlkZg_CbcbJPlX-TUYwEq5KtC3pBmaIHeQuG0lxIxdkV-70se5pRfixCFc9LjMt0W9T2N3-Lfy4ryXyvSKl_Igb1EEvDr75oN_Kv6or1KuhVKJ5PerfXx-62QR6CQrqBc7czJGg4icKtZNK1lK63dExR1iFjZ69CYOfJOebWXY1K66sbc9c19o2ewA37paO26U1edVEPsqQ0dSOmU5KYU_GhLSubyi-uEBAthqCoUoWxNxjR0iIJ4UVlLf2G2_STyon2bEIZD4Ij5A0sX9n6VX6B7g8Pe2KAUwAQ; kuaishou.shop.b_ph=b2dbe7d03836b57db1b348733c1e494af8c7; kuaishou.ad.analysis_st=ChdrdWFpc2hvdS5hZC5hbmFseXNpcy5zdBKwAdXS1rHRt1MkBGyqxkYuHWexllYzE2dAlAGnXwsXTvawdg-LFD8hldfmRpy2XgIu5PsKdil_hiZqAleh8EQwl_PO6YODPZaf5ko45On_zc6oE_X_6cOHZhPrmIcqgCFixYDrnatweR0IvEW-G_2E-AYP9sE8EAb77fDuYSJyKndpPnYLdVP2j_8uZgJkFyMFERH1CqJqYLXCW5BiSu4VkxiG-cSLn1VMFyzOeKOzVhliGhLBQhRfbrn6PLR9rKF_P-8CoiUiIFBU9dBSej611VZ1OdHo9Nx7PSLI_dc6tsDRYgU1bY6vKAUwAQ; kuaishou.ad.analysis_ph=d39a28db33c71aefc439a6260455cc4e1dc9" ),
      baibai( "摆摆" ,"userId=20229201","https://v1.ecukwai.com/uhead/AB/2024/09/04/04/BMjAyNDA5MDQwNDExNTZfMjAyMjkyMDFfMV9oZDU4MF8xMTM=_s.jpg?x-oss-process=image/resize,w_88,h_88,m=lfit,limit=1/quality,Q_100/format,png",
            "did=web_hchi4ir4zzogskj9o0q7v5cltgk99f1x; bUserId=1000036306896; userId=20229201; kuaishou.shop.b_st=ChJrdWFpc2hvdS5zaG9wLmIuc3QSoAFiCiHKytSlJyKv3KZJaQT_eWjitgw38WzcJ7onMTgJFbrKsGKxoJMQzZc3bkKfYTRO5L0KFUzuRPJidysfbpXw6183VwATSNfqWprUROFm8unnID1aPnNEHjk6Sj5hnavZngCKD38Z2CU4Br8iz_ynDEuB6zOTPJBhCThLgCajh_0Al4-B4hqm2vRsb5NPyZM1E1tX2MpaGTDSUPU_-vuwGhIJ-jd8Zkse4A6ziMrVM2OpCxciIBB6m_4EWRdEIcW-aw20DutN9KLE9gIg2Ool6yJk8K-6KAUwAQ; kuaishou.shop.b_ph=37e3ed32144affe8a298728da7daf962c051; kuaishou.ad.analysis_st=ChdrdWFpc2hvdS5hZC5hbmFseXNpcy5zdBKwAfL6Ch42HqaLbrc0JJEqJTpQ2R_0g4x1GmNvD7xLBRd4-eOmXfIQDwe6ga2bwV229PI3T-lqpEU9mxiluXttbkvFwfg0fy4zhqn2O3Whsq_LnQ3G_WjKGeElCMEBLDMEwu4WtIgEoL3T3PSCBZcRSjGdvfSztE4reWYQRArBC74KV47irPjzlp_NE6heKaWhOiiEuytlzvKyef248lLG-5IMB2wJEkvgU0S3o4urDhkTGhIszidpTUyKE-jZUURCKcG_ackiIMZoeZ42AaeygSmdiPI6He0VKm5y6FZ0L1OU1GBiOQwsKAUwAQ; kuaishou.ad.analysis_ph=11c82c6e0e1a558806aec53a17aba06f1bc3" )
    ;
    //
    public final String nickname;
    public final String userId;
    public final String img;
    public final String cookie;
    private String today = DateUtil.today();
    private Double todayAmountDouble = 0d; // 当天的金额
    private Double total = 0d; // 总额。
    private Double totalMonth = 0d; // 当月总额
    private int dive_count = 0;
    private final int dive_amount;
    CyberStar(String nickname,String userId, String img, String cookie) {
      this(nickname,userId,img,cookie,10 * 10000);
    }
    CyberStar(String nickname,String userId, String img, String cookie,int dive_amount) {
      this.nickname = nickname;
      this.userId = userId;
      this.img = img;
      this.cookie = cookie;
      this.dive_amount = dive_amount;
    }

    /**
     * 通过名称查找对应的内容。
     * @param nickname
     * @return
     */
    public static CyberStar searchByName(String nickname){
      for (CyberStar cyberStar : CyberStar.values()) {
        if (cyberStar.nickname.equalsIgnoreCase(nickname)) {
          return cyberStar;
        }
      }
      return null;
    }
    public String getReidsKey(){
       return "vote:touping:"+ this.name();
    }
    public String getOtherReidsKey(){
      return "vote:touping:other-"+ this.name();
    }

    public String getCustomizedReidsKey(String customized){
      return "vote:touping:"+ customized + "-" + this.name();
    }
  }
}
