package cn.iocoder.yudao.module.vote.service.jnd28.counter;

import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONObject;
import cn.iocoder.yudao.framework.common.util.collection.ListCountUtils;

import java.util.*;

public class Jnd28BetCounterMemoryImpl implements Jnd28BetCounter {
    private static final Map<String,Jnd28BetCounterMemoryImpl> map = new HashMap<>();

    int betAmountCount = 0;
    int totalBetAmount = 0;
    int chaseLongCount = 0; // 投注次数
    int chaseHuCount = 0;
    double profitSituation = 0;
    List<Integer> betList = new ArrayList<>();

    private Jnd28BetCounterMemoryImpl(){}

    public static Jnd28BetCounter getInstance(String key){
        return map.computeIfAbsent(key , k -> new Jnd28BetCounterMemoryImpl());
    }


    @Override
    public int getBetAmountCount() {
        return betAmountCount;
    }

    @Override
    public double getProfitSituation() {
        return profitSituation;
    }

    @Override
    public void awardGiving() {
        int curAmount = getCurAmount();
        profitSituation +=  (curAmount * 2 - curAmount);
    }

    @Override
    public JSONObject betSituation() {
        Integer max = ListCountUtils.max(betList);
        int sum = betList.stream().mapToInt(x -> x).sum();
        JSONObject jsonObject = new JSONObject();
        jsonObject.set("max",max);
        jsonObject.set("sum",sum);
        jsonObject.set("betList",betList);
        return jsonObject ;
    }

    @Override
    public int betByCurAmount() {
        int curAmount = getCurAmount();
        profitSituation -= curAmount;
        totalBetAmount += curAmount;
        return curAmount;
    }

    private int getCurAmount() {
        return 1 * (int) Math.pow(2, this.betAmountCount);
    }

    @Override
    public int getChaseLongCount() {
        return chaseLongCount;
    }

    @Override
    public int getChaseHuCount() {
        return chaseHuCount;
    }

    @Override
    public void incrementChaseLongCount() {
        chaseLongCount++;
    }

    @Override
    public void incrementChaseHuCount() {
        chaseHuCount++;
    }

    @Override
    public void incrementBetAmountCount() {
        betAmountCount++;
    }

    @Override
    public void resetChaseLongCount() {
        chaseLongCount = 0;
    }

    @Override
    public void resetChaseHuCount() {
        chaseHuCount = 0;
    }

    @Override
    public void resetBetAmountCount() {
        betList.add(betAmountCount);
        betAmountCount = 0;
    }

    public int getTotalBetAmount() {
        return totalBetAmount;
    }
}
