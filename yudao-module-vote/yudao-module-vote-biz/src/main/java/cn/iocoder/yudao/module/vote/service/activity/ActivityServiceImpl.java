package cn.iocoder.yudao.module.vote.service.activity;

import org.springframework.stereotype.Service;
import javax.annotation.Resource;
import org.springframework.validation.annotation.Validated;

import java.util.*;
import cn.iocoder.yudao.module.vote.controller.admin.activity.vo.*;
import cn.iocoder.yudao.module.vote.dal.dataobject.activity.ActivityDO;
import cn.iocoder.yudao.framework.common.pojo.PageResult;

import cn.iocoder.yudao.module.vote.convert.activity.ActivityConvert;
import cn.iocoder.yudao.module.vote.dal.mysql.activity.ActivityMapper;

import static cn.iocoder.yudao.framework.common.exception.util.ServiceExceptionUtil.exception;
import static cn.iocoder.yudao.module.vote.enums.ErrorCodeConstants.*;

/**
 * 投票活动 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class ActivityServiceImpl implements ActivityService {

    @Resource
    private ActivityMapper activityMapper;

    @Override
    public Long createActivity(ActivityCreateReqVO createReqVO) {
        // 插入
        ActivityDO activity = ActivityConvert.INSTANCE.convert(createReqVO);
        activityMapper.insert(activity);
        // 返回
        return activity.getId();
    }

    @Override
    public void updateActivity(ActivityUpdateReqVO updateReqVO) {
        // 校验存在
        validateActivityExists(updateReqVO.getId());
        // 更新
        ActivityDO updateObj = ActivityConvert.INSTANCE.convert(updateReqVO);
        activityMapper.updateById(updateObj);
    }

    @Override
    public void deleteActivity(Long id) {
        // 校验存在
        validateActivityExists(id);
        // 删除
        activityMapper.deleteById(id);
    }

    private void validateActivityExists(Long id) {
        if (activityMapper.selectById(id) == null) {
            throw exception(ACTIVITY_NOT_EXISTS);
        }
    }

    @Override
    public ActivityDO getActivity(Long id) {
        return activityMapper.selectById(id);
    }

    @Override
    public List<ActivityDO> getActivityList(Collection<Long> ids) {
        return activityMapper.selectBatchIds(ids);
    }

    @Override
    public PageResult<ActivityDO> getActivityPage(ActivityPageReqVO pageReqVO) {
        return activityMapper.selectPage(pageReqVO);
    }

    @Override
    public List<ActivityDO> getActivityList(ActivityExportReqVO exportReqVO) {
        return activityMapper.selectList(exportReqVO);
    }

}
