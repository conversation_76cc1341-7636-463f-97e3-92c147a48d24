package cn.iocoder.yudao.module.vote.service;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.collection.ListUtil;
import cn.iocoder.yudao.framework.common.core.ArrayMap;
import com.influxdb.client.InfluxDBClient;
import com.influxdb.client.InfluxDBClientFactory;
import com.influxdb.client.QueryApi;
import com.influxdb.client.WriteApiBlocking;
import com.influxdb.client.domain.WritePrecision;
import com.influxdb.client.write.Point;
import com.influxdb.query.FluxRecord;
import com.influxdb.query.FluxTable;
import org.jetbrains.annotations.NotNull;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.time.Instant;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Service
public class InfluxDBService {

    private final InfluxDBClient influxDBClient;
    private final String bucket;
    private final String org;

    public InfluxDBService(@Value("${influxdb.url}") String url,
                           @Value("${influxdb.token}") String token,
                           @Value("${influxdb.org}") String org,
                           @Value("${influxdb.bucket}") String bucket) {
        this.influxDBClient = InfluxDBClientFactory.create(url, token.toCharArray(), org, bucket);
        this.bucket = bucket;
        this.org = org;
    }

    /**
     *
     * @param team
     * @param status 1:正常，2:锁住, 4: , 5:结束之后
     * @param type
     * @param odds
     * @param time
     */
    public void insertOddsData(String team,String status, String type, double odds, Long time) {
        WriteApiBlocking writeApi = influxDBClient.getWriteApiBlocking();
        Point point = Point.measurement("odds")
                .addTag("team", team)
                .addTag("status", status)
                .addTag("type", type)
                .addField("odds", odds)
                .time(time, WritePrecision.S);
        writeApi.writePoint(bucket, org, point);
    }



    public List<Map<String,Object>> queryOddsData(String team) {
        // 定义Flux查询语句
        List<FluxTable> query = getFluxTables(team);
        List<Map<String,Object>> mapList = new ArrayList<>();
        for (FluxTable fluxTable : query) {
            if(fluxTable != null){
                List<FluxRecord> records = fluxTable.getRecords();
                records.forEach(fluxRecord -> mapList.add(fluxRecord.getValues()));
            }
        }
        return mapList;
    }

    /**
     * 注意这里要保证是同一个team哦。否则相同时间点会覆盖掉里面的内容。
     * @param team
     * @return
     */
    public Map<Instant, Map<String, Object>> queryOddsDataMap(String team) {
        // 定义Flux查询语句
        List<FluxTable> query = getFluxTables(team);
        Map<Instant, Map<String, Object>> resultMap = new HashMap<>();
        for (FluxTable fluxTable : query) {
            if (fluxTable != null) {
                List<FluxRecord> records = fluxTable.getRecords();
                for (FluxRecord fluxRecord : records) {
                    // 获取时间作为 key
                    Instant timeKey = fluxRecord.getTime(); // 或者使用其他格式化方式
                    // 获取值作为内层 map
                    Map<String, Object> valuesMap = fluxRecord.getValues();
                    // 将内层 map 放入外层 map
                    resultMap.put(timeKey, valuesMap);
                }
            }
        }
        return resultMap;
    }

    /**
     * 查询数据

     from(bucket: "wlh")
     |> range(start: 0)
     |> filter(fn: (r) => r["_measurement"] == "odds")
     |> filter(fn: (r) => r["_field"] == "odds")
     |> filter(fn: (r) => r["team"] =~ /^38047347-.* /)
     |> pivot(rowKey:["_time"], columnKey: ["_field"], valueColumn: "_value")
     |> sort(columns: ["_time"], desc: false)
     |> yield(name: "all_data")
     */
    @NotNull
    private List<FluxTable> getFluxTables(String team) {
        String flux = String.format(
                "    from(bucket: \"%s\")\n" +
                        "    |> range(start: 0)\n" +
                        "    |> filter(fn: (r) => r[\"_measurement\"] == \"odds\")\n" +
                        "    |> filter(fn: (r) => r[\"_field\"] == \"odds\")\n" +
                        "    |> filter(fn: (r) => r[\"team\"] =~ /^%s.*/)\n" +
                        "    |> pivot(rowKey:[\"_time\"], columnKey: [\"_field\"], valueColumn: \"_value\")\n" +
                        "    |> sort(columns: [\"_time\"], desc: false)\n" +
                        "    |> yield(name: \"all_data\")", bucket, team);
        QueryApi queryApi = influxDBClient.getQueryApi();
        List<FluxTable> query = queryApi.query(flux, org);
        return query;
    }

    public static void main(String[] args) {
        System.out.println(Math.pow(25/49d, 5));
    }
}
