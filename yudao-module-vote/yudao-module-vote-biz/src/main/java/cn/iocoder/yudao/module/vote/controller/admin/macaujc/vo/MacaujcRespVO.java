package cn.iocoder.yudao.module.vote.controller.admin.macaujc.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;
import java.util.*;
import org.springframework.format.annotation.DateTimeFormat;
import java.time.LocalDateTime;
import com.alibaba.excel.annotation.*;

@Schema(description = "管理后台 - 彩票 Response VO")
@Data
@ExcelIgnoreUnannotated
public class MacaujcRespVO {

    @Schema(description = "期数", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("期数")
    private Integer expect;

    @Schema(description = "开奖号码", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("开奖号码")
    private String openCode;

    @Schema(description = "生肖", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("生肖")
    private String zodiac;

    @Schema(description = "波色", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("波色")
    private String wave;

    @Schema(description = "开奖时间")
    @ExcelProperty("开奖时间")
    private LocalDateTime openTime;

    @Schema(description = "类型", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("类型")
    private String kind;

    @Schema(description = "创建时间", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("创建时间")
    private LocalDateTime createTime;

}