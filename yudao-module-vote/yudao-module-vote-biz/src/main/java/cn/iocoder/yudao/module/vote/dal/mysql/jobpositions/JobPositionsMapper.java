package cn.iocoder.yudao.module.vote.dal.mysql.jobpositions;

import java.util.*;

import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.mybatis.core.query.LambdaQueryWrapperX;
import cn.iocoder.yudao.framework.mybatis.core.mapper.BaseMapperX;
import cn.iocoder.yudao.module.vote.dal.dataobject.jobpositions.JobPositionsDO;
import org.apache.ibatis.annotations.Mapper;
import cn.iocoder.yudao.module.vote.controller.admin.jobpositions.vo.*;

/**
 * 职位信息 Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface JobPositionsMapper extends BaseMapperX<JobPositionsDO> {

    default PageResult<JobPositionsDO> selectPage(JobPositionsPageReqVO reqVO,boolean pay) {
        LambdaQueryWrapperX<JobPositionsDO> queryWrapperX = new LambdaQueryWrapperX<>();
        if(!pay){
//            queryWrapperX.select()
        }
        return selectPage(reqVO, queryWrapperX
                .eqIfPresent(JobPositionsDO::getCompanyId, reqVO.getCompanyId())
                .likeIfPresent(JobPositionsDO::getCompanyName, reqVO.getCompanyName())
                .eqIfPresent(JobPositionsDO::getAnnoId, reqVO.getAnnoId())
                .likeIfPresent(JobPositionsDO::getAnnoName, reqVO.getAnnoName())
                .likeIfPresent(JobPositionsDO::getEducationCnlist, reqVO.getEducationCnlist())
                .eqIfPresent(JobPositionsDO::getAnnoLink, reqVO.getAnnoLink())
                .eqIfPresent(JobPositionsDO::getType, reqVO.getType())
                .eqIfPresent(JobPositionsDO::getTitle, reqVO.getTitle())
                .likeIfPresent(JobPositionsDO::getLocation, reqVO.getLocation())
                .eqIfPresent(JobPositionsDO::getLink, reqVO.getLink())
                .likeIfPresent(JobPositionsDO::getSpecialty, reqVO.getSpecialty())
                .eqIfPresent(JobPositionsDO::getEducation, reqVO.getEducation())
                .eqIfPresent(JobPositionsDO::getExperience, reqVO.getExperience())
                .eqIfPresent(JobPositionsDO::getSalaryRange, reqVO.getSalaryRange())
                .likeIfPresent(JobPositionsDO::getContent, reqVO.getContent())
                .betweenIfPresent(JobPositionsDO::getJobStartTime, reqVO.getJobStartTime())
                .betweenIfPresent(JobPositionsDO::getJobEndTime, reqVO.getJobEndTime())
                .eqIfPresent(JobPositionsDO::getJobStartTimeCn, reqVO.getJobStartTimeCn())
                .eqIfPresent(JobPositionsDO::getJobEndTimeCn, reqVO.getJobEndTimeCn())
                .eqIfPresent(JobPositionsDO::getIsCollect, reqVO.getIsCollect())
                .betweenIfPresent(JobPositionsDO::getCreateTime, reqVO.getCreateTime())
                .orderByDesc(JobPositionsDO::getPositionId));
    }

    default List<JobPositionsDO> selectList(JobPositionsExportReqVO reqVO) {
        return selectList(new LambdaQueryWrapperX<JobPositionsDO>()
                .eqIfPresent(JobPositionsDO::getCompanyId, reqVO.getCompanyId())
                .likeIfPresent(JobPositionsDO::getCompanyName, reqVO.getCompanyName())
                .eqIfPresent(JobPositionsDO::getAnnoId, reqVO.getAnnoId())
                .likeIfPresent(JobPositionsDO::getAnnoName, reqVO.getAnnoName())
                .eqIfPresent(JobPositionsDO::getAnnoLink, reqVO.getAnnoLink())
                .eqIfPresent(JobPositionsDO::getType, reqVO.getType())
                .eqIfPresent(JobPositionsDO::getTitle, reqVO.getTitle())
                .eqIfPresent(JobPositionsDO::getLocation, reqVO.getLocation())
                .eqIfPresent(JobPositionsDO::getLink, reqVO.getLink())
                .eqIfPresent(JobPositionsDO::getSpecialty, reqVO.getSpecialty())
                .eqIfPresent(JobPositionsDO::getEducation, reqVO.getEducation())
                .eqIfPresent(JobPositionsDO::getEducationCnlist, reqVO.getEducationCnlist())
                .eqIfPresent(JobPositionsDO::getExperience, reqVO.getExperience())
                .eqIfPresent(JobPositionsDO::getSalaryRange, reqVO.getSalaryRange())
                .eqIfPresent(JobPositionsDO::getContent, reqVO.getContent())
                .betweenIfPresent(JobPositionsDO::getJobStartTime, reqVO.getJobStartTime())
                .betweenIfPresent(JobPositionsDO::getJobEndTime, reqVO.getJobEndTime())
                .eqIfPresent(JobPositionsDO::getJobStartTimeCn, reqVO.getJobStartTimeCn())
                .eqIfPresent(JobPositionsDO::getJobEndTimeCn, reqVO.getJobEndTimeCn())
                .eqIfPresent(JobPositionsDO::getIsCollect, reqVO.getIsCollect())
                .betweenIfPresent(JobPositionsDO::getCreateTime, reqVO.getCreateTime())
                .orderByDesc(JobPositionsDO::getPositionId));
    }

}
