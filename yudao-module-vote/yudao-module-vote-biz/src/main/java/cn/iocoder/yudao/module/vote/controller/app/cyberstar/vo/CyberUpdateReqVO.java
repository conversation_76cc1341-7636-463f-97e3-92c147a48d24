package cn.iocoder.yudao.module.vote.controller.app.cyberstar.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import javax.validation.constraints.NotNull;
import java.util.Date;

@Schema(description = "管理后台 - 网红主播 Response VO")
@Data
@ExcelIgnoreUnannotated
public class CyberUpdateReqVO {
    @Schema(description = "文件编号", requiredMode = Schema.RequiredMode.REQUIRED, example = "12791")
    @NotNull(message = "id不能为空")
    private Long id;


    @Schema(description = "开始日期", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    private Date date;

    @Schema(description = "金额")
    @NotNull(message = "金额不能为空")
    private Double amount;
}