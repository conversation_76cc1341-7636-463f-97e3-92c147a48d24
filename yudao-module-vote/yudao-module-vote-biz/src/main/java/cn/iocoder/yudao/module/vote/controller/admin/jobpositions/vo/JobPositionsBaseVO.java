package cn.iocoder.yudao.module.vote.controller.admin.jobpositions.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;
import java.util.*;
import java.time.LocalDateTime;
import java.time.LocalDateTime;
import java.time.LocalDateTime;
import java.time.LocalDateTime;
import javax.validation.constraints.*;
import org.springframework.format.annotation.DateTimeFormat;

import static cn.iocoder.yudao.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;

/**
 * 职位信息 Base VO，提供给添加、修改、详细的子 VO 使用
 * 如果子 VO 存在差异的字段，请不要添加到这里，影响 Swagger 文档生成
 */
@Data
public class JobPositionsBaseVO {

    @Schema(description = "公司ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "17113")
    @NotNull(message = "公司ID不能为空")
    private Integer companyId;

    @Schema(description = "公司名称", requiredMode = Schema.RequiredMode.REQUIRED, example = "李四")
    @NotNull(message = "公司名称不能为空")
    private String companyName;

    @Schema(description = "公告ID", example = "25758")
    private Integer annoId;

    @Schema(description = "公告名称", example = "芋艿")
    private String annoName;

    @Schema(description = "公告链接")
    private String annoLink;

    @Schema(description = "类型", example = "1")
    private String type;

    @Schema(description = "职位名称")
    private String title;

    @Schema(description = "工作地点")
    private String location;

    @Schema(description = "链接")
    private String link;

    @Schema(description = "专业要求")
    private String specialty;

    @Schema(description = "学历要求")
    private String education;

    @Schema(description = "学历列表 (使用 JSON 类型存储)")
    private String educationCnlist;

    @Schema(description = "工作经验要求")
    private String experience;

    @Schema(description = "薪资范围")
    private String salaryRange;

    @Schema(description = "职位描述")
    private String content;

    @Schema(description = "职位开始时间")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime jobStartTime;

    @Schema(description = "职位结束时间")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime jobEndTime;

    @Schema(description = "职位开始时间 (中文格式)")
    private String jobStartTimeCn;

    @Schema(description = "职位结束时间 (中文格式)")
    private String jobEndTimeCn;

    @Schema(description = "是否被收藏 (布尔类型)")
    private Boolean isCollect;

}
