package cn.iocoder.yudao.module.vote.service.lottery;

import cn.iocoder.yudao.module.vote.dal.dataobject.lottery.LotteryDO;
import cn.iocoder.yudao.module.vote.service.lottery.bo.JiLvBo;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import java.util.*;
/**
 * 伊利分析结果并返回追注的信息内容。
 */
@Slf4j
@Service  // 他现在来不及下注。
public class LotteryYLAnalyzer {



    // 定义事件概率
    static Map<String, Double> eventProbabilities = new HashMap<>();
    static {
        eventProbabilities.put("大", 0.5);
        eventProbabilities.put("小", 0.5);
        eventProbabilities.put("单", 0.5);
        eventProbabilities.put("双", 0.5);
//        eventProbabilities.put("极大", 0.056);
//        eventProbabilities.put("极小", 0.056);
        eventProbabilities.put("龙", 0.45);
        eventProbabilities.put("虎", 0.45);
        //
//        eventProbabilities.put("大单", 0.213);  // 171  --> 521. 0.32
//        eventProbabilities.put("小双", 0.253); // 363   --> 1100  0.33

//        eventProbabilities.put("和", 0.1);
//        for (int i = 0; i <= 27; i++) {
        for (int i = 9; i <= 27-9; i++) {
            double[] probs = {
                    0.001, 0.003, 0.006, 0.01, 0.015, 0.021, 0.028, 0.036, 0.045,
                    0.055, 0.063, 0.069, 0.073, 0.075, 0.075, 0.073, 0.069, 0.063,
                    0.055, 0.045, 0.036, 0.028, 0.021, 0.015, 0.01, 0.006, 0.003, 0.001
            };
//            eventProbabilities.put("特码" + i, probs[i]);
        }
    }
    // 定义事件概率
    static Map<String, Integer> followCountMap = new HashMap<>();
    static {
        followCountMap.put("大", 3);
        followCountMap.put("小", 3);
        followCountMap.put("单", 3);
        followCountMap.put("双", 3); // 1 2 4
        followCountMap.put("极大", 16);
        followCountMap.put("极小", 16);
        followCountMap.put("龙", 3);
        followCountMap.put("虎", 3);
        followCountMap.put("和", 8);

        followCountMap.put("大单", 3);
        followCountMap.put("小双", 3);
        for (int i = 0; i <= 27; i++) {
            int[] probs = {
                    700, 250, 130, 80, 56, 40, 30, 24, 20,17, 15, 14, 13, 13,
                    13, 13, 14, 15,17, 20, 24, 30, 40, 56, 80, 130, 250, 700
            };
            followCountMap.put("特码" + i, probs[i]-1); // 1 1 1 1 1 1 1 1 -》
        }
    }


    public static int calculateMissCount(List<LotteryDO> history, String eventType) {
        int missCount = 0;
        for (int i = history.size() - 1; i >= 0; i--) {
            LotteryDO item = history.get(i);
            String[] nums = item.getOpenCode().split(",");
            int num1 = Integer.parseInt(nums[0]);
            int num3 = Integer.parseInt(nums[2]);
            int sum = item.getSum();

            boolean isEvent = false;
            switch (eventType) {
                case "大":
                    isEvent = sum >= 14 && sum <= 27;
                    break;
                case "小":
                    isEvent = sum >= 0 && sum <= 13;
                    break;
                case "单":
                    isEvent = sum % 2 == 1;
                    break;
                case "双":
                    isEvent = sum % 2 == 0;
                    break;
                case "极大":
                    isEvent = sum >= 22 && sum <= 27;
                    break;
                case "极小":
                    isEvent = sum >= 0 && sum <= 5;
                    break;
                case "龙":
                    isEvent = num1 > num3;
                    break;
                case "虎":
                    isEvent = num1 < num3;
                    break;
                case "和":
                    isEvent = num1 == num3;
                    break;
                case "大单":
                    isEvent = sum >= 14 && sum <= 27 && sum % 2 == 1;
                    break;
                case "大双":
                    isEvent = sum >= 14 && sum <= 27 && sum % 2 == 0;
                    break;
                case "小单":
                    isEvent = sum >= 0 && sum <= 13 && sum % 2 == 1;
                    break;
                case "小双":
                    isEvent = sum >= 0 && sum <= 13 && sum % 2 == 0;
                    break;
                default:
                    if (eventType.startsWith("特码")) {
                        int target = Integer.parseInt(eventType.replace("特码", ""));
                        isEvent = sum == target;
                    }
                    break;
            }

            if (!isEvent) {
                missCount++;
            } else {
                break;
            }
        }
        return missCount;
    }

    public static double calculateNextMissProbability(String eventType, int missCount) {
        double p = eventProbabilities.getOrDefault(eventType, 0.0);
        return Math.pow(1 - p, missCount + 1);
    }

    public static Map<String, JiLvBo> analyzeMissProbabilities(List<LotteryDO> history) {
        Map<String, JiLvBo> results = new LinkedHashMap<>();
        for (String eventType : eventProbabilities.keySet()) {
            int missCount = calculateMissCount(history, eventType);
            double probability = calculateNextMissProbability(eventType, missCount);
            Integer followCount = followCountMap.get(eventType);
            JiLvBo result = new JiLvBo();
            result.setEventType(eventType);
            result.setMissCount(missCount);
            result.setProbability(probability);
            result.setFollowCount(followCount);
//            result.put("missCount", String.valueOf(missCount));
//            result.put("nextMissProbability", String.format("%.2f%%", probability * 100));
            results.put(eventType, result);
        }
        return results;
    }
}
