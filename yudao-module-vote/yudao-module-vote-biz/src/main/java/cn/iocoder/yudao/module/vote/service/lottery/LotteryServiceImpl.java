package cn.iocoder.yudao.module.vote.service.lottery;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.collection.ListUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.date.LocalDateTimeUtil;
import cn.hutool.core.thread.ThreadUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.http.HttpRequest;
import cn.hutool.http.HttpUtil;
import cn.hutool.json.JSONArray;
import cn.hutool.json.JSONObject;
import cn.iocoder.yudao.framework.common.util.date.DateUtils;
import cn.iocoder.yudao.module.vote.service.jnd28.counter.Jnd28BetCounter;
import cn.iocoder.yudao.module.vote.service.jnd28.counter.Jnd28BetCounterMemoryImpl;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.ApplicationArguments;
import org.springframework.boot.ApplicationRunner;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;
import javax.annotation.Resource;
import org.springframework.validation.annotation.Validated;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.concurrent.*;
import java.util.stream.Collectors;

import cn.iocoder.yudao.module.vote.controller.admin.lottery.vo.*;
import cn.iocoder.yudao.module.vote.dal.dataobject.lottery.LotteryDO;
import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.common.util.object.BeanUtils;

import cn.iocoder.yudao.module.vote.dal.mysql.lottery.LotteryMapper;

import static cn.iocoder.yudao.framework.common.exception.util.ServiceExceptionUtil.exception;
import static cn.iocoder.yudao.module.vote.enums.ErrorCodeConstants.*;

/**
 * kuai28.com Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
@Slf4j
public class LotteryServiceImpl implements LotteryService {

    @Resource
    private LotteryMapper lotteryMapper;


    @Override
    public Long createLottery(LotterySaveReqVO createReqVO) {
        // 插入
        LotteryDO lottery = BeanUtils.toBean(createReqVO, LotteryDO.class);
        lotteryMapper.insert(lottery);
        // 返回
        return lottery.getId();
    }

    @Override
    public void updateLottery(LotterySaveReqVO updateReqVO) {
        // 校验存在
        validateLotteryExists(updateReqVO.getId());
        // 更新
        LotteryDO updateObj = BeanUtils.toBean(updateReqVO, LotteryDO.class);
        lotteryMapper.updateById(updateObj);
    }

    @Override
    public void deleteLottery(Long id) {
        // 校验存在
        validateLotteryExists(id);
        // 删除
        lotteryMapper.deleteById(id);
    }

    private void validateLotteryExists(Long id) {
        if (lotteryMapper.selectById(id) == null) {
            throw exception(LOTTERY_NOT_EXISTS);
        }
    }

    @Override
    public LotteryDO getLottery(Long id) {
        return lotteryMapper.selectById(id);
    }

    @Override
    public PageResult<LotteryDO> getLotteryPage(LotteryPageReqVO pageReqVO) {
        return lotteryMapper.selectPage(pageReqVO);
    }

    // 1. 期数查询数据库。
//        LotteryDO lotteryDO = null;
//        // 2. 没有就调用接口查询出来并且保存起来。
//        if(lotteryDO == null){
//            //TODO curl http://xxxxxx
//            lotteryDO = new LotteryDO();
//            // 期数相同 并且 有对应的开奖结果
//            if (Objects.equals(fullExpect,lotteryDO.getFullExpect()) && StrUtil.isNotBlank(lotteryDO.getOpenCode()) ) {
//                // 保存数据库.
//
//                // 判断可以分析啦。
//            }
//            //
//        }






    /**
     * 保存历史记录到数据库中。
     */
    @Scheduled(cron = "0 0 3 * * ?")
//        @Scheduled(initialDelay = 1, fixedRate = 60, timeUnit = TimeUnit.SECONDS)
    public void storeHistoryDb() {
        // LocalDate
        LocalDateTime localDateTime = DateUtil.toLocalDateTime(DateUtil.yesterday());
        storeDb(localDateTime.toLocalDate(),HttpType.kuai28);

            // 定时之后
//            LocalDate startDatedate = LocalDate.parse("2024-11-25");
//            List<LocalDate> between = DateUtils.getBetween(startDatedate, LocalDate.now());
//            between.forEach(date ->{
//                String time = DateUtil.format(date.atStartOfDay(), "yyyy-M-d");
//                storeDb(time);
//            });

//            System.out.println("------");
    }

    // 实现 ApplicationRunner 接口。
    public void run(ApplicationArguments args) throws Exception {
        LocalDate startDatedate = LocalDate.parse("2020-12-01");  // 2022/08/26
        LocalDate endDatedate = LocalDate.parse("2021-03-18");
        List<LocalDate> tasks = DateUtils.getBetween(startDatedate, endDatedate);





//        tasks = DateUtils.formatterToLocalDateList(DateTimeFormatter.ISO_LOCAL_DATE , Arrays.asList("2022-10-20","2021-04-16","2021-04-17","2021-04-18","2021-04-19","2021-04-20","2021-09-03","2021-09-04","2021-09-05","2021-09-09","2021-09-10" ,"2024-05-26","2024-05-27" ));

//        between.forEach(date ->{ // between.parallelStream() // 他这种并发是乱序的，日期都是乱的。
//            String time = DateUtil.format(date.atStartOfDay(), "yyyy/MM/dd");
//            ThreadUtil.execute(()->storeDb(time,true)); // 这样是有序的。 ， 但是他还是乱序的。
//        });

        /*
        想局部乱序执行（每批是乱序并发），但 整个提交过程是全局有序的（即第二批在第一批执行完之后才开始）
        一批任务并发执行并等待结果	invokeAll() ✅
        多批任务，每批执行完再下一批	循环 invokeAll() ✅
        每个任务独立、无返回值	Callable<Void> ✅
        不关心结果、只关注执行	Runnable -> Callable<Void> 转换 ✅
         */
        int nThreads = 1;
        ExecutorService executor = Executors.newFixedThreadPool(nThreads);
        int batchSize = nThreads;
        for (int i = 0; i < tasks.size(); i += batchSize) {
            List<LocalDate> batch = tasks.subList(i, Math.min(i + batchSize, tasks.size()));

            List<Callable<Void>> taskList = batch.stream()
                    .map(date -> (Callable<Void>) () -> {
                        storeDb(date, HttpType.ez10288,false);
                        return null;
                    }).collect(Collectors.toList());

            executor.invokeAll(taskList); // 每批 8 条，乱序并发执行
            ThreadUtil.safeSleep(1 * 1000);
        }
        executor.shutdown();
    }
    private void storeDb(LocalDate date , HttpType type ) {
        this.storeDb(date,type,true);
    }
    private void storeDb(LocalDate date , HttpType type , boolean check) {
        List<LotteryDO> dataList;
        if(type == HttpType.ez10288){
            String time = DateUtil.format(date.atStartOfDay(), "yyyy/MM/dd");
            dataList = doEzHttp(time);
        }else if(type == HttpType.kuai28){
            String time = DateUtil.format(date.atStartOfDay(), "yyyy-M-d");
            dataList = doKuaiHttp(time,9999);
        }else if(type == HttpType.wapi){
            String time = DateUtil.format(date.atStartOfDay(), "yyyy-MM-dd");
            dataList = doWapiHttp(time);
        } else {
            dataList = null;
        }

        ThreadUtil.execute(()->{
            Collection<LotteryDO> entities = new ArrayList<>();
            for (int i = 0; i < dataList.size(); i++) {
                LotteryDO lotteryDO = dataList.get(i);
                if(check){
                    if (!lotteryMapper.exists(lotteryDO.getFullExpect(),lotteryDO.getVarietyType())) {
                        entities.add(lotteryDO);
                    }
                }else{
                    entities.add(lotteryDO);
                }
            }
            lotteryMapper.insertBatch(entities);
        });


    }

    public static List<LotteryDO> doKuaiHttp(String time, int limits) {
        JSONObject requestBody = new JSONObject();
        requestBody.set("limits", limits);
        requestBody.set("date", time);
        requestBody.set("type", "jnd28");
        requestBody.set("curr", 1);
        String response = HttpUtil.post("https://www.kuai28.com/portal/api/lottery_details", requestBody.toString());
        JSONObject jsonResponse = new JSONObject(response);
        JSONArray dataList = jsonResponse.getJSONObject("data").getJSONArray("list");
        return dataList.toList(LotteryDO.class);
    }


    /**
     * 易利的开奖网 <br/> 这个需要vpn翻墙才能访问通，有时候会找不到域名。
     * @param time
     * @return
     */
    public static List<LotteryDO> doEzHttp(String time) {
        JSONObject requestBody = new JSONObject();
        requestBody.set("cLotteryDT", time);
        requestBody.set("cType", "2");
        requestBody.set("cGameWinNBNO", "");
        String url = "https://ez10288.com/api/Lottery/GetLotteryGameNBList";
        Map<String, String> headers = new HashMap<>();
//        headers.put("Referer","https://ez10288.com/"); // https://ez10288.com/
        headers.put("Content-Type","application/x-www-form-urlencoded");
//        headers.put("User-Agent","Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/134.0.0.0 Safari/537.36");
        List<LotteryDO> list = new ArrayList<>();
        try{
            String response = HttpRequest.post(url).form(requestBody).headerMap(headers,true).execute().body();
            JSONObject jsonResponse = new JSONObject(response);
            JSONObject jsonData = jsonResponse.getJSONObject("Data");
            Integer nowTotal = jsonData.getInt("nowTotal");
            JSONArray dataList = jsonData.getJSONArray("objList");
            if (dataList == null && nowTotal ==0) {
                log.info("[{}]时间，没有对应的数据",time);
            }else{
                log.info("[{}]时间，有数据",time);
                for (int i = 0; i < dataList.size(); i++) {
                    JSONObject jsonObject = dataList.getJSONObject(i);
                    Integer cGameWinNB1 = jsonObject.getInt("cGameWinNB1");
                    Integer cGameWinNB2 = jsonObject.getInt("cGameWinNB2");
                    Integer cGameWinNB3 = jsonObject.getInt("cGameWinNB3");
                    Long cGameWinNBNO = jsonObject.getLong("cGameWinNBNO");
                    int sum = cGameWinNB1 + cGameWinNB2 + cGameWinNB3;
                    String cLotteryDT = jsonObject.getStr("cLotteryDT");
                    String num_sort = "小";
                    if(sum > 13){
                        num_sort = "大";
                    }
                    String num_type = "单";
                    if(sum % 2 == 0){
                        num_type = "双";
                    }
                    String open_code = StrUtil.format("{},{},{}",cGameWinNB1,cGameWinNB2,cGameWinNB3);
                    LocalDateTime openTime = LocalDateTime.parse(cLotteryDT);
                    LotteryDO lotteryDO = new LotteryDO();
                    lotteryDO.setEfficacy(2);
                    lotteryDO.setFullExpect(cGameWinNBNO);
                    lotteryDO.setNumSort(num_sort);
                    lotteryDO.setNumType(num_type);
                    lotteryDO.setOpenCode(open_code);
                    lotteryDO.setOpenTime(openTime);
                    lotteryDO.setSum(sum);
                    lotteryDO.setTypeName("加拿大28");
                    lotteryDO.setVarietyType("jnd28");
                    list.add(lotteryDO);
                }
            }
            return list;
        } catch (Exception e) {
            log.info( time + "失败" + e.getMessage());
            ThreadUtil.safeSleep( 30 * 1000);
            return doEzHttp(time);
            //
        }
    }


    /**
     * 挖数据官网的操作内容： https://www.wapi.cn/api_debug/119/260.html <br/>
     * 注意设置： hash模式验证。 <br/>
     * 需要开通对应的接口,新号有1天试用。 <br/>
     * @param time
     * @return
     */
    public static List<LotteryDO> doWapiHttp(String time) {
        String appid = "42499";
        String sign = "6cc5620345e0e49d6be2cf35a784c1ea";
        String url = StrUtil.format("https://vyps.api.storeapi.net/api/119/260?format=json&date={}&limit=999&appid={}&sign={}",time,appid,sign);
        List<LotteryDO> list = new ArrayList<>();
        try{
            String response = HttpUtil.get(url);
            JSONObject jsonResponse = new JSONObject(response);
            JSONArray dataList = jsonResponse.getJSONArray("retdata");
            if (CollectionUtil.isEmpty(dataList)) {
                log.info("[{}]时间，Wapi没有对应的数据",time);
            }else{
                for (int i = 0; i < dataList.size(); i++) {
                    JSONObject jsonObject = dataList.getJSONObject(i);
                    JSONArray numberArray = jsonObject.getJSONArray("number");
                    Integer cGameWinNB1 = numberArray.getInt(0);
                    Integer cGameWinNB2 = numberArray.getInt(1);
                    Integer cGameWinNB3 = numberArray.getInt(2);
                    Long cGameWinNBNO = jsonObject.getLong("long_issue");
                    int sum = cGameWinNB1 + cGameWinNB2 + cGameWinNB3;
                    String cLotteryDT = jsonObject.getStr("kjtime");
                    String num_sort = "小";
                    if(sum > 13){
                        num_sort = "大";
                    }
                    String num_type = "单";
                    if(sum % 2 == 0){
                        num_type = "双";
                    }
                    String open_code = StrUtil.format("{},{},{}",cGameWinNB1,cGameWinNB2,cGameWinNB3);
                    LocalDateTime openTime = LocalDateTimeUtil.parse(cLotteryDT,"yyyy-MM-dd HH:mm:ss");
                    LotteryDO lotteryDO = new LotteryDO();
                    lotteryDO.setEfficacy(2);
                    lotteryDO.setFullExpect(cGameWinNBNO);
                    lotteryDO.setNumSort(num_sort);
                    lotteryDO.setNumType(num_type);
                    lotteryDO.setOpenCode(open_code);
                    lotteryDO.setOpenTime(openTime);
                    lotteryDO.setSum(sum);
                    lotteryDO.setTypeName("加拿大28");
                    lotteryDO.setVarietyType("jnd28");
                    list.add(lotteryDO);
                }
            }
            return list;
        } catch (Exception e) {
            log.info( time + "失败" + e.getMessage());
            ThreadUtil.safeSleep( 30 * 1000);
            return doWapiHttp(time);
            //
        }
    }


    public static void main(String[] args) {
        LocalDateTime localDateTime = DateUtil.toLocalDateTime(DateUtil.yesterday());
        String time = DateUtil.format(localDateTime.toLocalDate().atStartOfDay(), "yyyy/MM/dd");
        System.out.println(time);
//        System.out.println(17%2);
//        // 给定的日期时间字符串
//        String dateTimeString = "2025-03-18T23:59:51";
//
//        // 解析字符串为LocalDateTime对象
//        // 由于给定的字符串是ISO日期时间格式，可以直接解析
//        LocalDateTime localDateTime = LocalDateTime.parse(dateTimeString);
//
//        // 输出转换后的LocalDateTime对象
//        System.out.println("Parsed LocalDateTime: " + localDateTime.toString());

        System.out.println(doWapiHttp("2025-03-19"));
    }

    enum HttpType{
        kuai28,ez10288,wapi
    }
}
/*
[2023-07/02]时间，没有对应的数据

 */