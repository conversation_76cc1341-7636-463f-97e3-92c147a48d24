package cn.iocoder.yudao.module.vote.controller.app;

/**
 * 在 Controller 的命名上，额外增加 App 作为前缀，一方面区分是管理后台还是用户 App 的 Controller，另一方面避免 Spring Bean 的名字冲突。
 *
 * 可能你会奇怪，这里我们定义了两个 /demo/test/get 接口，会不会存在重复导致冲突呢？答案，当然是并不会。原因是：
 *
 * controller.admin 包下的接口，默认会增加 /admin-api，即最终的访问地址是 /admin-api/demo/test/get
 * controller.app 包下的接口，默认会增加 /app-api，即最终的访问地址是 /app-api/demo/test/get
 *
 * <AUTHOR>
 * @version 1.0
 * @module Socialuni
 * @date 2023/4/29 10:25
 * @since 1.0
 */
import cn.hutool.core.collection.ListUtil;
import cn.hutool.core.date.LocalDateTimeUtil;
import cn.hutool.core.thread.ThreadUtil;
import cn.hutool.core.util.RandomUtil;
import cn.hutool.core.util.RuntimeUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.http.Header;
import cn.hutool.http.HttpRequest;
import cn.hutool.json.JSONArray;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import cn.hutool.poi.excel.ExcelReader;
import cn.hutool.system.SystemUtil;
import cn.iocoder.yudao.framework.common.pojo.CommonResult;
import cn.iocoder.yudao.framework.mybatis.core.query.LambdaQueryWrapperX;
import cn.iocoder.yudao.framework.tenant.core.util.TenantUtils;
import cn.iocoder.yudao.module.vote.controller.app.vo.AppVoteTestPrintlnReqVO;
import cn.iocoder.yudao.module.vote.controller.app.vo.RaytwoGameOdd;
import cn.iocoder.yudao.module.vote.controller.app.vo.RaytwoTeamsOdd;
import cn.iocoder.yudao.module.vote.controller.app.vo.RaytwoTeamsOddEnum;
import cn.iocoder.yudao.module.vote.dal.dataobject.activity.RaybetMatchDO;
import cn.iocoder.yudao.module.vote.dal.dataobject.activity.RaybetWinnerDO;
import cn.iocoder.yudao.module.vote.dal.mysql.activity.RaybetMatchMapper;
import cn.iocoder.yudao.module.vote.dal.mysql.activity.RaybetWinnerMapper;
import cn.iocoder.yudao.module.vote.service.InfluxDBService;
import cn.iocoder.yudao.module.vote.service.RayAnalysisService;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Operation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.data.redis.core.ValueOperations;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.text.Collator;
import java.time.Instant;
import java.time.LocalDateTime;
import java.time.temporal.ChronoUnit;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import static cn.iocoder.yudao.framework.common.pojo.CommonResult.success;

@Tag(name = "用户 App - Test")
//@RestController
//@RequestMapping("/vote/test") // 需要忽略租户信息。
@Validated
@Slf4j
public class AppVoteTestController {
  public static final String USER_AGENT = "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/129.0.0.0 Safari/537.36";
  public static final Collator collator = Collator.getInstance(Locale.CHINESE);
  public static final String TYPE_XHR = "xhr";
  public static final String TYPE_RECEIVED = "received";
  @Resource
  private InfluxDBService influxDBService;
  @Resource
  private RaybetMatchMapper raybetMatchMapper;
  @Resource
  private RaybetWinnerMapper raybetWinnerMapper;
  @Resource
  private RayAnalysisService rayAnalysisService;
  @Resource // 保证 aj-captcha 的 SPI 创建时的注入
  private StringRedisTemplate stringRedisTemplate;
  /**
   * 验证码的开关，默认为 true
   */
  @Value("${yudao.vote.raybet.enable:true}")
  private Boolean raybetEnable;

  @PostMapping("/vueDataUi")
  @Operation(summary = "vueDataUi的报表内容")
  public CommonResult<Map<String, Object>> vueDataUi(@RequestBody AppVoteTestPrintlnReqVO appVoteTestPrintlnReqVO) {
    String matchId = appVoteTestPrintlnReqVO.getData();
    RaybetWinnerDO raybetWinnerDO = raybetWinnerMapper.selectById(matchId);
    String team1Id = raybetWinnerDO.getTeam1Id();
    String team2Id = raybetWinnerDO.getTeam2Id();
    // 获得第一组所有的数据。 并转化成
    Map<Instant, Map<String, Object>> maps1 = influxDBService.queryOddsDataMap(team1Id);
    // 获得第二组所有的数据。
    Map<Instant, Map<String, Object>> maps2 = influxDBService.queryOddsDataMap(team2Id);
    // 合并所有的time的点位。
    // 使用 Stream API 合并两个 Set 并进行排序
    List<Instant> sortedList  = Stream.concat(maps1.keySet().stream(), maps2.keySet().stream())
            .distinct()  // 去重
            .sorted()  // 按自然顺序排序
            .collect(Collectors.toList());  // 收集到 List 中
    // 循环1 补全数据组
    Map<String, Object> result = new HashMap<>();
    List<Object> team1OddsList = new ArrayList<>();
    List<Object> team2OddsList = new ArrayList<>();
    for (int i = 0; i < sortedList.size(); i++) {
      Object odds1 = getOdds(maps1, sortedList, i);
      team1OddsList.add(odds1);
      Object odds2 = getOdds(maps2, sortedList, i);
      team2OddsList.add(odds2);
    }
    result.put("team1Id",team1Id);
    result.put("team2Id",team2Id);
    result.put("team1OddsList",team1OddsList);
    result.put("team2OddsList",team2OddsList);
    result.put("timeList",sortedList);
    return success(result);
  }

  private Object getOdds(Map<Instant, Map<String, Object>> maps , List<Instant> sortedList,int index){
    if(index == -1) return null;
    Instant key = sortedList.get(index);
    Map<String, Object> map = maps.get(key);
    Object odds = null;
    if(map != null){
      odds = map.get("odds");
    }
    if(odds == null){
      return getOdds(maps,sortedList,index-1);
    }else{
      return odds;
    }
  }
  @GetMapping("/get")
  @Operation(summary = "获取 test 信息")
  public CommonResult<List<Object>> get(@RequestBody AppVoteTestPrintlnReqVO appVoteTestPrintlnReqVO) {
    // 统计对应的所有的比赛。
    List<RaybetWinnerDO> raybetMatchDOS = raybetWinnerMapper.selectAnalysis();
    List<Object> list = new ArrayList<>();
    String type = appVoteTestPrintlnReqVO.getType();
    // 进行分析。
    for (RaybetWinnerDO raybetMatchDO : raybetMatchDOS) {
      // get dada
      String matchId = raybetMatchDO.getMatchId();
      String winner = raybetMatchDO.getWinner();
      List<Map<String, Object>> maps = influxDBService.queryOddsData(matchId);
      RaytwoGameOdd raytwoGameOdd = RaytwoGameOdd.newInstanceInfluxMap(maps);
      if(raytwoGameOdd != null){
        List<RaytwoTeamsOdd> raytwoTeamsOdds = raytwoGameOdd.sortInstants();
        //
        Map<String, Object> hedge = null;
        switch (type){
          case "hedge":
            hedge = rayAnalysisService.hedge(raytwoTeamsOdds);break;
          case "hedgeTimeDiff":
            hedge = rayAnalysisService.hedgeTimeDiff(raytwoTeamsOdds);break;
          case "tangle":
            hedge = rayAnalysisService.tangle(raytwoTeamsOdds);break;
          case "all":
            hedge = rayAnalysisService.all(raytwoTeamsOdds);break;
          default:
            break;
        }

        if(hedge != null){
          hedge.put("matchId",matchId);
          hedge.put("winner",winner);
          hedge.put("remark",raybetMatchDO.getRemark());
          list.add(hedge);
        }
      }
    }
    return success(list);
  }

  @GetMapping("/bo")
  @Operation(summary = "bo分析情况")
  public CommonResult<List<Object>> bo(@RequestBody AppVoteTestPrintlnReqVO appVoteTestPrintlnReqVO) {
    String data = appVoteTestPrintlnReqVO.getData();
    String type = appVoteTestPrintlnReqVO.getType();
    // 统计对应的所有的比赛。
    List<RaybetMatchDO> raybetMatchDOS = raybetMatchMapper.selectAnalysis(data);
    List<Object> list = new ArrayList<>();
    // 进行分析。
    for (RaybetMatchDO raybetMatchDO : raybetMatchDOS) {
      // get dada
      Long matchId = raybetMatchDO.getId();
      List<RaybetWinnerDO> winners = raybetWinnerMapper.selectList("match_fork", matchId);

//      List<String> keys = ListUtil.sortByPinyin(list);  ;
      winners = ListUtil.sort(winners,  (o1,o2) ->{
          if (StrUtil.contains(o1.getMatchId(),"-final-")) {
            return -1; // s1 是特殊字符串，s2 不是，s1 排在前面
          } else if (StrUtil.contains(o2.getMatchId(),"-final-")) {
            return 1; // s1 不是特殊字符串，s2 是，s2 排在前面
          } else {
            return collator.compare(o1.getMatchId(), o2.getMatchId()); // 其他情况按自然顺序排序
          }
      });
      Map<String, Object> hedge = null;
      switch (type){
        case "hedgeTimeDiff":
//          hedge = rayAnalysisService.hedgeTimeDiff(raytwoTeamsOdds);break;
        case "predominate":
          hedge = rayAnalysisService.bo_predominate( raybetMatchDO, winners);break;
        default:
          hedge = rayAnalysisService.bo_predominate( raybetMatchDO, winners);break;
      }

      if(hedge != null){
        hedge.put("matchId",matchId+"");
        hedge.put("score",raybetMatchDO.getScore());
        hedge.put("remark",raybetMatchDO.getGameName());
        list.add(hedge);
      }

    }
    return success(list);
  }
  @PostMapping("/println")
  @Operation(summary = "打印信息信息")
  /*
  默认情况下，表单数据或查询参数的绑定不需要 @RequestBody：
  使用 JSON 格式传输数据时，通常需要 @RequestBody：
   */
  public CommonResult<String> println(@RequestBody AppVoteTestPrintlnReqVO appVoteTestPrintlnReqVO) {
    String body = appVoteTestPrintlnReqVO.getData();
    String url = appVoteTestPrintlnReqVO.getUrl();
    String type = appVoteTestPrintlnReqVO.getType();
    Long timestamp = appVoteTestPrintlnReqVO.getTimestamp();
    ValueOperations<String, String> redis = stringRedisTemplate.opsForValue();
    if(TYPE_RECEIVED.equals(type)){
      if("wss://ydsocket.esportsgameinforay.com/socketcluster/".equals(url) && JSONUtil.isTypeJSONObject(body)){
        JSONObject dataJson = new JSONObject(body);
        String id = dataJson.getByPath("data.data.odds[0].id", String.class);
        String status = dataJson.getByPath("data.data.odds[0].status", String.class);
        Double odds = dataJson.getByPath("data.data.odds[0].odds", Double.class);
        Long last_update = dataJson.getByPath("data.data.odds[0].last_update", Long.class);
//      System.out.println(id + ";" + odds + ";" + last_update);
        String team = redis.get("vote:raybet:" + id);
        if(odds != null && last_update != null && StrUtil.isNotBlank(team)){
//          log.info("ws-team=[{}],odds_num={}",team,odds);
          influxDBService.insertOddsData(team,status,type,odds,last_update);
        }
      }
    }else if(TYPE_XHR.equals(type)){
      if(StrUtil.contains(url,"/v2/odds?match_id=")){
        JSONObject dataJson = new JSONObject(body);
        JSONObject respJson = dataJson.getByPath("response.data.body", JSONObject.class);
        JSONArray odds = respJson.getByPath("result.odds", JSONArray.class);
        // 插入比赛的基本情况信息。
        RaybetMatchDO raybetMatchDO = respJson.getBean("result", RaybetMatchDO.class);
        // 重复插入的报错信息。
        if (raybetMatchMapper.selectById(raybetMatchDO.getId()) == null) {
          raybetMatchMapper.insert(raybetMatchDO);
        }
        for (int i = 0; i < odds.size(); i++) {
          JSONObject odd = odds.getJSONObject(i);
          String groupName = odd.getStr("group_name");   // 地图获胜者率先获得十杀  一杀 五杀
          if(isWinnerFlg(groupName)){
            String matchId = odd.getStr("match_id");
            String matchStage = odd.getStr("match_stage"); // 全场，第一场。。。
            String name = odd.getStr("name"); // 名称
            String status = odd.getStr("status"); // 名称
            String odds_id = odd.getStr("odds_id"); //
            Double odds_num = odd.getDouble("odds"); //
            String team = StrUtil.join("-", matchId, matchStage, name);
            redis.set("vote:raybet:" + odds_id,team,24,TimeUnit.HOURS);
            if(odds != null && timestamp != null && StrUtil.isNotBlank(team)){
              log.info("http-team=[{}],odds_num={},odds_id={}",team,odds_num,odds_id);
              influxDBService.insertOddsData(team,status,type,odds_num,timestamp/1000);
            }
          }
        }
      }
    }else{
      log.debug(appVoteTestPrintlnReqVO.toString());
    }


    return success("true");
  }

  @PostMapping("/winner")
  @Operation(summary = "记录胜利者")
  /*
  默认情况下，表单数据或查询参数的绑定不需要 @RequestBody：
  使用 JSON 格式传输数据时，通常需要 @RequestBody：
   */
  public CommonResult<List<RaybetWinnerDO>> winner(@RequestHeader(value = "User-Agent") String userAgent ,  @RequestBody AppVoteTestPrintlnReqVO appVoteTestPrintlnReqVO) {
    String url = appVoteTestPrintlnReqVO.getUrl();
    List<RaybetWinnerDO> resList = new ArrayList<>();
    if(StrUtil.contains(url,"/v2/odds?match_id=")){
      //链式构建请求
      String body = doHttp(userAgent, url);
      JSONObject respJson = new JSONObject(body);
      JSONArray odds = respJson.getByPath("result.odds", JSONArray.class);
      JSONObject game1Result = respJson.getByPath("result.team[0]", JSONObject.class);
      JSONObject game2Result = respJson.getByPath("result.team[1]", JSONObject.class);
      // 对game1进行排序操作。
      {
        Map<String,JSONObject> map = new HashMap<>();
        String team1NameTmp = game1Result.getStr("team_name");
        map.putIfAbsent(team1NameTmp,game1Result);
        String team2NameTmp = game2Result.getStr("team_name");
        map.putIfAbsent(team2NameTmp,game2Result);
        ArrayList<String> list = ListUtil.toList(map.keySet());
        List<String> keys = ListUtil.sortByPinyin(list);
        game1Result = map.get(keys.get(0));
        game2Result = map.get(keys.get(1));
      }

      // 插入比赛的基本情况信息。
      RaybetMatchDO raybetMatchDO = respJson.getBean("result", RaybetMatchDO.class);
      String gameName = raybetMatchDO.getGameName();
      LocalDateTime endTime = raybetMatchDO.getEndTime();
      Map<String,Double> teamMap = new HashMap<>();
      if(endTime!=null &&  LocalDateTime.now().isAfter(endTime)){
        for (int i = 0; i < odds.size(); i++) {
          JSONObject odd = odds.getJSONObject(i);
          String groupName = odd.getStr("group_name");   // 地图获胜者率先获得十杀  一杀 五杀
          if(isWinnerFlg(groupName)){
            String matchId = odd.getStr("match_id");
            String matchStage = odd.getStr("match_stage"); // 全场，第一场。。。
            String name = odd.getStr("name"); // 名称
            Double odds_num = odd.getDouble("odds"); //
            String team = StrUtil.join("-", matchId, matchStage, name);
            teamMap.put(team,odds_num);
          }
        }
      }
      // keys 中排序之后 用数组 i 和 i+1 来操作。获取。
      ArrayList<String> list = ListUtil.toList(teamMap.keySet());
      List<String> keys = ListUtil.sort(list, (o1,o2) ->{
          if (StrUtil.contains(o1,"-final-")) {
            return -1; // s1 是特殊字符串，s2 不是，s1 排在前面
          } else if (StrUtil.contains(o2,"-final-")) {
            return 1; // s1 不是特殊字符串，s2 是，s2 排在前面
          } else {
            // ListUtil#sortByPinyin
            return collator.compare(o1, o2); // 其他情况按自然顺序排序
          }
      });
      for (int i = 0; i < keys.size(); i+=2) {
        String team1_id = keys.get(i);
        String team2_id = keys.get(i+1);
        Double odds1 = teamMap.get(team1_id);
        Double odds2 = teamMap.get(team2_id);

        // 设置内容
        CharSequence match_id = StrUtil.commonPrefix(team1_id, team2_id);
        String winner = "team1";

        String matchIdString = match_id.toString();
        if (raybetWinnerMapper.selectById(matchIdString) == null) {
          RaybetWinnerDO winnerDO = new RaybetWinnerDO();
          winnerDO.setMatchId(matchIdString);
          String matchStr = StrUtil.split(matchIdString, "-").get(0);
          winnerDO.setMatchFork(Long.parseLong(matchStr));
          winnerDO.setTeam1Id(team1_id);
          winnerDO.setTeam2Id(team2_id);
          winnerDO.setRemark(odds1 + ":" + odds2);
          // 最终的比赛内容。
          int teamCount = i/2;
          if(teamCount == 0){
            // 队伍2的得分比较高
            Integer game1total = game1Result.getByPath("score.total", Integer.class);
            Integer game2total = game2Result.getByPath("score.total", Integer.class);
            if (game1total < game2total ) {
              winner = "team2";
            }
            winnerDO.setWinner(winner);
            winnerDO.setStatus(0);
            //
            raybetMatchDO.setScore( game1total + ":" + game2total);
            raybetMatchMapper.updateById(raybetMatchDO);
//            raybetMatchDO
          }else{
            // 普通的比赛
            if (StrUtil.containsAny(gameName, "DOTA2", "英雄联盟" , "王者荣耀" , "CS2")){
              Integer scoreFlag1 = game1Result.getByPath("score.r"+teamCount, Integer.class);
              Integer scoreFlag2 = game2Result.getByPath("score.r"+teamCount, Integer.class);
              winnerDO.setStatus(0);
              if(scoreFlag1 == 0 && scoreFlag2 ==0 ){
                winnerDO.setStatus(1);
              }else if(scoreFlag2 ==1 ){
                winner = "team2";
              }
              winnerDO.setWinner(winner);
            }else{
              if (odds1 > odds2) {
                winner = "team2";
              }
              // 保持原来，用赔率获得比赛
              winnerDO.setWinner(winner);
              if(odds1>3 || odds2>3){
                winnerDO.setStatus(0);
              }else{
                winnerDO.setStatus(1);
              }
            }
          }




          raybetWinnerMapper.insert(winnerDO);
          resList.add(winnerDO);
        }

      }
    } else{
      log.debug(appVoteTestPrintlnReqVO.toString());
    }
    return success(resList);
  }

  /**
   * 判断获胜者的标识内容。
   * @param groupName
   * @return
   */
  private static boolean isWinnerFlg(String groupName) {
//    return StrUtil.contains(groupName, "获胜者") && !StrUtil.startWith(groupName, "地图获胜者率先");
    return StrUtil.equalsAny(groupName, "获胜者","获胜者(含加时)");
  }

  /**
   * 通过定时任务轮询，刷新缓存
   *
   * 目的：多节点部署时，通过轮询”通知“所有节点，进行刷新
   */
  @Scheduled(initialDelay = 3, fixedRate = 200, timeUnit = TimeUnit.SECONDS)
  public void refreshLocalCache() {
    if(!raybetEnable) return;
    String body = doHttp(USER_AGENT,
            "https://cfinfo.365raylinks.com/v2/match?page=1&match_type=0");
    JSONObject respJson = new JSONObject(body);
    JSONArray odds = respJson.getJSONArray("result");
    LocalDateTime now = LocalDateTime.now();
    // 创建一个表示一小时的Duration对象
    for (int i = 0; i < odds.size(); i++) {
      RaybetMatchDO raybetMatchDO = odds.getBean(i, RaybetMatchDO.class);
      LocalDateTime startTime = raybetMatchDO.getStartTime();
      LocalDateTime endTime = raybetMatchDO.getEndTime();
//
      String gameName = raybetMatchDO.getGameName();
//      if (StrUtil.containsAny(gameName,"英雄联盟","CS2","王者荣耀","DOTA2")) {
      if (true) {
        // 检查startTime是否在一个小时内
        long startBetween = LocalDateTimeUtil.between(now, startTime, ChronoUnit.SECONDS);
        if ( startBetween <= 60 * 60 * 1 ) {
          long endSeconds = LocalDateTimeUtil.between(now, endTime, ChronoUnit.SECONDS);
          // 注意：忽略自动多租户，因为要全局初始化缓存
          if(SystemUtil.getOsInfo().isLinux()){
            TenantUtils.executeIgnore(() -> {
              // 执行某些指令
              Long id = raybetMatchDO.getId();
              if (raybetMatchMapper.selectById(id) == null) {
                System.out.println(RuntimeUtil.execForStr( "sh" ,"/home/<USER>/project/raybet/scripts/docker-log.sh",  id+""));
              }
            });
          }
        }
      }

    }
  }

  /**
   * 定时缓存
   *
   * 目的：多节点部署时，通过轮询”通知“所有节点，进行刷新
   */
  @Scheduled(initialDelay = 5, fixedRate = 60 * 125, timeUnit = TimeUnit.SECONDS)
  public void loadMatch() {
    if(!raybetEnable) return;
    TenantUtils.executeIgnore(() -> {
      // 执行某些指令
      // 获取当前时间
//      Date now = new Date();
//
//      // 计算一天前的时间
//      Date oneDayAgo = new Date(now.getTime() - 24 * 60 * 60 * 1000L); // 减去一天
//
      // 创建查询包装器
      LambdaQueryWrapperX<RaybetMatchDO> queryWrapperX = new LambdaQueryWrapperX<>();
      LocalDateTime now = LocalDateTime.now();

      LocalDateTime lastNow = now.minusDays(1);
      List<RaybetMatchDO> raybetMatchDOS = raybetMatchMapper.selectList(queryWrapperX
              .between(RaybetMatchDO::getEndTime, lastNow, now));

      //
      System.out.println(raybetMatchDOS);
      // 遍历之后，查询winner是否存在，不存在就调用。
      for (RaybetMatchDO matchDO : raybetMatchDOS) {

        Long id = matchDO.getId();
        LambdaQueryWrapperX<RaybetWinnerDO> wrapperX = new LambdaQueryWrapperX<>();
        wrapperX.eq(RaybetWinnerDO::getMatchFork,id);
        if (!raybetWinnerMapper.exists(wrapperX)) {
          AppVoteTestPrintlnReqVO appVoteTestPrintlnReqVO = new AppVoteTestPrintlnReqVO();
          appVoteTestPrintlnReqVO.setUrl("https://ydinfo.esportsgameinforay.com/v2/odds?match_id="+id);
          this.winner(USER_AGENT,appVoteTestPrintlnReqVO);
          // 随机执行一下。不要被封ip啦。
          ThreadUtil.sleep(RandomUtil.randomInt(0,10),TimeUnit.SECONDS);
        }
        // 
      }
    });
  }
  private static String doHttp(String userAgent, String url) {
    return HttpRequest.get(url)
            .header(Header.USER_AGENT, userAgent)//头信息，多个头信息多次调用此方法即可
            .header(Header.ORIGIN, "https://www.raybet.lol")//头信息，多个头信息多次调用此方法即可
            .header(Header.PRAGMA, "no-cache")//头信息，多个头信息多次调用此方法即可
            .cookie("session_id=" + UUID.randomUUID().toString())
            .timeout(120000)//超时，毫秒
            .execute().body();
  }

  public static void main(String[] args) {
    System.out.println(RaytwoTeamsOddEnum.ONE.equals(RaytwoTeamsOddEnum.ONE));
    System.out.println(RaytwoTeamsOddEnum.ONE.equals("team1"));
    List<String> winners = new ArrayList<>();
    winners.add("38044935-0final-");
    winners.add("38044935-final-");
    winners.add("38044935-r1-");
    winners.add("38044935-0r2-");
    ListUtil.sort(winners,  (o1,o2) ->{
      if (StrUtil.contains(o1,"-final-")) {
        return -1; // s1 是特殊字符串，s2 不是，s1 排在前面
      } else if (StrUtil.contains(o2,"-final-")) {
        return 1; // s1 不是特殊字符串，s2 是，s2 排在前面
      } else {
        return collator.compare(o1, o2); // 其他情况按自然顺序排序
      }
    }  );
    System.out.println(winners);

    System.out.println(StrUtil.replace("38043196-00final-", "00final", "final"));
    List<String> list = Arrays.asList("38040379-final-","38040379-r1-","38040379-map1-", "38040379-0final-","38040379-afinal-","38040379-1st-","38040379-q1-");
    List<String> keys = ListUtil.sortByPinyin(list);
    System.out.println(keys);
    System.out.println(3/2 + "" + 2/2  );
    //链式构建请求
//    String result2 = HttpRequest.get("https://iminfo.esportsworldlink.com/v2/odds?match_id=38026108")
//            .header(Header.USER_AGENT, "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/128.0.0.0 Safari/537.36")//头信息，多个头信息多次调用此方法即可
//            .header(Header.ORIGIN, "https://www.raybet.lol")//头信息，多个头信息多次调用此方法即可
//            .header(Header.PRAGMA, "no-cache")//头信息，多个头信息多次调用此方法即可
////            .form(paramMap)//表单内容
//            .cookie("nlbi_1702036=ujMAMFmdu02ZTZ6XzNuCHgAAAACNKplcR/nVCHO5qjqjudcu;incap_ses_138_1702036=yUVPcNlnIVpxa8mGTkbqAZ6izmYAAAAAklvJeiQR/yHiKcaQb0fWoA==;visid_incap_1702036=BQIYl3IJQcOozEcgHdRZKp6izmYAAAAAQUIPAAAAAADqZHiXqqwvEzVxTYowohEW")
//            .timeout(20000)//超时，毫秒
//            .execute().body();
//    Console.log(result2);
//    String response = HttpUtil.get();
//    System.out.println(response);
  }

  public static void read(ExcelReader reader,int colIndex,String suf){
    List<Object> zisiall = reader.readColumn(colIndex, 1);
    zisiall.forEach(x->{
      if(StrUtil.isNotBlank(x.toString())){
        System.out.print("\"" + suf + x  + "\" ");
      }
    });
  }
}



