package cn.iocoder.yudao.module.vote.dal.dataobject.macaujc;

import lombok.*;
import java.util.*;
import java.time.LocalDateTime;
import java.time.LocalDateTime;
import java.time.LocalDateTime;
import com.baomidou.mybatisplus.annotation.*;
import cn.iocoder.yudao.framework.mybatis.core.dataobject.BaseDO;

/**
 * 彩票 DO
 *
 * <AUTHOR>
 */
@TableName("vote_macaujc")
@KeySequence("vote_macaujc_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class MacaujcDO extends BaseDO {

    /**
     * 期数
     */
    @TableId
    private Integer expect;
    /**
     * 开奖号码
     */
    private String openCode;
    /**
     * 生肖
     */
    private String zodiac;
    /**
     * 波色
     */
    private String wave;
    /**
     * 开奖时间
     */
    private LocalDateTime openTime;
    /**
     * 类型
     */
    private String kind;

}