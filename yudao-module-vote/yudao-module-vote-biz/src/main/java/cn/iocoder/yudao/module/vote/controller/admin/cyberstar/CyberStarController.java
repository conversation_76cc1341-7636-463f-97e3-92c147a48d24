package cn.iocoder.yudao.module.vote.controller.admin.cyberstar;

import org.springframework.web.bind.annotation.*;
import javax.annotation.Resource;
import org.springframework.validation.annotation.Validated;
import org.springframework.security.access.prepost.PreAuthorize;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.Operation;

import javax.validation.constraints.*;
import javax.validation.*;
import javax.servlet.http.*;
import java.util.*;
import java.io.IOException;

import cn.iocoder.yudao.framework.common.pojo.PageParam;
import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.common.pojo.CommonResult;
import cn.iocoder.yudao.framework.common.util.object.BeanUtils;
import static cn.iocoder.yudao.framework.common.pojo.CommonResult.success;

import cn.iocoder.yudao.framework.excel.core.util.ExcelUtils;

import cn.iocoder.yudao.framework.apilog.core.annotation.ApiAccessLog;
import static cn.iocoder.yudao.framework.apilog.core.enums.OperateTypeEnum.*;

import cn.iocoder.yudao.module.vote.controller.admin.cyberstar.vo.*;
import cn.iocoder.yudao.module.vote.dal.dataobject.cyberstar.CyberStarDO;
import cn.iocoder.yudao.module.vote.service.cyberstar.CyberStarService;

@Tag(name = "管理后台 - 网红主播")
@RestController
@RequestMapping("/vote/cyber-star")
@Validated
public class CyberStarController {

    @Resource
    private CyberStarService cyberStarService;

    @PostMapping("/create")
    @Operation(summary = "创建网红主播")
    @PreAuthorize("@ss.hasPermission('vote:cyber-star:create')")
    public CommonResult<Long> createCyberStar(@Valid @RequestBody CyberStarSaveReqVO createReqVO) {
        return success(cyberStarService.createCyberStar(createReqVO));
    }

    @PutMapping("/update")
    @Operation(summary = "更新网红主播")
    @PreAuthorize("@ss.hasPermission('vote:cyber-star:update')")
    public CommonResult<Boolean> updateCyberStar(@Valid @RequestBody CyberStarSaveReqVO updateReqVO) {
        cyberStarService.updateCyberStar(updateReqVO);
        return success(true);
    }

    @DeleteMapping("/delete")
    @Operation(summary = "删除网红主播")
    @Parameter(name = "id", description = "编号", required = true)
    @PreAuthorize("@ss.hasPermission('vote:cyber-star:delete')")
    public CommonResult<Boolean> deleteCyberStar(@RequestParam("id") Long id) {
        cyberStarService.deleteCyberStar(id);
        return success(true);
    }

    @GetMapping("/get")
    @Operation(summary = "获得网红主播")
    @Parameter(name = "id", description = "编号", required = true, example = "1024")
    @PreAuthorize("@ss.hasPermission('vote:cyber-star:query')")
    public CommonResult<CyberStarRespVO> getCyberStar(@RequestParam("id") Long id) {
        CyberStarDO cyberStar = cyberStarService.getCyberStar(id);
        return success(BeanUtils.toBean(cyberStar, CyberStarRespVO.class));
    }

    @GetMapping("/page")
    @Operation(summary = "获得网红主播分页")
    @PreAuthorize("@ss.hasPermission('vote:cyber-star:query')")
    public CommonResult<PageResult<CyberStarRespVO>> getCyberStarPage(@Valid CyberStarPageReqVO pageReqVO) {
        PageResult<CyberStarDO> pageResult = cyberStarService.getCyberStarPage(pageReqVO);
        return success(BeanUtils.toBean(pageResult, CyberStarRespVO.class));
    }

    @GetMapping("/export-excel")
    @Operation(summary = "导出网红主播 Excel")
    @PreAuthorize("@ss.hasPermission('vote:cyber-star:export')")
    @ApiAccessLog(operateType = EXPORT)
    public void exportCyberStarExcel(@Valid CyberStarPageReqVO pageReqVO,
              HttpServletResponse response) throws IOException {
        pageReqVO.setPageSize(PageParam.PAGE_SIZE_NONE);
        List<CyberStarDO> list = cyberStarService.getCyberStarPage(pageReqVO).getList();
        // 导出 Excel
        ExcelUtils.write(response, "网红主播.xls", "数据", CyberStarRespVO.class,
                        BeanUtils.toBean(list, CyberStarRespVO.class));
    }

}