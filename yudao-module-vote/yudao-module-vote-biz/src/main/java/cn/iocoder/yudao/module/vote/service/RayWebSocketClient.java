package cn.iocoder.yudao.module.vote.service;
import org.java_websocket.client.WebSocketClient;
import org.java_websocket.handshake.ServerHandshake;

import java.net.URI;
import java.net.URISyntaxException;
// websocket连接操作处理。
public class RayWebSocketClient extends WebSocketClient {

    public RayWebSocketClient(URI serverUri) {
        super(serverUri);
    }

    @Override
    public void onOpen(ServerHandshake handshake) {
        System.out.println("Connected to WebSocket server");
    }

    @Override
    public void onMessage(String message) {
        System.out.println("Received message: " + message);
    }

    @Override
    public void onClose(int code, String reason, boolean remote) {
        System.out.println("Connection closed, code: " + code + ", reason: " + reason);
        // 重连逻辑
        System.out.println("Attempting to reconnect...");
        reconnect();
    }

    @Override
    public void onError(Exception ex) {
        System.err.println("WebSocket error: " + ex.getMessage());
    }

    public static void main(String[] args) {
        try {
            URI serverUri = new URI("wss://ydsocket.esportsgameinforay.com/socketcluster/"); // 替换为目标 WebSocket URL
            RayWebSocketClient client = new RayWebSocketClient(serverUri);
            client.connect();

            // 保持主线程活跃以持续监听消息
            while (true) {
                Thread.sleep(10000); // 每10秒保持一次心跳
                client.send("1");
            }
        } catch (URISyntaxException | InterruptedException e) {
            e.printStackTrace();
        }
    }
}
