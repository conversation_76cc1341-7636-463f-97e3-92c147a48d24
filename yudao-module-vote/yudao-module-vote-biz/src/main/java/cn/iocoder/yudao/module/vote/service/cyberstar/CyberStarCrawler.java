package cn.iocoder.yudao.module.vote.service.cyberstar;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.io.IoUtil;
import cn.hutool.core.thread.ThreadUtil;
import cn.hutool.core.util.ArrayUtil;
import cn.hutool.core.util.NumberUtil;
import cn.hutool.core.util.RandomUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.extra.template.Template;
import cn.hutool.extra.template.TemplateEngine;
import cn.hutool.extra.template.TemplateUtil;
import cn.hutool.http.HttpRequest;
import cn.hutool.json.JSONArray;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import cn.iocoder.yudao.framework.common.core.CounterMap;
import cn.iocoder.yudao.framework.common.util.collection.MergeUtils;
import cn.iocoder.yudao.framework.tenant.core.util.TenantUtils;
import cn.iocoder.yudao.module.vote.controller.admin.cyberstar.vo.CyberStarSaveReqVO;
import cn.iocoder.yudao.module.vote.dal.dataobject.cyberstar.CyberStarDO;
import com.microsoft.playwright.*;
import com.microsoft.playwright.options.Cookie;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;
import org.jetbrains.annotations.NotNull;
import org.springframework.boot.CommandLineRunner;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;
import org.springframework.validation.annotation.Validated;

import javax.annotation.Resource;
import java.time.Duration;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.TimeUnit;
import java.util.function.Function;
import java.util.stream.Collectors;

import static cn.hutool.core.date.DatePattern.NORM_DATE_PATTERN;
import static cn.iocoder.yudao.framework.common.util.date.DateUtils.getDateRange;

/**
 * 网红主播 爬虫 类 获取里面的数据。
 * <AUTHOR>
 */
//@Service
@Validated
@Slf4j
public class CyberStarCrawler implements CommandLineRunner {
    public static final int YESTERDAY_OFFSET = -1;
    public static final CounterMap<String> DIVE_COUNT = new CounterMap(new HashMap<String, Integer>(), 0);
    public static final int EXPIRE_DAYS = 70;

    /**
     * 内存缓存：主播ID -> 热度值（整数）
     * 包含礼物金额、商品金额和综合热度值
     */
    @Getter
    private final Map<Long, Long> hotAmountMap = new ConcurrentHashMap<>();
    @Getter
    private final Map<Long, Long> hotAmountMapPage19 = new ConcurrentHashMap<>();

    @Resource
    private CyberStarService cyberStarService;
    @Resource
    private StringRedisTemplate stringRedisTemplate;

    @Override
    public void run(String... args) throws Exception {
        this.reconciliation();
        // 实时爬取数据
        ThreadUtil.execute(()->{
                    while(validity()){
                        try {
                            this.realTime();
                        } catch (Throwable e) {
                            log.error("Error in realTime", e);
                        }
                    }
        });
    }



    /**
     * 用于对账
     */
//    @Scheduled(cron = "0 0 3,5,7 * * ?")
    public void reconciliation() {
        if(validity()){

        }
    }
    /**
     * 实时爬取数据
     *
     * expire = 600: 锁的过期时间10分钟，考虑到爬取数据可能需要较长时间
     * acquireTimeout = 1: 获取锁的超时时间1秒，如果获取不到锁则跳过执行
     */
    public void realTime() {
        TenantUtils.executeIgnore(()->{
            List<CyberStarDO> starServiceAll = cyberStarService.getAll();
            List<CyberStarSaveReqVO> storeGiftList = this.storeGift(starServiceAll);
            List<CyberStarSaveReqVO> storeGmvList =  callBack(starServiceAll,this::storeGmv);

            // 合并storeGiftList 和 storeGmvList，将重复id里面修改的内容都放到一个对象里面，没有重复的就添加
            List<CyberStarSaveReqVO> mergedList = MergeUtils.mergeLists(
                    storeGiftList,
                    storeGmvList,
                    CyberStarSaveReqVO::getId,
                    (existingItem, newItem) -> {
                        // 合并属性：如果newItem有gmvTodayAmountDouble属性，则设置到existingItem
                        if (newItem.getGmvTodayAmountDouble() != null) {
                            existingItem.setGmvTodayAmountDouble(newItem.getGmvTodayAmountDouble());
                        }
                        return existingItem;
                    }
            );

            if (!mergedList.isEmpty()) {
//                cyberStarService.updateBatch(mergedList);
            }

            // 计算并更新内存缓存
            // 18号的数据
//            calculateAndUpdateCache(starServiceAll, Collections.singletonList("2025-05-18"),hotAmountMap);
            // 19号-22号的数据情况 39,40,75,27,30        19,5,24,76,25
//            long [] star40 = {1,2,3,4,5,6,7,8,9,10,11,12,13,15,16,17,18,19,20,21,22,23,24,25,26,27,29,30,31,32,34,39,40,43,44,55,71,74,75,76};
//            long [] star40 = {1,2,3,4,6,7,8,9,10,11,12,13,15,16,17,18,20,21,22,23,26,29,31,32,34,43,44,55,71,74};
            List<CyberStarDO> starDOList40 = null;
            starDOList40 = getFilterCyberStarDOS(starServiceAll, new long[]{11,55,10,17,9,21});
            calculateAndUpdateCache(starDOList40, getDateRange("2025-06-08", "2025-06-08", NORM_DATE_PATTERN),hotAmountMapPage19);
            starDOList40 = getFilterCyberStarDOS(starServiceAll, new long[]{44,20,16,43,71,31});
            calculateAndUpdateCache(starDOList40, getDateRange("2025-06-09", "2025-06-09", NORM_DATE_PATTERN),hotAmountMapPage19);
            starDOList40 = getFilterCyberStarDOS(starServiceAll, new long[]{22,8,12,34,15,13});
            calculateAndUpdateCache(starDOList40, getDateRange("2025-06-10", "2025-06-10", NORM_DATE_PATTERN),hotAmountMapPage19);
            starDOList40 = getFilterCyberStarDOS(starServiceAll, new long[]{3,18,74,32,6,26});
            calculateAndUpdateCache(starDOList40, getDateRange("2025-06-11", "2025-06-11", NORM_DATE_PATTERN),hotAmountMapPage19);
            starDOList40 = getFilterCyberStarDOS(starServiceAll, new long[]{1,2,7,4,29,23});
            calculateAndUpdateCache(starDOList40, getDateRange("2025-06-12", "2025-06-12", NORM_DATE_PATTERN),hotAmountMapPage19);

            List<CyberStarDO> starDOList12 = null;
            starDOList12 = getFilterCyberStarDOS(starServiceAll, new long[]{20,55,11,3,10,30});
            calculateAndUpdateCache(starDOList12, getDateRange("2025-06-15", "2025-06-15", NORM_DATE_PATTERN),hotAmountMap);
            starDOList12 = getFilterCyberStarDOS(starServiceAll, new long[]{6,29,1,4,7,23});
            calculateAndUpdateCache(starDOList12, getDateRange("2025-06-16", "2025-06-16", NORM_DATE_PATTERN),hotAmountMap);
        });

    }

    @NotNull
    private static List<CyberStarDO> getFilterCyberStarDOS(List<CyberStarDO> starServiceAll, long[] star40) {
        List<CyberStarDO> starDOList40 = starServiceAll.stream().filter(x -> ArrayUtil.contains(star40, x.getId())).collect(Collectors.toList());
        return starDOList40;
    }

    /**
     * 从hotAmountMap中获得热度值排名前40名的主播对应的 CyberStarDO 对象
     *
     * @param all 所有主播列表
     * @return 热度值排名前40的主播列表
     */
    public List<CyberStarDO> getTop40HotAmountMap(List<CyberStarDO> all){
        if (CollUtil.isEmpty(all) || hotAmountMap.isEmpty()) {
            return Collections.emptyList();
        }

        // 创建主播ID到主播对象的映射，方便后续查找
        Map<Long, CyberStarDO> starMap = new HashMap<>();
        for (CyberStarDO star : all) {
            starMap.put(star.getId(), star);
        }

        // 对hotAmountMap按热度值降序排序
        List<Map.Entry<Long, Long>> sortedEntries = new ArrayList<>(hotAmountMap.entrySet());
        sortedEntries.sort((e1, e2) -> Long.compare(e2.getValue(), e1.getValue()));

        // 取前40名，并获取对应的CyberStarDO对象
        int limit = Math.min(40, sortedEntries.size());
        List<CyberStarDO> result = new ArrayList<>(limit);

        for (int i = 0; i < limit; i++) {
            Long starId = sortedEntries.get(i).getKey();
            CyberStarDO star = starMap.get(starId);
            if (star != null) {
                result.add(star);
            }
        }

        return result;
    }


    /**
     * 计算并更新内存缓存
     *
     * @param stars 所有主播列表
     */
    private void calculateAndUpdateCache(List<CyberStarDO> stars , List<String> todays, Map<Long, Long> map ) {
        // 检查是否需要计算（避免频繁计算）

        try {
            log.info("开始计算并更新内存缓存...");

            // 清空缓存
//            map.clear();

            // 获取当前日期
//            String today =  "2025-05-18"; //formatDate(new Date());

            // 遍历所有主播
            for (CyberStarDO star : stars) {
                if(star.getType() == null) continue; // 跳过类型为空的数据

                Long cyberStarId = star.getId();
                // 计算热度值
                long hotAmount = 0;
                Map<String,Double> gittTmpMap = new HashMap<>();
                // todays
                for (String today : todays) {
                    // 计算礼物金额
                    String giftRedisKey = "vote:cyber_star:gift:" + cyberStarId + ":" + today;
                    calculateTotalIncomeMap(giftRedisKey,gittTmpMap);
                    double giftAmount = calculateTotalIncome(giftRedisKey);
                    // 计算商品金额
                    String gmvRedisKey = "vote:cyber_star:gmv:" + cyberStarId + ":" + today;
                    String gmvValue = stringRedisTemplate.opsForValue().get(gmvRedisKey);
                    double gmvAmount = NumberUtil.parseDouble(gmvValue, 0.0);


                    String gmvSubRedisKey = "vote:cyber_star:gmvSub:" + cyberStarId + ":" + today;
                    String gmvSubValue = stringRedisTemplate.opsForValue().get(gmvSubRedisKey);
                    double gmvSubAmount = NumberUtil.parseDouble(gmvSubValue, 0.0);


                    if(star.getType() == 0){
                        hotAmount += (long)  (gmvAmount - gmvSubAmount);
                    }else if (star.getType() == 2){ // 手工修改
                        hotAmount += (long) (giftAmount * 10);
                    }
                }
                //
                if(star.getType() == 1){
                    hotAmount = (long) (gittTmpMap.values().stream().mapToDouble(Double::doubleValue).sum() * 10);
                }


                // 放入map缓存里面去。
                map.put(cyberStarId, hotAmount);
            }

            log.info("内存缓存更新完成，共处理 {} 个主播", stars.size());
        } catch (Exception e) {
            log.error("计算并更新内存缓存时发生错误", e);
        }
    }




    private void calculateAndUpdateCache0606(List<CyberStarDO> stars , List<String> todays, Map<Long, Long> map ) {
        // 检查是否需要计算（避免频繁计算）

        try {
            log.info("开始计算并更新内存缓存...0606");

            // 清空缓存
//            map.clear();

            // 获取当前日期
//            String today =  "2025-05-18"; //formatDate(new Date());

            // 遍历所有主播
            for (CyberStarDO star : stars) {
                if(star.getType() == null) continue; // 跳过类型为空的数据

                Long cyberStarId = star.getId();
                // 计算热度值
                long hotAmount = 0;
                // todays
                for (String today : todays) {
                    // 计算礼物金额
                    String giftRedisKey = "vote:cyber_star:gift:" + cyberStarId + ":" + today;
                    double giftAmount = calculateTotalIncome(giftRedisKey);
                    hotAmount += giftAmount;
                }
                // 放入map缓存里面去。
                map.put(cyberStarId, hotAmount);
                if(cyberStarId == 71){
                    map.put(54L, hotAmount);
                }
            }

            log.info("0606内存缓存更新完成，共处理 {} 个主播", stars.size());
        } catch (Exception e) {
            log.error("0606计算并更新内存缓存时发生错误", e);
        }
    }


    public List<CyberStarSaveReqVO> storeGift(List<CyberStarDO> starServiceAll){
        Map<String,CyberStarDO> map = new HashMap<>();
        CollUtil.toMap(starServiceAll,map,CyberStarDO::getUserId);
        List<CyberStarSaveReqVO> list = new ArrayList<>();
        try{
            String cookie =  this.stringRedisTemplate.opsForValue().get("vote:cyber_star:crawler:gift") ; //
            if(StrUtil.isEmpty(cookie)) cookie = "didv=1745146141000; kwpsecproductname=PCLive; did=web_89b044ef3a7f34c9cb9ed9145063ef23d818; kwfv1=PnGU+9+Y8008S+nH0U+0mjPf8fP08f+98f+nLlwnrIP9P9G98YPf8jPBQSweS0+nr9G0mD8B+fP/L98/qlPe4f8e+D8ncIPBQf808S+/DFPe4Y8nGIP0LlGfc7G/PlGf+j8eP7+fPF+fH98/GFP/Z9+nLhP0qAP/Qf+fPE8nbfPBc=; bUserId=1000008388866; userId=2329492732; kuaishou.web.cp.api_st=ChZrdWFpc2hvdS53ZWIuY3AuYXBpLnN0ErABRsU2D-HdweP9QtaqQMJClY__qrrhawZiIO6hC4AM9jJQo9yJu3TF5HUY1JO9ZvO7uIJFSTHF5YRdbKjSzUYDtLwrn4Nk4qktWkKS10UtyjAgdbFlsdo5LRY0YKZIujUgvxgWXzLeobc5ssLFatjihXLxbrpLwOByWEWHIWL9u4sZsdXwDLpmmsLS9AiCqYxOANqmbmfZX6NjkDn920dm94pL4LyoF9M6YpIf1XFZa80aEupCKNegFFkJbfdZdBO64A9x1SIgBAG8FZYd-ew2oogmKhuy3mndAgVjYbzGxgjQkk9rhpcoBTAB; kuaishou.web.cp.api_ph=c662f411293d57b830bca9bcbed514dfaa80";
            String requestBody = "{\"page\":1,\"count\":900,\"liveStreamType\":0,\"violationStatus\":0,\"sortKey\":\"sumValidIncome\",\"sortType\":2,\"kuaishou.web.cp.api_ph\":\"18b179f9af3e1f8a44e80c5a9f3a32f978e8\"}";
            // 链式构建请求
            String result = HttpRequest.post("https://666.kuaishou.com/rest/live/mcn/live/monitor/list")
                    .header("Cookie", cookie)//头信息，多个头信息多次调用此方法即可
                    .body(requestBody)//表单内容
                    .timeout(20000)//超时，毫秒
                    .execute().body();
            JSONObject resultJson = new JSONObject(result);
            JSONArray jsonArray = resultJson.getByPath("data.list", JSONArray.class);

            // 获取当前日期
            String today = formatDate(new Date());

            // 当前时间，用于判断是否在跨天时间段（0点到0点5分）
            boolean isMidnightPeriod = isIsMidnightPeriod();

            for (int i = 0; i < jsonArray.size(); i++) {
                JSONObject otherJsonObject = jsonArray.getJSONObject(i);
                String userId = otherJsonObject.getByPath("userInfo.userId", String.class);
                String commodityValue = otherJsonObject.getStr("sumValidIncome");
                // 处理包含"万"字的金额字符串，如"31.0万"转换为"310000"
                if(StrUtil.contains(commodityValue,"万")){
                    try {
                        // 去掉"万"字
                        String numStr = StrUtil.removeSuffix(commodityValue, "万");
                        // 转换为数字并乘以10000
                        double num = NumberUtil.parseDouble(numStr) * 10000;
                        // 转回字符串
                        commodityValue = String.valueOf(num);
                    } catch (Exception e) {
                        log.error("处理'万'字金额失败: {}", commodityValue, e);
                    }
                }
                String liveStreamId = otherJsonObject.getStr("liveStreamId");
                Long startTime = otherJsonObject.getLong("startTime"); // 开始时间戳
                CyberStarDO cyberStar = map.get(userId);

                if (cyberStar != null && amountIsOk(commodityValue)) {
                    double income = Double.parseDouble(commodityValue);
                    Long cyberStarId = cyberStar.getId();

                    // 构建Redis的key: cyberStarId+today
                    String redisKey = "vote:cyber_star:gift:" + cyberStarId + ":" + today;

                    // 判断是否是跨天直播
                    boolean isCrossDayLive = false;
                    if (startTime != null) {
                        Date startDate = new Date(startTime);
                        String startDateStr = formatDate(startDate);
                        // 如果开始时间不是今天，则认为是跨天直播
                        isCrossDayLive = !today.equals(startDateStr);
                    }

                    if (isMidnightPeriod && isCrossDayLive) {
                            // 如果是跨天时间段（0点到0点5分）且是跨天直播
                            // 检查昨天的记录中是否已有该直播间的数据
                            Object yesterdayObject = stringRedisTemplate.opsForHash().get(redisKey, "yesterday");
                            String yesterdayValue = yesterdayObject != null ?
                                    yesterdayObject.toString() : null;

                            if (yesterdayValue == null) {
                                // 如果昨天没有记录，则将当前数据作为负值存入昨天的记录
                                stringRedisTemplate.opsForHash().put(redisKey, "yesterday", String.valueOf(-income));
                            }
                    }

                    // 修改Redis内容
                    updateGift(cyberStarId,  liveStreamId, income);

                    // 为了兼容现有逻辑，仍然更新数据库中的字段
                    double totalIncome = calculateTotalIncome(redisKey);
                    Double giftTodayAmountDouble = cyberStar.getGiftTodayAmountDouble();
                    if (giftTodayAmountDouble < totalIncome) {
                        list.add(new CyberStarSaveReqVO().setId(cyberStarId).setGiftTodayAmountDouble(totalIncome));
                    }
                } else if (cyberStar != null) {
                    log.error("gift-amount-is-error : {}, {}", cyberStar.getNickname(), commodityValue);
                }
            }
        } catch (Throwable e) {
            log.error("Error in storeGift", e);
        }
        return list;
    }

    /**
     * 判断是否是午夜时间段
     * @return
     */
    private boolean isIsMidnightPeriod() {
        Calendar calendar = Calendar.getInstance();
        int hour = calendar.get(Calendar.HOUR_OF_DAY);
        int minute = calendar.get(Calendar.MINUTE);
        boolean isMidnightPeriod = hour == 0 && minute < 5;
        return isMidnightPeriod;
    }
    public boolean updateGift(Long cyberStarId,  String liveStreamId, double income) {
        // 获取当前日期
        String today = formatDate(new Date());
        return updateGift(cyberStarId,today,liveStreamId,income,false);
    }
    public boolean updateGift(Long cyberStarId,String today,  String liveStreamId, double income , boolean forceUpdate) {
        String redisKey = "vote:cyber_star:gift:" + cyberStarId + ":" + today;
        Calendar calendar = Calendar.getInstance();
        int hour = calendar.get(Calendar.HOUR_OF_DAY);
        // 将当前直播场次的数据存入Redis
        double old_income = NumberUtil.parseDouble(StrUtil.toStringOrNull(stringRedisTemplate.opsForHash().get(redisKey, liveStreamId)));
        if( (hour > 21 && old_income < income ) || forceUpdate){
            stringRedisTemplate.opsForHash().put(redisKey, liveStreamId, String.valueOf(income));
            // 更新有效期
            stringRedisTemplate.expire(redisKey, Duration.ofDays(EXPIRE_DAYS));
            return true;
        }
        return false;
    }

    public void updateGmvSub(Long cyberStarId,  String today, double income) {
        String gmvSubRedisKey = "vote:cyber_star:gmvSub:" + cyberStarId + ":" + today;
        stringRedisTemplate.opsForValue().set(gmvSubRedisKey,""+income);
    }


    /**
     * 计算Redis中存储的所有直播场次收入总和
     *
     * @param redisKey Redis键
     * @return 总收入
     */
    private void calculateTotalIncomeMap(String redisKey,Map<String,Double> map) {
        Map<Object, Object> entries = stringRedisTemplate.opsForHash().entries(redisKey);

        for (Map.Entry<Object, Object> entry : entries.entrySet()) {
            String value = entry.getValue().toString();
            try {
                double amount = Double.parseDouble(value);
                // 只累加正值，负值表示0点前的收入，不计入当天
//                total += amount;
                if (amount > 0) {
                    map.put(entry.getKey().toString(),amount);
                }
            } catch (NumberFormatException e) {
                log.warn("Invalid amount value in Redis: {}", value);
            }
        }
    }
    /**
     * 计算Redis中存储的所有直播场次收入总和
     *
     * @param redisKey Redis键
     * @return 总收入
     */
    private double calculateTotalIncome(String redisKey) {
        Map<Object, Object> entries = stringRedisTemplate.opsForHash().entries(redisKey);
        double total = 0.0;

        for (Map.Entry<Object, Object> entry : entries.entrySet()) {
            String value = entry.getValue().toString();
            try {
                double amount = Double.parseDouble(value);
                // 只累加正值，负值表示0点前的收入，不计入当天
                if (amount > 0) {
                    total += amount;
                }
            } catch (NumberFormatException e) {
                log.warn("Invalid amount value in Redis: {}", value);
            }
        }

        return total;
    }

    public CyberStarSaveReqVO storeGmv(CyberStarDO cyberStar){
        String nickname = cyberStar.getNickname();
        Long id = cyberStar.getId();
        String userId = cyberStar.getUserId();
        String cookie = cyberStar.getCookie();
        // 添加校验userId的校验。
        if(StrUtil.isEmpty(cookie)){
            return null;
        }
        if(!cookieIsContainUserId(cyberStar, cookie)){
            log.error(nickname + " : userId is error , please update "  );
            return null;
        }

        Calendar calendar = Calendar.getInstance();
        int hour = calendar.get(Calendar.HOUR_OF_DAY);
//        if(hour > 21) return null;
        //
        JSONObject resultJson = httpTodayAmount(cookie, 0);
        String todayAmount = resultJson.getByPath("data[0].value.sourceValue", String.class);

        // 获取当前日期
        String today = formatDate(new Date());
        //
        Long cyberStarId = cyberStar.getId();

        // 构建Redis的key: cyberStarId+today
        String redisKey = "vote:cyber_star:gmv:" + cyberStarId + ":" + today;

        double todayAmountDouble = NumberUtil.parseDouble(todayAmount);
            // 添加 如果数据突然跳水，就需要添加处理一下。
//        Double gmvTodayAmountDouble = cyberStar.getGmvTodayAmountDouble();
        double gmvTodayAmountDouble =  NumberUtil.parseDouble(stringRedisTemplate.opsForValue().get(redisKey));
        double amountDifference = todayAmountDouble - gmvTodayAmountDouble;
        int dive_count = DIVE_COUNT.incrementAndGet(userId);
        // 判断跳水
        boolean amountDive = amountDifference > cyberStar.getDiveAmount() || todayAmountDouble / gmvTodayAmountDouble > 10; // 数字跳水.
//        boolean justStarted = !isIsMidnightPeriod() && gmvTodayAmountDouble > 0; // >0 防止刚启动的时候第一次重启特别慢。 午夜时间段会跳水几率很高.这个时间让他多计算几次.

        if( amountDive && dive_count < 10  ){
                log.error("{}的数据第{}次跳水,原数据{},现数据{},差值{}",nickname, dive_count, gmvTodayAmountDouble,todayAmountDouble,amountDifference);
        }else{
                DIVE_COUNT.setDefaultValues(userId);
                // 不要没变化就存库，快手跳低的数据就崩啦。
                if(gmvTodayAmountDouble < todayAmountDouble){
                    // 将当前直播场次的数据存入Redis
                    stringRedisTemplate.opsForValue().set(redisKey,  String.valueOf(todayAmountDouble));
                    // 更新有效期
                    stringRedisTemplate.expire(redisKey, Duration.ofDays(EXPIRE_DAYS));
                    //
                    return new CyberStarSaveReqVO().setId(id).setGmvTodayAmountDouble(todayAmountDouble);
                }
                ThreadUtil.sleep(RandomUtil.randomInt(0,2000), TimeUnit.MILLISECONDS);
        }
        return null;
    }

    /**
     * cookie是否包含对应的userId
     * @param cyberStar
     * @param cookie
     * @return true 包含
     */
    private static boolean cookieIsContainUserId(CyberStarDO cyberStar, String cookie) {
        return cookie.contains("userId=" + cyberStar.getUserId());
    }

    public List<CyberStarSaveReqVO> callBack(List<CyberStarDO> starServiceAll,Function<CyberStarDO,CyberStarSaveReqVO> consumer){

        List<CyberStarSaveReqVO> list = new ArrayList<>();


        for (CyberStarDO cyberStar : starServiceAll) {

            try {
                CyberStarSaveReqVO apply = consumer.apply(cyberStar);
                if(Objects.nonNull(apply)) list.add(apply);
            } catch (Throwable e) {
                log.warn("TenantUtils for iter----value is error {}", cyberStar.getNickname(), e);
            }
        }
        return list;
    }


    public JSONObject httpTodayAmount(String cookie,int startOffset){
        if(StrUtil.isBlank(cookie)) return null;
        cookie = StrUtil.trim(cookie);
        cookie = StrUtil.removeAllLineBreaks(cookie);
        //
        JSONObject jsonObject = new JSONObject();
        Date now = new Date();
        now =  DateUtil.offsetDay(now,startOffset);
        Date lastNow =  DateUtil.offsetDay(now,-1);
        String nowDate = formatDate(now);
        String lastNowDate = formatDate(lastNow);
        //
        jsonObject.set("timeRange", "TODAY");
        jsonObject.set("currentStartDay", nowDate);
        jsonObject.set("currentEndDay", nowDate);
        jsonObject.set("compareStartDay", lastNowDate);
        jsonObject.set("compareEndDay", lastNowDate);

        //链式构建请求
        String result = HttpRequest.post("https://syt.kwaixiaodian.com/rest/business/gateway/home/<USER>")
                .header("Cookie", cookie)//头信息，多个头信息多次调用此方法即可
                .body(jsonObject.toString())//表单内容
                .timeout(20000)//超时，毫秒
                .execute().body();
        // 获得 今日成交金额
        JSONObject resultJson = new JSONObject(result);
        String errorMsg = resultJson.getStr("error_msg");
        if(StrUtil.isNotBlank(errorMsg)){
            throw new RuntimeException(errorMsg);
        }
        return resultJson;
    }

    private static String formatDate(Date date){
        return DateUtil.format(date, "yyyy-MM-dd");
    }
    public boolean amountIsOk(String todayAmount){
        double amount = 0;
        try {
            amount =Double.parseDouble( todayAmount);
        }catch (Exception e){
        }
        return amount > 0;
    }
    /**
     * @return 是在有效期内
     */
    public boolean validity(){
        return new Date().getTime() < 1752940800000l; // 2025-07-20 00:00:00
    }

    public static void main(String[] args) {
        long [] star40 = {1,2,3,4,6,7,8,9,10,11,12,13,15,16,17,18,20,21,22,23,26,29,31,32,34,43,44,55,71,74};
        System.out.println(star40.length);
    }

}



