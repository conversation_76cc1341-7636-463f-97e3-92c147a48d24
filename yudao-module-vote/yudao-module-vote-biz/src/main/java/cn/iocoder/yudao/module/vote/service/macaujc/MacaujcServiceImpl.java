package cn.iocoder.yudao.module.vote.service.macaujc;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.lang.WeightRandom;
import cn.hutool.core.text.csv.CsvUtil;
import cn.hutool.core.text.csv.CsvWriter;
import cn.hutool.core.thread.ThreadUtil;
import cn.hutool.core.util.*;
import cn.hutool.http.HttpUtil;
import cn.hutool.json.JSONArray;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import cn.iocoder.yudao.framework.common.core.ArrayMap;
import cn.iocoder.yudao.framework.common.core.CounterMap;
import cn.iocoder.yudao.framework.mybatis.core.query.LambdaQueryWrapperX;
import cn.iocoder.yudao.module.vote.dal.dataobject.activity.RaybetWinnerDO;
import cn.iocoder.yudao.module.vote.service.Macaujc2Service;
import lombok.Data;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;
import javax.annotation.Resource;
import org.springframework.validation.annotation.Validated;
import org.springframework.transaction.annotation.Transactional;

import java.io.PrintStream;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.function.Consumer;
import java.util.stream.Collectors;

import cn.iocoder.yudao.module.vote.controller.admin.macaujc.vo.*;
import cn.iocoder.yudao.module.vote.dal.dataobject.macaujc.MacaujcDO;
import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.common.pojo.PageParam;
import cn.iocoder.yudao.framework.common.util.object.BeanUtils;

import cn.iocoder.yudao.module.vote.dal.mysql.macaujc.MacaujcMapper;

import static cn.iocoder.yudao.framework.common.exception.util.ServiceExceptionUtil.exception;
import static cn.iocoder.yudao.module.vote.enums.ErrorCodeConstants.*;

/**
 * 彩票 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class MacaujcServiceImpl implements MacaujcService {
    public final static String KINDS[] = { "macaujc" , "macaujc2"};
    /** 生肖数组 */ public static final String[] ANIMAL_ARRAY = {"蛇","龍", "兔","虎", "牛","鼠", "豬","狗","雞","猴","羊", "馬"};
    /** 占比个数 */public static final Map<String,Integer> PROPORTION_NUMBER_MAP = new HashMap<>();
    static {
        //
        PROPORTION_NUMBER_MAP.put("1-小",24);
        PROPORTION_NUMBER_MAP.put("1-大",25);
        //
        PROPORTION_NUMBER_MAP.put("2-双",24);
        PROPORTION_NUMBER_MAP.put("2-单",25);
        // 合大
        PROPORTION_NUMBER_MAP.put("3-合大",24);
        PROPORTION_NUMBER_MAP.put("3-合小",25);
        //
        PROPORTION_NUMBER_MAP.put("4-合单",25);
        PROPORTION_NUMBER_MAP.put("4-合双",24);
        //  "大单", "大双", "小单", "小双"
        PROPORTION_NUMBER_MAP.put("5-大单",13);
        PROPORTION_NUMBER_MAP.put("5-大双",12);
        PROPORTION_NUMBER_MAP.put("5-小单",12);
        PROPORTION_NUMBER_MAP.put("5-小双",12);
        //
        PROPORTION_NUMBER_MAP.put("6-尾大",25);
        PROPORTION_NUMBER_MAP.put("6-尾小",24);
        //
        PROPORTION_NUMBER_MAP.put("7-家禽",25);
        PROPORTION_NUMBER_MAP.put("7-野兽",25);
        // 生肖
        for (int head = 0; head < ANIMAL_ARRAY.length; head++) {
            String key = "8-" + ANIMAL_ARRAY[head] + head;
            if(head == 0 ){
                PROPORTION_NUMBER_MAP.put(key, 5);
            }else{
                PROPORTION_NUMBER_MAP.put(key, 4);
            }
        }
        PROPORTION_NUMBER_MAP.put("9-红波",17);
        PROPORTION_NUMBER_MAP.put("9-绿波",16);
        PROPORTION_NUMBER_MAP.put("9-蓝波",16);

        PROPORTION_NUMBER_MAP.put("10-0头数",9);
        PROPORTION_NUMBER_MAP.put("10-1头数",10);
        PROPORTION_NUMBER_MAP.put("10-2头数",10);
        PROPORTION_NUMBER_MAP.put("10-3头数",10);
        PROPORTION_NUMBER_MAP.put("10-4头数",10);
        //
        PROPORTION_NUMBER_MAP.put("11-1合数",2);
        PROPORTION_NUMBER_MAP.put("11-2合数",3);
        PROPORTION_NUMBER_MAP.put("11-3合数",4);
        PROPORTION_NUMBER_MAP.put("11-4合数",5);
        PROPORTION_NUMBER_MAP.put("11-5合数",5);
        PROPORTION_NUMBER_MAP.put("11-6合数",5);
        PROPORTION_NUMBER_MAP.put("11-7合数",5);
        PROPORTION_NUMBER_MAP.put("11-8合数",5);
        PROPORTION_NUMBER_MAP.put("11-9合数",5);
        PROPORTION_NUMBER_MAP.put("11-10合数",4);
        PROPORTION_NUMBER_MAP.put("11-11合数",3);
        PROPORTION_NUMBER_MAP.put("11-12合数",2);
        PROPORTION_NUMBER_MAP.put("11-13合数",1);
        PROPORTION_NUMBER_MAP.put("12-红单",8);
        PROPORTION_NUMBER_MAP.put("12-红双",9);
        PROPORTION_NUMBER_MAP.put("12-绿单",9);
        PROPORTION_NUMBER_MAP.put("12-绿双",7);
        PROPORTION_NUMBER_MAP.put("12-蓝单",8);
        PROPORTION_NUMBER_MAP.put("12-蓝双",8);
        PROPORTION_NUMBER_MAP.put("13-重码",1);
        PROPORTION_NUMBER_MAP.put("14-重7码",7);
        for (int head = 1; head < 50; head++) {
            PROPORTION_NUMBER_MAP.put("15-"+head,1);
        }

        for (int head = 0; head < 10; head++) {
            PROPORTION_NUMBER_MAP.put("16-"+head+"尾数",5);
        }
    }
    /** 当前遍历的计数 */public static final CounterMap<String> CURRENT_MISS_COUNT = new CounterMap(new HashMap<String, Integer>(), 0);
    /** 最长时间的计数 */public static final CounterMap<String> MAX_MISS_COUNT = new CounterMap(new HashMap<String, Integer>(), 0);
    /** 最近没出现的计数 */ public static final CounterMap<String> LAST_MISS_COUNT = new CounterMap(new HashMap<String, Integer>(), 0);
    /** 权重随机的计数 */ public static final CounterMap<Integer> WEIGHT_OBJ_COUNT = new CounterMap(new HashMap<Integer, Integer>(), 1);
    /** 所有没出现的计数 */ public static final ArrayMap<String, Integer> ALL_MISS_COUNT = new ArrayMap(new HashMap<String, List<Integer>>());
    public static final String [] csv_head_fields = new String[]{
            "size","oddEven","sumSize","sumOddEven","combined","tailSize" , "animal" , "12Animal", "color", "missCounts" , "sumCount" , "colorSingle" , "code" , "normalCode1", "normalCode2", "normalCode3", "normalCode4", "normalCode5", "normalCode6" , "color1", "color2", "color3", "color4", "color5", "color6","expect"
    };
    public static String [] fields = csv_head_fields;
    public static int j = 0;
    public static int maxIndex = 0;
    public static int lastSpecialCode = -1;

    @Value("${wlh.macaujc.path-directory}")
    private String pathDirectory;

    // {head-1=42, head-2=20, head-3=33, head-4=23, head-0=32}
    //2: {head-1=42, head-2=33, head-3=33, head-4=25, head-0=32}
    // 统计
    public synchronized String statistics(String kind )  {
        return statistics(kind,true);
    }

    public synchronized String statistics(String kind , boolean showAll)  {
        StringBuffer SB = new StringBuffer();
        SB.append( DateUtil.today() + " 类型：" + ( "macaujc".equals(kind) ? "澳門六合彩" : "新澳門六合彩" )   + "\n");
        j = 0;
        fields = csv_head_fields;
        LambdaQueryWrapperX<MacaujcDO> wrapperX = new LambdaQueryWrapperX<>();
        wrapperX.eq(MacaujcDO::getKind,kind);
        wrapperX.orderByDesc(MacaujcDO::getExpect);
        List<MacaujcDO> dataArray = macaujcMapper.selectList(wrapperX);
        ArrayMap<String, Integer> all_max_miss_count = new ArrayMap(new HashMap<String, List<Integer>>());
        maxIndex = dataArray.size() - 1;
        CsvWriter csvWriter = CsvUtil.getWriter(pathDirectory + kind + ".csv", CharsetUtil.CHARSET_UTF_8);
        csvWriter.writeHeaderLine(fields);
            for (; j <= Macaujc2Info.SPECIAL_INDEX; j++) {
                for (int i = 0; i <= maxIndex-1; i++) {
                    fields = new String[fields.length];
                    MacaujcDO macaujcDO = dataArray.get(i);
                    MacaujcDO macaujcLastDO = dataArray.get(i+1);
                    Macaujc2Info info = BeanUtils.toBean(macaujcDO,Macaujc2Info.class);
                    Macaujc2Info lastinfo = BeanUtils.toBean(macaujcLastDO,Macaujc2Info.class);
                    //
                    int specialCode = info.getCode(j);
                    String specialWave = info.getWave(j);
                    String specialZodiac = info.getZodiac(j);
                    String expect = info.getExpect();
                    print("期号: " + expect + " 码数: " + NumberUtil.decimalFormat("00", specialCode) + "\t");

                    // 大小分析
                    analyzeSize(specialCode);
                    print(" ");

                    // 单双分析
                    analyzeOddEven(specialCode);
                    print(" ");
                    // 合大合小分析
                    analyzeSumSize(specialCode);
                    print(" ");
                    // 合单合双分析
                    analyzeSumOddEven(specialCode);
                    print(" ");
                    // 大单、小单、大双、小双分析
                    analyzeCombined(specialCode);
                    print(" ");
                    // 尾大尾小分析
                    analyzeTailSize(specialCode);
                    print(" ");
                    // 家禽野兽分析
                    analyzeAnimal(specialZodiac);
                    print(" ");
                    //
                    analyze12Animal(specialCode);
                    print(" ");
                    // 色波分析
                    analyzeColor(specialWave);

                    // 计算特码的头数
                    updateMissCounts(specialCode);

                    // 分析所有的合数内容。
                    analyzeSumCount(specialCode);

                    // 分析红蓝绿的单双情况。
                    analyzeColorSingle(specialWave,specialCode);

                    // 分析重复code情况。
                    analyzeDuplicateCode(lastinfo.getCode(j),specialCode);
                    // 分析重复code情况。
                    analyzeDuplicateAllCode(lastinfo,specialCode);

                    // 分析code 次数的情况。
                    analyzeCountCode(specialCode);

                    // 分析 尾数的情况。
                    analyzeTailNumber(specialCode);

                    if(j == Macaujc2Info.SPECIAL_INDEX){
                        fields[12] = specialCode + "";
                        for (int k = 0; k < 6; k++) {
                            fields[13 + k] = info.getCode(k) + "";
                            fields[19 + k] = info.getWave(k) ;
                        }
                        fields[25] = expect;
                        csvWriter.writeLine(fields);
                        csvWriter.flush();
                    }
                }




                // 多维度分析没有出得内容。
                SB.append("\n\n-----多维度分析------第几个码位：" + (j+1)  + "\n");

                Set<String> set = ALL_MISS_COUNT.keySet();
                // 使用Stream排序并收集回List
                List<String> sortedList = set.stream()
                        .sorted(Comparator.comparingInt(s -> Integer.parseInt(s.split("-")[0])))
                        .collect(Collectors.toList());

                for (String key : sortedList) {
//            String key = entry.getKey();
                    List<Integer> list = ALL_MISS_COUNT.get(key);
                    list = list.stream().filter(n -> n > 4).collect(Collectors.toList()); // 过滤掉小于0的元素
                    // 计算平均值
                    double average = list.stream()
                            .mapToInt(Integer::intValue)
                            .average().orElse(Double.NaN);


                    Integer lastCount = LAST_MISS_COUNT.get(key);
                    Integer maxCount = MAX_MISS_COUNT.get(key);
                    all_max_miss_count.add(key,maxCount); // 添加所有的码进入列表里面去执行。
                    Integer proportion = 49 - PROPORTION_NUMBER_MAP.get(key);
                    String outStr = StrUtil.format("{}:平均出现期数：{}；历史最长记录:{}；最近没出的内容:{}；下期再不出现的概率:{}\n"
                            , key.split("-")[1], NumberUtil.roundStr(average, 1)
                            , maxCount, lastCount , NumberUtil.decimalFormat("0.0000%", Math.pow( proportion / 49d, lastCount)));
                    if( lastCount >  maxCount * 0.63 ){
                        SB.append(outStr + "\n");
                    }else{
//                    out(outStr);
                    }

                }

                // clear
                CURRENT_MISS_COUNT.clear();
                MAX_MISS_COUNT.clear();
                LAST_MISS_COUNT.clear();
                ALL_MISS_COUNT.clear();
                WEIGHT_OBJ_COUNT.clear();
            }


            if(showAll){
                Set<String> set = all_max_miss_count.keySet();
                // 使用Stream排序并收集回List
                List<String> sortedList = set.stream()
                        .sorted(Comparator.comparingInt(s -> Integer.parseInt(s.split("-")[0])))
                        .collect(Collectors.toList());
                SB.append("\n\n\n【特征】: 【最长期数】: 【平一最长，平二最长，平三最长，平四最长，平五最长，平六最长，特碼最长】\n");
                for (String key : sortedList) {
                    List<Integer> all_max_miss = all_max_miss_count.get(key);
                    SB.append(key + ": 最长期数:" + Collections.max(all_max_miss) +  all_max_miss + "\n");
                }
            }
        return SB.toString();
    }


    public static void for49(Consumer<Integer> consumer){
        for (int i = 1; i <= 49; i++) {
            consumer.accept(i);
        }
    }
    public static void print(String str) {
//        System.out.print(str);
    }

    // 大小分析
    public static void analyzeSize(int num) {
        String aKey = "1-大";
        String bKey = "1-小";
        String result = null;
        if (num >= 25) {
            result = aKey;
            int oldCount = CURRENT_MISS_COUNT.setDefaultValues(result);
            Integer ifAbsent = LAST_MISS_COUNT.putIfAbsent(result, oldCount);
            if(ifAbsent == null){
                for49(code->{ if(code >= 25) WEIGHT_OBJ_COUNT.addAndGet(code,oldCount); });
            }
            ALL_MISS_COUNT.add(result, oldCount);

            String key = bKey;
            // 未出现的头数 +1 。
            int current_count = CURRENT_MISS_COUNT.incrementAndGet(key);
            // 之后再 更新最长未出现期数
            if (current_count > MAX_MISS_COUNT.getOrDefault(key)) {
                MAX_MISS_COUNT.put(key, current_count);

            }
        } else {
            result = bKey;
            int oldCount = CURRENT_MISS_COUNT.setDefaultValues(result);
            Integer ifAbsent = LAST_MISS_COUNT.putIfAbsent(result, oldCount);
            if(ifAbsent == null){
                for49(code->{ if(code < 25) WEIGHT_OBJ_COUNT.addAndGet(code,oldCount); });
            }
            ALL_MISS_COUNT.add(result, oldCount);

            String key = aKey;
            // 未出现的头数 +1 。
            int current_count = CURRENT_MISS_COUNT.incrementAndGet(key);
            // 之后再 更新最长未出现期数
            if (current_count > MAX_MISS_COUNT.getOrDefault(key)) {
                MAX_MISS_COUNT.put(key, current_count);
            }

        }
        print("结果: " + result);
        fields[0] = result;
    }

    // 单双分析
    public static void analyzeOddEven(int num) {
        String aKey = "2-双";
        String bKey = "2-单";
        String result = null;
        if (num % 2 == 0) {
            result = aKey;
            int oldCount = CURRENT_MISS_COUNT.setDefaultValues(result);
            Integer ifAbsent = LAST_MISS_COUNT.putIfAbsent(result, oldCount);
            if(ifAbsent == null){
                for49(code->{ if(code % 2 == 0) WEIGHT_OBJ_COUNT.addAndGet(code,oldCount); });
            }
            ALL_MISS_COUNT.add(result, oldCount);

            String key = bKey;
            // 未出现的头数 +1 。
            int current_count = CURRENT_MISS_COUNT.incrementAndGet(key);
            // 之后再 更新最长未出现期数
            if (current_count > MAX_MISS_COUNT.getOrDefault(key)) {
                MAX_MISS_COUNT.put(key, current_count);
            }
        } else {
            result = bKey;
            int oldCount = CURRENT_MISS_COUNT.setDefaultValues(result);
            Integer ifAbsent = LAST_MISS_COUNT.putIfAbsent(result, oldCount);
            if(ifAbsent == null){
                for49(code->{ if(code % 2 != 0) WEIGHT_OBJ_COUNT.addAndGet(code,oldCount); });
            }
            ALL_MISS_COUNT.add(result, oldCount);

            String key = aKey;
            // 未出现的头数 +1 。
            int current_count = CURRENT_MISS_COUNT.incrementAndGet(key);
            // 之后再 更新最长未出现期数
            if (current_count > MAX_MISS_COUNT.getOrDefault(key)) {
                MAX_MISS_COUNT.put(key, current_count);
            }

        }
        print("结果: " + result);
        fields[1] = result;
    }

    // 合大合小分析
    public static void analyzeSumSize(int num) {
        {
            String aKey = "3-合大";
            String bKey = "3-合小";
            String result = null;
            int sum = (num / 10) + (num % 10);
            if (sum >= 7) {
                result = aKey;
                int oldCount = CURRENT_MISS_COUNT.setDefaultValues(result);
                Integer ifAbsent = LAST_MISS_COUNT.putIfAbsent(result, oldCount);
                if(ifAbsent == null){
                    for49(code->{ if( ((code / 10) + (code % 10))  >= 7) WEIGHT_OBJ_COUNT.addAndGet(code,oldCount); });
                }
                ALL_MISS_COUNT.add(result, oldCount);

                String key = bKey;
                // 未出现的头数 +1 。
                int current_count = CURRENT_MISS_COUNT.incrementAndGet(key);
                // 之后再 更新最长未出现期数
                if (current_count > MAX_MISS_COUNT.getOrDefault(key)) {
                    MAX_MISS_COUNT.put(key, current_count);
                }
            } else {
                result = bKey;
                int oldCount = CURRENT_MISS_COUNT.setDefaultValues(result);
                Integer ifAbsent = LAST_MISS_COUNT.putIfAbsent(result, oldCount);
                if(ifAbsent == null){
                    for49(code->{ if( ((code / 10) + (code % 10))  < 7) WEIGHT_OBJ_COUNT.addAndGet(code,oldCount); });
                }
                ALL_MISS_COUNT.add(result, oldCount);

                String key = aKey;
                // 未出现的头数 +1 。
                int current_count = CURRENT_MISS_COUNT.incrementAndGet(key);
                // 之后再 更新最长未出现期数
                if (current_count > MAX_MISS_COUNT.getOrDefault(key)) {
                    MAX_MISS_COUNT.put(key, current_count);
                }

            }
            print("结果: " + result);
            fields[2] = result;
        }
    }

    // 合单合双分析
    public static void analyzeSumOddEven(int num) {
        {
            String aKey = "4-合双";
            String bKey = "4-合单";
            String result = null;
            int sum = (num / 10) + (num % 10);
            if (sum % 2 == 0) {
                result = aKey;
                int oldCount = CURRENT_MISS_COUNT.setDefaultValues(result);
                Integer ifAbsent = LAST_MISS_COUNT.putIfAbsent(result, oldCount);
                if(ifAbsent == null){
                    for49(code->{ if( ((code / 10) + (code % 10))  == 0 ) WEIGHT_OBJ_COUNT.addAndGet(code,oldCount); });
                }
                ALL_MISS_COUNT.add(result, oldCount);

                String key = bKey;
                // 未出现的头数 +1 。
                int current_count = CURRENT_MISS_COUNT.incrementAndGet(key);
                // 之后再 更新最长未出现期数
                if (current_count > MAX_MISS_COUNT.getOrDefault(key)) {
                    MAX_MISS_COUNT.put(key, current_count);
                }

            } else {

                result = bKey;
                int oldCount = CURRENT_MISS_COUNT.setDefaultValues(result);
                Integer ifAbsent = LAST_MISS_COUNT.putIfAbsent(result, oldCount);
                if(ifAbsent == null){
                    for49(code->{ if( ((code / 10) + (code % 10))  != 0 ) WEIGHT_OBJ_COUNT.addAndGet(code,oldCount); });
                }
                ALL_MISS_COUNT.add(result, oldCount);

                String key = aKey;
                // 未出现的头数 +1 。
                int current_count = CURRENT_MISS_COUNT.incrementAndGet(key);
                // 之后再 更新最长未出现期数
                if (current_count > MAX_MISS_COUNT.getOrDefault(key)) {
                    MAX_MISS_COUNT.put(key, current_count);
                }
            }
            print("结果: " + result);
            fields[3] = result;
        }
    }

    // 大单、小单、大双、小双分析
    public static void analyzeCombined(int num) {
        int resulteIndex = -1;
        if (num >= 25 && num % 2 != 0) {
            resulteIndex = 0;
        } else if (num >= 25) {
            resulteIndex = 1;
        } else if (num % 2 != 0) {
            resulteIndex = 2;
        } else {
            resulteIndex = 3;
        }

        String[] allArray = {"大单", "大双", "小单", "小双"};
        // 遍历所有头数
        for (int head = 0; head < allArray.length; head++) {
            String key = "5-" + allArray[head];
            if (head == resulteIndex) {
                // 如果当前头数出现，重置未出现期数
//                currentMissCount.put(head, 0);
                int oldCount = CURRENT_MISS_COUNT.setDefaultValues(key);
                LAST_MISS_COUNT.putIfAbsent(key, oldCount);
                ALL_MISS_COUNT.add(key, oldCount);
            } else {
                // 未出现的头数 +1 。
                int current_count = CURRENT_MISS_COUNT.incrementAndGet(key);
                // 之后再 更新最长未出现期数
                if (current_count > MAX_MISS_COUNT.getOrDefault(key)) {
                    MAX_MISS_COUNT.put(key, current_count);
                }
            }
        }


        print("结果: " + allArray[resulteIndex]);
        fields[4] = allArray[resulteIndex];
    }

    // 尾大尾小分析
    public static void analyzeTailSize(int num) {
        {
            int tail = num % 10;
            String aKey = "6-尾大";
            String bKey = "6-尾小";
            String result = null;
            if (tail >= 5) {
                result = aKey;
                int oldCount = CURRENT_MISS_COUNT.setDefaultValues(result);
                Integer ifAbsent = LAST_MISS_COUNT.putIfAbsent(result, oldCount);
                if(ifAbsent == null){
                    for49(code->{ if( (code % 10)  >= 5 ) WEIGHT_OBJ_COUNT.addAndGet(code,oldCount); });
                }
                ALL_MISS_COUNT.add(result, oldCount);

                String key = bKey;
                // 未出现的头数 +1 。
                int current_count = CURRENT_MISS_COUNT.incrementAndGet(key);
                // 之后再 更新最长未出现期数
                if (current_count > MAX_MISS_COUNT.getOrDefault(key)) {
                    MAX_MISS_COUNT.put(key, current_count);
                }

            } else {
                result = bKey;
                int oldCount = CURRENT_MISS_COUNT.setDefaultValues(result);
                Integer ifAbsent = LAST_MISS_COUNT.putIfAbsent(result, oldCount);
                if(ifAbsent == null){
                    for49(code->{ if( (code % 10)  < 5 ) WEIGHT_OBJ_COUNT.addAndGet(code,oldCount); });
                }
                ALL_MISS_COUNT.add(result, oldCount);

                String key = aKey;
                // 未出现的头数 +1 。
                int current_count = CURRENT_MISS_COUNT.incrementAndGet(key);
                // 之后再 更新最长未出现期数
                if (current_count > MAX_MISS_COUNT.getOrDefault(key)) {
                    MAX_MISS_COUNT.put(key, current_count);
                }

            }
            print("结果: " + result);
            fields[5] = result;
        }
    }

    // 家禽野兽分析
    public static void analyzeAnimal(String zodiac) {
        // 家禽：牛、马、羊、鸡、狗、猪
        // 野兽：鼠、虎、龙、蛇、兔、猴
        List<String> domesticAnimals = Arrays.asList("牛", "馬", "羊", "雞", "狗", "豬");
        List<String> wildAnimals = Arrays.asList("鼠", "虎", "龍", "蛇", "兔", "猴");
        String jKey = "7-家禽";
        String yKey = "7-野兽";
        String result = null;
        if (domesticAnimals.contains(zodiac)) {
            result = jKey;
            int oldCount = CURRENT_MISS_COUNT.setDefaultValues(result);
            LAST_MISS_COUNT.putIfAbsent(result, oldCount);
            ALL_MISS_COUNT.add(result, oldCount);
            String key = yKey;
            // 未出现的头数 +1 。
            int current_count = CURRENT_MISS_COUNT.incrementAndGet(key);
            // 之后再 更新最长未出现期数
            if (current_count > MAX_MISS_COUNT.getOrDefault(key)) {
                MAX_MISS_COUNT.put(key, current_count);
            }
        } else if (wildAnimals.contains(zodiac)) {
            result = yKey;
            int oldCount = CURRENT_MISS_COUNT.setDefaultValues(result);
            LAST_MISS_COUNT.putIfAbsent(result, oldCount);
            ALL_MISS_COUNT.add(result, oldCount);
            String key = jKey;
            // 未出现的头数 +1 。
            int current_count = CURRENT_MISS_COUNT.incrementAndGet(key);
            // 之后再 更新最长未出现期数
            if (current_count > MAX_MISS_COUNT.getOrDefault(key)) {
                MAX_MISS_COUNT.put(key, current_count);
            }
        }
        print("结果: " + result);
        fields[6] = result;
    }

    public static void analyze12Animal(int specialCode) {
        String[] allArray = ANIMAL_ARRAY;
        int resulteIndex = specialCode % 12;  // ArrayUtil.indexOf(allArray, zodiac);
        // 遍历所有头数
        for (int head = 0; head < allArray.length; head++) {
            String key = "8-" + allArray[head] + head;
            if (head == resulteIndex) {
                // 如果当前头数出现，重置未出现期数
//                currentMissCount.put(head, 0);
                int oldCount = CURRENT_MISS_COUNT.setDefaultValues(key);
                LAST_MISS_COUNT.putIfAbsent(key, oldCount);
                ALL_MISS_COUNT.add(key, oldCount);
            } else {
                // 未出现的头数 +1 。
                int current_count = CURRENT_MISS_COUNT.incrementAndGet(key);
                // 之后再 更新最长未出现期数
                if (current_count > MAX_MISS_COUNT.getOrDefault(key)) {
                    MAX_MISS_COUNT.put(key, current_count);
                }
            }
        }
        print("生肖: " + allArray[resulteIndex]);
        fields[7] = allArray[resulteIndex];
    }

    // 色波分析
    public static void analyzeColor(String wave) {
        int resulteIndex = -1;
        switch (wave) {
            case "red":
                resulteIndex = 0;
                break;
            case "green":
                resulteIndex = 1;
                break;
            case "blue":
                resulteIndex = 2;
                break;
        }

        String[] waves = {"红波", "绿波", "蓝波"};
        // 遍历所有头数
        for (int head = 0; head < waves.length; head++) {
            String key = "9-" + waves[head];
            if (head == resulteIndex) {
                // 如果当前头数出现，重置未出现期数
//                currentMissCount.put(head, 0);
                int oldCount = CURRENT_MISS_COUNT.setDefaultValues(key);
                LAST_MISS_COUNT.putIfAbsent(key, oldCount);
                ALL_MISS_COUNT.add(key, oldCount);
            } else {
                // 未出现的头数 +1 。
                int current_count = CURRENT_MISS_COUNT.incrementAndGet(key);
                // 之后再 更新最长未出现期数
                if (current_count > MAX_MISS_COUNT.getOrDefault(key)) {
                    MAX_MISS_COUNT.put(key, current_count);
                }
            }
        }


        print("结果: " + waves[resulteIndex] + "\n");

        fields[8] = waves[resulteIndex];
    }


    // 更新头数的未出现期数
    public static void updateMissCounts(int specialCode) {
//        int specialCode = info.getSpecialCode();
        int currentHead = specialCode / 10;
        // 遍历所有头数
        for (int head = 0; head <= 4; head++) {
            String key = "10-" + head + "头数";
            if (head == currentHead) {
                // 如果当前头数出现，重置未出现期数
//                currentMissCount.put(head, 0);
                int oldCount = CURRENT_MISS_COUNT.setDefaultValues(key);
                Integer ifAbsent = LAST_MISS_COUNT.putIfAbsent(key, oldCount);
                if(ifAbsent == null){
                    int finalHead = head;
                    for49(code->{ if( (code / 10)  == finalHead) WEIGHT_OBJ_COUNT.addAndGet(code,oldCount); });
                }
                ALL_MISS_COUNT.add(key, oldCount);
            } else {
                // 未出现的头数 +1 。
                int current_count = CURRENT_MISS_COUNT.incrementAndGet(key);
                // 之后再 更新最长未出现期数
                if (current_count > MAX_MISS_COUNT.getOrDefault(key)) {
                    MAX_MISS_COUNT.put(key, current_count);
//                    System.err.println( key + ":" + info.expect + ":" + current_count);
                }
            }
        }
        fields[9] = currentHead + "";

    }


    // 更新头数的未出现期数
    public static void analyzeSumCount(int num) {
        int sum = (num / 10) + (num % 10);
        // 遍历所有头数
        for (int head = 1; head <= 13; head++) {
            String key = "11-" + head + "合数";
            if (head == sum) {
                // 如果当前头数出现，重置未出现期数
//                currentMissCount.put(head, 0);
                int oldCount = CURRENT_MISS_COUNT.setDefaultValues(key);
                Integer ifAbsent = LAST_MISS_COUNT.putIfAbsent(key, oldCount);
                if(ifAbsent == null){
                    int finalHead = head;
                    for49(code->{ if( ( (code / 10) + (code % 10) )  == finalHead) WEIGHT_OBJ_COUNT.addAndGet(code,oldCount); });
                }
                ALL_MISS_COUNT.add(key, oldCount);
            } else {
                // 未出现的头数 +1 。
                int current_count = CURRENT_MISS_COUNT.incrementAndGet(key);
                // 之后再 更新最长未出现期数
                if (current_count > MAX_MISS_COUNT.getOrDefault(key)) {
                    MAX_MISS_COUNT.put(key, current_count);
                }
            }
        }
        fields[10] = sum + "";
    }


    // 色波带单双分析
    public static void analyzeColorSingle(String wave,int specialCode) {
        int resulteIndex = -1;
        if("red".equals(wave) && specialCode % 2 == 0 ){
            resulteIndex = 0;
        }else if ("red".equals(wave) && specialCode % 2 != 0){
            resulteIndex = 1;
        } else if("green".equals(wave) && specialCode % 2 == 0 ){
            resulteIndex = 2;
        } else if ("green".equals(wave) && specialCode % 2 != 0){
            resulteIndex = 3;
        } else if("blue".equals(wave) && specialCode % 2 == 0 ){
            resulteIndex = 4;
        } else if ("blue".equals(wave) && specialCode % 2 != 0){
            resulteIndex = 5;
        }

        String[] waves = {"红双","红单", "绿双","绿单", "蓝双","蓝单"};
        // 遍历所有头数
        for (int head = 0; head < waves.length; head++) {
            String key = "12-" + waves[head];
            if (head == resulteIndex) {
                // 如果当前头数出现，重置未出现期数
//                currentMissCount.put(head, 0);
                int oldCount = CURRENT_MISS_COUNT.setDefaultValues(key);
                LAST_MISS_COUNT.putIfAbsent(key, oldCount);
                ALL_MISS_COUNT.add(key, oldCount);
            } else {
                // 未出现的头数 +1 。
                int current_count = CURRENT_MISS_COUNT.incrementAndGet(key);
                // 之后再 更新最长未出现期数
                if (current_count > MAX_MISS_COUNT.getOrDefault(key)) {
                    MAX_MISS_COUNT.put(key, current_count);
                }
            }
        }


        print("结果: " + waves[resulteIndex] + "\n");

        fields[11] = waves[resulteIndex];
    }


    // 分析重码情况
    public static void analyzeDuplicateCode( int lastSpecialCode , int specialCode) {
        String key = "13-重码";
        if (lastSpecialCode == specialCode) {
            // 如果出现，重置未出现期数
//                currentMissCount.put(head, 0);
            int oldCount = CURRENT_MISS_COUNT.setDefaultValues(key);
            LAST_MISS_COUNT.putIfAbsent(key, oldCount);
            ALL_MISS_COUNT.add(key, oldCount);
        } else {
            // 未出现的头数 +1 。
            int current_count = CURRENT_MISS_COUNT.incrementAndGet(key);
            // 之后再 更新最长未出现期数
            if (current_count > MAX_MISS_COUNT.getOrDefault(key)) {
                MAX_MISS_COUNT.put(key, current_count);
            }
        }

        print("结果: " + key + "\n");

//        fields[12] = waves[resulteIndex];
    }

    // 分析重码情况，重复情况
    public static void analyzeDuplicateAllCode(Macaujc2Info lastSpecialCode , int specialCode) {
        String key = "14-重7码";
        int[] splitToInt = StrUtil.splitToInt(lastSpecialCode.openCode, ",");
        if (Arrays.binarySearch(splitToInt,specialCode) > 0) {
            // 如果出现，重置未出现期数
//                currentMissCount.put(head, 0);
            int oldCount = CURRENT_MISS_COUNT.setDefaultValues(key);
            LAST_MISS_COUNT.putIfAbsent(key, oldCount);
            ALL_MISS_COUNT.add(key, oldCount);
        } else {
            // 未出现的头数 +1 。
            int current_count = CURRENT_MISS_COUNT.incrementAndGet(key);
            // 之后再 更新最长未出现期数
            if (current_count > MAX_MISS_COUNT.getOrDefault(key)) {
                MAX_MISS_COUNT.put(key, current_count);
            }
        }

        print("结果: " + key + "\n");

//        fields[12] = waves[resulteIndex];
    }


    // 分析code 次数的情况
    public static void analyzeCountCode(int specialCode) {
        // 遍历49个数字
        for (int head = 1; head < 50; head++) {
            String key = "15-" + head;
            if (head == specialCode) {
                // 如果出现，重置未出现期数
//                currentMissCount.put(head, 0);
                int oldCount = CURRENT_MISS_COUNT.setDefaultValues(key);
                LAST_MISS_COUNT.putIfAbsent(key, oldCount);
                ALL_MISS_COUNT.add(key, oldCount);
            } else {
                // 未出现
                int current_count = CURRENT_MISS_COUNT.incrementAndGet(key);
                // 之后再 更新最长未出现期数
                if (current_count > MAX_MISS_COUNT.getOrDefault(key)) {
                    MAX_MISS_COUNT.put(key, current_count);
                }
            }
        }
    }


    // 分析code 次数的情况
    public static void analyzeTailNumber(int specialCode) {
        int sum = specialCode % 10;
        // 遍历所有头数
        for (int head = 0; head <= 9; head++) {
            String key = "16-" + head + "尾数";
            if (head == sum) {
                // 如果当前头数出现，重置未出现期数
//                currentMissCount.put(head, 0);
                int oldCount = CURRENT_MISS_COUNT.setDefaultValues(key);
                Integer ifAbsent = LAST_MISS_COUNT.putIfAbsent(key, oldCount);
                if(ifAbsent == null){
                    int finalHead = head;
                    for49(code->{ if( ( code % 10 )  == finalHead) WEIGHT_OBJ_COUNT.addAndGet(code,oldCount); });
                }
                ALL_MISS_COUNT.add(key, oldCount);
            } else {
                // 未出现的头数 +1 。
                int current_count = CURRENT_MISS_COUNT.incrementAndGet(key);
                // 之后再 更新最长未出现期数
                if (current_count > MAX_MISS_COUNT.getOrDefault(key)) {
                    MAX_MISS_COUNT.put(key, current_count);
                }
            }
        }
    }



    @Data
    public class Macaujc2Info {
        public static final int SPECIAL_INDEX = 6;
        private String expect;
        private String openCode;
        private String zodiac;
        private String openTime;
        private String wave;

        public String split(String str, int index) {
            String[] split = StrUtil.splitToArray(str, ",");
            return split[index];
        }

        public int getCode(int index) {
            String split = this.split(openCode, index);
            return Integer.parseInt(split);
        }

        public String getZodiac(int index) {
            return this.split(zodiac, index);
        }

        public String getWave(int index) {
            return this.split(wave, index);
        }

        //////////////
        //  获得特码
        ////////////
        public int getSpecialCode() {
            return getCode(SPECIAL_INDEX);
        }

        public String getSpecialZodiac() {
            return this.getZodiac(SPECIAL_INDEX);
        }

        public String getSpecialWave() {
            return this.getWave(SPECIAL_INDEX);
        }
    }


    @Resource
    private MacaujcMapper macaujcMapper;

    @Override
    public Integer createMacaujc(MacaujcSaveReqVO createReqVO) {
        // 插入
        MacaujcDO macaujc = BeanUtils.toBean(createReqVO, MacaujcDO.class);
        macaujcMapper.insert(macaujc);
        // 返回
        return macaujc.getExpect();
    }

    @Override
    public void updateMacaujc(MacaujcSaveReqVO updateReqVO) {
        // 校验存在
        validateMacaujcExists(updateReqVO.getExpect());
        // 更新
        MacaujcDO updateObj = BeanUtils.toBean(updateReqVO, MacaujcDO.class);
        macaujcMapper.updateById(updateObj);
    }

    @Override
    public void deleteMacaujc(Integer id) {
        // 校验存在
        validateMacaujcExists(id);
        // 删除
        macaujcMapper.deleteById(id);
    }

    private void validateMacaujcExists(Integer id) {
        if (macaujcMapper.selectById(id) == null) {
            throw exception(MACAUJC_NOT_EXISTS);
        }
    }

    @Override
    public MacaujcDO getMacaujc(Integer id) {
        return macaujcMapper.selectById(id);
    }

    @Override
    public PageResult<MacaujcDO> getMacaujcPage(MacaujcPageReqVO pageReqVO) {
        return macaujcMapper.selectPage(pageReqVO);
    }




    @Scheduled(cron = "0 0 23 * * ?")
//    @Scheduled(cron = "35 * * * * ?")
    public void storageOpen() {
        for (String kind : KINDS) {
            JSONArray dataArray = new JSONArray();

//            final String URL = "https://api.macaumarksix.com/history/"+KIND+"/y/";
//            String years[] = {"2025","2024", "2023", "2022", "2021", "2020"};
//            for (String year : years) {
//                String body = HttpUtil.get(URL + year);
//                JSONObject jsonObject = JSONUtil.parseObj(body);
//                JSONArray jsonArray = jsonObject.getJSONArray("data");
//                dataArray.addAll(jsonArray);
//            }

            final String URL = "https://macaumarksix.com/api/"+kind+".com";
            String body = HttpUtil.get(URL);
            dataArray = JSONUtil.parseArray(body);

            for (int i = 0; i < dataArray.size(); i++) {
                MacaujcDO macaujcDO = dataArray.getBean(i, MacaujcDO.class);
                macaujcDO.setKind(kind);
                LambdaQueryWrapperX<MacaujcDO> wrapperX = new LambdaQueryWrapperX<>();
                wrapperX.eq(MacaujcDO::getExpect,macaujcDO.getExpect());
                wrapperX.eq(MacaujcDO::getKind,macaujcDO.getKind());
                if (!macaujcMapper.exists(wrapperX)) {
                    macaujcMapper.insert(macaujcDO);
                }
            }
        }
    }


    @Scheduled(cron = "1 1 0 * * ?")
//        @Scheduled(initialDelay = 1, fixedRate = 60, timeUnit = TimeUnit.SECONDS)
    public void notice() {
        for (String kind : KINDS) {
            String statistics = this.statistics(kind,false);
            JSONObject jsonObject = new JSONObject();
            jsonObject.set("message",statistics);
            String post = HttpUtil.post("http://127.0.0.1:5000/send-message", jsonObject.toStringPretty());
            System.out.println( "send-message：" + post);
            String python3 = RuntimeUtil.execForStr("python3", "/home/<USER>/project/macaujc/lapulasi.py", kind);
            System.out.println( "send-python3：" + python3);
        }
    }
}
 // request.getContentLength();
