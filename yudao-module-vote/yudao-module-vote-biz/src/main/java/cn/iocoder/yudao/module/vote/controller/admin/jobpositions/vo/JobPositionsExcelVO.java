package cn.iocoder.yudao.module.vote.controller.admin.jobpositions.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;
import java.util.*;
import java.time.LocalDateTime;
import java.time.LocalDateTime;
import java.time.LocalDateTime;
import java.time.LocalDateTime;

import com.alibaba.excel.annotation.ExcelProperty;

/**
 * 职位信息 Excel VO
 *
 * <AUTHOR>
 */
@Data
public class JobPositionsExcelVO {

    @ExcelProperty("职位ID")
    private Integer positionId;

    @ExcelProperty("公司ID")
    private Integer companyId;

    @ExcelProperty("公司名称")
    private String companyName;

    @ExcelProperty("公告ID")
    private Integer annoId;

    @ExcelProperty("公告名称")
    private String annoName;

    @ExcelProperty("公告链接")
    private String annoLink;

    @ExcelProperty("类型")
    private String type;

    @ExcelProperty("职位名称")
    private String title;

    @ExcelProperty("工作地点")
    private String location;

    @ExcelProperty("链接")
    private String link;

    @ExcelProperty("专业要求")
    private String specialty;

    @ExcelProperty("学历要求")
    private String education;

    @ExcelProperty("学历列表 (使用 JSON 类型存储)")
    private String educationCnlist;

    @ExcelProperty("工作经验要求")
    private String experience;

    @ExcelProperty("薪资范围")
    private String salaryRange;

    @ExcelProperty("职位描述")
    private String content;

    @ExcelProperty("职位开始时间")
    private LocalDateTime jobStartTime;

    @ExcelProperty("职位结束时间")
    private LocalDateTime jobEndTime;

    @ExcelProperty("职位开始时间 (中文格式)")
    private String jobStartTimeCn;

    @ExcelProperty("职位结束时间 (中文格式)")
    private String jobEndTimeCn;

    @ExcelProperty("是否被收藏 (布尔类型)")
    private Boolean isCollect;

    @ExcelProperty("创建时间")
    private LocalDateTime createTime;

}
