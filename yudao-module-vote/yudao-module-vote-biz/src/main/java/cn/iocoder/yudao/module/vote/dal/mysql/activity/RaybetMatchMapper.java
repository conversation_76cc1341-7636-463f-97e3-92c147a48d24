package cn.iocoder.yudao.module.vote.dal.mysql.activity;

import cn.iocoder.yudao.framework.mybatis.core.mapper.BaseMapperX;
import cn.iocoder.yudao.module.vote.dal.dataobject.activity.RaybetMatchDO;
import cn.iocoder.yudao.module.vote.dal.dataobject.activity.RaybetWinnerDO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Select;

import java.util.List;


/**
 * 投票活动 Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface RaybetMatchMapper extends BaseMapperX<RaybetMatchDO> {
    @Select("select * from vote_raybet_match m where m.score is not null and m.start_time  >  STR_TO_DATE('2024-09-29', '%Y-%m-%d') and  m.game_name in ('DOTA2', '英雄联盟' , '王者荣耀' , 'CS2') and round = #{round}" )
    List<RaybetMatchDO> selectAnalysis(String round);
}
