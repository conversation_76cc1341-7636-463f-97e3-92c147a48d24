package cn.iocoder.yudao.module.vote.convert.jobpositions;

import java.util.*;

import cn.iocoder.yudao.framework.common.pojo.PageResult;

import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;
import cn.iocoder.yudao.module.vote.controller.admin.jobpositions.vo.*;
import cn.iocoder.yudao.module.vote.dal.dataobject.jobpositions.JobPositionsDO;

/**
 * 职位信息 Convert
 *
 * <AUTHOR>
 */
@Mapper
public interface JobPositionsConvert {

    JobPositionsConvert INSTANCE = Mappers.getMapper(JobPositionsConvert.class);

    JobPositionsDO convert(JobPositionsCreateReqVO bean);

    JobPositionsDO convert(JobPositionsUpdateReqVO bean);

    JobPositionsRespVO convert(JobPositionsDO bean);

    List<JobPositionsRespVO> convertList(List<JobPositionsDO> list);

    PageResult<JobPositionsRespVO> convertPage(PageResult<JobPositionsDO> page);

    List<JobPositionsExcelVO> convertList02(List<JobPositionsDO> list);

}
