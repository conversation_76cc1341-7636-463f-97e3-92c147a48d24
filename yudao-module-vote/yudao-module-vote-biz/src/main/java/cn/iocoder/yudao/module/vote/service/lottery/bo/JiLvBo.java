package cn.iocoder.yudao.module.vote.service.lottery.bo;

import cn.hutool.core.util.StrUtil;
import lombok.Data;

/**
 * 几率Bo类
 */
@Data
public class JiLvBo {
    private String eventType;
    /**
     * 多少期没有出
     */
    private int missCount;
    /**
     * 对应的几率内容是？
     */
    private double probability;
    /**
     * 需要追注的次数。
     */
    private int followCount;

    @Override
    public String toString() {
//        return eventType + ": 连续不出次数 = " + missCount + ", 下一次不出的概率 = " + String.format("%.2f%%", probability * 100);
        return StrUtil.format("{}[{}]:{}",eventType,missCount,followCount);

    }
}
