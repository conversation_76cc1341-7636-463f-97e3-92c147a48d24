package cn.iocoder.yudao.module.vote.controller.admin.jobpositions.vo;

import lombok.*;
import java.util.*;
import io.swagger.v3.oas.annotations.media.Schema;
import cn.iocoder.yudao.framework.common.pojo.PageParam;
import org.springframework.format.annotation.DateTimeFormat;
import java.time.LocalDateTime;

import static cn.iocoder.yudao.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;

@Schema(description = "管理后台 - 职位信息分页 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class JobPositionsPageReqVO extends PageParam {

    @Schema(description = "公司ID", example = "17113")
    private Integer companyId;

    @Schema(description = "公司名称", example = "李四")
    private String companyName;

    @Schema(description = "公告ID", example = "25758")
    private Integer annoId;

    @Schema(description = "公告名称", example = "芋艿")
    private String annoName;

    @Schema(description = "公告链接")
    private String annoLink;

    @Schema(description = "类型", example = "1")
    private String type;

    @Schema(description = "职位名称")
    private String title;

    @Schema(description = "工作地点")
    private String location;

    @Schema(description = "链接")
    private String link;

    @Schema(description = "专业要求")
    private String specialty;

    @Schema(description = "学历要求")
    private String education;

    @Schema(description = "学历列表 (使用 JSON 类型存储)")
    private String educationCnlist;

    @Schema(description = "工作经验要求")
    private String experience;

    @Schema(description = "薪资范围")
    private String salaryRange;

    @Schema(description = "职位描述")
    private String content;

    @Schema(description = "职位开始时间")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime[] jobStartTime;

    @Schema(description = "职位结束时间")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime[] jobEndTime;

    @Schema(description = "职位开始时间 (中文格式)")
    private String jobStartTimeCn;

    @Schema(description = "职位结束时间 (中文格式)")
    private String jobEndTimeCn;

    @Schema(description = "是否被收藏 (布尔类型)")
    private Boolean isCollect;

    @Schema(description = "创建时间")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime[] createTime;

}
