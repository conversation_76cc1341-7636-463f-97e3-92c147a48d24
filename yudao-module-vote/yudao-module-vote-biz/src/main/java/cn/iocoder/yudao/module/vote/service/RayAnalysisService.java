package cn.iocoder.yudao.module.vote.service;

import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.math.MathUtil;
import cn.hutool.json.JSONObject;
import cn.iocoder.yudao.framework.common.function.ThreePredicates;
import cn.iocoder.yudao.module.vote.controller.app.vo.RaytwoTeamsOdd;
import cn.iocoder.yudao.module.vote.controller.app.vo.RaytwoTeamsOddEnum;
import cn.iocoder.yudao.module.vote.dal.dataobject.activity.RaybetMatchDO;
import cn.iocoder.yudao.module.vote.dal.dataobject.activity.RaybetWinnerDO;
import org.springframework.stereotype.Service;

import java.time.Instant;
import java.time.ZoneId;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.concurrent.atomic.AtomicBoolean;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.concurrent.atomic.AtomicReference;
import java.util.function.BiPredicate;

@Service
public class RayAnalysisService {
    /*
     bo全局的情况，属于一边倒的。 比如： 4:0 ,  3:0 ， 2:0.
     然后在，最后一句投最后一小句比赛。
     */
    public Map<String,Object> bo_predominate(RaybetMatchDO raybetMatchDO , List<RaybetWinnerDO> winners){
        Map<String,Object> res = new HashMap<>();
        int size = winners.size();
        int winnerNum = size/2; // 胜利的次数，比如5句3胜制度。
        int team1 = 0;
        int team2 = 0;
        boolean isWin = true; // 投的情况，赢还是输
        // 跳过final局数
        for (int i = 1; i < size; i++) {
            RaybetWinnerDO raybetWinner = winners.get(i);
            String winner = raybetWinner.getWinner();
            Integer status = raybetWinner.getStatus();
            if(status == 0 ){
                if( i < winnerNum){ // 1:1的情况，就不投，就过滤掉。
                    if(RaytwoTeamsOddEnum.ONE.equals(winner)){
                        if(team2 > 0 ){
                            return null;
                        }else{
                            team1 ++;
                        }
                    } else if (RaytwoTeamsOddEnum.TWO.equals(winner)) {
                        if(team1 > 0 ){
                            return null;
                        }else{
                            team2 ++;
                        }
                    }
                }else if ( winnerNum == i){ // 判断投的情况，是赢还是输
                    if(RaytwoTeamsOddEnum.ONE.equals(winner)){
                        team1 ++;
                        if(team2 > 0 ){
                            isWin = false;
                        }else{
                            break; // 胜利啦，后面都是失效的状态
                        }
                    } else if (RaytwoTeamsOddEnum.TWO.equals(winner)) {
                        team2 ++;
                        if(team1 > 0 ){
                            isWin = false;
                        }else{
                            break; // 胜利啦，后面都是失效的状态
                        }
                    }
                }else{  // 最后几句的情况总结。
                    if(RaytwoTeamsOddEnum.ONE.equals(winner)){
                        team1 ++;
                    } else if (RaytwoTeamsOddEnum.TWO.equals(winner)) {
                        team2 ++;
                    }
                }

            }
        }
        //
        res.put("size",size + "");
        res.put("winner", winners.get(0).getWinner() );
        res.put("winnerNum",winnerNum + "");
        res.put("team1",team1 + "");
        res.put("team2",team2 + "");
        res.put("isWin",isWin + "");
        //
        return res;
    }
    // 对冲
    public Map<String,Object> hedge(List<RaytwoTeamsOdd> sortInstants){
            final AtomicReference<RaytwoTeamsOddEnum> follow = new AtomicReference<>(RaytwoTeamsOddEnum.ONE);

            return handleRaytwoTeamsOddList(sortInstants, (json,raytwoTeamsOdd,index) ->{
                Double team1Odd = raytwoTeamsOdd.getTeam1Odd();
                Double team2Odd = raytwoTeamsOdd.getTeam2Odd();
                if (index == 0) {
                    if (team1Odd < 1.7 || team2Odd < 1.7 || team1Odd == team2Odd) {
                        return true; // 第一个点位，没到预设目标。排除。
                    }
                    //
                    if (team1Odd < team2Odd) {
                        follow.set(RaytwoTeamsOddEnum.TWO);
                    }
                    // 设置跟随的内容。
                    json.putIfAbsent("follow" , follow.get().getTeam() );
                }else{
                    RaytwoTeamsOddEnum teamsOddEnum = follow.get();
                    double followOdd = raytwoTeamsOdd.getRealOdd(teamsOddEnum);
                    if (followOdd > 3.3 ) {
                        // 第一次就跟随。
                        json.putIfAbsent("followOdd" , followOdd );
                    }
                }
                return false;
            } );


    }



    // 对冲 时间差的情况。  这个分析得出,1.1以下是稳赢。
    public Map<String,Object> hedgeTimeDiff(List<RaytwoTeamsOdd> sortInstants){
            final AtomicReference<RaytwoTeamsOddEnum> follow = new AtomicReference<>(RaytwoTeamsOddEnum.ONE);

            return handleRaytwoTeamsOddList(sortInstants, (json,raytwoTeamsOdd,index) ->{
                Double team1Odd = raytwoTeamsOdd.getTeam1Odd();
                Double team2Odd = raytwoTeamsOdd.getTeam2Odd();
                if (index == 0) {
                    if (team1Odd < 1.2) {
                        follow.set(RaytwoTeamsOddEnum.TWO);
                    } else if (team2Odd < 1.2) {
                        follow.set(RaytwoTeamsOddEnum.ONE);
                    } else {
                        return true; // 第一个点位，没到预设目标。排除。
                    }
                    // 设置跟随的内容。
                    json.putIfAbsent("follow" , follow.get().getTeam() );
                }else{
                    RaytwoTeamsOddEnum teamsOddEnum = follow.get();
                    double followOdd = raytwoTeamsOdd.getOdd(teamsOddEnum);
                    double reverseOdd = raytwoTeamsOdd.getReverseOdd(teamsOddEnum);
                    // 这个时间点，第一次交汇  ( 接近交汇的时候)
                    if ((Math.abs(followOdd - reverseOdd) <= 0.3 || followOdd < reverseOdd)) {
                        json.putIfAbsent("followOdd",followOdd);

                    }
                    //
                    if (reverseOdd > 3.0) {
                        json.putIfAbsent("reverseOdd",reverseOdd);
                    }
                }
                return false;
            } );

    }


    // 混乱的纠缠   (team1Odd >= 1.1  && team1Odd <= 1.7)  || (team2Odd >= 1.1  && team2Odd <= 1.7) || team1Odd == team2Odd
    public Map<String,Object> tangle(List<RaytwoTeamsOdd> sortInstants){
        final AtomicReference<RaytwoTeamsOddEnum> bet = new AtomicReference<>(RaytwoTeamsOddEnum.ONE);
        final AtomicReference<RaytwoTeamsOddEnum> follow = new AtomicReference<>(null);

        return handleRaytwoTeamsOddList(sortInstants, true , (json,raytwoTeamsOdd,index) ->{
            Double team1Odd = raytwoTeamsOdd.getTeam1Odd();
            Double team2Odd = raytwoTeamsOdd.getTeam2Odd();
            if (index == 0) {
                if (  (team1Odd >= 1.1  && team1Odd <= 1.7) ) {
//                    return false;
                } else if ((team2Odd >= 1.1  && team2Odd <= 1.7)) {
                    bet.set(RaytwoTeamsOddEnum.TWO); // 设置投注的内容。
                } else{
                    return true;  // 第一个点位，没到预设目标。排除。 查看所有的比赛类型。
                }
            }else{
                RaytwoTeamsOddEnum teamsOddEnum = bet.get();
                double betOdd = raytwoTeamsOdd.getOdd(teamsOddEnum);
                double reverseBetOdd = raytwoTeamsOdd.getReverseOdd(teamsOddEnum);
                if(betOdd >= 1.7){ // 关注投注的内容
                    // 可以开始跟随投注啦。
                    follow.set(teamsOddEnum.reverse());
                    json.putIfAbsent("follow" , teamsOddEnum.reverse().getTeam() );
                }
                RaytwoTeamsOddEnum followTeamsOddEnum = follow.get();
                // 开始跟随以后到达啦3.3以上的内容。
                if(followTeamsOddEnum != null && reverseBetOdd > 3.3){
                    json.putIfAbsent("followOdd",reverseBetOdd);
                }
            }
            return false;
        } );


    }

    /**
     * 所有展示的内容，用于自己的分析操作。
     * @param sortInstants
     * @return
     */
    public Map<String,Object> all(List<RaytwoTeamsOdd> sortInstants){
        return handleRaytwoTeamsOddList(sortInstants, true , (json,raytwoTeamsOdd,index) ->{
            return false;
        } );
    }
    /**
     * 循环处理sortInstants+回调的形式。  抽取公共方法出来。
     * @param sortInstants
     * @param isFilter 里面的数据并不保证是真实的数据，可能是拉起过的，需要自己进行判断。
     * @return
     */
    private JSONObject handleRaytwoTeamsOddList(List<RaytwoTeamsOdd> sortInstants  , ThreePredicates<JSONObject, RaytwoTeamsOdd , Integer> isFilter){
        return  handleRaytwoTeamsOddList(sortInstants,false,isFilter);
    }
    private JSONObject handleRaytwoTeamsOddList(List<RaytwoTeamsOdd> sortInstants , boolean showTangle , ThreePredicates<JSONObject, RaytwoTeamsOdd , Integer> isFilter){
        int size = sortInstants.size();
        JSONObject res = new JSONObject();
        res.put("size",size);
        //
        double team1MinOdd = 1000; // 代表没投上
        double team2MinOdd = 1000; // 代表没投上
        double team1MaxOdd = 0;
        double team2MaxOdd = 0;
        // 纠缠的最值
        double tangleMinOdd = 1000;
        double tangleMaxOdd = 0;
        //
        int tangleCount = 0; // 纠缠的次数。
        RaytwoTeamsOddEnum follow = RaytwoTeamsOddEnum.ONE; // 默认为： 投啦队伍2 ， 仅仅需要关注队伍1
        for (int i = 0; i < size; i++) {
            RaytwoTeamsOdd raytwoTeamsOdd = sortInstants.get(i);
            Double team1Odd = raytwoTeamsOdd.getTeam1Odd();
            Double team2Odd = raytwoTeamsOdd.getTeam2Odd();
            Instant time = raytwoTeamsOdd.getTime();
            String timeFormat = DateUtil.format(time.atZone(ZoneId.systemDefault()).toLocalDateTime(), DatePattern.NORM_DATETIME_PATTERN);

            if( (team1Odd >= 1 && team2Odd >= 1)  || i+1 == size ) { // 有0.01这种垃圾数据。 或者最后一个数据，这个在有些时候需要用到
                res.putIfAbsent("teamOddTime", timeFormat);
                // 1是真实的情况才赋值
                if(raytwoTeamsOdd.isTeam1Real()){
                    // 记录一开始的值
                    res.putIfAbsent("team1Odd", team1Odd.toString());
                    // 记录最小值
                    if (team1Odd < team1MinOdd) {
                        team1MinOdd = team1Odd;
                    }
                    // 记录最大值
                    if (team1Odd > team1MaxOdd) {
                        team1MaxOdd = team1Odd;
                    }
                }
                // 2是真实的情况才赋值
                if (raytwoTeamsOdd.isTeam2Real()) {
                    // 记录一开始的值
                    res.putIfAbsent("team2Odd", team2Odd.toString());
                    // 记录最小值
                    if (team2Odd < team2MinOdd) {
                        team2MinOdd = team2Odd;
                    }
                    // 记录最大值
                    if (team2Odd > team2MaxOdd) {
                        team2MaxOdd = team2Odd;
                    }
                }
                // 记录纠缠的情况（一会1数字大，一会2数字大）
                if(showTangle){
                    if(i == 0){
                        // 设置起始关注的队伍
                        if (team1Odd < team2Odd) {
                            follow = RaytwoTeamsOddEnum.TWO; // 1小于2 ，跟随2 =》 跟随大的。
                        }
                    }
                    // 判断是否纠缠
                    if( raytwoTeamsOdd.isTeam1Real() && raytwoTeamsOdd.isTeam2Real()){
                        double followOdd = raytwoTeamsOdd.getOdd(follow);
                        double reverseOdd = raytwoTeamsOdd.getReverseOdd(follow);

                        if (RaytwoTeamsOddEnum.ONE.equals(follow) && followOdd <= reverseOdd) {
                            res.put("tangle-team1Odd-"+tangleCount,team1Odd);
                            res.put("tangle-team2Odd-"+tangleCount,team2Odd);
                            res.put("tangle-tangleMinOdd-"+tangleCount,tangleMinOdd);
                            res.put("tangle-tangleMaxOdd-"+tangleCount,tangleMaxOdd);
                            res.put("tangle-follow-"+tangleCount,follow.getTeam());
                            res.putIfAbsent("tangle-time-"+tangleCount, timeFormat);
                            tangleCount++;
                            res.put("tangle",tangleCount);
                            // 重置内容
                            follow = RaytwoTeamsOddEnum.TWO;
                            tangleMinOdd = 1000;
                            tangleMaxOdd = 0;
                        } else if (RaytwoTeamsOddEnum.TWO.equals(follow) && followOdd <= reverseOdd) {
                            res.put("tangle-team1Odd-"+tangleCount,team1Odd);
                            res.put("tangle-team2Odd-"+tangleCount,team2Odd);
                            res.put("tangle-tangleMinOdd-"+tangleCount,tangleMinOdd);
                            res.put("tangle-tangleMaxOdd-"+tangleCount,tangleMaxOdd);
                            res.put("tangle-follow-"+tangleCount,follow.getTeam());
                            res.putIfAbsent("tangle-time-"+tangleCount, timeFormat);
                            tangleCount++;
                            res.put("tangle",tangleCount);
                            // 重置内容
                            follow = RaytwoTeamsOddEnum.ONE;
                            tangleMinOdd = 1000;
                            tangleMaxOdd = 0;
                        }
                    }
                    // 判断纠缠的高低峰值。
                    if(RaytwoTeamsOddEnum.ONE.equals(follow) ){
                        if (raytwoTeamsOdd.isTeam1Real() && team1Odd > tangleMaxOdd ) {
                            tangleMaxOdd = team1Odd;
                        }

                        if (raytwoTeamsOdd.isTeam2Real() && team2Odd < tangleMinOdd ) {
                            tangleMinOdd = team2Odd;
                        }
                    } else if (RaytwoTeamsOddEnum.TWO.equals(follow)) {
                        if (raytwoTeamsOdd.isTeam2Real() && team2Odd > tangleMaxOdd ) {
                            tangleMaxOdd = team2Odd;
                        }

                        if (raytwoTeamsOdd.isTeam1Real() && team1Odd < tangleMinOdd ) {
                            tangleMinOdd = team1Odd;
                        }
                    }
                }
                // 执行业务逻辑，并判断是否过滤。
                if(isFilter.test(res,raytwoTeamsOdd,i)) return null;

            }
        }
        //
        res.put("team1MinOdd",team1MinOdd + "");
        res.put("team2MinOdd",team2MinOdd + "");
        res.put("team1MaxOdd",team1MaxOdd+ "");
        res.put("team2MaxOdd",team2MaxOdd+ "");

        //
        return res;
    }
}
