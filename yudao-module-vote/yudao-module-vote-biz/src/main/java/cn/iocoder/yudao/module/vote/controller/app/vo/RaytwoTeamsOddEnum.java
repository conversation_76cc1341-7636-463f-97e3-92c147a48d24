package cn.iocoder.yudao.module.vote.controller.app.vo;

public enum RaytwoTeamsOddEnum {
    ONE("team1") , TWO("team2");
    private final String team;

    RaytwoTeamsOddEnum(String team) {
        this.team = team;
    }

    public String getTeam() {
        return team;
    }

    /**
     * 获得反转的对象
     * @return
     */
    public RaytwoTeamsOddEnum reverse() {
        if (ONE.equals(this))  return TWO;
        if (TWO.equals(this))  return ONE;
        return null;
    }

    public boolean equals(String team){
        return this.team.equals(team);
    }

    public final static RaytwoTeamsOddEnum valueOfTeam(String team){
        RaytwoTeamsOddEnum[] values = values();
        for (RaytwoTeamsOddEnum teamsOddEnum : values) {
            if (teamsOddEnum.equals(team)) return teamsOddEnum;
        }
        return null;
    }
}
