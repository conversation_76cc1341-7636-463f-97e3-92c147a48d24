package cn.iocoder.yudao.module.vote.dal.mysql.cyberstar;

import java.util.*;

import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.mybatis.core.query.LambdaQueryWrapperX;
import cn.iocoder.yudao.framework.mybatis.core.mapper.BaseMapperX;
import cn.iocoder.yudao.module.vote.dal.dataobject.cyberstar.CyberStarDO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import cn.iocoder.yudao.module.vote.controller.admin.cyberstar.vo.*;

/**
 * 网红主播 Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface CyberStarMapper extends BaseMapperX<CyberStarDO> {

    default PageResult<CyberStarDO> selectPage(CyberStarPageReqVO reqVO) {
        return selectPage(reqVO, new LambdaQueryWrapperX<CyberStarDO>()
                .likeIfPresent(CyberStarDO::getNickname, reqVO.getNickname())
                .eqIfPresent(CyberStarDO::getUserId, reqVO.getUserId())
                .eqIfPresent(CyberStarDO::getImg, reqVO.getImg())
                .eqIfPresent(CyberStarDO::getCookie, reqVO.getCookie())
                .eqIfPresent(CyberStarDO::getGmvTodayAmountDouble, reqVO.getGmvTodayAmountDouble())
                .eqIfPresent(CyberStarDO::getDiveAmount, reqVO.getDiveAmount())
                .eqIfPresent(CyberStarDO::getGiftTodayAmountDouble, reqVO.getGiftTodayAmountDouble())
                .betweenIfPresent(CyberStarDO::getCreateTime, reqVO.getCreateTime())
                .orderByDesc(CyberStarDO::getId));
    }

    /**
     * 查询前N名GMV的网红主播数据，只返回id和gmvTodayAmountDouble字段
     *
     * @param limit 限制数量
     * @return 网红主播列表
     */
    @Select("SELECT id, gmv_today_amount_double FROM vote_cyber_star WHERE deleted = 0 AND gmv_today_amount_double > 0 ORDER BY gmv_today_amount_double DESC LIMIT #{limit}")
    List<CyberStarDO> selectTopGmv(@Param("limit") int limit);

    /**
     * 查询前N名Gift的网红主播数据，只返回id和giftTodayAmountDouble字段
     *
     * @param limit 限制数量
     * @return 网红主播列表
     */
    @Select("SELECT id, gift_today_amount_double FROM vote_cyber_star WHERE deleted = 0 AND gift_today_amount_double > 0 ORDER BY gift_today_amount_double DESC LIMIT #{limit}")
    List<CyberStarDO> selectTopGift(@Param("limit") int limit);
}