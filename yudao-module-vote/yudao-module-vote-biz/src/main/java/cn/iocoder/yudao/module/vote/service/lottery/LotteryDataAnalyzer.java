package cn.iocoder.yudao.module.vote.service.lottery;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.StrUtil;
import cn.iocoder.yudao.framework.common.util.collection.CollectionUtils;
import cn.iocoder.yudao.framework.common.util.date.DateUtils;
import cn.iocoder.yudao.framework.tenant.core.util.TenantUtils;
import cn.iocoder.yudao.module.system.api.mail.MailSendApi;
import cn.iocoder.yudao.module.system.api.mail.dto.MailSendSingleToUserReqDTO;
import com.google.gson.Gson;
import com.google.gson.JsonObject;
import com.google.gson.JsonArray;
import lombok.extern.slf4j.Slf4j;
import okhttp3.*;
import org.springframework.boot.CommandLineRunner;
import org.springframework.scheduling.annotation.Scheduled;

import javax.annotation.Resource;
import java.io.IOException;
import java.time.LocalDate;
import java.util.*;
import java.util.concurrent.Executors;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.TimeUnit;

@Slf4j
//@Service  // 他现在来不及下注。
public class LotteryDataAnalyzer implements CommandLineRunner {
    private static final String URL = "https://www.kuai28.com/portal/api/lottery_details";
    private  final OkHttpClient client = new OkHttpClient();
    private  final Gson gson = new Gson();
    private int threshold = 7; // 超过X次就报警。
    // 定义一个线程池来调度任务，这个是单个的池子
    private final ScheduledExecutorService scheduler = Executors.newSingleThreadScheduledExecutor();

    @Resource
    private MailSendApi mailSendApi;

    private List<Integer> analyzerAllList; // 统计整体出现次数的list
    
    public  String getLotteryData(String time , int limit) {
        String flag = "";
        try {
            // 构建请求体
            JsonObject requestBody = new JsonObject();
            requestBody.addProperty("limits", limit);
            requestBody.addProperty("date", time);
            requestBody.addProperty("type", "jnd28");
            requestBody.addProperty("curr", 1);

            // 创建请求
            RequestBody body = RequestBody.create(
                requestBody.toString(),
                MediaType.parse("application/json; charset=utf-8")
            );

            Request request = new Request.Builder()
                .url(URL)
                .post(body)
                .build();

            // 发送请求
            try (Response response = client.newCall(request).execute()) {
                if (!response.isSuccessful()) throw new IOException("Unexpected code " + response);
                
                String responseData = response.body().string();
                JsonObject jsonResponse = gson.fromJson(responseData, JsonObject.class);
                JsonArray dataList = jsonResponse.getAsJsonObject("data").getAsJsonArray("list");

                // 标记龙虎和
                for (int i = 0; i < dataList.size(); i++) {
                    JsonObject item = dataList.get(i).getAsJsonObject();
                    String openCode = item.get("open_code").getAsString();
                    int firstNum = Integer.parseInt(openCode.substring(0, 1));
                    int lastNum = Integer.parseInt(openCode.substring(openCode.length() - 1));
                    
                    if (firstNum > lastNum) {
                        item.addProperty("type_name", "龙");
                    } else if (firstNum < lastNum) {
                        item.addProperty("type_name", "虎");
                    } else {
                        item.addProperty("type_name", "和");
                    }
                }

                // 统计连续不出现的次数
                List<Integer> longStreaks = new ArrayList<>();
                List<Integer> huStreaks = new ArrayList<>();
                int noLongStreak = 0;
                int noHuStreak = 0;

                for (int i = 0; i < dataList.size(); i++) {
                    JsonObject item = dataList.get(i).getAsJsonObject();
                    String openTime = item.get("open_time").getAsString();
                    String typeName = item.get("type_name").getAsString();

                    // 计算不出龙的连续次数
                    if (!typeName.equals("龙")) {
                        noLongStreak++;
                    } else {
                        if (noLongStreak >= threshold) {
                            log.debug( "{}时间里连续{}次不出龙" , openTime , noLongStreak);
                            longStreaks.add(noLongStreak);
                            CollectionUtils.addIfNotNull(this.analyzerAllList,noLongStreak);
                        }
                        noLongStreak = 0;
                    }

                    // 计算不出虎的连续次数
                    if (!typeName.equals("虎")) {
                        noHuStreak++;
                    } else {
                        if (noHuStreak >= threshold) {
                            log.debug( "{}时间里连续{}次不出虎" , openTime , noHuStreak);
                            huStreaks.add(noHuStreak);
                            CollectionUtils.addIfNotNull(this.analyzerAllList,noHuStreak);
                        }
                        noHuStreak = 0;
                    }
                }

                // 检查最后一段
                if (noLongStreak >= threshold) {
                    longStreaks.add(noLongStreak);
                }
                if (noHuStreak >= threshold) {
                    huStreaks.add(noHuStreak);
                }

                // 输出结果
                log.debug("连续不出现龙超过8次的次数: " + longStreaks.size());
                log.debug("具体连续不出龙次数: " + longStreaks);
                log.debug("连续不出现虎超过8次的次数: " + huStreaks.size());
                log.debug("具体连续不出虎次数: " + huStreaks);
                log.debug("日期: " + time);
                if(!longStreaks.isEmpty())  flag = "龙";
                if(!huStreaks.isEmpty())    flag = "虎";

            }
        } catch (Exception e) {
            log.error("jianada28 api error",e);
        }
        return flag;
    }

    public static void main(String[] args) {
        LotteryDataAnalyzer lotteryDataAnalyzer = new LotteryDataAnalyzer();
        lotteryDataAnalyzer.analyzerAllList = new ArrayList<>();
        lotteryDataAnalyzer.threshold = 9;
        LocalDate startDatedate = LocalDate.parse("2024-11-30");
//        LocalDate startDatedate = LocalDate.of(2025,2,1);
        List<LocalDate> between = DateUtils.getBetween(startDatedate, LocalDate.now());
        between.forEach(date ->{
            String time = DateUtil.format(date.atStartOfDay(), "yyyy-M-d");
            String lotteryData = lotteryDataAnalyzer.getLotteryData(time,9999999);
        });
        log.debug("一共{}次;展示所有的内容情况为:{}",CollectionUtil.size(lotteryDataAnalyzer.analyzerAllList) ,lotteryDataAnalyzer.analyzerAllList);
    }
    /*
    用cron表达式，生成每天00:03:00 开始， 每隔3分40秒 执行一次。
    1. 先设定每天 00:03:00 执行一次任务。
    2. 任务内部处理每隔 3 分 40 秒执行一次。

     */
    @Scheduled(cron = "0 3 0 * * ?") // 每天 00:03:00 执行任务。
    public void schedulerLookForOpportunities(){
        // 每隔 3 分 40 秒执行一次任务
        scheduler.scheduleWithFixedDelay(this::lookForOpportunities, 0, 220, TimeUnit.SECONDS);
    }
    @Override
    public void run(String... args) throws Exception {
        // 这个解决刚启动，没有定时调度，必须要等待00:03分才开始调度。
        this.schedulerLookForOpportunities();
    }

    public void lookForOpportunities(){
        try{
            String today = DateUtil.today();
            String lotteryData = getLotteryData(today,threshold+2);
            // 有提示就报警咯。
            if(StrUtil.isNotEmpty(lotteryData)){
                this.sendMail(lotteryData,threshold);
            }
        } catch (Exception e) {
            log.error("jianada28 Look for opportunities",e);
        }
    }

    public void sendMail(String type , Integer count){
        // 1. 准备参数
        Long userId = 141L; // 示例中写死，你可以改成你业务中的 userId 噢
        String templateCode = "lottery_jianada28_follow"; // 邮件模版，记得在【邮箱管理】中配置噢
        Map<String, Object> templateParams = new HashMap<>();
        templateParams.put("type",  type);
        templateParams.put("count", count);
        // 2. 发送邮件
        MailSendSingleToUserReqDTO mailSendSingleToUserReqDTO = new MailSendSingleToUserReqDTO();
        mailSendSingleToUserReqDTO.setUserId(userId).setTemplateCode(templateCode).setTemplateParams(templateParams);
        TenantUtils.execute(1l, () -> { mailSendApi.sendSingleMailToAdmin(mailSendSingleToUserReqDTO);});

    }


}