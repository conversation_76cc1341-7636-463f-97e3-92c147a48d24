package cn.iocoder.yudao.module.vote.controller.admin.lottery.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;
import java.util.*;
import javax.validation.constraints.*;
import org.springframework.format.annotation.DateTimeFormat;
import java.time.LocalDateTime;

@Schema(description = "管理后台 - kuai28.com新增/修改 Request VO")
@Data
public class LotterySaveReqVO {

    @Schema(description = "标识符ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "9903")
    private Long id;

    @Schema(description = "效果标识，用于表示某种效果或状态的整数类型")
    private Integer efficacy;

    @Schema(description = "完整预期值，用于存储特定的大整数值，如期号等")
    private Long fullExpect;

    @Schema(description = "数字排序，用于表示开奖号码按某种规则排序后的结果，字符串类型")
    private String numSort;

    @Schema(description = "数字类型，例如单双、大小等分类，字符串类型", example = "1")
    private String numType;

    @Schema(description = "开奖号码，实际开奖的号码组合，以逗号分隔的字符串形式存储")
    private String openCode;

    @Schema(description = "开奖时间，记录具体的开奖日期和时间")
    private LocalDateTime openTime;

    @Schema(description = "和数值，所有开奖号码相加得到的总和，整数类型")
    private Integer sum;

    @Schema(description = "类型名称，用于描述彩票类型的名称，字符串类型", example = "张三")
    private String typeName;

    @Schema(description = "品种类型，用于区分不同种类的游戏，例如jnd28, 字符串类型", example = "1")
    private String varietyType;

}