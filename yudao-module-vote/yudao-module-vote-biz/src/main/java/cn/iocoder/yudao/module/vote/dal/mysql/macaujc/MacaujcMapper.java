package cn.iocoder.yudao.module.vote.dal.mysql.macaujc;

import java.util.*;

import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.mybatis.core.query.LambdaQueryWrapperX;
import cn.iocoder.yudao.framework.mybatis.core.mapper.BaseMapperX;
import cn.iocoder.yudao.module.vote.dal.dataobject.macaujc.MacaujcDO;
import org.apache.ibatis.annotations.Mapper;
import cn.iocoder.yudao.module.vote.controller.admin.macaujc.vo.*;

/**
 * 彩票 Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface MacaujcMapper extends BaseMapperX<MacaujcDO> {

    default PageResult<MacaujcDO> selectPage(MacaujcPageReqVO reqVO) {
        return selectPage(reqVO, new LambdaQueryWrapperX<MacaujcDO>()
                .eqIfPresent(MacaujcDO::getOpenCode, reqVO.getOpenCode())
                .eqIfPresent(MacaujcDO::getZodiac, reqVO.getZodiac())
                .eqIfPresent(MacaujcDO::getWave, reqVO.getWave())
                .betweenIfPresent(MacaujcDO::getOpenTime, reqVO.getOpenTime())
                .eqIfPresent(MacaujcDO::getKind, reqVO.getKind())
                .betweenIfPresent(MacaujcDO::getCreateTime, reqVO.getCreateTime())
                .orderByDesc(MacaujcDO::getExpect));
    }

}