package cn.iocoder.yudao.module.vote.controller.admin.activity.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;
import java.util.*;
import java.time.LocalDateTime;
import java.time.LocalDateTime;
import java.time.LocalDateTime;
import java.time.LocalDateTime;
import javax.validation.constraints.*;
import org.springframework.format.annotation.DateTimeFormat;

import static cn.iocoder.yudao.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;

/**
* 投票活动 Base VO，提供给添加、修改、详细的子 VO 使用
* 如果子 VO 存在差异的字段，请不要添加到这里，影响 Swagger 文档生成
*/
@Data
public class ActivityBaseVO {

    @Schema(description = "名称", example = "赵六")
    private String name;

    @Schema(description = "横幅图片")
    private String bannerImage;

    @Schema(description = "首页轮播图片")
    private String homeImage;

    @Schema(description = "奖品图片")
    private String prizeImage;

    @Schema(description = "介绍图片")
    private String introduceImage;

    @Schema(description = "分享标题")
    private String shareTitle;

    @Schema(description = "活动公告")
    private String notice;

    @Schema(description = "背景图")
    private String background;

    @Schema(description = "开始时间")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime startTime;

    @Schema(description = "结束时间")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime endTime;

    @Schema(description = "浏览数")
    private Integer viewNumber;

    @Schema(description = "票数总计")
    private Integer voteNumber;

    @Schema(description = "礼物展示条数")
    private Integer giftShowNumber;

    @Schema(description = "去其他的微信小程序appId", example = "30188")
    private String goWxAppid;

    @Schema(description = "状态标识", required = true, example = "2")
    @NotNull(message = "状态标识不能为空")
    private Byte status;

    @Schema(description = "每天可为每个选手投几票")
    private Integer votesPerPerson;

    @Schema(description = "可为最多几名选手投票", example = "26423")
    private Integer playerMaxCount;

    @Schema(description = "主题")
    private String theme;

    @Schema(description = "乐观锁")
    private Integer revision;

    @Schema(description = "部门ID", example = "495")
    private Long deptId;

    @Schema(description = "用户ID", example = "24221")
    private Long userId;

}
