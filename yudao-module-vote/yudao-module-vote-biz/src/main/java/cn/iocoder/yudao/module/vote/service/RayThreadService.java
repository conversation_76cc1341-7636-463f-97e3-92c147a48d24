package cn.iocoder.yudao.module.vote.service;

import cn.hutool.core.date.DateUnit;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.date.LocalDateTimeUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.http.Header;
import cn.hutool.http.HttpRequest;
import cn.hutool.json.JSONArray;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.TaskScheduler;
import org.springframework.scheduling.support.CronTrigger;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.Date;
import java.util.concurrent.Executors;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.ScheduledFuture;
import java.util.concurrent.TimeUnit;

import static cn.iocoder.yudao.framework.common.exception.util.ServiceExceptionUtil.exception;
import static cn.iocoder.yudao.module.vote.enums.ErrorCodeConstants.*;
// 定时任务管理
@Slf4j
@Service
public class RayThreadService {
    @Autowired
    private InfluxDBService influxDBService;
    public void runScheduled(String match_id)  {
            JSONObject resTimeJsonObject = doHttp(match_id);
            String startTimeStr =   resTimeJsonObject.getByPath("result.start_time", String.class);
            String endTimeStr =     resTimeJsonObject.getByPath("result.end_time", String.class);
            if(StrUtil.isNotBlank(startTimeStr) && StrUtil.isNotBlank(endTimeStr) ){
                // 定义开始时间和结束时间
                String format = "yyyy-MM-dd HH:mm:ss";
                LocalDateTime startTime = LocalDateTimeUtil.parse(startTimeStr, format);
                LocalDateTime endTime =   LocalDateTimeUtil.parse(endTimeStr, format);
                // 创建一个ScheduledExecutorService实例
                ScheduledExecutorService executor = Executors.newScheduledThreadPool(20);

                // 定义任务
                Runnable task = () -> {
                    try{
                        JSONObject resOddsJson = doHttp(match_id);
                        JSONArray odds = resOddsJson.getByPath("result.odds", JSONArray.class);
                        for (int i = 0; i < odds.size(); i++) {
                            JSONObject odd = odds.getJSONObject(i);
                            String groupName = odd.getStr("group_name");   //
                            if(StrUtil.contains(groupName,"获胜者")){
                                String matchId = odd.getStr("match_id");
                                String matchStage = odd.getStr("match_stage"); // 全场，第一场。。。
                                String name = odd.getStr("name"); // 名称
                                String odds_id = odd.getStr("odds_id"); //
                                Double odds_num = odd.getDouble("odds"); //
                                String team = StrUtil.join("-", matchId, matchStage, name);
                                if(odds != null && StrUtil.isNotBlank(team)){
                                    log.info("team=[{}],odds_num={}",team,odds_num);
//                                    influxDBService.insertOddsData(team,odds_num,DateUtil.currentSeconds());
                                }
                            }
                        }
                    }catch (Exception e){
                        log.error("ray task running",e);
                    }
                    // 这里可以放置你需要执行的业务逻辑
                };

                // 计算距离开始时间的延迟
                long initialDelay = calculateDelayInSeconds(startTime);

                // 每5秒执行一次任务
                long period = 5; // 秒

                // 安排任务在初始延迟之后开始执行，并且每5秒执行一次
                ScheduledFuture<?> scheduledFuture = executor.scheduleAtFixedRate(task, initialDelay, period, TimeUnit.SECONDS);

                // 计算从开始时间到结束时间的总秒数
                long totalDurationInSeconds = java.time.Duration.between(startTime, endTime).getSeconds();

                // 在指定的时间段结束后取消任务
                executor.schedule(() -> scheduledFuture.cancel(false), totalDurationInSeconds, TimeUnit.SECONDS);

            }else{
                throw exception( MATCH_TIME_NOT_EXISTS );
            }

    }

    /**
     * 计算距离开始时间的延迟（秒）
     *
     * @param startTime 开始时间
     * @return 延迟时间（秒）
     */
    private static long calculateDelayInSeconds(LocalDateTime startTime) {
        LocalDateTime now = LocalDateTime.now();
        if (!now.isBefore(startTime)) {
            return 0;
        }
        return java.time.Duration.between(now, startTime).getSeconds();
    }


    private JSONObject doHttp(String match_id){
        String resJson = HttpRequest.get("https://iminfo.esportsworldlink.com/v2/odds?match_id="+match_id)
                .header(Header.USER_AGENT, "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/128.0.0.0 Safari/537.36")//头信息，多个头信息多次调用此方法即可
                .header(Header.ORIGIN, "https://www.raybet.lol")//头信息，多个头信息多次调用此方法即可
                .header(Header.PRAGMA, "no-cache")//头信息，多个头信息多次调用此方法即可
                .cookie("nlbi_1702036=X3viFoIIihz0gIb2zNuCHgAAAAC9Y6HdptaBXxA9+JzhI7Ec; HttpOnly; path=/; Domain=.esportsworldlink.com")
                .timeout(20000)//超时，毫秒
                .execute().body();
        if (JSONUtil.isTypeJSONObject(resJson)) {
            return JSONUtil.parseObj(resJson);
        }else{
            throw exception( MATCH_NOT_EXISTS );
        }
    }


    public static void main(String[] args) throws InterruptedException {
        System.out.println(DateUtil.second(new Date()));
        System.out.println(DateUtil.currentSeconds());
        RayThreadService rayThreadService = new RayThreadService();
        rayThreadService.runScheduled("38026436");
        // 阻塞主线程，以便观察任务执行情况
        Thread.sleep( 60*60* 1000L + 1000); // 加上额外的一秒以确保任务被取消
    }
}


