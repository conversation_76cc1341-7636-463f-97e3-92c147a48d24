package cn.iocoder.yudao.module.vote.controller.admin.cyberstar.vo;

import lombok.*;
import java.util.*;
import io.swagger.v3.oas.annotations.media.Schema;
import cn.iocoder.yudao.framework.common.pojo.PageParam;
import org.springframework.format.annotation.DateTimeFormat;
import java.time.LocalDateTime;

import static cn.iocoder.yudao.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;

@Schema(description = "管理后台 - 网红主播分页 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class CyberStarPageReqVO extends PageParam {

    @Schema(description = "昵称", example = "赵六")
    private String nickname;

    @Schema(description = "用户ID", example = "1718")
    private String userId;

    @Schema(description = "头像图片URL")
    private String img;

    @Schema(description = "Cookie信息")
    private String cookie;

    @Schema(description = "gmv当天金额")
    private Double gmvTodayAmountDouble;

    @Schema(description = "跳水金额")
    private Integer diveAmount;

    @Schema(description = "gift当天金额")
    private Double giftTodayAmountDouble;

    @Schema(description = "创建时间")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime[] createTime;

}