package cn.iocoder.yudao.module.vote.controller.admin.jobpositions.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;
import java.util.*;
import javax.validation.constraints.*;

@Schema(description = "管理后台 - 职位信息更新 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class JobPositionsUpdateReqVO extends JobPositionsBaseVO {

    @Schema(description = "职位ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "15068")
    @NotNull(message = "职位ID不能为空")
    private Integer positionId;

}
