package cn.iocoder.yudao.module.vote.controller.app.vo;

import cn.hutool.core.collection.ListUtil;
import cn.hutool.core.util.NumberUtil;
import cn.hutool.core.util.StrUtil;

import java.time.Instant;
import java.util.*;

public class RayOverall {

    private Map<Instant,RaytwoTeamsOdd> finalMap = new HashMap();
    private Map<Instant,RaytwoTeamsOdd> map1Map = new HashMap();
    private Map<Instant,RaytwoTeamsOdd> map2Map = new HashMap();
    private Map<Instant,RaytwoTeamsOdd> map3Map = new HashMap();
    private Map<Instant,RaytwoTeamsOdd> map4Map = new HashMap();
    private Map<Instant,RaytwoTeamsOdd> map5Map = new HashMap();
    private Map<Instant,RaytwoTeamsOdd> map6Map = new HashMap();
    private Map<Instant,RaytwoTeamsOdd> map7Map = new HashMap();
    private String team1_name;
    private String team2_name;
    private String boNum; // bo5

    public RayOverall(String team1_name, String team2_name) {
        // 排序获得第一个和第二个的名称。
        List<String> list = Arrays.asList(team1_name,team2_name);
        List<String> keys = ListUtil.sortByPinyin(list);
        this.team1_name = keys.get(0);
        this.team2_name = keys.get(1);
    }




    private void putMap(Map<Instant,RaytwoTeamsOdd> map , Instant time ,  String team, Double odd){
        // 从map里面获得对应的
        RaytwoTeamsOdd orDefault = map.getOrDefault(time, new RaytwoTeamsOdd(time));
        // 通过team 区分第一还是第二。
        if(StrUtil.contains(team,this.team1_name)){
            orDefault.setTeam1Odd(odd);
        }else if(StrUtil.contains(team,this.team2_name)){
            orDefault.setTeam2Odd(odd);
        }else{
            throw new RuntimeException("没有匹配的比赛名");
        }
        map.put(time,orDefault);
    }
}
