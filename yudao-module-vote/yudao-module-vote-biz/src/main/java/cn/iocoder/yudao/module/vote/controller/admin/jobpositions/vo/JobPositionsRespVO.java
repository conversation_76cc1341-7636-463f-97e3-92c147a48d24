package cn.iocoder.yudao.module.vote.controller.admin.jobpositions.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;
import java.time.LocalDateTime;

@Schema(description = "管理后台 - 职位信息 Response VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class JobPositionsRespVO extends JobPositionsBaseVO {

    @Schema(description = "职位ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "15068")
    private Integer positionId;

    @Schema(description = "创建时间", requiredMode = Schema.RequiredMode.REQUIRED)
    private LocalDateTime createTime;

}
