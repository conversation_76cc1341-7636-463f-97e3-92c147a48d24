package cn.iocoder.yudao.module.vote.controller.admin.activity.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;
import java.util.*;
import javax.validation.constraints.*;

@Schema(description = "管理后台 - 投票活动更新 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class ActivityUpdateReqVO extends ActivityBaseVO {

    @Schema(description = "编号", required = true, example = "26021")
    @NotNull(message = "编号不能为空")
    private Long id;

}
