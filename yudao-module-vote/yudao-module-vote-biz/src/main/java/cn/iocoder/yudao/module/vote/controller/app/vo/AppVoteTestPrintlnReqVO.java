package cn.iocoder.yudao.module.vote.controller.app.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import org.springframework.web.multipart.MultipartFile;

import javax.validation.constraints.NotNull;



@Schema(description = "投屏 App - 打印 Request VO")
@Data
public class AppVoteTestPrintlnReqVO {
    @Schema(description = "类型", example = "received", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "类型不能为空")
    private String type;

    @Schema(description = "url", example = "baidu.com", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "url不能为空")
    private String url;

    @Schema(description = "时间戳", example = "1724124740261", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "时间戳不能为空")
    private Long timestamp;
    @Schema(description = "数据", example = "...", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "数据不能为空")
    private String data;

}
