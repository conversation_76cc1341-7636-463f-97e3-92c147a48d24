package cn.iocoder.yudao.module.vote.service;

import cn.iocoder.yudao.framework.common.pojo.PageParam;
import cn.iocoder.yudao.framework.common.pojo.PageResult;

import javax.validation.Valid;

public interface JobPayOrderService {
    /**
     * 用户是否充值啦内容呢？
     * @param userId
     * @return
     */
    public boolean isPay(Long userId);
    /**
     * 创建示例订单
     *
     * @param userId      用户编号
     * @return 编号
     */
    Long createJobOrder(Long userId);


    /**
     * 更新示例订单为已支付
     *
     * @param id 编号
     * @param payOrderId 支付订单号
     */
    void updateJobOrderPaid(Long id, Long payOrderId);


}
