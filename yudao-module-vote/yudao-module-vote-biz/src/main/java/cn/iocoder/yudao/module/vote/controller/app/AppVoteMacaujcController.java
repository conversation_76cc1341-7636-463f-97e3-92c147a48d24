package cn.iocoder.yudao.module.vote.controller.app;

/**
 * 在 Controller 的命名上，额外增加 App 作为前缀，一方面区分是管理后台还是用户 App 的 Controller，另一方面避免 Spring Bean 的名字冲突。
 *
 * 可能你会奇怪，这里我们定义了两个 /demo/test/get 接口，会不会存在重复导致冲突呢？答案，当然是并不会。原因是：
 *
 * controller.admin 包下的接口，默认会增加 /admin-api，即最终的访问地址是 /admin-api/demo/test/get
 * controller.app 包下的接口，默认会增加 /app-api，即最终的访问地址是 /app-api/demo/test/get
 *
 * <AUTHOR>
 * @version 1.0
 * @module Socialuni
 * @date 2023/4/29 10:25
 * @since 1.0
 */

import cn.iocoder.yudao.framework.common.pojo.CommonResult;
import cn.iocoder.yudao.framework.common.util.object.BeanUtils;
import cn.iocoder.yudao.module.vote.controller.admin.macaujc.vo.MacaujcRespVO;
import cn.iocoder.yudao.module.vote.dal.dataobject.macaujc.MacaujcDO;
import cn.iocoder.yudao.module.vote.service.macaujc.MacaujcService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.annotation.security.PermitAll;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import static cn.iocoder.yudao.framework.common.pojo.CommonResult.success;

@Tag(name = "用户 App - Test")
@RestController
@RequestMapping("/vote/macaujc") // 需要忽略租户信息。
@Validated
@Slf4j
public class AppVoteMacaujcController {
    @Resource
    private MacaujcService macaujcService;

    @GetMapping(value = "/statistics", produces = "text/plain;charset=UTF-8")
    @Operation(summary = "统计")
    @Parameter(name = "kind", description = "类型", required = true, example = "macaujc")
    @PermitAll
    public String statistics(HttpServletResponse response, HttpServletRequest request, @RequestParam("kind") String kind) {
        String statistics = macaujcService.statistics(kind);
        // 这样缓存才可以生效。
        // 计算 ETag
        String eTag = Integer.toHexString(statistics.hashCode());
        response.setHeader("Cache-Control", "public, max-age=3600");
        response.setHeader("ETag", eTag);

        // 处理 If-None-Match 头，返回 304 Not Modified
        String ifNoneMatch = request.getHeader("If-None-Match");
        if (eTag.equals(ifNoneMatch)) {
            response.setStatus(HttpServletResponse.SC_NOT_MODIFIED);
            return null;
        }

        return statistics;
    }
}
