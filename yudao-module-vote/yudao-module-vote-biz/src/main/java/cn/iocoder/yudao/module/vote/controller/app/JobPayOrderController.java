package cn.iocoder.yudao.module.vote.controller.app;

import cn.iocoder.yudao.framework.common.pojo.CommonResult;
import cn.iocoder.yudao.framework.common.pojo.PageParam;
import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.module.pay.api.notify.dto.PayOrderNotifyReqDTO;
import cn.iocoder.yudao.module.pay.api.notify.dto.PayRefundNotifyReqDTO;
import cn.iocoder.yudao.module.vote.service.JobPayOrderService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.annotation.security.PermitAll;
import javax.validation.Valid;

import static cn.iocoder.yudao.framework.common.pojo.CommonResult.success;
import static cn.iocoder.yudao.framework.common.util.servlet.ServletUtils.getClientIP;
import static cn.iocoder.yudao.framework.security.core.util.SecurityFrameworkUtils.getLoginUserId;

@Tag(name = "管理后台 - 工作会员订单")
@RestController
@RequestMapping("/vote/zhidi")
@Validated
public class JobPayOrderController {

    @Resource
    private JobPayOrderService jobPayOrderService;

    @PostMapping("/create")
    @Operation(summary = "发起充值会员")
    public CommonResult<Long> createMenber() {
        return success(jobPayOrderService.createJobOrder(getLoginUserId()));
    }

//    @GetMapping("/page")
//    @Operation(summary = "获得示例订单分页")
//    public CommonResult<PageResult<PayDemoOrderRespVO>> getDemoOrderPage(@Valid PageParam pageVO) {
//        PageResult<PayDemoOrderDO> pageResult = payDemoOrderService.getDemoOrderPage(pageVO);
//        return success(PayDemoOrderConvert.INSTANCE.convertPage(pageResult));
//    }

    @PostMapping("/update-paid")
    @Operation(summary = "更新示例订单为已支付") // 由 pay-module 支付服务，进行回调，可见 PayNotifyJob
    @PermitAll // 无需登录，安全由 PayDemoOrderService 内部校验实现
    public CommonResult<Boolean> updateDemoOrderPaid(@RequestBody PayOrderNotifyReqDTO notifyReqDTO) {
        jobPayOrderService.updateJobOrderPaid(Long.valueOf(notifyReqDTO.getMerchantOrderId()),
                notifyReqDTO.getPayOrderId());
        return success(true);
    }

//    @PutMapping("/refund")
//    @Operation(summary = "发起示例订单的退款")
//    @Parameter(name = "id", description = "编号", required = true, example = "1024")
//    public CommonResult<Boolean> refundDemoOrder(@RequestParam("id") Long id) {
//        payDemoOrderService.refundDemoOrder(id, getClientIP());
//        return success(true);
//    }
//
//    @PostMapping("/update-refunded")
//    @Operation(summary = "更新示例订单为已退款") // 由 pay-module 支付服务，进行回调，可见 PayNotifyJob
//    @PermitAll // 无需登录，安全由 PayDemoOrderService 内部校验实现
//    public CommonResult<Boolean> updateDemoOrderRefunded(@RequestBody PayRefundNotifyReqDTO notifyReqDTO) {
//        payDemoOrderService.updateDemoOrderRefunded(Long.valueOf(notifyReqDTO.getMerchantOrderId()),
//                notifyReqDTO.getPayRefundId());
//        return success(true);
//    }

}
