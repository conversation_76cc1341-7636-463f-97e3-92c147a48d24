package cn.iocoder.yudao.module.vote.controller.admin;

/**
 * 注意，/demo 是该模块所有 RESTful API 的基础路径，/test 是 Test 功能的基础路径。
 * <AUTHOR>
 * @version 1.0
 * @module Socialuni
 * @date 2023/4/29 10:24
 * @since 1.0
 */

import cn.iocoder.yudao.framework.common.pojo.CommonResult;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.Operation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import static cn.iocoder.yudao.framework.common.pojo.CommonResult.success;

@Tag(name = "管理后台 - Test")
@RestController
@RequestMapping("/vote/test")
@Validated
public class VoteTestController {

  @GetMapping("/get")
  @Operation(summary = "获取 test 信息")
  public CommonResult<String> get() {
    return success("true");
  }

}