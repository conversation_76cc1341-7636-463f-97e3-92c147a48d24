package cn.iocoder.yudao.module.vote.controller.admin.activity.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;
import java.time.LocalDateTime;

@Schema(description = "管理后台 - 投票活动 Response VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class ActivityRespVO extends ActivityBaseVO {

    @Schema(description = "编号", required = true, example = "26021")
    private Long id;

    @Schema(description = "创建时间", required = true)
    private LocalDateTime createTime;

}
