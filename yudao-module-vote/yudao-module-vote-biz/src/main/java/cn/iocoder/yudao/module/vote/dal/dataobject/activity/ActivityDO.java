package cn.iocoder.yudao.module.vote.dal.dataobject.activity;

import lombok.*;
import java.util.*;
import java.time.LocalDateTime;
import java.time.LocalDateTime;
import java.time.LocalDateTime;
import java.time.LocalDateTime;
import com.baomidou.mybatisplus.annotation.*;
import cn.iocoder.yudao.framework.mybatis.core.dataobject.BaseDO;

/**
 * 投票活动 DO
 *
 * <AUTHOR>
 */
@TableName("vote_activity")
@KeySequence("vote_activity_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ActivityDO extends BaseDO {

    /**
     * 编号
     */
    @TableId
    private Long id;
    /**
     * 名称
     */
    private String name;
    /**
     * 横幅图片
     */
    private String bannerImage;
    /**
     * 首页轮播图片
     */
    private String homeImage;
    /**
     * 奖品图片
     */
    private String prizeImage;
    /**
     * 介绍图片
     */
    private String introduceImage;
    /**
     * 分享标题
     */
    private String shareTitle;
    /**
     * 活动公告
     */
    private String notice;
    /**
     * 背景图
     */
    private String background;
    /**
     * 开始时间
     */
    private LocalDateTime startTime;
    /**
     * 结束时间
     */
    private LocalDateTime endTime;
    /**
     * 浏览数
     */
    private Integer viewNumber;
    /**
     * 票数总计
     */
    private Integer voteNumber;
    /**
     * 礼物展示条数
     */
    private Integer giftShowNumber;
    /**
     * 去其他的微信小程序appId
     */
    private String goWxAppid;
    /**
     * 状态标识
     */
    private Byte status;
    /**
     * 每天可为每个选手投几票
     */
    private Integer votesPerPerson;
    /**
     * 可为最多几名选手投票
     */
    private Integer playerMaxCount;
    /**
     * 主题
     */
    private String theme;
    /**
     * 乐观锁
     */
    private Integer revision;
    /**
     * 部门ID
     */
    private Long deptId;
    /**
     * 用户ID
     */
    private Long userId;

}
