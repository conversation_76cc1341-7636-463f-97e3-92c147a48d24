package cn.iocoder.yudao.module.vote.dal.mysql.lottery;

import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.mybatis.core.query.LambdaQueryWrapperX;
import cn.iocoder.yudao.framework.mybatis.core.mapper.BaseMapperX;
import cn.iocoder.yudao.module.vote.dal.dataobject.lottery.LotteryDO;
import org.apache.ibatis.annotations.Mapper;
import cn.iocoder.yudao.module.vote.controller.admin.lottery.vo.*;

/**
 * kuai28.com Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface LotteryMapper extends BaseMapperX<LotteryDO> {

    default PageResult<LotteryDO> selectPage(LotteryPageReqVO reqVO) {
        return selectPage(reqVO, new LambdaQueryWrapperX<LotteryDO>()
                .eqIfPresent(LotteryDO::getEfficacy, reqVO.getEfficacy())
                .eqIfPresent(LotteryDO::getFullExpect, reqVO.getFullExpect())
                .eqIfPresent(LotteryDO::getNumSort, reqVO.getNumSort())
                .eqIfPresent(LotteryDO::getNumType, reqVO.getNumType())
                .eqIfPresent(LotteryDO::getOpenCode, reqVO.getOpenCode())
                .betweenIfPresent(LotteryDO::getOpenTime, reqVO.getOpenTime())
                .eqIfPresent(LotteryDO::getSum, reqVO.getSum())
                .likeIfPresent(LotteryDO::getTypeName, reqVO.getTypeName())
                .eqIfPresent(LotteryDO::getVarietyType, reqVO.getVarietyType())
                .orderByDesc(LotteryDO::getId));
    }

    default boolean exists(Long fullExpect,String varietyType) {
        return exists( new LambdaQueryWrapperX<LotteryDO>()
                .eqIfPresent(LotteryDO::getFullExpect, fullExpect)
                .eqIfPresent(LotteryDO::getVarietyType, varietyType)
                );
    }
}