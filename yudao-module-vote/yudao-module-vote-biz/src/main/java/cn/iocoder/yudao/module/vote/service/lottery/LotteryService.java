package cn.iocoder.yudao.module.vote.service.lottery;

import java.util.*;
import javax.validation.*;
import cn.iocoder.yudao.module.vote.controller.admin.lottery.vo.*;
import cn.iocoder.yudao.module.vote.dal.dataobject.lottery.LotteryDO;
import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.common.pojo.PageParam;

/**
 * kuai28.com Service 接口
 *
 * <AUTHOR>
 */
public interface LotteryService {

    /**
     * 创建kuai28.com
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    Long createLottery(@Valid LotterySaveReqVO createReqVO);

    /**
     * 更新kuai28.com
     *
     * @param updateReqVO 更新信息
     */
    void updateLottery(@Valid LotterySaveReqVO updateReqVO);

    /**
     * 删除kuai28.com
     *
     * @param id 编号
     */
    void deleteLottery(Long id);

    /**
     * 获得kuai28.com
     *
     * @param id 编号
     * @return kuai28.com
     */
    LotteryDO getLottery(Long id);

    /**
     * 获得kuai28.com分页
     *
     * @param pageReqVO 分页查询
     * @return kuai28.com分页
     */
    PageResult<LotteryDO> getLotteryPage(LotteryPageReqVO pageReqVO);

}