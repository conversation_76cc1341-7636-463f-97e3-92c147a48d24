package cn.iocoder.yudao.module.vote.controller.app.cyberstar;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.NumberUtil;
import cn.iocoder.yudao.framework.common.pojo.CommonResult;
import cn.iocoder.yudao.framework.common.util.date.DateUtils;
import cn.iocoder.yudao.module.vote.controller.admin.cyberstar.vo.CyberStarRespVO;
import cn.iocoder.yudao.module.vote.controller.app.cyberstar.vo.CyberStarDateRangeReqVO;
import cn.iocoder.yudao.module.vote.controller.app.cyberstar.vo.CyberStarTopRespVO;
import cn.iocoder.yudao.module.vote.controller.app.cyberstar.vo.CyberUpdateReqVO;
import cn.iocoder.yudao.module.vote.convert.cyberstar.CyberStarConvert;
import cn.iocoder.yudao.module.vote.dal.dataobject.cyberstar.CyberStarDO;
import cn.iocoder.yudao.module.vote.service.cyberstar.CyberStarCrawler;
import cn.iocoder.yudao.module.vote.service.cyberstar.CyberStarService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.*;

import static cn.hutool.core.date.DatePattern.NORM_DATE_PATTERN;
import static cn.iocoder.yudao.framework.common.pojo.CommonResult.success;
import static cn.iocoder.yudao.framework.common.util.date.DateUtils.getDateRange;

@Tag(name = "用户中心 - 网红主播")
//@RestController
@RequestMapping("/vote/cyber-star")
@Validated
@Slf4j
public class AppCyberStarController {

    @Resource
    private CyberStarService cyberStarService;

    @Resource
    private StringRedisTemplate stringRedisTemplate;

    @Resource
    private CyberStarCrawler cyberStarCrawler;

//    @GetMapping("/topGmv")
//    @Operation(summary = "获得前20名GMV的数据")
//    public CommonResult<List<CyberStarTopRespVO>> topGmv() {
//        // 获取前20名GMV的数据，只查询需要的字段以提高查询效率
//        List<CyberStarDO> topGmvList = cyberStarService.getTopGmv(20);
//        // 使用 MapStruct 转换为响应VO
//        return success(CyberStarConvert.INSTANCE.convertToTopGmvVOList(topGmvList));
//    }
//
//    @GetMapping("/topGift")
//    @Operation(summary = "获得前30名gift的数据")
//    public CommonResult<List<CyberStarTopRespVO>> topGift() {
//        // 获取前30名Gift的数据，只查询需要的字段以提高查询效率
//        List<CyberStarDO> topGiftList = cyberStarService.getTopGift(30);
//        // 使用 MapStruct 转换为响应VO
//        return success(CyberStarConvert.INSTANCE.convertToTopGiftVOList(topGiftList));
//    }

    @GetMapping("/now")
    @Operation(summary = "当前时间")
    public CommonResult<String> now() {
        return success(DateUtil.now());
    }

    @GetMapping("/getAll")
    @Operation(summary = "获得所有的网红主播信息")
    public CommonResult<List<CyberStarRespVO>> getAll() {
        List<CyberStarDO> pageResult = cyberStarService.getAll();
        return success(CyberStarConvert.INSTANCE.convertList(pageResult));
    }

    @PostMapping("/updateGift")
    @Operation(summary = "获取所有主播的热度映射")
    public CommonResult<Boolean> updateGift(@Valid @RequestBody CyberUpdateReqVO cyberUpdateReqVO) {
        String date = DateUtil.formatDate(cyberUpdateReqVO.getDate());
        cyberStarCrawler.updateGift(cyberUpdateReqVO.getId(),date,date,cyberUpdateReqVO.getAmount(),true);
        return success(true);
    }

    @PostMapping("/updateGmvSub")
    @Operation(summary = "更新电商")
    public CommonResult<Boolean> updateGmvSub(@Valid @RequestBody CyberUpdateReqVO cyberUpdateReqVO) {
        cyberStarCrawler.updateGmvSub(cyberUpdateReqVO.getId(),DateUtil.formatDate(cyberUpdateReqVO.getDate()),cyberUpdateReqVO.getAmount());
        return success(true);
    }

    @GetMapping("/page0615")
    @Operation(summary = "获取所有主播的热度映射")
    public CommonResult<Map<Long, Long>> page0518() {
        // 直接从内存缓存中获取数据
        return success(cyberStarCrawler.getHotAmountMap());
    }

    @GetMapping("/page0608")
    @Operation(summary = "获取所有主播的热度映射")
    public CommonResult<Map<Long, Long>> page0519() {
        // 直接从内存缓存中获取数据
        return success(cyberStarCrawler.getHotAmountMapPage19());
    }

//    @PostMapping("/topGiftByDateRange")
    @Operation(summary = "获取指定日期范围内礼物总和排名公会")
    public CommonResult<List<CyberStarTopRespVO>> topGiftByDateRangeOfGH(@Valid @RequestBody CyberStarDateRangeReqVO reqVO) {
        return topGiftByDateRange(reqVO,1);
    }

//    @PostMapping("/topGiftByDateRangeOfGR")
    @Operation(summary = "获取指定日期范围内礼物总和排名个人")
    public CommonResult<List<CyberStarTopRespVO>> topGiftByDateRangeOfGR(@Valid @RequestBody CyberStarDateRangeReqVO reqVO) {
        return topGiftByDateRange(reqVO,2);
    }


        public CommonResult<List<CyberStarTopRespVO>> topGiftByDateRange( CyberStarDateRangeReqVO reqVO , int type) {
        // 获取所有主播
        List<CyberStarDO> allStars = cyberStarService.getListByType(type);
        if (allStars.isEmpty()) {
            return success(Collections.emptyList());
        }

        // 计算日期范围内的所有日期
        List<Date> dateRange = getDateRange(reqVO.getStartDate(), reqVO.getEndDate());
        if (dateRange.isEmpty()) {
            return success(Collections.emptyList());
        }

        // 存储每个主播在日期范围内的礼物总和
        Map<Long, Double> starGiftMap = new HashMap<>();

        // 遍历所有主播和日期，累加礼物金额
        for (CyberStarDO star : allStars) {
            Long cyberStarId = star.getId();
            double totalGift = 0.0;

            for (Date date : dateRange) {
                String formattedDate = formatDate(date);
                String redisKey = "vote:cyber_star:gift:" + cyberStarId + ":" + formattedDate;

                // 获取该主播在当天的所有直播场次礼物收入
                Map<Object, Object> entries = stringRedisTemplate.opsForHash().entries(redisKey);
                if (!entries.isEmpty()) {
                    for (Map.Entry<Object, Object> entry : entries.entrySet()) {
                        try {
                            // 只累加正值，负值表示0点前的收入，不计入当天
                            double amount = Double.parseDouble(entry.getValue().toString());
                            if (amount > 0) {
                                totalGift += amount;
                            }
                        } catch (NumberFormatException e) {
                            log.warn("Invalid amount value in Redis: {}", entry.getValue());
                        }
                    }
                }
            }

            // 只记录有礼物收入的主播
            if (totalGift > 0) {
                starGiftMap.put(cyberStarId, totalGift * 10);
            }else{
                starGiftMap.put(cyberStarId, 0d);
            }
        }

        // 按礼物总和降序排序
        List<Map.Entry<Long, Double>> sortedEntries = new ArrayList<>(starGiftMap.entrySet());
        sortedEntries.sort((e1, e2) -> Double.compare(e2.getValue(), e1.getValue()));

        // 取前N名
        int limit = Math.min(reqVO.getLimit(), sortedEntries.size());
        List<CyberStarTopRespVO> result = new ArrayList<>(limit);

        for (int i = 0; i < limit; i++) {
            Map.Entry<Long, Double> entry = sortedEntries.get(i);
            CyberStarTopRespVO vo = new CyberStarTopRespVO();
            vo.setId(entry.getKey());
            vo.setAmountDouble(entry.getValue().intValue());
            result.add(vo);
        }

        return success(result);
    }

//    @PostMapping("/topGmvByDateRange")
    @Operation(summary = "获取指定日期范围内商品总和排名")
    public CommonResult<List<CyberStarTopRespVO>> topGmvByDateRange(@Valid @RequestBody CyberStarDateRangeReqVO reqVO) {
        // 获取所有主播
        List<CyberStarDO> allStars = cyberStarService.getListByType(0);
        if (allStars.isEmpty()) {
            return success(Collections.emptyList());
        }

        // 计算日期范围内的所有日期
        List<Date> dateRange = getDateRange(reqVO.getStartDate(), reqVO.getEndDate());
        if (dateRange.isEmpty()) {
            return success(Collections.emptyList());
        }

        // 存储每个主播在日期范围内的商品总和
        Map<Long, Double> starGmvMap = new HashMap<>();

        // 遍历所有主播和日期，累加商品金额
        for (CyberStarDO star : allStars) {
            Long cyberStarId = star.getId();
            double totalGmv = 0.0;

            for (Date date : dateRange) {
                String formattedDate = formatDate(date);
                String redisKey = "vote:cyber_star:gmv:" + cyberStarId + ":" + formattedDate;

                // 获取该主播在当天的所有直播场次商品收入
                double amount = NumberUtil.parseDouble(stringRedisTemplate.opsForValue().get(redisKey));
                totalGmv += amount;
            }

            // 只记录有商品收入的主播
            if (totalGmv > 0) {
                starGmvMap.put(cyberStarId, totalGmv);
            }else{
                starGmvMap.put(cyberStarId, 0d);
            }
        }

        // 按商品总和降序排序
        List<Map.Entry<Long, Double>> sortedEntries = new ArrayList<>(starGmvMap.entrySet());
        sortedEntries.sort((e1, e2) -> Double.compare(e2.getValue(), e1.getValue()));

        // 取前N名
        int limit = Math.min(reqVO.getLimit(), sortedEntries.size());
        List<CyberStarTopRespVO> result = new ArrayList<>(limit);

        for (int i = 0; i < limit; i++) {
            Map.Entry<Long, Double> entry = sortedEntries.get(i);
            CyberStarTopRespVO vo = new CyberStarTopRespVO();
            vo.setId(entry.getKey());
            vo.setAmountDouble(entry.getValue().intValue());
            result.add(vo);
        }

        return success(result);
    }



    /**
     * 格式化日期为 yyyy-MM-dd 格式
     *
     * @param date 日期
     * @return 格式化后的日期字符串
     */
    private static String formatDate(Date date) {
        return DateUtil.format(date, "yyyy-MM-dd");
    }

}