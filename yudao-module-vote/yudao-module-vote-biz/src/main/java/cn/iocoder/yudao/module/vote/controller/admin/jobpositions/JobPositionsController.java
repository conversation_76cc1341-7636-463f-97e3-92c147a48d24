package cn.iocoder.yudao.module.vote.controller.admin.jobpositions;

import org.springframework.web.bind.annotation.*;
import javax.annotation.Resource;
import org.springframework.validation.annotation.Validated;
import org.springframework.security.access.prepost.PreAuthorize;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.Operation;

import javax.validation.constraints.*;
import javax.validation.*;
import javax.servlet.http.*;
import java.util.*;
import java.io.IOException;

import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.common.pojo.CommonResult;
import static cn.iocoder.yudao.framework.common.pojo.CommonResult.success;

import cn.iocoder.yudao.framework.excel.core.util.ExcelUtils;


import cn.iocoder.yudao.module.vote.controller.admin.jobpositions.vo.*;
import cn.iocoder.yudao.module.vote.dal.dataobject.jobpositions.JobPositionsDO;
import cn.iocoder.yudao.module.vote.convert.jobpositions.JobPositionsConvert;
import cn.iocoder.yudao.module.vote.service.jobpositions.JobPositionsService;

@Tag(name = "管理后台 - 职位信息")
@RestController
@RequestMapping("/vote/job-positions")
@Validated
public class JobPositionsController {

    @Resource
    private JobPositionsService jobPositionsService;

    @PostMapping("/create")
    @Operation(summary = "创建职位信息")
    @PreAuthorize("@ss.hasPermission('vote:job-positions:create')")
    public CommonResult<Integer> createJobPositions(@Valid @RequestBody JobPositionsCreateReqVO createReqVO) {
        return success(jobPositionsService.createJobPositions(createReqVO));
    }

    @PutMapping("/update")
    @Operation(summary = "更新职位信息")
    @PreAuthorize("@ss.hasPermission('vote:job-positions:update')")
    public CommonResult<Boolean> updateJobPositions(@Valid @RequestBody JobPositionsUpdateReqVO updateReqVO) {
        jobPositionsService.updateJobPositions(updateReqVO);
        return success(true);
    }

    @DeleteMapping("/delete")
    @Operation(summary = "删除职位信息")
    @Parameter(name = "id", description = "编号", required = true)
    @PreAuthorize("@ss.hasPermission('vote:job-positions:delete')")
    public CommonResult<Boolean> deleteJobPositions(@RequestParam("id") Integer id) {
        jobPositionsService.deleteJobPositions(id);
        return success(true);
    }

    @GetMapping("/get")
    @Operation(summary = "获得职位信息")
    @Parameter(name = "id", description = "编号", required = true, example = "1024")
    @PreAuthorize("@ss.hasPermission('vote:job-positions:query')")
    public CommonResult<JobPositionsRespVO> getJobPositions(@RequestParam("id") Integer id) {
        JobPositionsDO jobPositions = jobPositionsService.getJobPositions(id);
        return success(JobPositionsConvert.INSTANCE.convert(jobPositions));
    }

    @GetMapping("/list")
    @Operation(summary = "获得职位信息列表")
    @Parameter(name = "ids", description = "编号列表", required = true, example = "1024,2048")
    @PreAuthorize("@ss.hasPermission('vote:job-positions:query')")
    public CommonResult<List<JobPositionsRespVO>> getJobPositionsList(@RequestParam("ids") Collection<Integer> ids) {
        List<JobPositionsDO> list = jobPositionsService.getJobPositionsList(ids);
        return success(JobPositionsConvert.INSTANCE.convertList(list));
    }

    @GetMapping("/page")
    @Operation(summary = "获得职位信息分页")
    @PreAuthorize("@ss.hasPermission('vote:job-positions:query')")
    public CommonResult<PageResult<JobPositionsRespVO>> getJobPositionsPage(@Valid JobPositionsPageReqVO pageVO) {
        PageResult<JobPositionsDO> pageResult = jobPositionsService.getJobPositionsPage(pageVO);
        return success(JobPositionsConvert.INSTANCE.convertPage(pageResult));
    }

    @GetMapping("/export-excel")
    @Operation(summary = "导出职位信息 Excel")
    @PreAuthorize("@ss.hasPermission('vote:job-positions:export')")
    public void exportJobPositionsExcel(@Valid JobPositionsExportReqVO exportReqVO,
              HttpServletResponse response) throws IOException {
        List<JobPositionsDO> list = jobPositionsService.getJobPositionsList(exportReqVO);
        // 导出 Excel
        List<JobPositionsExcelVO> datas = JobPositionsConvert.INSTANCE.convertList02(list);
        ExcelUtils.write(response, "职位信息.xls", "数据", JobPositionsExcelVO.class, datas);
    }

}
