<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
  xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
  xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
  <parent>
    <artifactId>yudao-module-vote</artifactId>
    <groupId>cn.iocoder.boot</groupId>
    <version>${revision}</version> <!-- 1. 修改 version 为 ${revision} -->
  </parent>
  <modelVersion>4.0.0</modelVersion>
  <packaging>jar</packaging> <!-- 2. 新增 packaging 为 jar -->

  <artifactId>yudao-module-vote-biz</artifactId>
  <name>${project.artifactId}</name> <!-- 3. 新增 name 为 ${project.artifactId} -->

  <description> <!-- 4. 新增 description 为该模块的描述 -->
    投票 模块，主要实现 活动以及作品、作品的投票内容 等功能。
  </description>

  <properties>
  </properties>

  <dependencies>  <!-- 5. 新增依赖，这里引入的都是比较常用的业务组件、技术组件 -->
    <dependency>
      <groupId>com.microsoft.playwright</groupId>
      <artifactId>playwright</artifactId>
      <version>1.43.0</version> <!-- 使用最新版 -->
    </dependency>
    <dependency>
      <groupId>cn.iocoder.boot</groupId>
      <artifactId>yudao-module-vote-api</artifactId>
      <version>${revision}</version>
    </dependency>
    <dependency>
      <groupId>cn.iocoder.boot</groupId>
      <artifactId>yudao-module-system-api</artifactId>
      <version>${revision}</version>
    </dependency>
    <!-- Web 相关 -->
    <dependency>
      <groupId>cn.iocoder.boot</groupId>
      <artifactId>yudao-spring-boot-starter-web</artifactId>
    </dependency>

    <dependency>
      <groupId>cn.iocoder.boot</groupId>
      <artifactId>yudao-spring-boot-starter-security</artifactId>
    </dependency>

    <!-- DB 相关 -->
    <dependency>
      <groupId>cn.iocoder.boot</groupId>
      <artifactId>yudao-spring-boot-starter-mybatis</artifactId>
    </dependency>
    <dependency>
      <groupId>cn.iocoder.boot</groupId>
      <artifactId>yudao-spring-boot-starter-redis</artifactId>
    </dependency>
    <!-- 引入分布式锁 -->
    <dependency>
      <groupId>cn.iocoder.boot</groupId>
      <artifactId>yudao-spring-boot-starter-protection</artifactId>
    </dependency>
    <!-- Test 测试相关 -->
    <dependency>
      <groupId>cn.iocoder.boot</groupId>
      <artifactId>yudao-spring-boot-starter-test</artifactId>
    </dependency>
    <dependency>
      <groupId>cn.iocoder.boot</groupId>
      <artifactId>yudao-spring-boot-starter-excel</artifactId>
    </dependency>
    <dependency>
      <groupId>cn.iocoder.boot</groupId>
      <artifactId>yudao-spring-boot-starter-biz-data-permission</artifactId>
    </dependency>
    <!-- WebSocket -->
    <dependency>
      <groupId>org.springframework.boot</groupId>
      <artifactId>spring-boot-starter-websocket</artifactId>
    </dependency>

    <!-- InfluxDB Spring Boot Starter -->
    <dependency>
      <groupId>com.influxdb</groupId>
      <artifactId>influxdb-client-java</artifactId>
      <version>6.8.0</version>
    </dependency>

    <dependency>
      <groupId>org.java-websocket</groupId>
      <artifactId>Java-WebSocket</artifactId>
      <version>1.5.2</version>
    </dependency>
      <dependency>
          <groupId>cn.iocoder.boot</groupId>
          <artifactId>yudao-spring-boot-starter-biz-tenant</artifactId>
      </dependency>
      <dependency>
          <groupId>cn.iocoder.boot</groupId>
          <artifactId>yudao-module-pay-api</artifactId>
          <version>${revision}</version> <!-- 1. 修改 version 为 ${revision} -->
      </dependency>


  </dependencies>
</project>