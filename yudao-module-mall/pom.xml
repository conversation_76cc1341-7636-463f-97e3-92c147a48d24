<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <artifactId>yudao</artifactId>
        <groupId>cn.iocoder.boot</groupId>
        <version>${revision}</version>
    </parent>
    <modelVersion>4.0.0</modelVersion>

    <artifactId>yudao-module-mall</artifactId>
    <packaging>pom</packaging>

    <name>${project.artifactId}</name>

    <description>
        商城大模块，由 product 商品、promotion 营销、trade 交易、statistics 统计等组成
    </description>
    <modules>
        <module>yudao-module-promotion-api</module>
        <module>yudao-module-promotion-biz</module>
        <module>yudao-module-product-api</module>
        <module>yudao-module-product-biz</module>
        <module>yudao-module-trade-api</module>
        <module>yudao-module-trade-biz</module>
        <module>yudao-module-statistics-api</module>
        <module>yudao-module-statistics-biz</module>
    </modules>

</project>
