package cn.iocoder.yudao.module.product.enums.cardcode;

import cn.iocoder.yudao.framework.common.core.IntArrayValuable;
import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Arrays;

/**
 * 商品卡密状态枚举
 *
 * <AUTHOR>
 */
@AllArgsConstructor
@Getter
public enum ProductCardCodeStatusEnum implements IntArrayValuable {

    UNSOLD(0, "未售出"),
    SOLD(1, "已售出"),
    USED(2, "已使用");

    public static final int[] ARRAYS = Arrays.stream(values()).mapToInt(ProductCardCodeStatusEnum::getStatus).toArray();

    /**
     * 状态值
     */
    private final Integer status;
    /**
     * 状态名
     */
    private final String name;

    @Override
    public int[] array() {
        return ARRAYS;
    }

    /**
     * 判断是否未售出
     *
     * @param status 状态
     * @return 是否未售出
     */
    public static boolean isUnsold(Integer status) {
        return UNSOLD.getStatus().equals(status);
    }

    /**
     * 判断是否已售出
     *
     * @param status 状态
     * @return 是否已售出
     */
    public static boolean isSold(Integer status) {
        return SOLD.getStatus().equals(status);
    }

    /**
     * 判断是否已使用
     *
     * @param status 状态
     * @return 是否已使用
     */
    public static boolean isUsed(Integer status) {
        return USED.getStatus().equals(status);
    }

}
