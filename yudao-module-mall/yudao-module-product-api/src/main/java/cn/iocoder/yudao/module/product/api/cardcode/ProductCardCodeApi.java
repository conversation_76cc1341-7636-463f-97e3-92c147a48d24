package cn.iocoder.yudao.module.product.api.cardcode;

import cn.iocoder.yudao.module.product.api.cardcode.dto.ProductCardCodeRespDTO;

import java.util.List;

/**
 * 商品卡密 API 接口
 *
 * <AUTHOR>
 */
public interface ProductCardCodeApi {

    /**
     * 根据订单项ID获取卡密列表
     *
     * @param orderItemId 订单项编号
     * @return 卡密列表
     */
    List<ProductCardCodeRespDTO> getCardCodesByOrderItemId(Long orderItemId);

    /**
     * 获取指定商品的可用卡密数量
     *
     * @param spuId SPU 编号
     * @param skuId SKU 编号
     * @return 可用卡密数量
     */
    Long getAvailableCardCodeCount(Long spuId, Long skuId);

    /**
     * 分配卡密给订单项
     *
     * @param spuId SPU 编号
     * @param skuId SKU 编号
     * @param count 需要的数量
     * @param orderId 订单编号
     * @param orderItemId 订单项编号
     * @return 分配的卡密列表
     */
    List<ProductCardCodeRespDTO> allocateCardCodes(Long spuId, Long skuId, Integer count, Long orderId, Long orderItemId);

    /**
     * 回滚卡密分配（订单取消时使用）
     *
     * @param orderItemId 订单项编号
     */
    void rollbackCardCodeAllocation(Long orderItemId);

}
