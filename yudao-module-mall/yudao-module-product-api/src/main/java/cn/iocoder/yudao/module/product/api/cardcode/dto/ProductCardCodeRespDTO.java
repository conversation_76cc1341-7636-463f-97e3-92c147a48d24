package cn.iocoder.yudao.module.product.api.cardcode.dto;

import lombok.Data;

import java.time.LocalDateTime;

/**
 * 商品卡密 Response DTO
 *
 * <AUTHOR>
 */
@Data
public class ProductCardCodeRespDTO {

    /**
     * 编号
     */
    private Long id;

    /**
     * 商品 SPU 编号
     */
    private Long spuId;

    /**
     * 商品 SKU 编号
     */
    private Long skuId;

    /**
     * 卡密内容
     */
    private String code;

    /**
     * 卡密状态
     */
    private Integer status;

    /**
     * 关联订单编号
     */
    private Long orderId;

    /**
     * 关联订单项编号
     */
    private Long orderItemId;

    /**
     * 售出时间
     */
    private LocalDateTime soldTime;

    /**
     * 使用时间
     */
    private LocalDateTime usedTime;

    /**
     * 备注
     */
    private String remark;

}
