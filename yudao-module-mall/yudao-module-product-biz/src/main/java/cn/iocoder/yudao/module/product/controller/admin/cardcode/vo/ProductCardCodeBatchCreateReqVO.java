package cn.iocoder.yudao.module.product.controller.admin.cardcode.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.util.List;

@Schema(description = "管理后台 - 商品卡密批量创建 Request VO")
@Data
public class ProductCardCodeBatchCreateReqVO {

    @Schema(description = "商品 SPU 编号", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
    @NotNull(message = "商品 SPU 编号不能为空")
    private Long spuId;

    @Schema(description = "商品 SKU 编号", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
    @NotNull(message = "商品 SKU 编号不能为空")
    private Long skuId;

    @Schema(description = "卡密内容列表", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotEmpty(message = "卡密内容列表不能为空")
    private List<String> codes;

    @Schema(description = "备注", example = "批量导入卡密")
    private String remark;

}
