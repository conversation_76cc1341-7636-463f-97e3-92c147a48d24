package cn.iocoder.yudao.module.product.dal.mysql.cardcode;

import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.mybatis.core.mapper.BaseMapperX;
import cn.iocoder.yudao.framework.mybatis.core.query.LambdaQueryWrapperX;
import cn.iocoder.yudao.module.product.controller.admin.cardcode.vo.ProductCardCodePageReqVO;
import cn.iocoder.yudao.module.product.dal.dataobject.cardcode.ProductCardCodeDO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 商品卡密 Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface ProductCardCodeMapper extends BaseMapperX<ProductCardCodeDO> {

    default PageResult<ProductCardCodeDO> selectPage(ProductCardCodePageReqVO reqVO) {
        return selectPage(reqVO, new LambdaQueryWrapperX<ProductCardCodeDO>()
                .eqIfPresent(ProductCardCodeDO::getSpuId, reqVO.getSpuId())
                .eqIfPresent(ProductCardCodeDO::getSkuId, reqVO.getSkuId())
                .eqIfPresent(ProductCardCodeDO::getStatus, reqVO.getStatus())
                .eqIfPresent(ProductCardCodeDO::getOrderId, reqVO.getOrderId())
                .betweenIfPresent(ProductCardCodeDO::getCreateTime, reqVO.getCreateTime())
                .orderByDesc(ProductCardCodeDO::getId));
    }

    /**
     * 根据 SPU 和 SKU 查询未售出的卡密数量
     *
     * @param spuId SPU 编号
     * @param skuId SKU 编号
     * @return 未售出的卡密数量
     */
    default Long selectUnsoldCountBySpuIdAndSkuId(Long spuId, Long skuId) {
        return selectCount(new LambdaQueryWrapperX<ProductCardCodeDO>()
                .eq(ProductCardCodeDO::getSpuId, spuId)
                .eq(ProductCardCodeDO::getSkuId, skuId)
                .eq(ProductCardCodeDO::getStatus, 0)); // 0 表示未售出
    }

    /**
     * 根据 SPU 和 SKU 查询未售出的卡密列表
     *
     * @param spuId SPU 编号
     * @param skuId SKU 编号
     * @param count 需要的数量
     * @return 未售出的卡密列表
     */
    default List<ProductCardCodeDO> selectUnsoldListBySpuIdAndSkuId(Long spuId, Long skuId, Integer count) {
        return selectList(new LambdaQueryWrapperX<ProductCardCodeDO>()
                .eq(ProductCardCodeDO::getSpuId, spuId)
                .eq(ProductCardCodeDO::getSkuId, skuId)
                .eq(ProductCardCodeDO::getStatus, 0) // 0 表示未售出
                .orderByAsc(ProductCardCodeDO::getId)
                .last("LIMIT " + count));
    }

    /**
     * 批量更新卡密状态为已售出
     *
     * @param ids 卡密ID列表
     * @param orderId 订单ID
     * @param orderItemId 订单项ID
     * @return 更新的记录数
     */
    int updateStatusToSoldByIds(@Param("ids") List<Long> ids, 
                               @Param("orderId") Long orderId, 
                               @Param("orderItemId") Long orderItemId);

    /**
     * 根据订单项ID查询已售出的卡密列表
     *
     * @param orderItemId 订单项ID
     * @return 卡密列表
     */
    default List<ProductCardCodeDO> selectListByOrderItemId(Long orderItemId) {
        return selectList(new LambdaQueryWrapperX<ProductCardCodeDO>()
                .eq(ProductCardCodeDO::getOrderItemId, orderItemId)
                .eq(ProductCardCodeDO::getStatus, 1)); // 1 表示已售出
    }

}
