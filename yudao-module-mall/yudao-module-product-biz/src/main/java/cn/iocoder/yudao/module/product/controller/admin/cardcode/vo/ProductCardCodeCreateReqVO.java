package cn.iocoder.yudao.module.product.controller.admin.cardcode.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

@Schema(description = "管理后台 - 商品卡密创建 Request VO")
@Data
public class ProductCardCodeCreateReqVO {

    @Schema(description = "商品 SPU 编号", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
    @NotNull(message = "商品 SPU 编号不能为空")
    private Long spuId;

    @Schema(description = "商品 SKU 编号", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
    @NotNull(message = "商品 SKU 编号不能为空")
    private Long skuId;

    @Schema(description = "卡密内容", requiredMode = Schema.RequiredMode.REQUIRED, example = "ABCD-1234-EFGH-5678")
    @NotBlank(message = "卡密内容不能为空")
    private String code;

    @Schema(description = "备注", example = "测试卡密")
    private String remark;

}
