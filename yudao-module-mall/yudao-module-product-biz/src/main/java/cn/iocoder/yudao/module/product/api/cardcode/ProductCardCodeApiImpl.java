package cn.iocoder.yudao.module.product.api.cardcode;

import cn.iocoder.yudao.framework.common.util.object.BeanUtils;
import cn.iocoder.yudao.module.product.api.cardcode.dto.ProductCardCodeRespDTO;
import cn.iocoder.yudao.module.product.dal.dataobject.cardcode.ProductCardCodeDO;
import cn.iocoder.yudao.module.product.service.cardcode.ProductCardCodeService;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

/**
 * 商品卡密 API 实现类
 *
 * <AUTHOR>
 */
@Service
public class ProductCardCodeApiImpl implements ProductCardCodeApi {

    @Resource
    private ProductCardCodeService cardCodeService;

    @Override
    public List<ProductCardCodeRespDTO> getCardCodesByOrderItemId(Long orderItemId) {
        List<ProductCardCodeDO> cardCodes = cardCodeService.getCardCodesByOrderItemId(orderItemId);
        return BeanUtils.toBean(cardCodes, ProductCardCodeRespDTO.class);
    }

    @Override
    public Long getAvailableCardCodeCount(Long spuId, Long skuId) {
        return cardCodeService.getAvailableCardCodeCount(spuId, skuId);
    }

    @Override
    public List<ProductCardCodeRespDTO> allocateCardCodes(Long spuId, Long skuId, Integer count, Long orderId, Long orderItemId) {
        List<ProductCardCodeDO> cardCodes = cardCodeService.allocateCardCodes(spuId, skuId, count, orderId, orderItemId);
        return BeanUtils.toBean(cardCodes, ProductCardCodeRespDTO.class);
    }

    @Override
    public void rollbackCardCodeAllocation(Long orderItemId) {
        cardCodeService.rollbackCardCodeAllocation(orderItemId);
    }

}
