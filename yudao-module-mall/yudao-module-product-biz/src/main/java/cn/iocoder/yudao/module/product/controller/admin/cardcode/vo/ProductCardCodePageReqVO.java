package cn.iocoder.yudao.module.product.controller.admin.cardcode.vo;

import cn.iocoder.yudao.framework.common.pojo.PageParam;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import org.springframework.format.annotation.DateTimeFormat;

import java.time.LocalDateTime;

import static cn.iocoder.yudao.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;

@Schema(description = "管理后台 - 商品卡密分页 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class ProductCardCodePageReqVO extends PageParam {

    @Schema(description = "商品 SPU 编号", example = "1")
    private Long spuId;

    @Schema(description = "商品 SKU 编号", example = "1")
    private Long skuId;

    @Schema(description = "卡密状态", example = "0")
    private Integer status;

    @Schema(description = "关联订单编号", example = "1")
    private Long orderId;

    @Schema(description = "创建时间")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime[] createTime;

}
