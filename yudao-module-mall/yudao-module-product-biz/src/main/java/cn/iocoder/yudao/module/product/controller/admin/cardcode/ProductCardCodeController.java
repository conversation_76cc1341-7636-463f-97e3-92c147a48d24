package cn.iocoder.yudao.module.product.controller.admin.cardcode;

import cn.iocoder.yudao.framework.common.pojo.CommonResult;
import cn.iocoder.yudao.framework.common.pojo.PageParam;
import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.common.util.object.BeanUtils;
import cn.iocoder.yudao.framework.excel.core.util.ExcelUtils;
import cn.iocoder.yudao.module.product.controller.admin.cardcode.vo.*;
import cn.iocoder.yudao.module.product.dal.dataobject.cardcode.ProductCardCodeDO;
import cn.iocoder.yudao.module.product.service.cardcode.ProductCardCodeService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import java.io.IOException;
import java.util.List;

import static cn.iocoder.yudao.framework.common.pojo.CommonResult.success;

@Tag(name = "管理后台 - 商品卡密")
@RestController
@RequestMapping("/product/card-code")
@Validated
public class ProductCardCodeController {

    @Resource
    private ProductCardCodeService cardCodeService;

    @PostMapping("/create")
    @Operation(summary = "创建商品卡密")
    @PreAuthorize("@ss.hasPermission('product:card-code:create')")
    public CommonResult<Long> createCardCode(@Valid @RequestBody ProductCardCodeCreateReqVO createReqVO) {
        return success(cardCodeService.createCardCode(createReqVO));
    }

    @PostMapping("/batch-create")
    @Operation(summary = "批量创建商品卡密")
    @PreAuthorize("@ss.hasPermission('product:card-code:create')")
    public CommonResult<Integer> batchCreateCardCode(@Valid @RequestBody ProductCardCodeBatchCreateReqVO createReqVO) {
        return success(cardCodeService.batchCreateCardCode(createReqVO));
    }

    @DeleteMapping("/delete")
    @Operation(summary = "删除商品卡密")
    @Parameter(name = "id", description = "编号", required = true)
    @PreAuthorize("@ss.hasPermission('product:card-code:delete')")
    public CommonResult<Boolean> deleteCardCode(@RequestParam("id") Long id) {
        cardCodeService.deleteCardCode(id);
        return success(true);
    }

    @GetMapping("/get")
    @Operation(summary = "获得商品卡密")
    @Parameter(name = "id", description = "编号", required = true, example = "1024")
    @PreAuthorize("@ss.hasPermission('product:card-code:query')")
    public CommonResult<ProductCardCodeRespVO> getCardCode(@RequestParam("id") Long id) {
        ProductCardCodeDO cardCode = cardCodeService.getCardCode(id);
        return success(BeanUtils.toBean(cardCode, ProductCardCodeRespVO.class));
    }

    @GetMapping("/page")
    @Operation(summary = "获得商品卡密分页")
    @PreAuthorize("@ss.hasPermission('product:card-code:query')")
    public CommonResult<PageResult<ProductCardCodeRespVO>> getCardCodePage(@Valid ProductCardCodePageReqVO pageReqVO) {
        PageResult<ProductCardCodeDO> pageResult = cardCodeService.getCardCodePage(pageReqVO);
        return success(BeanUtils.toBean(pageResult, ProductCardCodeRespVO.class));
    }

    @GetMapping("/export-excel")
    @Operation(summary = "导出商品卡密 Excel")
    @PreAuthorize("@ss.hasPermission('product:card-code:export')")
    public void exportCardCodeExcel(@Valid ProductCardCodePageReqVO pageReqVO,
                                   HttpServletResponse response) throws IOException {
        pageReqVO.setPageSize(PageParam.PAGE_SIZE_NONE);
        List<ProductCardCodeDO> list = cardCodeService.getCardCodePage(pageReqVO).getList();
        // 导出 Excel
        ExcelUtils.write(response, "商品卡密.xls", "数据", ProductCardCodeRespVO.class,
                BeanUtils.toBean(list, ProductCardCodeRespVO.class));
    }

    @GetMapping("/available-count")
    @Operation(summary = "获取指定商品的可用卡密数量")
    @Parameter(name = "spuId", description = "SPU编号", required = true)
    @Parameter(name = "skuId", description = "SKU编号", required = true)
    @PreAuthorize("@ss.hasPermission('product:card-code:query')")
    public CommonResult<Long> getAvailableCardCodeCount(@RequestParam("spuId") Long spuId,
                                                       @RequestParam("skuId") Long skuId) {
        return success(cardCodeService.getAvailableCardCodeCount(spuId, skuId));
    }

}
