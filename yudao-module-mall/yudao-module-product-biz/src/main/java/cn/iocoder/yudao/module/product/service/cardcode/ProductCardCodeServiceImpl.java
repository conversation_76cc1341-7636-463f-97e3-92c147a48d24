package cn.iocoder.yudao.module.product.service.cardcode;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.common.util.object.BeanUtils;
import cn.iocoder.yudao.module.product.controller.admin.cardcode.vo.ProductCardCodeBatchCreateReqVO;
import cn.iocoder.yudao.module.product.controller.admin.cardcode.vo.ProductCardCodeCreateReqVO;
import cn.iocoder.yudao.module.product.controller.admin.cardcode.vo.ProductCardCodePageReqVO;
import cn.iocoder.yudao.module.product.dal.dataobject.cardcode.ProductCardCodeDO;
import cn.iocoder.yudao.module.product.dal.mysql.cardcode.ProductCardCodeMapper;
import cn.iocoder.yudao.module.product.enums.cardcode.ProductCardCodeStatusEnum;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.validation.annotation.Validated;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

import static cn.iocoder.yudao.framework.common.exception.util.ServiceExceptionUtil.exception;
import static cn.iocoder.yudao.module.product.enums.ErrorCodeConstants.*;

/**
 * 商品卡密 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
@Slf4j
public class ProductCardCodeServiceImpl implements ProductCardCodeService {

    @Resource
    private ProductCardCodeMapper cardCodeMapper;

    @Override
    public Long createCardCode(ProductCardCodeCreateReqVO createReqVO) {
        // 校验卡密内容不能为空
        if (StrUtil.isBlank(createReqVO.getCode())) {
            throw exception(CARD_CODE_NOT_EMPTY);
        }

        // 创建卡密
        ProductCardCodeDO cardCode = BeanUtils.toBean(createReqVO, ProductCardCodeDO.class);
        cardCode.setStatus(ProductCardCodeStatusEnum.UNSOLD.getStatus());
        cardCodeMapper.insert(cardCode);
        return cardCode.getId();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Integer batchCreateCardCode(ProductCardCodeBatchCreateReqVO createReqVO) {
        if (CollUtil.isEmpty(createReqVO.getCodes())) {
            throw exception(CARD_CODE_LIST_NOT_EMPTY);
        }

        List<ProductCardCodeDO> cardCodes = new ArrayList<>();
        for (String code : createReqVO.getCodes()) {
            if (StrUtil.isBlank(code)) {
                continue; // 跳过空的卡密
            }
            ProductCardCodeDO cardCode = new ProductCardCodeDO();
            cardCode.setSpuId(createReqVO.getSpuId());
            cardCode.setSkuId(createReqVO.getSkuId());
            cardCode.setCode(code.trim());
            cardCode.setStatus(ProductCardCodeStatusEnum.UNSOLD.getStatus());
            cardCode.setRemark(createReqVO.getRemark());
            cardCodes.add(cardCode);
        }

        if (CollUtil.isEmpty(cardCodes)) {
            throw exception(CARD_CODE_LIST_NOT_EMPTY);
        }

        // 批量插入
        cardCodes.forEach(cardCodeMapper::insert);
        return cardCodes.size();
    }

    @Override
    public void deleteCardCode(Long id) {
        // 校验存在
        validateCardCodeExists(id);
        // 删除
        cardCodeMapper.deleteById(id);
    }

    private ProductCardCodeDO validateCardCodeExists(Long id) {
        ProductCardCodeDO cardCode = cardCodeMapper.selectById(id);
        if (cardCode == null) {
            throw exception(CARD_CODE_NOT_EXISTS);
        }
        return cardCode;
    }

    @Override
    public ProductCardCodeDO getCardCode(Long id) {
        return cardCodeMapper.selectById(id);
    }

    @Override
    public PageResult<ProductCardCodeDO> getCardCodePage(ProductCardCodePageReqVO pageReqVO) {
        return cardCodeMapper.selectPage(pageReqVO);
    }

    @Override
    public Long getAvailableCardCodeCount(Long spuId, Long skuId) {
        return cardCodeMapper.selectUnsoldCountBySpuIdAndSkuId(spuId, skuId);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public List<ProductCardCodeDO> allocateCardCodes(Long spuId, Long skuId, Integer count, Long orderId, Long orderItemId) {
        // 查询可用的卡密
        List<ProductCardCodeDO> availableCardCodes = cardCodeMapper.selectUnsoldListBySpuIdAndSkuId(spuId, skuId, count);
        
        if (CollUtil.isEmpty(availableCardCodes) || availableCardCodes.size() < count) {
            throw exception(CARD_CODE_STOCK_NOT_ENOUGH);
        }

        // 更新卡密状态为已售出
        List<Long> cardCodeIds = availableCardCodes.stream()
                .map(ProductCardCodeDO::getId)
                .collect(Collectors.toList());
        
        int updateCount = cardCodeMapper.updateStatusToSoldByIds(cardCodeIds, orderId, orderItemId);
        if (updateCount != count) {
            throw exception(CARD_CODE_ALLOCATE_FAIL);
        }

        // 返回分配的卡密（重新查询以获取最新状态）
        return cardCodeMapper.selectListByOrderItemId(orderItemId);
    }

    @Override
    public List<ProductCardCodeDO> getCardCodesByOrderItemId(Long orderItemId) {
        return cardCodeMapper.selectListByOrderItemId(orderItemId);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void rollbackCardCodeAllocation(Long orderItemId) {
        List<ProductCardCodeDO> cardCodes = cardCodeMapper.selectListByOrderItemId(orderItemId);
        if (CollUtil.isEmpty(cardCodes)) {
            return;
        }

        // 将卡密状态回滚为未售出
        for (ProductCardCodeDO cardCode : cardCodes) {
            ProductCardCodeDO updateObj = new ProductCardCodeDO();
            updateObj.setId(cardCode.getId());
            updateObj.setStatus(ProductCardCodeStatusEnum.UNSOLD.getStatus());
            updateObj.setOrderId(null);
            updateObj.setOrderItemId(null);
            updateObj.setSoldTime(null);
            cardCodeMapper.updateById(updateObj);
        }
    }

}
