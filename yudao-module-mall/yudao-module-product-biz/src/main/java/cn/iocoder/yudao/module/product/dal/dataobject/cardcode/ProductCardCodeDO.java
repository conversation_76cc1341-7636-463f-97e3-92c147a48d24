package cn.iocoder.yudao.module.product.dal.dataobject.cardcode;

import cn.iocoder.yudao.framework.mybatis.core.dataobject.BaseDO;
import cn.iocoder.yudao.module.product.dal.dataobject.spu.ProductSpuDO;
import cn.iocoder.yudao.module.product.dal.dataobject.sku.ProductSkuDO;
import com.baomidou.mybatisplus.annotation.KeySequence;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.*;

/**
 * 商品卡密 DO
 *
 * <AUTHOR>
 */
@TableName("product_card_code")
@KeySequence("product_card_code_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ProductCardCodeDO extends BaseDO {

    /**
     * 编号，自增
     */
    @TableId
    private Long id;

    /**
     * 商品 SPU 编号
     *
     * 关联 {@link ProductSpuDO#getId()}
     */
    private Long spuId;

    /**
     * 商品 SKU 编号
     *
     * 关联 {@link ProductSkuDO#getId()}
     */
    private Long skuId;

    /**
     * 卡密内容（加密存储）
     */
    private String code;

    /**
     * 卡密状态
     *
     * 0 - 未售出
     * 1 - 已售出
     * 2 - 已使用
     */
    private Integer status;

    /**
     * 关联订单编号
     *
     * 售出后记录订单ID
     */
    private Long orderId;

    /**
     * 关联订单项编号
     *
     * 售出后记录订单项ID
     */
    private Long orderItemId;

    /**
     * 售出时间
     */
    private java.time.LocalDateTime soldTime;

    /**
     * 使用时间
     */
    private java.time.LocalDateTime usedTime;

    /**
     * 备注
     */
    private String remark;

}
