package cn.iocoder.yudao.module.product.service.cardcode;

import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.module.product.controller.admin.cardcode.vo.ProductCardCodeBatchCreateReqVO;
import cn.iocoder.yudao.module.product.controller.admin.cardcode.vo.ProductCardCodeCreateReqVO;
import cn.iocoder.yudao.module.product.controller.admin.cardcode.vo.ProductCardCodePageReqVO;
import cn.iocoder.yudao.module.product.dal.dataobject.cardcode.ProductCardCodeDO;

import javax.validation.Valid;
import java.util.List;

/**
 * 商品卡密 Service 接口
 *
 * <AUTHOR>
 */
public interface ProductCardCodeService {

    /**
     * 创建商品卡密
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    Long createCardCode(@Valid ProductCardCodeCreateReqVO createReqVO);

    /**
     * 批量创建商品卡密
     *
     * @param createReqVO 批量创建信息
     * @return 创建的卡密数量
     */
    Integer batchCreateCardCode(@Valid ProductCardCodeBatchCreateReqVO createReqVO);

    /**
     * 删除商品卡密
     *
     * @param id 编号
     */
    void deleteCardCode(Long id);

    /**
     * 获得商品卡密
     *
     * @param id 编号
     * @return 商品卡密
     */
    ProductCardCodeDO getCardCode(Long id);

    /**
     * 获得商品卡密分页
     *
     * @param pageReqVO 分页查询
     * @return 商品卡密分页
     */
    PageResult<ProductCardCodeDO> getCardCodePage(ProductCardCodePageReqVO pageReqVO);

    /**
     * 获取指定商品的可用卡密数量
     *
     * @param spuId SPU 编号
     * @param skuId SKU 编号
     * @return 可用卡密数量
     */
    Long getAvailableCardCodeCount(Long spuId, Long skuId);

    /**
     * 分配卡密给订单项
     *
     * @param spuId SPU 编号
     * @param skuId SKU 编号
     * @param count 需要的数量
     * @param orderId 订单编号
     * @param orderItemId 订单项编号
     * @return 分配的卡密列表
     */
    List<ProductCardCodeDO> allocateCardCodes(Long spuId, Long skuId, Integer count, Long orderId, Long orderItemId);

    /**
     * 根据订单项ID获取卡密列表
     *
     * @param orderItemId 订单项编号
     * @return 卡密列表
     */
    List<ProductCardCodeDO> getCardCodesByOrderItemId(Long orderItemId);

    /**
     * 回滚卡密分配（订单取消时使用）
     *
     * @param orderItemId 订单项编号
     */
    void rollbackCardCodeAllocation(Long orderItemId);

}
