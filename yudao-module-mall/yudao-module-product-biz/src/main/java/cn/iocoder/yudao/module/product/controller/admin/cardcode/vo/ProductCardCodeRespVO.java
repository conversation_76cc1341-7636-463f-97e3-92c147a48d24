package cn.iocoder.yudao.module.product.controller.admin.cardcode.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDateTime;

@Schema(description = "管理后台 - 商品卡密 Response VO")
@Data
public class ProductCardCodeRespVO {

    @Schema(description = "编号", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
    private Long id;

    @Schema(description = "商品 SPU 编号", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
    private Long spuId;

    @Schema(description = "商品 SKU 编号", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
    private Long skuId;

    @Schema(description = "卡密内容", requiredMode = Schema.RequiredMode.REQUIRED, example = "ABCD-1234-EFGH-5678")
    private String code;

    @Schema(description = "卡密状态", requiredMode = Schema.RequiredMode.REQUIRED, example = "0")
    private Integer status;

    @Schema(description = "关联订单编号", example = "1")
    private Long orderId;

    @Schema(description = "关联订单项编号", example = "1")
    private Long orderItemId;

    @Schema(description = "售出时间")
    private LocalDateTime soldTime;

    @Schema(description = "使用时间")
    private LocalDateTime usedTime;

    @Schema(description = "备注", example = "测试卡密")
    private String remark;

    @Schema(description = "创建时间", requiredMode = Schema.RequiredMode.REQUIRED)
    private LocalDateTime createTime;

}
