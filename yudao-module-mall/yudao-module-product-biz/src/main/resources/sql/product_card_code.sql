-- 商品卡密表
CREATE TABLE `product_card_code` (
    `id` bigint NOT NULL AUTO_INCREMENT COMMENT '编号，自增',
    `spu_id` bigint NOT NULL COMMENT '商品 SPU 编号',
    `sku_id` bigint NOT NULL COMMENT '商品 SKU 编号',
    `code` varchar(500) NOT NULL COMMENT '卡密内容（加密存储）',
    `status` tinyint NOT NULL DEFAULT '0' COMMENT '卡密状态：0-未售出，1-已售出，2-已使用',
    `order_id` bigint DEFAULT NULL COMMENT '关联订单编号（售出后记录）',
    `order_item_id` bigint DEFAULT NULL COMMENT '关联订单项编号（售出后记录）',
    `sold_time` datetime DEFAULT NULL COMMENT '售出时间',
    `used_time` datetime DEFAULT NULL COMMENT '使用时间',
    `remark` varchar(255) DEFAULT NULL COMMENT '备注',
    `creator` varchar(64) DEFAULT '' COMMENT '创建者',
    `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `updater` varchar(64) DEFAULT '' COMMENT '更新者',
    `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `deleted` bit(1) NOT NULL DEFAULT b'0' COMMENT '是否删除',
    `tenant_id` bigint NOT NULL DEFAULT '0' COMMENT '租户编号',
    PRIMARY KEY (`id`),
    KEY `idx_spu_sku_status` (`spu_id`, `sku_id`, `status`),
    KEY `idx_order_item_id` (`order_item_id`),
    KEY `idx_status` (`status`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='商品卡密表';

-- 菜单权限
INSERT INTO `system_menu` (`name`, `permission`, `type`, `sort`, `parent_id`, `path`, `icon`, `component`, `component_name`, `status`, `visible`, `keep_alive`, `always_show`, `creator`, `create_time`, `updater`, `update_time`, `deleted`) VALUES
('商品卡密', '', 2, 6, 2000, 'card-code', 'ep:postcard', 'mall/product/cardcode/index', 'ProductCardCode', 0, b'1', b'1', b'1', '1', NOW(), '1', NOW(), b'0'),
('商品卡密查询', 'product:card-code:query', 3, 1, (SELECT id FROM system_menu WHERE name = '商品卡密' AND deleted = 0), '', '', '', NULL, 0, b'1', b'1', b'1', '1', NOW(), '1', NOW(), b'0'),
('商品卡密创建', 'product:card-code:create', 3, 2, (SELECT id FROM system_menu WHERE name = '商品卡密' AND deleted = 0), '', '', '', NULL, 0, b'1', b'1', b'1', '1', NOW(), '1', NOW(), b'0'),
('商品卡密删除', 'product:card-code:delete', 3, 4, (SELECT id FROM system_menu WHERE name = '商品卡密' AND deleted = 0), '', '', '', NULL, 0, b'1', b'1', b'1', '1', NOW(), '1', NOW(), b'0'),
('商品卡密导出', 'product:card-code:export', 3, 5, (SELECT id FROM system_menu WHERE name = '商品卡密' AND deleted = 0), '', '', '', NULL, 0, b'1', b'1', b'1', '1', NOW(), '1', NOW(), b'0');
