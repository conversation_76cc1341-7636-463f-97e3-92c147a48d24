<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.iocoder.yudao.module.product.dal.mysql.cardcode.ProductCardCodeMapper">

    <update id="updateStatusToSoldByIds">
        UPDATE product_card_code
        SET status = 1,
            order_id = #{orderId},
            order_item_id = #{orderItemId},
            sold_time = NOW(),
            update_time = NOW()
        WHERE id IN
        <foreach collection="ids" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
        AND status = 0
    </update>

</mapper>
