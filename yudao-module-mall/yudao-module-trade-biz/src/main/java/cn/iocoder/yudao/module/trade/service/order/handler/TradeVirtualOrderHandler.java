package cn.iocoder.yudao.module.trade.service.order.handler;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.iocoder.yudao.module.product.api.sku.ProductSkuApi;
import cn.iocoder.yudao.module.product.api.spu.ProductSpuApi;
import cn.iocoder.yudao.module.product.api.spu.dto.ProductSpuRespDTO;
import cn.iocoder.yudao.module.product.api.cardcode.ProductCardCodeApi;
import cn.iocoder.yudao.module.trade.dal.dataobject.order.TradeOrderDO;
import cn.iocoder.yudao.module.trade.dal.dataobject.order.TradeOrderItemDO;
import cn.iocoder.yudao.module.trade.enums.delivery.DeliveryTypeEnum;
import cn.iocoder.yudao.module.trade.service.order.TradeOrderUpdateService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;

/**
 * 虚拟商品订单处理器
 *
 * <AUTHOR>
 */
@Component
@Slf4j
public class TradeVirtualOrderHandler implements TradeOrderHandler {

    @Resource
    private ProductSpuApi productSpuApi;
    @Resource
    private ProductSkuApi productSkuApi;
    @Resource
    private ProductCardCodeApi cardCodeApi;
    @Resource
    @Lazy
    private TradeOrderUpdateService orderUpdateService;

    @Override
    public void beforeOrderCreate(TradeOrderDO order, List<TradeOrderItemDO> orderItems) {
        // 检查是否包含虚拟商品
        boolean hasVirtualProduct = false;
        boolean hasPhysicalProduct = false;
        
        for (TradeOrderItemDO orderItem : orderItems) {
            ProductSpuRespDTO spu = productSpuApi.getSpu(orderItem.getSpuId());
            if (spu != null && CollUtil.isNotEmpty(spu.getDeliveryTypes())) {
                if (spu.getDeliveryTypes().contains(DeliveryTypeEnum.VIRTUAL.getType())) {
                    hasVirtualProduct = true;
                    // 检查卡密库存是否充足
                    Long availableCount = cardCodeApi.getAvailableCardCodeCount(orderItem.getSpuId(), orderItem.getSkuId());
                    if (availableCount < orderItem.getCount()) {
                        throw new RuntimeException("商品【" + spu.getName() + "】卡密库存不足，可用数量：" + availableCount);
                    }
                } else {
                    hasPhysicalProduct = true;
                }
            }
        }
        
        // 如果订单中既有虚拟商品又有实物商品，则不允许
        if (hasVirtualProduct && hasPhysicalProduct) {
            throw new RuntimeException("虚拟商品不能与实物商品一起下单");
        }
        
        // 如果是纯虚拟商品订单，设置配送方式为虚拟商品
        if (hasVirtualProduct) {
            order.setDeliveryType(DeliveryTypeEnum.VIRTUAL.getType());
            // 虚拟商品无需收货地址
            order.setReceiverName(null);
            order.setReceiverMobile(null);
            order.setReceiverAreaId(null);
            order.setReceiverDetailAddress(null);
        }
    }

    @Override
    public void afterOrderCreate(TradeOrderDO order, List<TradeOrderItemDO> orderItems) {
        // 订单创建后暂不处理，等支付成功后再分配卡密
    }

    @Override
    public void afterPayOrder(TradeOrderDO order, List<TradeOrderItemDO> orderItems) {
        // 只处理虚拟商品订单
        if (ObjectUtil.notEqual(order.getDeliveryType(), DeliveryTypeEnum.VIRTUAL.getType())) {
            return;
        }

        try {
            // 1. 为每个订单项分配卡密
            for (TradeOrderItemDO orderItem : orderItems) {
                cardCodeApi.allocateCardCodes(
                        orderItem.getSpuId(),
                        orderItem.getSkuId(),
                        orderItem.getCount(),
                        order.getId(),
                        orderItem.getId()
                );
                log.info("[afterPayOrder][为订单项({})分配了{}个卡密]", orderItem.getId(), orderItem.getCount());
            }

            // 2. 自动发货
            orderUpdateService.autoDeliveryVirtualOrder(order.getId());
            log.info("[afterPayOrder][虚拟商品订单({})已自动发货]", order.getId());
            
        } catch (Exception e) {
            log.error("[afterPayOrder][虚拟商品订单({})处理失败]", order.getId(), e);
            // 回滚卡密分配
            for (TradeOrderItemDO orderItem : orderItems) {
                try {
                    cardCodeApi.rollbackCardCodeAllocation(orderItem.getId());
                } catch (Exception rollbackException) {
                    log.error("[afterPayOrder][回滚订单项({})卡密分配失败]", orderItem.getId(), rollbackException);
                }
            }
            throw e;
        }
    }

    @Override
    public void afterCancelOrder(TradeOrderDO order, List<TradeOrderItemDO> orderItems) {
        // 只处理虚拟商品订单
        if (ObjectUtil.notEqual(order.getDeliveryType(), DeliveryTypeEnum.VIRTUAL.getType())) {
            return;
        }

        // 回滚卡密分配
        for (TradeOrderItemDO orderItem : orderItems) {
            try {
                cardCodeApi.rollbackCardCodeAllocation(orderItem.getId());
                log.info("[afterCancelOrder][回滚订单项({})的卡密分配]", orderItem.getId());
            } catch (Exception e) {
                log.error("[afterCancelOrder][回滚订单项({})卡密分配失败]", orderItem.getId(), e);
            }
        }
    }

    @Override
    public void afterCancelOrderItem(TradeOrderDO order, TradeOrderItemDO orderItem) {
        // 只处理虚拟商品
        ProductSpuRespDTO spu = productSpuApi.getSpu(orderItem.getSpuId());
        if (spu == null || CollUtil.isEmpty(spu.getDeliveryTypes()) 
                || !spu.getDeliveryTypes().contains(DeliveryTypeEnum.VIRTUAL.getType())) {
            return;
        }

        // 回滚卡密分配
        try {
            cardCodeApi.rollbackCardCodeAllocation(orderItem.getId());
            log.info("[afterCancelOrderItem][回滚订单项({})的卡密分配]", orderItem.getId());
        } catch (Exception e) {
            log.error("[afterCancelOrderItem][回滚订单项({})卡密分配失败]", orderItem.getId(), e);
        }
    }

}
