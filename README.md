1954365742
2131929203
371800776
8427947
331833119
16194648
73911771
29982331
11569700
228816049
511012459
50913379
1256295378
47532992
2454678977
5913072
502669779
8940989
227811072
12222334
169308467
20229201
1289566627
4658786792
4377933002
305362176
641412925
881758958
45672093
5825207
95992100
3676208364
3644534973
4187716321
2347908554
102999341
1091856623
433149036
709084778
94198747
2208651504
102720800
4667423034
172766383
4495500846
2096489660
1437194718
843120258
3728326126
1391295492
532620584
1340215466
1214372729
1135009365
1392669964
59746572
80441927
1222649114
2056130882
1352787607
2125414678
1214488036 --
4766844132
*********  -- 
1427366253
1149413467  --
*********
4205257296
4750400152
1214372729
4516210283
*********  -- 
1385765837
*********
4424387038
1214507896
4339498161







1,2,3,4,5,6,7,8,9,10,11,12,13,15,16,17,18,19,20,21,22,23,24,25,26,27,29,30,31,32,34,39,40,43,44,55,71,74,75,76







ESC[0;39m | [executeFFmpegCommand][执行FFmpeg命令，参数: ffmpeg -y -i /tmp/TG@weimi2020 (24).png -i /root/caddy/www/company-logo.png -vf scale='if(gt(iw,20
48),2048,iw)':-1,[1:v]scale=iw*0.08:ih*0.08,format=rgba,colorchannelmixer=aa=0.6[watermark];[0:v][watermark]overlay=W-w-10:10 /tmp/TG@weimi2020 (24)_optimi
zed.avif]
2025-08-31 22:54:00.112 | ESC[39mDEBUG 841522ESC[0;39m | ESC[1;33mpool-11-thread-1 [TID: N/A]ESC[0;39m ESC[1;32mc.i.y.m.i.f.f.c.utils.FileCompressUtils :?
ESC[0;39m | [executeFFmpegCommand][命令执行完成，退出码: 234, 输出: ffmpeg version 7.0.2-static https://johnvansickle.com/ffmpeg/  Copyright (c) 2000-2024 
the FFmpeg developers
  built with gcc 8 (Debian 8.3.0-6)
  configuration: --enable-gpl --enable-version3 --enable-static --disable-debug --disable-ffplay --disable-indev=sndio --disable-outdev=sndio --cc=gcc --en
able-fontconfig --enable-frei0r --enable-gnutls --enable-gmp --enable-libgme --enable-gray --enable-libaom --enable-libfribidi --enable-libass --enable-lib
vmaf --enable-libfreetype --enable-libmp3lame --enable-libopencore-amrnb --enable-libopencore-amrwb --enable-libopenjpeg --enable-librubberband --enable-li
bsoxr --enable-libspeex --enable-libsrt --enable-libvorbis --enable-libopus --enable-libtheora --enable-libvidstab --enable-libvo-amrwbenc --enable-libvpx 
--enable-libwebp --enable-libx264 --enable-libx265 --enable-libxml2 --enable-libdav1d --enable-libxvid --enable-libzvbi --enable-libzimg
  libavutil      59.  8.100 / 59.  8.100
  libavcodec     61.  3.100 / 61.  3.100
  libavformat    61.  1.100 / 61.  1.100
  libavdevice    61.  1.100 / 61.  1.100
  libavfilter    10.  1.100 / 10.  1.100
  libswscale      8.  1.100 /  8.  1.100
  libswresample   5.  1.100 /  5.  1.100
  libpostproc    58.  1.100 / 58.  1.100
Input #0, png_pipe, from '/tmp/TG@weimi2020 (24).png':
  Duration: N/A, bitrate: N/A
  Stream #0:0: Video: png, rgb24(pc, gbr/unknown/unknown), 604x1080, 25 fps, 25 tbr, 25 tbn
  configuration: --enable-gpl --enable-version3 --enable-static --disable-debug --disable-ffplay --disable-indev=sndio --disable-outdev=sndio --cc=gcc --en
able-fontconfig --enable-frei0r --enable-gnutls --enable-gmp --enable-libgme --enable-gray --enable-libaom --enable-libfribidi --enable-libass --enable-lib
vmaf --enable-libfreetype --enable-libmp3lame --enable-libopencore-amrnb --enable-libopencore-amrwb --enable-libopenjpeg --enable-librubberband --enable-li
bsoxr --enable-libspeex --enable-libsrt --enable-libvorbis --enable-libopus --enable-libtheora --enable-libvidstab --enable-libvo-amrwbenc --enable-libvpx 
--enable-libwebp --enable-libx264 --enable-libx265 --enable-libxml2 --enable-libdav1d --enable-libxvid --enable-libzvbi --enable-libzimg
  libavutil      59.  8.100 / 59.  8.100
  libavcodec     61.  3.100 / 61.  3.100
  libavformat    61.  1.100 / 61.  1.100
  libavdevice    61.  1.100 / 61.  1.100
  libavfilter    10.  1.100 / 10.  1.100
  libswscale      8.  1.100 /  8.  1.100
  libswresample   5.  1.100 /  5.  1.100
  libpostproc    58.  1.100 / 58.  1.100
Input #0, png_pipe, from '/tmp/TG@weimi2020 (24).png':
  Duration: N/A, bitrate: N/A
  Stream #0:0: Video: png, rgb24(pc, gbr/unknown/unknown), 604x1080, 25 fps, 25 tbr, 25 tbn
Input #1, png_pipe, from '/root/caddy/www/company-logo.png':
  Duration: N/A, bitrate: N/A
  Stream #1:0: Video: png, rgba(pc, gbr/unknown/unknown), 700x150, 25 fps, 25 tbr, 25 tbn
[vf#0:0 @ 0x21c6d640] Simple filtergraph 'scale='if(gt(iw,2048),2048,iw)':-1,[1:v]scale=iw*0.08:ih*0.08,format=rgba,colorchannelmixer=aa=0.6[watermark];[0:
v][watermark]overlay=W-w-10:10' was expected to have exactly 1 input and 1 output. However, it had 3 input(s) and 2 output(s). Please adjust, or use a complex filtergraph (-filter_complex) instead.
[vost#0:0/libaom-av1 @ 0x21c70380] Error initializing a simple filtergraph
Error opening output file /tmp/TG@weimi2020 (24)_optimized.avif.
Error opening output files: Invalid argument]




ffmpeg -y -i "/tmp/unzip_1755701908381/小艾根本吃不饱 NO.002期 [26P-13MB]/TG@weimi2020 (20).png" -i /root/caddy/www/company-logo.png -filter_complex "[0:v]scale='if(gt(iw,2048),2048,iw)':-1[main];[1:v]scale=200:-1,format=rgba,colorchannelmixer=aa=0.8[watermark];[main][watermark]overlay=W-w-10:10" "/root/wx/vm/download_root/pk/31/20446/optimized.avif"


ffmpeg -y \
-i "/tmp/unzip_1755701908381/小艾根本吃不饱 NO.002期 [26P-13MB]/TG@weimi2020 (20).png" \
-i "/root/caddy/www/company-logo.png" \
-filter_complex "\
[1:v]scale=200:-1[logo]; \
[0:v][logo]overlay=W-w-10:10 \
" \
"/root/wx/vm/download_root/pk/31/20446/optimized.avif"


ffmpeg -y -i "/root/wx/java/content-upload/xxx/1.png" -i "/root/caddy/www/company-logo.png" -filter_complex "[0:v]scale='if(gt(iw,2048),2048,iw)':-1[bg];[1:v]scale=iw*0.08:ih*0.08,format=rgba,colorchannelmixer=aa=0.6[watermark];[bg][watermark]overlay=W-w-10:10" "/root/caddy/www/1.avif"



ffmpeg -y -i "/root/wx/java/content-upload/xxx/TG@weimi2020 (5).png" -i "/root/caddy/www/company-logo.png" -filter_complex "[0:v]scale='if(gt(iw,2048),2048,iw)':-1[main];[1:v]scale=160:-1,format=rgba,colorchannelmixer=aa=0.6[watermark];[main][watermark]overlay=W-w-10:(H-h)/2" "/root/caddy/www/1.avif"



内容处理失败: 处理混合媒体上传失败: 
### Error flushing statements.  Cause: org.apache.ibatis.executor.BatchExecutorException: cn.iocoder.yudao.module.tv.dal.mysql.content.ContentMediaMapper.insert (batch index #2) failed. 1 prior sub executor(s) completed successfully, but will be rolled back. Cause: java.sql.BatchUpdateException: Data truncation: Data too long for column 'url' at row 1
### Cause: org.apache.ibatis.executor.BatchExecutorException: cn.iocoder.yudao.module.tv.dal.mysql.content.ContentMediaMapper.insert (batch index #2) failed. 1 prior sub executor(s) completed successfully, but will be rolled back. Cause: java.sql.BatchUpdateException: Data truncation: Data too long for column 'url' at row 1






ffmpeg -y -i "/root/wx/java/content-upload/xxx/TG@weimi2020 (5).png" \
-i "/root/caddy/www/company-logo.png" \
-filter_complex "
  [0:v]scale='if(gt(iw,2048),2048,iw)':-1[main];
  [main]drawbox=x=w-170:y=h-70:w=160:h=60:color=white@1:t=fill[clean];
  [1:v]scale=160:-1,format=rgba,colorchannelmixer=aa=0.6[watermark];
  [clean][watermark]overlay=x=W-w-10:y=H-h-10
" "/root/caddy/www/1.avif"

