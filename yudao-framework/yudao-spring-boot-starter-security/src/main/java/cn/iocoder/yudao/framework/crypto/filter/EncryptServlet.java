package cn.iocoder.yudao.framework.crypto.filter;

import cn.hutool.core.codec.Base64;
import cn.hutool.core.util.CharsetUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.extra.servlet.ServletUtil;
import cn.hutool.json.JSONArray;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import lombok.AllArgsConstructor;
import org.springframework.http.MediaType;
import org.springframework.web.servlet.DispatcherServlet;

import javax.servlet.ServletConfig;
import javax.servlet.ServletException;
import javax.servlet.http.HttpServlet;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 废弃，因为他会触发 其他的Filter 导致数据不准确。
 * @see EncryptFilter
 */
@Deprecated
@AllArgsConstructor
public class EncryptServlet extends HttpServlet {

    private DispatcherServlet dispatcherServlet;

    @Override
    public void init(ServletConfig config) throws ServletException {
        super.init(config);
        // 必须要这样子实现，因为他这样子才能避免init逻辑没有执行的报错问题。
        if (dispatcherServlet.getServletConfig() == null) {
            dispatcherServlet.init(config);
        }
    }//

    @Override
    protected void doPost(HttpServletRequest originalRequest, HttpServletResponse originalResponse) throws ServletException, IOException {
        ModifiedHttpServletRequestWrapper newRequest = toModifiedHttpServletRequestWrapper(originalRequest);
        ModifiedResponseWrapper newResponse = new ModifiedResponseWrapper(originalResponse);

        // 手动调用 DispatcherServlet 处理新请求
        System.out.println("dispatcher init context: " + dispatcherServlet.getWebApplicationContext());
        dispatcherServlet.service(newRequest, newResponse);

        // 把处理结果写回原始响应体（注意设置 content-type）
        originalResponse.setContentType("text/plain; charset=UTF-8");
        originalResponse.setCharacterEncoding(newResponse.getCharacterEncoding());
        originalResponse.setStatus(newResponse.getStatus());
        String responseBodyAsString = newResponse.getResponseBodyAsString();
        // TODO 添加base64加密 responseBodyAsString 的方式。
        String response = Base64.encode(responseBodyAsString);
        originalResponse.getWriter().write(response);
    }


    public ModifiedHttpServletRequestWrapper toModifiedHttpServletRequestWrapper(HttpServletRequest originalRequest){
        String reqBody = ServletUtil.getBody(originalRequest);
        // TODO 添加base64解密 reqBody 的方法。
        String json = Base64.decodeStr(reqBody);
        JSONObject jsonObject = JSONUtil.parseObj(json);
        String url = jsonObject.getStr("url");
        String method = jsonObject.getStr("method");
        String body = jsonObject.getStr("body");
        byte[] bodies = StrUtil.bytes(body, CharsetUtil.UTF_8);  // jsonObject.getBytes("body");
        JSONObject parameter = jsonObject.getJSONObject("parameter");
        // ✅ 将 parameter JSONObject 转为 Map<String, String[]>
        Map<String, String[]> customParameterMap = new HashMap<>();
        parameter.forEach((key, value) -> {
            if (value instanceof JSONArray ) {
                JSONArray arr = (JSONArray) value;
                List<String> list = arr.toList(String.class);
                customParameterMap.put(key, list.toArray(new String[0]));
            } else {
                customParameterMap.put(key, new String[]{value.toString()});
            }
        });
        return new ModifiedHttpServletRequestWrapper(originalRequest, url, method, bodies, customParameterMap , MediaType.APPLICATION_JSON_VALUE);
    }


}
