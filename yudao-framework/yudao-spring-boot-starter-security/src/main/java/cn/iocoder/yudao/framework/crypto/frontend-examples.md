# 前端加密解密实现示例

本文档提供了与后端加密服务兼容的前端JavaScript实现示例。

## AES-256-GCM 实现

### 使用 Web Crypto API (现代浏览器)

```javascript
/**
 * 使用AES-GCM加密数据
 * @param {string} plaintext - 要加密的文本
 * @param {string} keyBase64 - Base64编码的密钥
 * @returns {Promise<string>} - Base64编码的加密结果 (包含nonce)
 */
async function encryptAesGcm(plaintext, keyBase64) {
  // 解码密钥
  const keyData = base64ToArrayBuffer(keyBase64);
  
  // 导入密钥
  const key = await window.crypto.subtle.importKey(
    "raw",
    keyData,
    { name: "AES-GCM" },
    false,
    ["encrypt"]
  );
  
  // 生成随机nonce
  const nonce = window.crypto.getRandomValues(new Uint8Array(12));
  
  // 将文本转换为UTF-8编码的ArrayBuffer
  const encoder = new TextEncoder();
  const data = encoder.encode(plaintext);
  
  // 加密数据
  const ciphertext = await window.crypto.subtle.encrypt(
    {
      name: "AES-GCM",
      iv: nonce,
      tagLength: 128
    },
    key,
    data
  );
  
  // 将nonce和密文组合在一起
  const result = new Uint8Array(nonce.length + ciphertext.byteLength);
  result.set(nonce);
  result.set(new Uint8Array(ciphertext), nonce.length);
  
  // 返回Base64编码的结果
  return arrayBufferToBase64(result);
}

/**
 * 使用AES-GCM解密数据
 * @param {string} encryptedBase64 - Base64编码的加密数据 (包含nonce)
 * @param {string} keyBase64 - Base64编码的密钥
 * @returns {Promise<string>} - 解密后的文本
 */
async function decryptAesGcm(encryptedBase64, keyBase64) {
  // 解码密钥和加密数据
  const keyData = base64ToArrayBuffer(keyBase64);
  const encryptedData = base64ToArrayBuffer(encryptedBase64);
  
  // 提取nonce和密文
  const nonce = encryptedData.slice(0, 12);
  const ciphertext = encryptedData.slice(12);
  
  // 导入密钥
  const key = await window.crypto.subtle.importKey(
    "raw",
    keyData,
    { name: "AES-GCM" },
    false,
    ["decrypt"]
  );
  
  // 解密数据
  const decrypted = await window.crypto.subtle.decrypt(
    {
      name: "AES-GCM",
      iv: nonce,
      tagLength: 128
    },
    key,
    ciphertext
  );
  
  // 将解密后的数据转换为文本
  const decoder = new TextDecoder();
  return decoder.decode(decrypted);
}

// 辅助函数：Base64转ArrayBuffer
function base64ToArrayBuffer(base64) {
  const binaryString = atob(base64);
  const bytes = new Uint8Array(binaryString.length);
  for (let i = 0; i < binaryString.length; i++) {
    bytes[i] = binaryString.charCodeAt(i);
  }
  return bytes;
}

// 辅助函数：ArrayBuffer转Base64
function arrayBufferToBase64(buffer) {
  const bytes = new Uint8Array(buffer);
  let binary = '';
  for (let i = 0; i < bytes.byteLength; i++) {
    binary += String.fromCharCode(bytes[i]);
  }
  return btoa(binary);
}
```

### 使用 CryptoJS (兼容性更好)

```javascript
// 需要引入 crypto-js 库
// <script src="https://cdnjs.cloudflare.com/ajax/libs/crypto-js/4.1.1/crypto-js.min.js"></script>

/**
 * 使用AES-GCM加密数据
 * @param {string} plaintext - 要加密的文本
 * @param {string} keyBase64 - Base64编码的密钥
 * @returns {string} - Base64编码的加密结果 (包含nonce)
 */
function encryptAesGcm(plaintext, keyBase64) {
  // 解码密钥
  const keyBytes = CryptoJS.enc.Base64.parse(keyBase64);
  
  // 生成随机nonce
  const nonce = CryptoJS.lib.WordArray.random(12);
  
  // 加密数据
  const encrypted = CryptoJS.AES.encrypt(plaintext, keyBytes, {
    iv: nonce,
    mode: CryptoJS.mode.GCM,
    padding: CryptoJS.pad.NoPadding,
    tagLength: 128
  });
  
  // 将nonce和密文组合在一起
  const ciphertext = encrypted.ciphertext;
  const result = CryptoJS.lib.WordArray.create()
    .concat(nonce)
    .concat(ciphertext);
  
  // 返回Base64编码的结果
  return CryptoJS.enc.Base64.stringify(result);
}

/**
 * 使用AES-GCM解密数据
 * @param {string} encryptedBase64 - Base64编码的加密数据 (包含nonce)
 * @param {string} keyBase64 - Base64编码的密钥
 * @returns {string} - 解密后的文本
 */
function decryptAesGcm(encryptedBase64, keyBase64) {
  // 解码密钥和加密数据
  const keyBytes = CryptoJS.enc.Base64.parse(keyBase64);
  const encryptedData = CryptoJS.enc.Base64.parse(encryptedBase64);
  
  // 提取nonce和密文
  const nonce = CryptoJS.lib.WordArray.create(
    encryptedData.words.slice(0, 3),
    12
  );
  const ciphertext = CryptoJS.lib.WordArray.create(
    encryptedData.words.slice(3),
    encryptedData.sigBytes - 12
  );
  
  // 创建CipherParams对象
  const cipherParams = CryptoJS.lib.CipherParams.create({
    ciphertext: ciphertext,
    iv: nonce,
    mode: CryptoJS.mode.GCM,
    padding: CryptoJS.pad.NoPadding,
    tagLength: 128
  });
  
  // 解密数据
  const decrypted = CryptoJS.AES.decrypt(cipherParams, keyBytes, {
    iv: nonce,
    mode: CryptoJS.mode.GCM,
    padding: CryptoJS.pad.NoPadding,
    tagLength: 128
  });
  
  // 返回UTF-8文本
  return decrypted.toString(CryptoJS.enc.Utf8);
}
```

## ChaCha20-Poly1305 实现

### 使用 libsodium.js

```javascript
// 需要引入 libsodium.js 库
// <script src="https://cdnjs.cloudflare.com/ajax/libs/libsodium-wrappers/0.7.10/sodium.min.js"></script>

/**
 * 使用ChaCha20-Poly1305加密数据
 * @param {string} plaintext - 要加密的文本
 * @param {string} keyBase64 - Base64编码的密钥
 * @returns {Promise<string>} - Base64编码的加密结果 (包含nonce)
 */
async function encryptChaCha20Poly1305(plaintext, keyBase64) {
  // 等待libsodium准备就绪
  await sodium.ready;
  
  // 解码密钥
  const keyBytes = sodium.from_base64(keyBase64, sodium.base64_variants.ORIGINAL);
  
  // 生成随机nonce
  const nonce = sodium.randombytes_buf(sodium.crypto_aead_chacha20poly1305_ietf_NPUBBYTES);
  
  // 加密数据
  const messageBytes = sodium.from_string(plaintext);
  const ciphertext = sodium.crypto_aead_chacha20poly1305_ietf_encrypt(
    messageBytes,
    null, // 附加数据
    null, // 秘密nonce
    nonce,
    keyBytes
  );
  
  // 将nonce和密文组合在一起
  const result = new Uint8Array(nonce.length + ciphertext.length);
  result.set(nonce);
  result.set(ciphertext, nonce.length);
  
  // 返回Base64编码的结果
  return sodium.to_base64(result, sodium.base64_variants.ORIGINAL);
}

/**
 * 使用ChaCha20-Poly1305解密数据
 * @param {string} encryptedBase64 - Base64编码的加密数据 (包含nonce)
 * @param {string} keyBase64 - Base64编码的密钥
 * @returns {Promise<string>} - 解密后的文本
 */
async function decryptChaCha20Poly1305(encryptedBase64, keyBase64) {
  // 等待libsodium准备就绪
  await sodium.ready;
  
  // 解码密钥和加密数据
  const keyBytes = sodium.from_base64(keyBase64, sodium.base64_variants.ORIGINAL);
  const encryptedData = sodium.from_base64(encryptedBase64, sodium.base64_variants.ORIGINAL);
  
  // 提取nonce和密文
  const nonceLength = sodium.crypto_aead_chacha20poly1305_ietf_NPUBBYTES;
  const nonce = encryptedData.slice(0, nonceLength);
  const ciphertext = encryptedData.slice(nonceLength);
  
  // 解密数据
  const decrypted = sodium.crypto_aead_chacha20poly1305_ietf_decrypt(
    null, // 秘密nonce
    ciphertext,
    null, // 附加数据
    nonce,
    keyBytes
  );
  
  // 返回UTF-8文本
  return sodium.to_string(decrypted);
}
```

### 使用 TweetNaCl.js

```javascript
// 需要引入 tweetnacl.js 和 tweetnacl-util.js 库
// <script src="https://cdnjs.cloudflare.com/ajax/libs/tweetnacl/1.0.3/nacl.min.js"></script>
// <script src="https://cdn.jsdelivr.net/npm/tweetnacl-util@0.15.1/nacl-util.min.js"></script>

/**
 * 使用XChaCha20-Poly1305加密数据
 * @param {string} plaintext - 要加密的文本
 * @param {string} keyBase64 - Base64编码的密钥
 * @returns {string} - Base64编码的加密结果 (包含nonce)
 */
function encryptXChaCha20Poly1305(plaintext, keyBase64) {
  // 解码密钥
  const keyBytes = nacl.util.decodeBase64(keyBase64);
  
  // 生成随机nonce
  const nonce = nacl.randomBytes(nacl.secretbox.nonceLength);
  
  // 加密数据
  const messageBytes = nacl.util.decodeUTF8(plaintext);
  const ciphertext = nacl.secretbox(messageBytes, nonce, keyBytes);
  
  // 将nonce和密文组合在一起
  const result = new Uint8Array(nonce.length + ciphertext.length);
  result.set(nonce);
  result.set(ciphertext, nonce.length);
  
  // 返回Base64编码的结果
  return nacl.util.encodeBase64(result);
}

/**
 * 使用XChaCha20-Poly1305解密数据
 * @param {string} encryptedBase64 - Base64编码的加密数据 (包含nonce)
 * @param {string} keyBase64 - Base64编码的密钥
 * @returns {string} - 解密后的文本
 */
function decryptXChaCha20Poly1305(encryptedBase64, keyBase64) {
  // 解码密钥和加密数据
  const keyBytes = nacl.util.decodeBase64(keyBase64);
  const encryptedData = nacl.util.decodeBase64(encryptedBase64);
  
  // 提取nonce和密文
  const nonceLength = nacl.secretbox.nonceLength;
  const nonce = encryptedData.slice(0, nonceLength);
  const ciphertext = encryptedData.slice(nonceLength);
  
  // 解密数据
  const decrypted = nacl.secretbox.open(ciphertext, nonce, keyBytes);
  if (!decrypted) {
    throw new Error('解密失败');
  }
  
  // 返回UTF-8文本
  return nacl.util.encodeUTF8(decrypted);
}
```

## 生成随机密钥

```javascript
/**
 * 生成随机AES-256密钥
 * @returns {Promise<string>} - Base64编码的随机密钥
 */
async function generateRandomAesKey() {
  const key = await window.crypto.subtle.generateKey(
    {
      name: "AES-GCM",
      length: 256
    },
    true,
    ["encrypt", "decrypt"]
  );
  
  const keyData = await window.crypto.subtle.exportKey("raw", key);
  return arrayBufferToBase64(keyData);
}

/**
 * 生成随机ChaCha20密钥
 * @returns {string} - Base64编码的随机密钥
 */
function generateRandomChaCha20Key() {
  const keyBytes = nacl.randomBytes(32); // ChaCha20需要32字节密钥
  return nacl.util.encodeBase64(keyBytes);
}
```
