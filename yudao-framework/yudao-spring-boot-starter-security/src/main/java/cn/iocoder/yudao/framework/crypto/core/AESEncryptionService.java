package cn.iocoder.yudao.framework.crypto.core;

import lombok.Setter;

import javax.annotation.PostConstruct;
import javax.crypto.Cipher;
import javax.crypto.KeyGenerator;
import javax.crypto.SecretKey;
import javax.crypto.spec.IvParameterSpec;
import javax.crypto.spec.SecretKeySpec;
import java.security.SecureRandom;

/**
 * AES对称加密服务实现
 */
public class AESEncryptionService extends AbstractEncryptionService {

    /**
     * AES算法名称
     */
    protected static final String ALGORITHM = "AES";

    /**
     * AES加密模式和填充方式
     */
    @Setter
    protected String transformation;

    /**
     * AES密钥长度
     */
    @Setter
    protected int keySize;


    /**
     * 验证参数
     */
    @PostConstruct
    protected void validateParameters() {
        if (transformation == null || transformation.isEmpty()) {
            throw new IllegalArgumentException("AES transformation cannot be null or empty");
        }

        // 验证密钥长度是否是AES支持的值
        if (keySize != 128 && keySize != 192 && keySize != 256) {
            throw new IllegalArgumentException("AES key size must be 128, 192, or 256 bits");
        }
    }

    @Override
    public String getType() {
        return "AES";
    }

    @Override
    public String encrypt(String data, String key, String iv) throws EncryptionException {
        try {
            Cipher cipher = Cipher.getInstance(transformation);
            SecretKeySpec keySpec = createKeySpec(key);
            IvParameterSpec ivSpec = new IvParameterSpec(base64ToBytes(iv));

            cipher.init(Cipher.ENCRYPT_MODE, keySpec, ivSpec);
            byte[] encrypted = cipher.doFinal(data.getBytes(getCharset()));

            return bytesToBase64(encrypted);
        } catch (Exception e) {
            throw new EncryptionException("AES encryption failed", e);
        }
    }

    @Override
    public String decrypt(String encryptedData, String key, String iv) throws EncryptionException {
        try {
            Cipher cipher = Cipher.getInstance(transformation);
            SecretKeySpec keySpec = createKeySpec(key);
            IvParameterSpec ivSpec = new IvParameterSpec(base64ToBytes(iv));

            cipher.init(Cipher.DECRYPT_MODE, keySpec, ivSpec);
            byte[] decrypted = cipher.doFinal(base64ToBytes(encryptedData));

            return new String(decrypted, getCharset());
        } catch (Exception e) {
            throw new EncryptionException("AES decryption failed", e);
        }
    }

    @Override
    public String generateIv() {
        try {
            SecureRandom random = new SecureRandom();
            byte[] iv = new byte[16]; // AES block size is 16 bytes
            random.nextBytes(iv);
            return bytesToBase64(iv);
        } catch (Exception e) {
            throw new EncryptionException("Failed to generate IV", e);
        }
    }

    /**
     * 创建AES密钥规范
     *
     * @param key 密钥字符串
     * @return 密钥规范
     */
    protected SecretKeySpec createKeySpec(String key) throws Exception {
        // 确保密钥长度正确
        byte[] keyBytes = key.getBytes(getCharset());
        KeyGenerator kgen = KeyGenerator.getInstance(ALGORITHM);
        SecureRandom random = SecureRandom.getInstance("SHA1PRNG");
        random.setSeed(keyBytes);
        kgen.init(keySize, random);
        SecretKey secretKey = kgen.generateKey();
        return new SecretKeySpec(secretKey.getEncoded(), ALGORITHM);
    }

    public static void main(String[] args) {
        /*
              - name: aes-cfb-nopadding-128
        transformation: AES/CFB/NoPadding
        key-size: 128
         */
        AESEncryptionService aesEncryptionService = new AESEncryptionService();
        aesEncryptionService.keySize = 128;
        aesEncryptionService.transformation = "AES/CBC/PKCS5Padding";
        aesEncryptionService.setServiceCharset("UTF-8");

        String data = "DPp5t8jNp6mi8Vc2MA/ylQ==:3h/dF+z0Zs/FraL+tTspMpbAFSI5aRR+jswF5tyqR3jjAWzcmkuhcXBVw6tIvKvAyhMtxHmLTkfatx2A0WTlIP6MMltwxilyrcfn9oYRXgl2tNcnOUKwjIAu782+gQp7";
        String key = "rZr8it3YHJj6eiQLbG";
        String iv = aesEncryptionService.generateIv();
        String encrypt = aesEncryptionService.encrypt("fdsafsda", key, iv);
        System.out.println(aesEncryptionService.decrypt(encrypt,key,iv));
//        System.out.println(aesEncryptionService.decrypt(data,key));
    }
}
