package cn.iocoder.yudao.framework.crypto.core;


/**
 * 加密解密服务接口
 *
 * 提供统一的加密解密操作，支持不同的加密算法和密钥
 */
public interface EncryptionService {

    /**
     * 获取加密算法类型
     *
     * @return 加密算法类型
     */
    String getType();

    /**
     * 使用默认密钥加密数据
     *
     * @param data 待加密的原始数据
     * @return 加密后的数据
     * @throws EncryptionException 加密过程中发生错误
     */
    String encrypt(String data) throws EncryptionException;

    /**
     * 使用默认密钥解密数据
     *
     * @param encryptedData 加密后的数据
     * @return 解密后的原始数据
     * @throws EncryptionException 解密过程中发生错误
     */
    String decrypt(String encryptedData) throws EncryptionException;

    /**
     * 使用指定密钥加密数据，自动生成IV并包含在结果中
     *
     * @param data 待加密的原始数据
     * @param key 加密密钥
     * @return 加密后的数据(包含IV)
     * @throws EncryptionException 加密过程中发生错误
     */
    String encrypt(String data, String key) throws EncryptionException;

    /**
     * 使用指定密钥解密数据，从密文中提取IV
     *
     * @param encryptedData 加密后的数据(包含IV)
     * @param key 解密密钥
     * @return 解密后的原始数据
     * @throws EncryptionException 解密过程中发生错误
     */
    String decrypt(String encryptedData, String key) throws EncryptionException;

    /**
     * 使用指定密钥和IV加密数据
     *
     * @param data 待加密的原始数据
     * @param key 加密密钥
     * @param iv 初始化向量
     * @return 加密后的数据
     * @throws EncryptionException 加密过程中发生错误
     */
    String encrypt(String data, String key, String iv) throws EncryptionException;

    /**
     * 使用指定密钥和IV解密数据
     *
     * @param encryptedData 加密后的数据
     * @param key 解密密钥
     * @param iv 初始化向量
     * @return 解密后的原始数据
     * @throws EncryptionException 解密过程中发生错误
     */
    String decrypt(String encryptedData, String key, String iv) throws EncryptionException;

    /**
     * 生成适合当前算法的随机IV
     *
     * @return 随机生成的IV
     */
    String generateIv();

    /**
     * 生成当前算法的一对密钥
     *
     * @return 随机生成密钥
     */
    SecretKeyBO generateSecretKey();

}
