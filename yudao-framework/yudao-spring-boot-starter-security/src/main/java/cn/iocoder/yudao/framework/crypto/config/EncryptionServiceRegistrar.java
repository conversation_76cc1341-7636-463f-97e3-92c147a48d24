package cn.iocoder.yudao.framework.crypto.config;

import cn.iocoder.yudao.framework.crypto.filter.EncryptFilter;
import cn.iocoder.yudao.framework.crypto.core.*;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;

import org.springframework.beans.factory.support.BeanDefinitionBuilder;
import org.springframework.beans.factory.support.BeanDefinitionRegistry;
import org.springframework.boot.ApplicationArguments;
import org.springframework.boot.ApplicationRunner;
import org.springframework.context.ApplicationContext;
import org.springframework.context.ConfigurableApplicationContext;
import cn.iocoder.yudao.framework.common.util.object.BeanUtils;

import java.util.Map;

/**
 * 动态 Bean 注册器，用于注册多个加密服务 Bean
 *  ❌ implements BeanDefinitionRegistryPostProcessor 这种实现会导致 EncryptionProperties还没有完全读取到配置文件就开始执行啦。
 *
 */
@Slf4j
@AllArgsConstructor
public class EncryptionServiceRegistrar implements ApplicationRunner {

    private final EncryptionProperties properties;
    private final ApplicationContext applicationContext;
    private final EncryptFilter encryptFilter;

    @Override
    public void run(ApplicationArguments args) throws Exception {
        if (properties.getServices() == null) {
            log.debug("No encryption services configured");
        } else {
            ConfigurableApplicationContext configurableContext = (ConfigurableApplicationContext) applicationContext;
            BeanDefinitionRegistry registry = (BeanDefinitionRegistry) configurableContext.getBeanFactory();

            // 注册 AES 加密服务
            if (properties.getServices().getAes() != null) {
                for (EncryptionProperties.AesServiceConfig config : properties.getServices().getAes()) {
                    registerEncryptionService(registry, config, AESEncryptionService.class);
                }
            }

            // 注册 DES 加密服务
            if (properties.getServices().getDes() != null) {
                for (EncryptionProperties.DesServiceConfig config : properties.getServices().getDes()) {
                    registerEncryptionService(registry, config, DESEncryptionService.class);
                }
            }

            // 注册 RSA 加密服务
            if (properties.getServices().getRsa() != null) {
                for (EncryptionProperties.RsaServiceConfig config : properties.getServices().getRsa()) {
                    registerEncryptionService(registry, config, RSAEncryptionService.class);
                }
            }

            // 注册 AES-GCM 加密服务
            if (properties.getServices().getAesGcm() != null) {
                for (EncryptionProperties.AesGcmServiceConfig config : properties.getServices().getAesGcm()) {
                    registerEncryptionService(registry, config, AESGCMEncryptionService.class);
                }
            }

            // 注册 AES-PBE 加密服务
            if (properties.getServices().getAesPBE() != null) {
                for (EncryptionProperties.AesPBEServiceConfig config : properties.getServices().getAesPBE()) {
                    registerEncryptionService(registry, config, AESPBEEncryptionService.class);
                }
            }

            // 注册 Base64 编码服务
            if (properties.getServices().getBase64() != null) {
                for (EncryptionProperties.Base64ServiceConfig config : properties.getServices().getBase64()) {
                    registerEncryptionService(registry, config, Base64EncodingService.class);
                }
            }
        }
        Map<String, EncryptionService> encryptionServiceMap = applicationContext.getBeansOfType(EncryptionService.class);
        this.encryptFilter.setEncryptionServiceMap(encryptionServiceMap);
    }

    /**
     * 通用的加密服务注册方法
     *
     * @param registry 注册表
     * @param config 配置对象
     * @param serviceClass 服务类型
     * @param <T> 配置类型
     * @param <S> 服务类型
     */
    private <T extends EncryptionProperties.BaseServiceConfig, S extends EncryptionService> void registerEncryptionService(BeanDefinitionRegistry registry, T config, Class<S> serviceClass) {
        try {
            // 获取名称属性
            String name =  config.getName();

            if (name == null || name.isEmpty()) {
                log.debug("Skipping {} service with empty name", serviceClass.getSimpleName());
                return;
            }

            if (applicationContext.containsBean(name)) {
                log.debug("Bean already exists: {}", name);
                return;
            }

            // 创建Bean定义
            BeanDefinitionBuilder builder = BeanDefinitionBuilder.genericBeanDefinition(serviceClass);

            // 将对象的所有属性添加到BeanDefinition中
            for (java.lang.reflect.Method method : config.getClass().getMethods()) {
                String methodName = method.getName();
                // 只处理getter方法
                if (methodName.startsWith("get") && !methodName.equals("getClass") && method.getParameterCount() == 0) {
                    String propertyName = methodName.substring(3, 4).toLowerCase() + methodName.substring(4);
                    Object value = method.invoke(config);
                    if (value != null) {
                        builder.addPropertyValue(propertyName, value);
                    }
                }
            }

            // 注册Bean
            registry.registerBeanDefinition(name, builder.getBeanDefinition());
            log.debug("Registered {} service: {}", serviceClass.getSimpleName(), name);

        } catch (Exception e) {
            log.error("Failed to register {} service", serviceClass.getSimpleName(), e);
        }
    }
}