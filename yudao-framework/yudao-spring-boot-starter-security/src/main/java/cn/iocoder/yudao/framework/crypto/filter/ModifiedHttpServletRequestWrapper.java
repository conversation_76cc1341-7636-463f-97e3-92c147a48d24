package cn.iocoder.yudao.framework.crypto.filter;
import cn.hutool.core.util.CharsetUtil;
import cn.hutool.core.util.URLUtil;

import javax.servlet.ReadListener;
import javax.servlet.ServletInputStream;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletRequestWrapper;

import java.io.*;
import java.util.*;

public class ModifiedHttpServletRequestWrapper extends HttpServletRequestWrapper {

    private final String newUri;
    private final String newMethod;
    private final byte[] newBody;
    private final Map<String, String[]> customParameterMap;
    private final String contentType;

    public ModifiedHttpServletRequestWrapper(
            HttpServletRequest request,
            String newUri,
            String newMethod,
            byte[] newBody,
            Map<String, String[]> customParameterMap, String contentType
    ) {
        super(request);
        this.newUri = URLUtil.getPath(newUri);
        this.newMethod = newMethod;
        this.newBody = newBody;
        this.customParameterMap = new HashMap<>(customParameterMap); // 防止外部修改引用
        this.contentType = contentType;
    }

    @Override
    public String getContentType() {
        return this.contentType;
    }

    @Override
    public String getHeader(String name) {
        if ("Content-Type".equalsIgnoreCase(name)) {
            return this.contentType;
        }
        return super.getHeader(name);
    }

    @Override
    public Enumeration<String> getHeaders(String name) {
        if ("Content-Type".equalsIgnoreCase(name)) {
            return Collections.enumeration(Collections.singletonList(this.contentType));
        }
        return super.getHeaders(name);
    }

    @Override
    public String getRequestURI() {
        return newUri;
    }

    @Override
    public String getMethod() {
        return newMethod;
    }

    @Override
    public ServletInputStream getInputStream() {
        ByteArrayInputStream bais = new ByteArrayInputStream(newBody);
        return new ServletInputStream() {
            @Override
            public boolean isFinished() {
                return bais.available() == 0;
            }

            @Override
            public boolean isReady() {
                return true;
            }

            @Override
            public void setReadListener(ReadListener listener) {
                // No-op for sync
            }

            @Override
            public int read() {
                return bais.read();
            }
        };
    }

    @Override
    public BufferedReader getReader() throws UnsupportedEncodingException {
        String characterEncoding = Optional.ofNullable(getCharacterEncoding()).orElse(CharsetUtil.UTF_8);
        return new BufferedReader(new InputStreamReader(getInputStream(), characterEncoding));
    }

    // ============ 参数相关重写 ============

    @Override
    public String getParameter(String name) {
        String[] values = customParameterMap.get(name);
        return (values != null && values.length > 0) ? values[0] : null;
    }

    @Override
    public String[] getParameterValues(String name) {
        return customParameterMap.get(name);
    }

    @Override
    public Enumeration<String> getParameterNames() {
        return Collections.enumeration(customParameterMap.keySet());
    }

    @Override
    public Map<String, String[]> getParameterMap() {
        return Collections.unmodifiableMap(customParameterMap);
    }

    // 可选：如果你希望 URL 中原始参数也能参与处理，可以合并参数
    public static Map<String, String[]> mergeParameters(HttpServletRequest request, Map<String, String[]> overrideParams) {
        Map<String, String[]> merged = new HashMap<>(request.getParameterMap());
        merged.putAll(overrideParams); // 以自定义参数为准
        return merged;
    }

    // 可选工具：从 JSON 字符串中提取参数（如果请求体是 JSON 而不是 x-www-form-urlencoded）
    public static Map<String, String[]> extractParametersFromJson(String json) {
        // 你可以用 Jackson/Gson 解析后转 map，这里是简单占位
        return new HashMap<>();
    }
}
