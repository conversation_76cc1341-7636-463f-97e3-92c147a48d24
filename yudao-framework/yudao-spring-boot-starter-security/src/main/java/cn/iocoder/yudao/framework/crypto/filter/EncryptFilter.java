package cn.iocoder.yudao.framework.crypto.filter;

import cn.hutool.core.map.MapUtil;
import cn.hutool.core.net.url.UrlQuery;
import cn.hutool.core.util.*;
import cn.hutool.extra.servlet.ServletUtil;
import cn.hutool.json.JSONArray;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import cn.iocoder.yudao.framework.common.pojo.CommonResult;
import cn.iocoder.yudao.framework.common.util.servlet.ServletUtils;
import cn.iocoder.yudao.framework.common.util.spring.SpringUtils;
import cn.iocoder.yudao.framework.crypto.core.EncryptionService;
import cn.iocoder.yudao.framework.security.config.SecurityProperties;
import cn.iocoder.yudao.framework.security.core.util.SecurityFrameworkUtils;
import cn.iocoder.yudao.module.system.api.application.ApplicationApi;
import cn.iocoder.yudao.module.system.api.crypto.CryptoApi;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.MediaType;
import org.springframework.web.filter.OncePerRequestFilter;
import org.springframework.web.util.UriUtils;

import javax.servlet.FilterChain;
import javax.servlet.ServletException;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.io.UnsupportedEncodingException;
import java.util.*;

import static cn.iocoder.yudao.framework.common.exception.enums.GlobalErrorCodeConstants.ERROR_CONFIGURATION;
import static cn.iocoder.yudao.framework.common.exception.enums.GlobalErrorCodeConstants.FORBIDDEN;
import static cn.iocoder.yudao.framework.web.core.util.WebFrameworkUtils.getAppId;

/**
 * 最高级别的过滤器。 直接就开始处理加密的情况内容是，因为要先保证把内容还原出来，后续的业务逻辑才能接着继续走下去。
 */
@Setter
@Slf4j
public class EncryptFilter extends OncePerRequestFilter {
    private Map<String, EncryptionService> encryptionServiceMap;
    private final CryptoApi cryptoApi;
    private final boolean force;

    public EncryptFilter(CryptoApi cryptoApi,boolean force) {
        this.cryptoApi = cryptoApi;
        this.force = force;
    }

    @Override
    protected void doFilterInternal(HttpServletRequest request, HttpServletResponse response, FilterChain filterChain) throws ServletException, IOException {
        String requestURI = request.getRequestURI();
        // 第一次判断
        String[] paths = StrUtil.splitToArray(requestURI, "/");
        if(ArrayUtil.length(paths) != 3){
            doFilter(request,response,filterChain);
            return;
        }
        // 第二次判断
        EncryptionService encryptionService = encryptionServiceMap.get(paths[1]);
        if (encryptionService == null) {
            doFilter(request,response,filterChain);
            return;
        }
        String secretKey = cryptoApi.getPrivateKey(paths[2]);


        // 解密、包装 request
        ModifiedHttpServletRequestWrapper newRequest = toModifiedHttpServletRequestWrapper(request,encryptionService,secretKey);
//        response.setHeader("originalUrl",newRequest.getRequestURI()); // 不加这个调试信息，不让他们破解。
        ModifiedResponseWrapper newResponse = new ModifiedResponseWrapper(response);
        // 手动调用 DispatcherServlet（走 Spring 所有 Filter）
//        dispatcherServlet.service(newRequest, newResponse);
        filterChain.doFilter(newRequest, newResponse);
        // 获取响应内容，进行加密后写回原始 response
        String respBody = newResponse.getResponseBodyAsString();
        // 打印 request 日志
        if (!SpringUtils.isProd()) {
            log.info("[encrypt-response][响应完整body ({}) ]", respBody);
        }
        String encrypted = encryptionService.encrypt(respBody,secretKey);
        // 把处理结果写回原始响应体（注意设置 content-type）
        String respCharacterEncoding = Optional.ofNullable(newResponse.getCharacterEncoding()).orElse(CharsetUtil.UTF_8);
        response.setContentType(formatContenType(MediaType.TEXT_PLAIN_VALUE,respCharacterEncoding));
        response.setCharacterEncoding(respCharacterEncoding);
        response.setStatus(newResponse.getStatus());
        response.getWriter().write(encrypted);
    }

    private static String formatContenType(String contenType,String encoding) {
        return String.format("%s; charset=%s", contenType, encoding );
    }

    public ModifiedHttpServletRequestWrapper toModifiedHttpServletRequestWrapper(HttpServletRequest originalRequest,EncryptionService encryptionService,String secretKey) throws UnsupportedEncodingException {
        String encoding = Optional.ofNullable(originalRequest.getCharacterEncoding()).orElse(CharsetUtil.UTF_8);
        originalRequest.setCharacterEncoding(encoding);
        // 从 text/plain 获得加密的base64
        String reqBody = ServletUtil.getBody(originalRequest);
        // 解密base64
        String json =  encryptionService.decrypt(reqBody,secretKey);
        JSONObject jsonObject = JSONUtil.parseObj(json);
        // 打印 request 日志
        if (!SpringUtils.isProd()) {
            log.info("[encrypt-request][请求完整内容 ({}) ]", jsonObject.toString());
        }
        String url = jsonObject.getStr("url");

        String method = jsonObject.getStr("method");
        String body = jsonObject.getStr("body");
        byte[] bodies = StrUtil.bytes(body, CharsetUtil.UTF_8);  // jsonObject.getBytes("body");
        JSONObject parameter = jsonObject.getJSONObject("parameter");
        // 兼容url中带?的操作内容...
        Map<CharSequence, CharSequence> queryMap = UrlQuery.of(url, CharsetUtil.CHARSET_UTF_8).getQueryMap();
        queryMap.forEach( (k,v) -> {
            if(!ObjectUtil.hasNull(k,v))
                parameter.set(k.toString(),v.toString());
        });

        // ✅ 将 parameter JSONObject 转为 Map<String, String[]>
        Map<String, String[]> customParameterMap = new HashMap<>();
        parameter.forEach((key, value) -> {
            if (value instanceof JSONArray) {
                JSONArray arr = (JSONArray) value;
                List<String> list = arr.toList(String.class);
                customParameterMap.put(key, list.toArray(new String[0]));
            } else {
                customParameterMap.put(key, new String[]{value.toString()});
            }
        });
        // 设置新的请求内容，重点设置 application/json
        return new ModifiedHttpServletRequestWrapper(originalRequest, url, method, bodies, customParameterMap , formatContenType(MediaType.APPLICATION_JSON_VALUE,encoding));
    }

    /**
     *  非桥接请求，正常放行
     * @param request
     * @param response
     * @param filterChain
     * @throws ServletException
     * @throws IOException
     */
    public void doFilter(HttpServletRequest request, HttpServletResponse response, FilterChain filterChain) throws ServletException, IOException {
        if(this.force){
            // 打印日志
            Map<String, String> queryString = ServletUtils.getParamMap(request);
            String clientIP = ServletUtil.getClientIP(request);
            String requestBody = ServletUtils.isJsonRequest(request) ? ServletUtils.getBody(request) : null;
            log.warn("[force-encrypt-error][ ip[{}] 开始请求 URL({}) 参数({})]", clientIP,request.getRequestURI(),
                    StrUtil.blankToDefault(requestBody, queryString.toString()));
            // 输出报错内容， 否则会抛出异常信息给其他过滤器拦截到。
            ServletUtils.writeJSON(response, CommonResult.error(ERROR_CONFIGURATION));
        }else{
            filterChain.doFilter(request, response); // 非桥接请求，正常放行
        }
    }
}
