package cn.iocoder.yudao.framework.crypto.filter;

import javax.servlet.ServletOutputStream;
import javax.servlet.WriteListener;
import javax.servlet.http.HttpServletResponse;
import javax.servlet.http.HttpServletResponseWrapper;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.io.OutputStreamWriter;
import java.io.PrintWriter;
import java.nio.charset.StandardCharsets;

public class ModifiedResponseWrapper extends HttpServletResponseWrapper {
    private final ByteArrayOutputStream buffer = new ByteArrayOutputStream();
    private final PrintWriter writer;
    private final ServletOutputStream outputStream;

    public ModifiedResponseWrapper(HttpServletResponse response) {
        super(response);

        this.outputStream = new ServletOutputStream() {
            @Override
            public void write(int b) {
                buffer.write(b);
            }

            @Override
            public boolean isReady() {
                return true;
            }

            @Override
            public void setWriteListener(WriteListener writeListener) {}
        };

        this.writer = new PrintWriter(new OutputStreamWriter(buffer, StandardCharsets.UTF_8));
    }

    @Override
    public ServletOutputStream getOutputStream() {
        return outputStream;
    }

    @Override
    public PrintWriter getWriter() {
        return writer;
    }

    public byte[] getResponseData() throws IOException {
        writer.flush(); // 确保 writer 的内容写入到 buffer 中
        return buffer.toByteArray();
    }

    public String getResponseBodyAsString() throws IOException {
        return new String(getResponseData(), getCharacterEncoding());
    }

    public void writeModifiedResponse(HttpServletResponse originalResponse, byte[] modifiedBody) throws IOException {
        originalResponse.setContentLength(modifiedBody.length);
        originalResponse.getOutputStream().write(modifiedBody);
    }
}
