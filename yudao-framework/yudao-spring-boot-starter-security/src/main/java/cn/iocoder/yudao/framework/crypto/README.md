# 加密解密服务

本模块提供了统一的加密解密服务接口和多种算法实现，支持AES、AES-GCM、ChaCha20-Poly1305、DES、RSA和Base64编码。

## 安全算法推荐

强烈推荐使用以下算法，它们提供了更高的安全性，并且支持UTF-8编码：

1. **AES-256-GCM**：提供认证加密，是目前最安全的对称加密算法之一
2. **ChaCha20-Poly1305**：在没有AES硬件加速的环境中性能更好

这两种算法都支持前端 JavaScript 实现，详见 `frontend-examples.md` 文件。

## 配置

在`application.yml`中可以配置加密相关参数：

```yaml
encryption:
  default-type: AES-GCM  # 默认加密类型
  default-key: yourDefaultKey  # 默认密钥
  default-iv: yourDefaultIv  # 默认IV

  # AES-GCM算法配置（推荐）
  aes-gcm:
    key-size: 256  # 密钥长度

  # ChaCha20-Poly1305算法配置（推荐）
  chacha20-poly1305:
    # 无需特殊配置

  # AES算法配置
  aes:
    transformation: AES/CBC/PKCS5Padding  # 加密模式和填充方式
    key-size: 128  # 密钥长度

  # DES算法配置
  des:
    transformation: DES/CBC/PKCS5Padding  # 加密模式和填充方式

  # RSA算法配置
  rsa:
    transformation: RSA/ECB/PKCS1Padding  # 加密模式和填充方式
    key-size: 2048  # 密钥长度
```

## 使用示例

### 基本用法

```java
@Autowired
private EncryptionFactory encryptionFactory;

// 获取默认加密服务
EncryptionService encryptionService = encryptionFactory.getDefaultEncryptionService();

// 使用默认密钥加密
String encrypted = encryptionService.encrypt("Hello, World!");

// 使用默认密钥解密
String decrypted = encryptionService.decrypt(encrypted);

// 使用指定密钥加密
String customEncrypted = encryptionService.encrypt("Hello, World!", "customKey");

// 使用指定密钥解密
String customDecrypted = encryptionService.decrypt(customEncrypted, "customKey");

// 使用指定密钥和IV加密
String ivEncrypted = encryptionService.encrypt("Hello, World!", "customKey", "customIv");

// 使用指定密钥和IV解密
String ivDecrypted = encryptionService.decrypt(ivEncrypted, "customKey", "customIv");
```

### 使用特定算法

```java
// 获取AES-GCM加密服务（推荐）
EncryptionService aesGcmService = encryptionFactory.getEncryptionService("AES-GCM");

// 获取ChaCha20-Poly1305加密服务（推荐）
EncryptionService chaChaService = encryptionFactory.getEncryptionService("ChaCha20-Poly1305");

// 获取AES加密服务
EncryptionService aesService = encryptionFactory.getEncryptionService("AES");

// 获取RSA加密服务
EncryptionService rsaService = encryptionFactory.getEncryptionService("RSA");

// 获取DES加密服务
EncryptionService desService = encryptionFactory.getEncryptionService("DES");

// 获取Base64编码服务
EncryptionService base64Service = encryptionFactory.getEncryptionService("Base64");
```

### 生成随机密钥

```java
// 生成AES-GCM随机密钥
AESGCMEncryptionService aesGcmService = (AESGCMEncryptionService) encryptionFactory.getEncryptionService("AES-GCM");
String aesKey = aesGcmService.generateRandomKey();

// 生成ChaCha20-Poly1305随机密钥
ChaCha20Poly1305EncryptionService chaChaService = (ChaCha20Poly1305EncryptionService) encryptionFactory.getEncryptionService("ChaCha20-Poly1305");
String chaChaKey = chaChaService.generateRandomKey();
```

### RSA密钥对生成

```java
// 获取RSA加密服务
RSAEncryptionService rsaService = (RSAEncryptionService) encryptionFactory.getEncryptionService("RSA");

// 生成RSA密钥对
String[] keyPair = rsaService.generateKeyPair();
String publicKey = keyPair[0];
String privateKey = keyPair[1];

// 使用公钥加密
String encrypted = rsaService.encrypt("Hello, World!", publicKey, "");

// 使用私钥解密
String decrypted = rsaService.decrypt(encrypted, privateKey, "");
```

## 异常处理

所有加密解密操作可能抛出`EncryptionException`异常，应当妥善处理：

```java
try {
    String encrypted = encryptionService.encrypt("Hello, World!");
    String decrypted = encryptionService.decrypt(encrypted);
} catch (EncryptionException e) {
    // 处理加密解密异常
    log.error("Encryption/decryption failed", e);
}
```

## 前端兼容性

所有加密算法都支持UTF-8编码，并且可以在前端 JavaScript 中实现兼容。详细的前端实现示例请参考 `frontend-examples.md` 文件。

推荐的前端库：

1. **AES-GCM**：
   - Web Crypto API（浏览器原生支持）
   - CryptoJS
   - Forge

2. **ChaCha20-Poly1305**：
   - libsodium.js
   - TweetNaCl.js
