package cn.iocoder.yudao.framework.crypto.core;

import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.RandomUtil;
import lombok.Data;
import lombok.experimental.SuperBuilder;
import org.springframework.beans.factory.annotation.Value;

import java.nio.charset.Charset;
import java.util.Base64;

/**
 * 加密服务抽象基类
 *
 * 提供通用功能和默认实现
 */
@Data
public abstract class AbstractEncryptionService implements EncryptionService {

    /**
     * IV和加密数据的分隔符
     */
    protected static final String IV_SEPARATOR = ":";

    /**
     * 默认密钥
     */
    @Value("${encryption.default-key:defaultEncryptionKey}")
    protected String defaultKey;

    /**
     * 默认IV
     */
    @Value("${encryption.default-iv:defaultEncryptionIv}")
    protected String defaultIv;

    /**
     * 全局字符编码
     */
    @Value("${encryption.charset:UTF-8}")
    protected String globalCharset;
    /**
     * bean名称
     */
    protected String name;
    /**
     * 服务特定字符编码
     */
    protected String charset;

    /**
     * 获取字符编码
     *
     * @return 字符编码
     */
    protected Charset getCharset() {
        // 优先级：服务特定编码 > 全局编码 > 默认UTF-8
        if (charset != null && !charset.isEmpty()) {
            return Charset.forName(charset);
        } else if (globalCharset != null && !globalCharset.isEmpty()) {
            return Charset.forName(globalCharset);
        } else {
            return Charset.forName("UTF-8");
        }
    }

    /**
     * 设置服务特定字符编码
     *
     * @param charset 字符编码
     */
    public void setServiceCharset(String charset) {
        this.charset = charset;
    }

    @Override
    public String encrypt(String data) throws EncryptionException {
        return encrypt(data, defaultKey);
    }

    @Override
    public String decrypt(String encryptedData) throws EncryptionException {
        return decrypt(encryptedData, defaultKey);
    }

    @Override
    public String encrypt(String data, String key) throws EncryptionException {
        String iv = generateIv();
        String encryptedData = encrypt(data, key, iv);
        // 将IV作为前缀添加到密文中，以便解密时提取
        return iv + IV_SEPARATOR + encryptedData;
    }

    @Override
    public String decrypt(String encryptedData, String key) throws EncryptionException {
        // 从密文中提取IV
        String[] parts = encryptedData.split(IV_SEPARATOR, 2);
        if (parts.length != 2) {
            throw new EncryptionException("Invalid encrypted data format");
        }
        String iv = parts[0];
        String actualEncryptedData = parts[1];
        return decrypt(actualEncryptedData, key, iv);
    }

    /**
     * 将字节数组转换为Base64编码的字符串
     *
     * @param bytes 字节数组
     * @return Base64编码的字符串
     */
    protected String bytesToBase64(byte[] bytes) {
        return Base64.getEncoder().encodeToString(bytes);
    }

    /**
     * 将Base64编码的字符串转换为字节数组
     *
     * @param base64 Base64编码的字符串
     * @return 字节数组
     */
    protected byte[] base64ToBytes(String base64) {
        return Base64.getDecoder().decode(base64);
    }

    @Override
    public SecretKeyBO generateSecretKey() {
        String randomed = RandomUtil.randomString(18);
        return new SecretKeyBO(randomed,randomed);
    }
}
