package cn.iocoder.yudao.framework.common.core;

import java.util.*;
import java.util.function.BiConsumer;
import java.util.function.BiFunction;
import java.util.function.Function;

/**
 * 计数器map
 */
public class ArrayMap<K,V> implements Map<K, List<V>> {
    private final Map<K,List<V>> integerMap;
    public ArrayMap(Map<K, List<V>> integerMap) {
        this.integerMap = integerMap;
    }

    public List<V> add(K key, V value){
        List<V> orDefault = getOrDefault(key, new ArrayList<>());
        orDefault.add(value);
        return this.put(key,orDefault);
    }

    ///////////////////
    //      委托方法
    ////////////////////


    @Override
    public int size() {
        return integerMap.size();
    }

    @Override
    public boolean isEmpty() {
        return integerMap.isEmpty();
    }

    @Override
    public boolean containsKey(Object key) {
        return integerMap.containsKey(key);
    }

    @Override
    public boolean containsValue(Object value) {
        return integerMap.containsValue(value);
    }

    @Override
    public List<V> get(Object key) {
        return integerMap.get(key);
    }

    @Override
    public List<V> put(K key, List<V> value) {
        return integerMap.put(key, value);
    }

    @Override
    public List<V> remove(Object key) {
        return integerMap.remove(key);
    }

    @Override
    public void putAll(Map<? extends K, ? extends List<V>> m) {
        integerMap.putAll(m);
    }

    @Override
    public void clear() {
        integerMap.clear();
    }

    @Override
    public Set<K> keySet() {
        return integerMap.keySet();
    }

    @Override
    public Collection<List<V>> values() {
        return integerMap.values();
    }

    @Override
    public Set<Entry<K, List<V>>> entrySet() {
        return integerMap.entrySet();
    }

    @Override
    public boolean equals(Object o) {
        return integerMap.equals(o);
    }

    @Override
    public int hashCode() {
        return integerMap.hashCode();
    }

    @Override
    public List<V> getOrDefault(Object key, List<V> defaultValue) {
        return integerMap.getOrDefault(key, defaultValue);
    }

    @Override
    public void forEach(BiConsumer<? super K, ? super List<V>> action) {
        integerMap.forEach(action);
    }

    @Override
    public void replaceAll(BiFunction<? super K, ? super List<V>, ? extends List<V>> function) {
        integerMap.replaceAll(function);
    }

    @Override
    public List<V> putIfAbsent(K key, List<V> value) {
        return integerMap.putIfAbsent(key, value);
    }

    @Override
    public boolean remove(Object key, Object value) {
        return integerMap.remove(key, value);
    }

    @Override
    public boolean replace(K key, List<V> oldValue, List<V> newValue) {
        return integerMap.replace(key, oldValue, newValue);
    }

    @Override
    public List<V> replace(K key, List<V> value) {
        return integerMap.replace(key, value);
    }

    @Override
    public List<V> computeIfAbsent(K key, Function<? super K, ? extends List<V>> mappingFunction) {
        return integerMap.computeIfAbsent(key, mappingFunction);
    }

    @Override
    public List<V> computeIfPresent(K key, BiFunction<? super K, ? super List<V>, ? extends List<V>> remappingFunction) {
        return integerMap.computeIfPresent(key, remappingFunction);
    }

    @Override
    public List<V> compute(K key, BiFunction<? super K, ? super List<V>, ? extends List<V>> remappingFunction) {
        return integerMap.compute(key, remappingFunction);
    }

    @Override
    public List<V> merge(K key, List<V> value, BiFunction<? super List<V>, ? super List<V>, ? extends List<V>> remappingFunction) {
        return integerMap.merge(key, value, remappingFunction);
    }

    @Override
    public String toString() {
        return integerMap.toString();
    }
}
