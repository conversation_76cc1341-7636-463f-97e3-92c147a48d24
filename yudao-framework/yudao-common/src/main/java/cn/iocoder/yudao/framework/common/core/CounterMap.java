package cn.iocoder.yudao.framework.common.core;

import java.util.Collection;
import java.util.Map;
import java.util.Set;
import java.util.function.BiConsumer;
import java.util.function.BiFunction;
import java.util.function.Function;

/**
 * 计数器map
 */
public class CounterMap<K> implements Map<K,Integer> {
    private final Map<K,Integer> integerMap;
    private final Integer defaultValue;
    public CounterMap(Map<K, Integer> integerMap , Integer defaultValue) {
        this.integerMap = integerMap;
        this.defaultValue = defaultValue;
    }

    public static <K> CounterMap<K> copyOf(CounterMap<K> original) {
        return new CounterMap<>(original.integerMap,original.defaultValue);
    }


    public Integer getDefaultValue() {
        return defaultValue;
    }


    /**
     * ssh -> 指定的一台服务器( 多个haproxy)    haproxy_port()
     * shell  容器命名（主服务器的ip`.`替换`_`） haproxy_port ips[]
     * 1.  docker rmi 容器命名
     * 2.  循环ips写文件( 固定目录/容器命名.config)  sh-> >>EOF
     * 3.  docker run --name 容器命名 -v 固定目录/容器命名.config:    -d : haproxy
     *
     * run ok -->    docker()
     */

    /**
     * Atomically increments by one the current value.
     *
     * @return the previous value
     */
    public final int getAndIncrement(K key) {
        return this.getAndAddInt(key,  1);
    }

    /**
     * Atomically decrements by one the current value.
     *
     * @return the previous value
     */
    public final int getAndDecrement(K key) {
        return this.getAndAddInt(key,  -1);
    }

    /**
     * Atomically adds the given value to the current value.
     *
     * @param delta the value to add
     * @return the previous value
     */
    public final int getAndAdd(K key,int delta) {
        return this.getAndAddInt(key,  delta);
    }

    /**
     * Atomically increments by one the current value.
     *
     * @return the updated value
     */
    public final int incrementAndGet(K key) {
        return this.getAndAddInt(key,  1) + 1;
    }

    /**
     * Atomically decrements by one the current value.
     *
     * @return the updated value
     */
    public final int decrementAndGet(K key) {
        return this.getAndAddInt(key,  -1) - 1;
    }

    /**
     * Atomically adds the given value to the current value.
     *
     * @param delta the value to add
     * @return the updated value
     */
    public final int addAndGet(K key,int delta) {
        return this.getAndAddInt(key,  delta) + delta;
    }

    private int getAndAddInt(K key, int delta){
        Integer orDefault = this.getOrDefault(key);
        this.put(key,orDefault + delta);
        return orDefault;
    }

    public Integer getOrDefault(Object key) {
        return integerMap.getOrDefault(key, defaultValue);
    }

    /**
     * 设置默认值。
     * @param key
     * @return
     */
    public int setDefaultValues(K key){
        Integer orDefault = this.getOrDefault(key, defaultValue);
        this.put(key,defaultValue);
        return orDefault;
    }

    ///////////////////
    //      委托方法
    ////////////////////

    @Override
    public int size() {
        return integerMap.size();
    }

    @Override
    public boolean isEmpty() {
        return integerMap.isEmpty();
    }

    @Override
    public boolean containsKey(Object key) {
        return integerMap.containsKey(key);
    }

    @Override
    public boolean containsValue(Object value) {
        return integerMap.containsValue(value);
    }

    @Override
    public Integer get(Object key) {
        return integerMap.get(key);
    }

    @Override
    public Integer put(K key, Integer value) {
        return integerMap.put(key, value);
    }

    @Override
    public Integer remove(Object key) {
        return integerMap.remove(key);
    }

    @Override
    public void putAll(Map<? extends K, ? extends Integer> m) {
        integerMap.putAll(m);
    }

    @Override
    public void clear() {
        integerMap.clear();
    }

    @Override
    public Set<K> keySet() {
        return integerMap.keySet();
    }

    @Override
    public Collection<Integer> values() {
        return integerMap.values();
    }

    @Override
    public Set<Entry<K, Integer>> entrySet() {
        return integerMap.entrySet();
    }

    @Override
    public boolean equals(Object o) {
        return integerMap.equals(o);
    }

    @Override
    public int hashCode() {
        return integerMap.hashCode();
    }

    @Override
    public Integer getOrDefault(Object key, Integer defaultValue) {
        return integerMap.getOrDefault(key, defaultValue);
    }

    @Override
    public void forEach(BiConsumer<? super K, ? super Integer> action) {
        integerMap.forEach(action);
    }

    @Override
    public void replaceAll(BiFunction<? super K, ? super Integer, ? extends Integer> function) {
        integerMap.replaceAll(function);
    }

    @Override
    public Integer putIfAbsent(K key, Integer value) {
        return integerMap.putIfAbsent(key, value);
    }

    @Override
    public boolean remove(Object key, Object value) {
        return integerMap.remove(key, value);
    }

    @Override
    public boolean replace(K key, Integer oldValue, Integer newValue) {
        return integerMap.replace(key, oldValue, newValue);
    }

    @Override
    public Integer replace(K key, Integer value) {
        return integerMap.replace(key, value);
    }

    @Override
    public Integer computeIfAbsent(K key, Function<? super K, ? extends Integer> mappingFunction) {
        return integerMap.computeIfAbsent(key, mappingFunction);
    }

    @Override
    public Integer computeIfPresent(K key, BiFunction<? super K, ? super Integer, ? extends Integer> remappingFunction) {
        return integerMap.computeIfPresent(key, remappingFunction);
    }

    @Override
    public Integer compute(K key, BiFunction<? super K, ? super Integer, ? extends Integer> remappingFunction) {
        return integerMap.compute(key, remappingFunction);
    }

    @Override
    public Integer merge(K key, Integer value, BiFunction<? super Integer, ? super Integer, ? extends Integer> remappingFunction) {
        return integerMap.merge(key, value, remappingFunction);
    }

    @Override
    public String toString() {
        return integerMap.toString();
    }
}
