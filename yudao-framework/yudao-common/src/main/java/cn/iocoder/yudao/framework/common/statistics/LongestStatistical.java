package cn.iocoder.yudao.framework.common.statistics;

import cn.hutool.core.collection.CollectionUtil;
import cn.iocoder.yudao.framework.common.core.CounterMap;
import cn.iocoder.yudao.framework.common.function.ThreeConsumer;
import cn.iocoder.yudao.framework.common.function.ThreePredicates;
import cn.iocoder.yudao.framework.common.util.collection.SetUtils;

import java.util.*;

/**
 * 最长的统计 抽象类。
 */
public abstract class LongestStatistical<K,T> {
    protected final CounterMap<K> maxMissCount = new CounterMap(new HashMap<K, Integer>(), 0);
    protected final CounterMap<K> currentMissCount = new CounterMap(new HashMap<K, Integer>(), 0);

    /**
     * 统计最近的最大值
     * @param obj
     */
    public void putIfAbsent(T obj ) {
        putOne(obj , CounterMap::putIfAbsent);
    }

    /**
     * 统计全局的最大值
     * @param obj
     */
    public void put(T obj ) {
        putOne(obj , CounterMap::put);
    }

    /**
     * 将每条数据放入进去，自动进行统计。
     * @param obj
     * @param predicates 再重置的拐点处，需要处理的方式。
     */
    public void putOne(T obj , ThreeConsumer<CounterMap<K>,K,Integer> predicates) {
        Collection<K> incrementKeys = incrementKeys(obj);
        Collection<K> resetKeys = resetKeys(obj);
        // 判断异常情况。
        if (CollectionUtil.containsAny(incrementKeys,resetKeys)) {
            throw new IllegalArgumentException("返回值数据不合法: 2个集合有重复的内容.");
        }
        // 1. 执行累加。
        Optional.ofNullable(incrementKeys).ifPresent(k -> {
            k.forEach(currentMissCount::incrementAndGet);
        });

        // 2. 执行重置 ， 并执行。 这个代表着再拐点执行。
        if(Objects.nonNull(resetKeys)){
            for (K resetKey : resetKeys) {
                int currentCount = currentMissCount.setDefaultValues(resetKey);
                // 开始正式计算。
                if (currentCount > maxMissCount.getOrDefault(resetKey)) {
//                    maxMissCount.put(resetKey, currentCount);
                    predicates.accept(maxMissCount,resetKey,currentCount);
                }
            }
        }
    }

    /**
     * 需要自增的keys
     * @param obj
     * @return
     */
    public abstract Collection<K> incrementKeys(T obj);

    /**
     * 需要重置的keys
     * @param obj
     * @return
     */
    public abstract Collection<K> resetKeys(T obj);

    /**
     * 获得最长的内容。
     * @return
     */
    public int getLongest(K key){
        // 如果没有就从当前的里面找。
        return maxMissCount.getOrDefault(key, currentMissCount.getOrDefault(key));
    }

    public  CounterMap<K> copyOf() {
        return CounterMap.copyOf(this.maxMissCount);
    }
}



