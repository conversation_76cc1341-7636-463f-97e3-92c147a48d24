package cn.iocoder.yudao.framework.common.util.collection;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.NumberUtil;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

/**
 * list计数工具类
 */
public class ListCountUtils {
    public static List<Integer> diffConsecutiveSame(List<Integer> intList, int value) {
        List<Integer> list = new ArrayList<>();
        for (Integer org : intList) {
            if (Math.abs(org) == Math.abs(value)) {
                list.add(org);
            }
        }
        return list;
    }

    /**
     * 统计连续相同类型的个数
     *
     * @param typeList 类型列表
     * @param length 需要连续相同的次数
     * @return 满足条件的连续相同类型的次数
     */
    public static List<Integer> countConsecutiveSameType(List<String> typeList, int length) {
        List<Integer> list = new ArrayList<>();
        int count = 0;
        int consecutiveCount = 1; // 从第二个开始比对

        for (int i = 1; i < typeList.size(); i++) {
            if (typeList.get(i).equals(typeList.get(i - 1))) {
                consecutiveCount++; // 如果当前元素与前一个元素相同，增加连续计数
            } else {
                // 如果连续相同次数达到要求的length，统计一次
                if (consecutiveCount >= length) {
                    list.add(consecutiveCount);
                    count++;
                }
                consecutiveCount = 1; // 否则重置为1
            }
        }
        return list;
    }


    /**
     * 统计列表中连续低于8的子序列数
     *
     * @param list 输入的整数列表
     * @return 连续低于8的子序列数
     */
    public static List<Integer> countConsecutiveLessThanLength(List<Integer> list, int length) {
        List<Integer> countList = new ArrayList<>();           // 用来统计符合条件的连续序列数
        int consecutiveCount = 0; // 当前连续低于8的元素数量

        for (int i = 0; i < list.size(); i++) {
            int num = list.get(i);

            if (num < length) {
                consecutiveCount++;  // 如果当前数字小于8，增加连续计数
            } else {
                if (consecutiveCount > 0) {
                    countList.add(consecutiveCount);// 如果之前有连续序列，统计一次
                    consecutiveCount = 0; // 重置连续计数
                }
            }
        }

        // 如果循环结束时仍然有未统计的连续序列，增加一次
        if (consecutiveCount > 0) {

            countList.add(consecutiveCount);
        }
        return countList;
    }

    /**
     * 统计列表中连续高于8的子序列数
     *
     * @param list 输入的整数列表
     * @return 连续低于8的子序列数
     */
    public static List<Integer> countConsecutiveThanLength(List<Integer> list, int length) {
        List<Integer> countList = new ArrayList<>();           // 用来统计符合条件的连续序列数
        int consecutiveCount = 0; // 当前连续低于8的元素数量

        for (int i = 0; i < list.size(); i++) {
            int num = list.get(i);

            if (num > length) {
                consecutiveCount++;  // 如果当前数字小于8，增加连续计数
            } else {
                if (consecutiveCount > 0) {
                    countList.add(consecutiveCount);// 如果之前有连续序列，统计一次
                    consecutiveCount = 0; // 重置连续计数
                }
            }
        }

        // 如果循环结束时仍然有未统计的连续序列，增加一次
        if (consecutiveCount > 0) {
            countList.add(consecutiveCount);
        }
        return countList;
    }

    public static int sum(List<Integer> list){
        return list.stream().mapToInt(x -> x).sum();
    }
    public static double average(List<Integer> list){
        return list.stream()
                .mapToInt(Integer::intValue)
                .average().orElse(Double.NaN);
    }
    public static Integer max(List<Integer> list){
//        Collections.max(list);
//        Collections.min()

        return CollectionUtil.max(list);
    }
}
