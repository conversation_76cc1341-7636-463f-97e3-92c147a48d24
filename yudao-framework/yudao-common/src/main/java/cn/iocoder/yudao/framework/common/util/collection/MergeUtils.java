package cn.iocoder.yudao.framework.common.util.collection;

import cn.hutool.core.collection.CollUtil;

import java.util.*;
import java.util.function.BiFunction;
import java.util.function.Function;

/**
 * 合并工具类，用于合并具有相同ID的对象列表
 *
 * <AUTHOR>
 */
public class MergeUtils {

    /**
     * 合并两个列表，将具有相同ID的对象合并为一个对象
     *
     * @param list1 第一个列表
     * @param list2 第二个列表
     * @param idExtractor 从对象中提取ID的函数
     * @param merger 合并两个对象的函数，当两个列表中存在相同ID的对象时调用
     * @param <T> 对象类型
     * @param <ID> ID类型
     * @return 合并后的列表
     */
    public static <T, ID> List<T> mergeLists(List<T> list1, List<T> list2,
                                            Function<T, ID> idExtractor,
                                            BiFunction<T, T, T> merger) {
        if (CollUtil.isEmpty(list1) && CollUtil.isEmpty(list2)) {
            return Collections.emptyList();
        }

        // 使用Map来存储合并后的结果，以ID为键
        Map<ID, T> resultMap = new HashMap<>();

        // 处理第一个列表
        if (CollUtil.isNotEmpty(list1)) {
            for (T item : list1) {
                if (item != null) {
                    ID id = idExtractor.apply(item);
                    if (id != null) {
                        resultMap.put(id, item);
                    }
                }
            }
        }

        // 处理第二个列表，如果有重复的ID，则合并属性
        if (CollUtil.isNotEmpty(list2)) {
            for (T item : list2) {
                if (item != null) {
                    ID id = idExtractor.apply(item);
                    if (id != null) {
                        // 检查是否已存在相同ID的对象
                        T existingItem = resultMap.get(id);
                        if (existingItem != null) {
                            // 合并属性
                            resultMap.put(id, merger.apply(existingItem, item));
                        } else {
                            // 如果不存在，直接添加到结果集合
                            resultMap.put(id, item);
                        }
                    }
                }
            }
        }

        // 返回合并后的列表
        return new ArrayList<>(resultMap.values());
    }
}
