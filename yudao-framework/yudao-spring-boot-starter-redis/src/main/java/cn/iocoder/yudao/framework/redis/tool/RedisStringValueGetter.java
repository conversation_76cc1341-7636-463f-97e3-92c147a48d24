package cn.iocoder.yudao.framework.redis.tool;

import cn.hutool.core.getter.OptNullBasicTypeFromObjectGetter;
import lombok.AllArgsConstructor;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.data.redis.core.ValueOperations;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * 用于扩展 {@link StringRedisTemplate} 获得各种数据类型的操作 <br/>
 * 扩展方法为： #getXXX <br/>
 * value数据类型为String <br/>
 * 使用方式用spring的主动注入
 */
@AllArgsConstructor
public class RedisStringValueGetter implements OptNullBasicTypeFromObjectGetter<String> {
    @Resource
    private StringRedisTemplate stringRedisTemplate;

    @Override
    public Object getObj(String key, Object defaultValue) {
        ValueOperations<String, String> opsForValue = opsForValue();
        String string = opsForValue.get(key);
        return string == null ? defaultValue : string;
    }

    public ValueOperations<String,String> opsForValue(){
        return stringRedisTemplate.opsForValue();
    }
}
