package cn.iocoder.yudao.module.job.service.positions;

import org.springframework.stereotype.Service;
import javax.annotation.Resource;
import org.springframework.validation.annotation.Validated;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import cn.iocoder.yudao.module.job.controller.admin.positions.vo.*;
import cn.iocoder.yudao.module.job.dal.dataobject.positions.PositionsDO;
import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.common.pojo.PageParam;
import cn.iocoder.yudao.framework.common.util.object.BeanUtils;

import cn.iocoder.yudao.module.job.dal.mysql.positions.PositionsMapper;

import static cn.iocoder.yudao.framework.common.exception.util.ServiceExceptionUtil.exception;
import static cn.iocoder.yudao.module.job.enums.ErrorCodeConstants.*;

/**
 * 职位信息 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class PositionsServiceImpl implements PositionsService {

    @Resource
    private PositionsMapper positionsMapper;

    @Override
    public Integer createPositions(PositionsSaveReqVO createReqVO) {
        // 插入
        PositionsDO positions = BeanUtils.toBean(createReqVO, PositionsDO.class);
        positionsMapper.insert(positions);
        // 返回
        return positions.getPositionId();
    }

    @Override
    public void updatePositions(PositionsSaveReqVO updateReqVO) {
        // 校验存在
        validatePositionsExists(updateReqVO.getPositionId());
        // 更新
        PositionsDO updateObj = BeanUtils.toBean(updateReqVO, PositionsDO.class);
        positionsMapper.updateById(updateObj);
    }

    @Override
    public void deletePositions(Integer id) {
        // 校验存在
        validatePositionsExists(id);
        // 删除
        positionsMapper.deleteById(id);
    }

    private void validatePositionsExists(Integer id) {
        if (positionsMapper.selectById(id) == null) {
            throw exception(POSITIONS_NOT_EXISTS);
        }
    }

    @Override
    public PositionsDO getPositions(Integer id) {
        return positionsMapper.selectById(id);
    }

    @Override
    public PageResult<PositionsDO> getPositionsPage(PositionsPageReqVO pageReqVO) {
        return positionsMapper.selectPage(pageReqVO);
    }

}