package cn.iocoder.yudao.module.job.dal.dataobject.question;

import lombok.*;
import java.util.*;
import java.time.LocalDateTime;
import java.time.LocalDateTime;
import com.baomidou.mybatisplus.annotation.*;
import cn.iocoder.yudao.framework.mybatis.core.dataobject.BaseDO;

/**
 * 工作题库 DO
 *
 * <AUTHOR>
 */
@TableName("job_question")
@KeySequence("job_question_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class QuestionDO extends BaseDO {

    /**
     * 题库id
     */
    @TableId
    private Long id;
    /**
     * 标题
     */
    private String title;
    /**
     * 描述
     */
    private String description;
    /**
     * 图片URL
     */
    private String img;
    /**
     * 文件URL
     */
    private String url;
    /**
     * 价格模式
     */
    private String price;
    /**
     * 类别
     */
    private String category;

}