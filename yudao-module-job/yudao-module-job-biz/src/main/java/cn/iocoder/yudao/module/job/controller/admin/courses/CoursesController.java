package cn.iocoder.yudao.module.job.controller.admin.courses;

import org.springframework.web.bind.annotation.*;
import javax.annotation.Resource;
import org.springframework.validation.annotation.Validated;
import org.springframework.security.access.prepost.PreAuthorize;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.Operation;

import javax.validation.constraints.*;
import javax.validation.*;
import javax.servlet.http.*;
import java.util.*;
import java.io.IOException;

import cn.iocoder.yudao.framework.common.pojo.PageParam;
import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.common.pojo.CommonResult;
import cn.iocoder.yudao.framework.common.util.object.BeanUtils;
import static cn.iocoder.yudao.framework.common.pojo.CommonResult.success;

import cn.iocoder.yudao.framework.excel.core.util.ExcelUtils;

import cn.iocoder.yudao.framework.apilog.core.annotation.ApiAccessLog;
import static cn.iocoder.yudao.framework.apilog.core.enums.OperateTypeEnum.*;

import cn.iocoder.yudao.module.job.controller.admin.courses.vo.*;
import cn.iocoder.yudao.module.job.dal.dataobject.courses.CoursesDO;
import cn.iocoder.yudao.module.job.dal.dataobject.courses.CoursesLessonsDO;
import cn.iocoder.yudao.module.job.service.courses.CoursesService;

@Tag(name = "管理后台 - 工作课程")
@RestController
@RequestMapping("/job/courses")
@Validated
public class CoursesController {

    @Resource
    private CoursesService coursesService;

    @PostMapping("/create")
    @Operation(summary = "创建工作课程")
    @PreAuthorize("@ss.hasPermission('job:courses:create')")
    public CommonResult<Long> createCourses(@Valid @RequestBody CoursesSaveReqVO createReqVO) {
        return success(coursesService.createCourses(createReqVO));
    }

    @PutMapping("/update")
    @Operation(summary = "更新工作课程")
    @PreAuthorize("@ss.hasPermission('job:courses:update')")
    public CommonResult<Boolean> updateCourses(@Valid @RequestBody CoursesSaveReqVO updateReqVO) {
        coursesService.updateCourses(updateReqVO);
        return success(true);
    }

    @DeleteMapping("/delete")
    @Operation(summary = "删除工作课程")
    @Parameter(name = "id", description = "编号", required = true)
    @PreAuthorize("@ss.hasPermission('job:courses:delete')")
    public CommonResult<Boolean> deleteCourses(@RequestParam("id") Long id) {
        coursesService.deleteCourses(id);
        return success(true);
    }

    @GetMapping("/get")
    @Operation(summary = "获得工作课程")
    @Parameter(name = "id", description = "编号", required = true, example = "1024")
    @PreAuthorize("@ss.hasPermission('job:courses:query')")
    public CommonResult<CoursesRespVO> getCourses(@RequestParam("id") Long id) {
        CoursesDO courses = coursesService.getCourses(id);
        return success(BeanUtils.toBean(courses, CoursesRespVO.class));
    }

    @GetMapping("/page")
    @Operation(summary = "获得工作课程分页")
    @PreAuthorize("@ss.hasPermission('job:courses:query')")
    public CommonResult<PageResult<CoursesRespVO>> getCoursesPage(@Valid CoursesPageReqVO pageReqVO) {
        PageResult<CoursesDO> pageResult = coursesService.getCoursesPage(pageReqVO);
        return success(BeanUtils.toBean(pageResult, CoursesRespVO.class));
    }

    @GetMapping("/export-excel")
    @Operation(summary = "导出工作课程 Excel")
    @PreAuthorize("@ss.hasPermission('job:courses:export')")
    @ApiAccessLog(operateType = EXPORT)
    public void exportCoursesExcel(@Valid CoursesPageReqVO pageReqVO,
              HttpServletResponse response) throws IOException {
        pageReqVO.setPageSize(PageParam.PAGE_SIZE_NONE);
        List<CoursesDO> list = coursesService.getCoursesPage(pageReqVO).getList();
        // 导出 Excel
        ExcelUtils.write(response, "工作课程.xls", "数据", CoursesRespVO.class,
                        BeanUtils.toBean(list, CoursesRespVO.class));
    }

    // ==================== 子表（工作课程课时） ====================

    @GetMapping("/courses-lessons/page")
    @Operation(summary = "获得工作课程课时分页")
    @Parameter(name = "coursesId", description = "课程id")
    @PreAuthorize("@ss.hasPermission('job:courses:query')")
    public CommonResult<PageResult<CoursesLessonsDO>> getCoursesLessonsPage(PageParam pageReqVO,
                                                                                        @RequestParam("coursesId") Long coursesId) {
        return success(coursesService.getCoursesLessonsPage(pageReqVO, coursesId));
    }

    @PostMapping("/courses-lessons/create")
    @Operation(summary = "创建工作课程课时")
    @PreAuthorize("@ss.hasPermission('job:courses:create')")
    public CommonResult<Long> createCoursesLessons(@Valid @RequestBody CoursesLessonsDO coursesLessons) {
        return success(coursesService.createCoursesLessons(coursesLessons));
    }

    @PutMapping("/courses-lessons/update")
    @Operation(summary = "更新工作课程课时")
    @PreAuthorize("@ss.hasPermission('job:courses:update')")
    public CommonResult<Boolean> updateCoursesLessons(@Valid @RequestBody CoursesLessonsDO coursesLessons) {
        coursesService.updateCoursesLessons(coursesLessons);
        return success(true);
    }

    @DeleteMapping("/courses-lessons/delete")
    @Parameter(name = "id", description = "编号", required = true)
    @Operation(summary = "删除工作课程课时")
    @PreAuthorize("@ss.hasPermission('job:courses:delete')")
    public CommonResult<Boolean> deleteCoursesLessons(@RequestParam("id") Long id) {
        coursesService.deleteCoursesLessons(id);
        return success(true);
    }

	@GetMapping("/courses-lessons/get")
	@Operation(summary = "获得工作课程课时")
	@Parameter(name = "id", description = "编号", required = true)
    @PreAuthorize("@ss.hasPermission('job:courses:query')")
	public CommonResult<CoursesLessonsDO> getCoursesLessons(@RequestParam("id") Long id) {
	    return success(coursesService.getCoursesLessons(id));
	}

}