package cn.iocoder.yudao.module.job.service.courses;

import org.springframework.stereotype.Service;
import javax.annotation.Resource;
import org.springframework.validation.annotation.Validated;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import cn.iocoder.yudao.module.job.controller.admin.courses.vo.*;
import cn.iocoder.yudao.module.job.dal.dataobject.courses.CoursesDO;
import cn.iocoder.yudao.module.job.dal.dataobject.courses.CoursesLessonsDO;
import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.common.pojo.PageParam;
import cn.iocoder.yudao.framework.common.util.object.BeanUtils;

import cn.iocoder.yudao.module.job.dal.mysql.courses.CoursesMapper;
import cn.iocoder.yudao.module.job.dal.mysql.courses.CoursesLessonsMapper;

import static cn.iocoder.yudao.framework.common.exception.util.ServiceExceptionUtil.exception;
import static cn.iocoder.yudao.module.job.enums.ErrorCodeConstants.*;

/**
 * 工作课程 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class CoursesServiceImpl implements CoursesService {

    @Resource
    private CoursesMapper coursesMapper;
    @Resource
    private CoursesLessonsMapper coursesLessonsMapper;

    @Override
    public Long createCourses(CoursesSaveReqVO createReqVO) {
        // 插入
        CoursesDO courses = BeanUtils.toBean(createReqVO, CoursesDO.class);
        coursesMapper.insert(courses);
        // 返回
        return courses.getId();
    }

    @Override
    public void updateCourses(CoursesSaveReqVO updateReqVO) {
        // 校验存在
        validateCoursesExists(updateReqVO.getId());
        // 更新
        CoursesDO updateObj = BeanUtils.toBean(updateReqVO, CoursesDO.class);
        coursesMapper.updateById(updateObj);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void deleteCourses(Long id) {
        // 校验存在
        validateCoursesExists(id);
        // 删除
        coursesMapper.deleteById(id);

        // 删除子表
        deleteCoursesLessonsByCoursesId(id);
    }

    private void validateCoursesExists(Long id) {
        if (coursesMapper.selectById(id) == null) {
            throw exception(COURSES_NOT_EXISTS);
        }
    }

    @Override
    public CoursesDO getCourses(Long id) {
        return coursesMapper.selectById(id);
    }

    @Override
    public PageResult<CoursesDO> getCoursesPage(CoursesPageReqVO pageReqVO) {
        return coursesMapper.selectPage(pageReqVO);
    }

    // ==================== 子表（工作课程课时） ====================

    @Override
    public PageResult<CoursesLessonsDO> getCoursesLessonsPage(PageParam pageReqVO, Long coursesId) {
        return coursesLessonsMapper.selectPage(pageReqVO, coursesId);
    }

    @Override
    public Long createCoursesLessons(CoursesLessonsDO coursesLessons) {
        coursesLessonsMapper.insert(coursesLessons);
        return coursesLessons.getId();
    }

    @Override
    public void updateCoursesLessons(CoursesLessonsDO coursesLessons) {
        // 校验存在
        validateCoursesLessonsExists(coursesLessons.getId());
        // 更新
        coursesLessons.setUpdater(null).setUpdateTime(null); // 解决更新情况下：updateTime 不更新
        coursesLessonsMapper.updateById(coursesLessons);
    }

    @Override
    public void deleteCoursesLessons(Long id) {
        // 校验存在
        validateCoursesLessonsExists(id);
        // 删除
        coursesLessonsMapper.deleteById(id);
    }

    @Override
    public CoursesLessonsDO getCoursesLessons(Long id) {
        return coursesLessonsMapper.selectById(id);
    }

    private void validateCoursesLessonsExists(Long id) {
        if (coursesLessonsMapper.selectById(id) == null) {
            throw exception(COURSES_LESSONS_NOT_EXISTS);
        }
    }

    private void deleteCoursesLessonsByCoursesId(Long coursesId) {
        coursesLessonsMapper.deleteByCoursesId(coursesId);
    }

}