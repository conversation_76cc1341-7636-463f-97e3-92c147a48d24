package cn.iocoder.yudao.module.job.controller.admin.positions.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;
import java.util.*;
import javax.validation.constraints.*;
import org.springframework.format.annotation.DateTimeFormat;
import java.time.LocalDateTime;

@Schema(description = "管理后台 - 职位信息新增/修改 Request VO")
@Data
public class PositionsSaveReqVO {

    @Schema(description = "职位ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "32409")
    private Integer positionId;

    @Schema(description = "公司ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "2680")
    @NotNull(message = "公司ID不能为空")
    private Integer companyId;

    @Schema(description = "公司名称", requiredMode = Schema.RequiredMode.REQUIRED, example = "张三")
    @NotEmpty(message = "公司名称不能为空")
    private String companyName;

    @Schema(description = "公告ID", example = "31626")
    private Integer annoId;

    @Schema(description = "公告名称", example = "王五")
    private String annoName;

    @Schema(description = "公告链接")
    private String annoLink;

    @Schema(description = "类型", example = "2")
    private String type;

    @Schema(description = "职位名称")
    private String title;

    @Schema(description = "工作地点")
    private String location;

    @Schema(description = "链接")
    private String link;

    @Schema(description = "专业要求")
    private String specialty;

    @Schema(description = "学历要求")
    private String education;

    @Schema(description = "学历列表 (使用 JSON 类型存储)")
    private String educationCnlist;

    @Schema(description = "工作经验要求")
    private String experience;

    @Schema(description = "薪资范围")
    private String salaryRange;

    @Schema(description = "职位描述")
    private String content;

    @Schema(description = "职位开始时间")
    private LocalDateTime jobStartTime;

    @Schema(description = "职位结束时间")
    private LocalDateTime jobEndTime;

    @Schema(description = "职位开始时间 (中文格式)")
    private String jobStartTimeCn;

    @Schema(description = "职位结束时间 (中文格式)")
    private String jobEndTimeCn;

    @Schema(description = "是否被收藏 (布尔类型)")
    private Boolean isCollect;

}