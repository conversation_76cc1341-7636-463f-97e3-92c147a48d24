package cn.iocoder.yudao.module.job.controller.admin.question.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;
import java.util.*;
import javax.validation.constraints.*;

@Schema(description = "管理后台 - 工作题库新增/修改 Request VO")
@Data
public class QuestionSaveReqVO {

    @Schema(description = "题库id", requiredMode = Schema.RequiredMode.REQUIRED, example = "9025")
    private Long id;

    @Schema(description = "标题")
    private String title;

    @Schema(description = "描述", requiredMode = Schema.RequiredMode.REQUIRED, example = "你说的对")
    @NotEmpty(message = "描述不能为空")
    private String description;

    @Schema(description = "图片URL", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotEmpty(message = "图片URL不能为空")
    private String img;

    @Schema(description = "文件URL", requiredMode = Schema.RequiredMode.REQUIRED, example = "https://www.iocoder.cn")
    @NotEmpty(message = "文件URL不能为空")
    private String url;

    @Schema(description = "价格模式", example = "16008")
    private String price;

    @Schema(description = "类别")
    private String category;

}