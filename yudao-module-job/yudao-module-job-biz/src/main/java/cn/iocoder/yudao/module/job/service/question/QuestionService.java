package cn.iocoder.yudao.module.job.service.question;

import java.util.*;
import javax.validation.*;
import cn.iocoder.yudao.module.job.controller.admin.question.vo.*;
import cn.iocoder.yudao.module.job.dal.dataobject.question.QuestionDO;
import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.common.pojo.PageParam;

/**
 * 工作题库 Service 接口
 *
 * <AUTHOR>
 */
public interface QuestionService {

    /**
     * 创建工作题库
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    Long createQuestion(@Valid QuestionSaveReqVO createReqVO);

    /**
     * 更新工作题库
     *
     * @param updateReqVO 更新信息
     */
    void updateQuestion(@Valid QuestionSaveReqVO updateReqVO);

    /**
     * 删除工作题库
     *
     * @param id 编号
     */
    void deleteQuestion(Long id);

    /**
     * 获得工作题库
     *
     * @param id 编号
     * @return 工作题库
     */
    QuestionDO getQuestion(Long id);

    /**
     * 获得工作题库分页
     *
     * @param pageReqVO 分页查询
     * @return 工作题库分页
     */
    PageResult<QuestionDO> getQuestionPage(QuestionPageReqVO pageReqVO);

}