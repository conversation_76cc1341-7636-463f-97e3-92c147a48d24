package cn.iocoder.yudao.module.job.service.courses;

import java.util.*;
import javax.validation.*;
import cn.iocoder.yudao.module.job.controller.admin.courses.vo.*;
import cn.iocoder.yudao.module.job.dal.dataobject.courses.CoursesDO;
import cn.iocoder.yudao.module.job.dal.dataobject.courses.CoursesLessonsDO;
import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.common.pojo.PageParam;

/**
 * 工作课程 Service 接口
 *
 * <AUTHOR>
 */
public interface CoursesService {

    /**
     * 创建工作课程
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    Long createCourses(@Valid CoursesSaveReqVO createReqVO);

    /**
     * 更新工作课程
     *
     * @param updateReqVO 更新信息
     */
    void updateCourses(@Valid CoursesSaveReqVO updateReqVO);

    /**
     * 删除工作课程
     *
     * @param id 编号
     */
    void deleteCourses(Long id);

    /**
     * 获得工作课程
     *
     * @param id 编号
     * @return 工作课程
     */
    CoursesDO getCourses(Long id);

    /**
     * 获得工作课程分页
     *
     * @param pageReqVO 分页查询
     * @return 工作课程分页
     */
    PageResult<CoursesDO> getCoursesPage(CoursesPageReqVO pageReqVO);

    // ==================== 子表（工作课程课时） ====================

    /**
     * 获得工作课程课时分页
     *
     * @param pageReqVO 分页查询
     * @param coursesId 课程id
     * @return 工作课程课时分页
     */
    PageResult<CoursesLessonsDO> getCoursesLessonsPage(PageParam pageReqVO, Long coursesId);

    /**
     * 创建工作课程课时
     *
     * @param coursesLessons 创建信息
     * @return 编号
     */
    Long createCoursesLessons(@Valid CoursesLessonsDO coursesLessons);

    /**
     * 更新工作课程课时
     *
     * @param coursesLessons 更新信息
     */
    void updateCoursesLessons(@Valid CoursesLessonsDO coursesLessons);

    /**
     * 删除工作课程课时
     *
     * @param id 编号
     */
    void deleteCoursesLessons(Long id);

	/**
	 * 获得工作课程课时
	 *
	 * @param id 编号
     * @return 工作课程课时
	 */
    CoursesLessonsDO getCoursesLessons(Long id);

}