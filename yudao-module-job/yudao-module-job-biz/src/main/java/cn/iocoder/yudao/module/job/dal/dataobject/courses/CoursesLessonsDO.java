package cn.iocoder.yudao.module.job.dal.dataobject.courses;

import lombok.*;
import java.util.*;
import java.time.LocalDateTime;
import java.time.LocalDateTime;
import com.baomidou.mybatisplus.annotation.*;
import cn.iocoder.yudao.framework.mybatis.core.dataobject.BaseDO;

/**
 * 工作课程课时 DO
 *
 * <AUTHOR>
 */
@TableName("job_courses_lessons")
@KeySequence("job_courses_lessons_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class CoursesLessonsDO extends BaseDO {

    /**
     * 课时id
     */
    @TableId
    private Long id;
    /**
     * 课程id
     */
    private Long coursesId;
    /**
     * 标题
     */
    private String title;
    /**
     * 时长
     */
    private String duration;
    /**
     * 锁定
     */
    private Boolean locked;
    /**
     * 视频URL
     */
    private String videoUrl;

}