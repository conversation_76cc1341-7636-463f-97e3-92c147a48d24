package cn.iocoder.yudao.module.job.controller.app.question;

import cn.iocoder.yudao.module.job.controller.admin.question.vo.QuestionPageReqVO;
import org.springframework.web.bind.annotation.*;
import javax.annotation.Resource;
import org.springframework.validation.annotation.Validated;

import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.Operation;

import javax.validation.*;
import javax.servlet.http.*;
import java.util.*;
import java.io.IOException;

import cn.iocoder.yudao.framework.common.pojo.PageParam;
import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.common.pojo.CommonResult;
import cn.iocoder.yudao.framework.common.util.object.BeanUtils;
import static cn.iocoder.yudao.framework.common.pojo.CommonResult.success;

import cn.iocoder.yudao.framework.excel.core.util.ExcelUtils;

import cn.iocoder.yudao.framework.apilog.core.annotation.ApiAccessLog;
import static cn.iocoder.yudao.framework.apilog.core.enums.OperateTypeEnum.*;

import cn.iocoder.yudao.module.job.controller.app.question.vo.*;
import cn.iocoder.yudao.module.job.dal.dataobject.question.QuestionDO;
import cn.iocoder.yudao.module.job.service.question.QuestionService;

@Tag(name = "用户 APP - 工作题库")
@RestController
@RequestMapping("/job/question")
@Validated
public class AppQuestionController {

    @Resource
    private QuestionService questionService;

    @GetMapping("/get")
    @Operation(summary = "获得工作题库")
    @Parameter(name = "id", description = "编号", required = true, example = "1024")
    public CommonResult<AppQuestionRespVO> getQuestion(@RequestParam("id") Long id) {
        QuestionDO question = questionService.getQuestion(id);
        return success(BeanUtils.toBean(question, AppQuestionRespVO.class));
    }

    @PostMapping("/page")
    @Operation(summary = "获得工作题库分页")
    public CommonResult<PageResult<AppQuestionRespVO>> getQuestionPage(@Valid QuestionPageReqVO pageReqVO) {
        PageResult<QuestionDO> pageResult = questionService.getQuestionPage(pageReqVO);
        return success(BeanUtils.toBean(pageResult, AppQuestionRespVO.class));
    }


}