package cn.iocoder.yudao.module.job.controller.admin.positions.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;
import java.util.*;
import org.springframework.format.annotation.DateTimeFormat;
import java.time.LocalDateTime;
import com.alibaba.excel.annotation.*;

@Schema(description = "管理后台 - 职位信息 Response VO")
@Data
@ExcelIgnoreUnannotated
public class PositionsRespVO {

    @Schema(description = "职位ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "32409")
    @ExcelProperty("职位ID")
    private Integer positionId;

    @Schema(description = "公司ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "2680")
    @ExcelProperty("公司ID")
    private Integer companyId;

    @Schema(description = "公司名称", requiredMode = Schema.RequiredMode.REQUIRED, example = "张三")
    @ExcelProperty("公司名称")
    private String companyName;

    @Schema(description = "公告ID", example = "31626")
    @ExcelProperty("公告ID")
    private Integer annoId;

    @Schema(description = "公告名称", example = "王五")
    @ExcelProperty("公告名称")
    private String annoName;

    @Schema(description = "公告链接")
    @ExcelProperty("公告链接")
    private String annoLink;

    @Schema(description = "类型", example = "2")
    @ExcelProperty("类型")
    private String type;

    @Schema(description = "职位名称")
    @ExcelProperty("职位名称")
    private String title;

    @Schema(description = "工作地点")
    @ExcelProperty("工作地点")
    private String location;

    @Schema(description = "链接")
    @ExcelProperty("链接")
    private String link;

    @Schema(description = "专业要求")
    @ExcelProperty("专业要求")
    private String specialty;

    @Schema(description = "学历要求")
    @ExcelProperty("学历要求")
    private String education;

    @Schema(description = "学历列表 (使用 JSON 类型存储)")
    @ExcelProperty("学历列表 (使用 JSON 类型存储)")
    private String educationCnlist;

    @Schema(description = "工作经验要求")
    @ExcelProperty("工作经验要求")
    private String experience;

    @Schema(description = "薪资范围")
    @ExcelProperty("薪资范围")
    private String salaryRange;

    @Schema(description = "职位描述")
    @ExcelProperty("职位描述")
    private String content;

    @Schema(description = "职位开始时间")
    @ExcelProperty("职位开始时间")
    private LocalDateTime jobStartTime;

    @Schema(description = "职位结束时间")
    @ExcelProperty("职位结束时间")
    private LocalDateTime jobEndTime;

    @Schema(description = "职位开始时间 (中文格式)")
    @ExcelProperty("职位开始时间 (中文格式)")
    private String jobStartTimeCn;

    @Schema(description = "职位结束时间 (中文格式)")
    @ExcelProperty("职位结束时间 (中文格式)")
    private String jobEndTimeCn;

    @Schema(description = "是否被收藏 (布尔类型)")
    @ExcelProperty("是否被收藏 (布尔类型)")
    private Boolean isCollect;

    @Schema(description = "创建时间", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("创建时间")
    private LocalDateTime createTime;

}