package cn.iocoder.yudao.module.job.service.positions;

import java.util.*;
import javax.validation.*;
import cn.iocoder.yudao.module.job.controller.admin.positions.vo.*;
import cn.iocoder.yudao.module.job.dal.dataobject.positions.PositionsDO;
import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.common.pojo.PageParam;

/**
 * 职位信息 Service 接口
 *
 * <AUTHOR>
 */
public interface PositionsService {

    /**
     * 创建职位信息
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    Integer createPositions(@Valid PositionsSaveReqVO createReqVO);

    /**
     * 更新职位信息
     *
     * @param updateReqVO 更新信息
     */
    void updatePositions(@Valid PositionsSaveReqVO updateReqVO);

    /**
     * 删除职位信息
     *
     * @param id 编号
     */
    void deletePositions(Integer id);

    /**
     * 获得职位信息
     *
     * @param id 编号
     * @return 职位信息
     */
    PositionsDO getPositions(Integer id);

    /**
     * 获得职位信息分页
     *
     * @param pageReqVO 分页查询
     * @return 职位信息分页
     */
    PageResult<PositionsDO> getPositionsPage(PositionsPageReqVO pageReqVO);

}