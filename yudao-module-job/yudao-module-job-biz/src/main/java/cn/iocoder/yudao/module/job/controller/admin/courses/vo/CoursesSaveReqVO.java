package cn.iocoder.yudao.module.job.controller.admin.courses.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;
import java.util.*;
import javax.validation.constraints.*;
import cn.iocoder.yudao.module.job.dal.dataobject.courses.CoursesLessonsDO;

@Schema(description = "管理后台 - 工作课程新增/修改 Request VO")
@Data
public class CoursesSaveReqVO {
    @Schema(description = "课程ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "32409")
    private Long id;

    @Schema(description = "标题")
    private String title;

    @Schema(description = "描述", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotEmpty(message = "描述不能为空")
    private String description;

    @Schema(description = "图片URL", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotEmpty(message = "图片URL不能为空")
    private String img;

    @Schema(description = "价格模式", example = "783")
    private String price;

    @Schema(description = "课时")
    private String lessons;

}