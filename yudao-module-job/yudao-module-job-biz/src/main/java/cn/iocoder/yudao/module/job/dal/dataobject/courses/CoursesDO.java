package cn.iocoder.yudao.module.job.dal.dataobject.courses;

import lombok.*;
import java.util.*;
import java.time.LocalDateTime;
import java.time.LocalDateTime;
import com.baomidou.mybatisplus.annotation.*;
import cn.iocoder.yudao.framework.mybatis.core.dataobject.BaseDO;

/**
 * 工作课程 DO
 *
 * <AUTHOR>
 */
@TableName("job_courses")
@KeySequence("job_courses_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class CoursesDO extends BaseDO {

    /**
     * 课程id
     */
    @TableId
    private Long id;
    /**
     * 标题
     */
    private String title;
    /**
     * 描述
     */
    private String description;
    /**
     * 图片URL
     */
    private String img;
    /**
     * 价格模式
     */
    private String price;
    /**
     * 课时
     */
    private String lessons;

}