package cn.iocoder.yudao.module.job.controller.app.courses.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;
import java.util.*;
import org.springframework.format.annotation.DateTimeFormat;
import java.time.LocalDateTime;
import com.alibaba.excel.annotation.*;

@Schema(description = "用户 APP - 工作课程 Response VO")
@Data
@ExcelIgnoreUnannotated
public class AppCoursesRespVO {

    @Schema(description = "课程id", requiredMode = Schema.RequiredMode.REQUIRED, example = "11646")
    @ExcelProperty("课程id")
    private Long id;

    @Schema(description = "标题")
    @ExcelProperty("标题")
    private String title;

    @Schema(description = "描述", requiredMode = Schema.RequiredMode.REQUIRED, example = "随便")
    @ExcelProperty("描述")
    private String description;

    @Schema(description = "图片URL", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("图片URL")
    private String img;

    @Schema(description = "价格模式", example = "19343")
    @ExcelProperty("价格模式")
    private String price;

    @Schema(description = "课时")
    @ExcelProperty("课时")
    private String lessons;

    @Schema(description = "创建时间", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("创建时间")
    private LocalDateTime createTime;

}