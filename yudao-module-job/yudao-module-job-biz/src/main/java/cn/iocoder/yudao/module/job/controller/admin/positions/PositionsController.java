package cn.iocoder.yudao.module.job.controller.admin.positions;

import org.springframework.web.bind.annotation.*;
import javax.annotation.Resource;
import org.springframework.validation.annotation.Validated;
import org.springframework.security.access.prepost.PreAuthorize;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.Operation;

import javax.validation.constraints.*;
import javax.validation.*;
import javax.servlet.http.*;
import java.util.*;
import java.io.IOException;

import cn.iocoder.yudao.framework.common.pojo.PageParam;
import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.common.pojo.CommonResult;
import cn.iocoder.yudao.framework.common.util.object.BeanUtils;
import static cn.iocoder.yudao.framework.common.pojo.CommonResult.success;

import cn.iocoder.yudao.framework.excel.core.util.ExcelUtils;

import cn.iocoder.yudao.framework.apilog.core.annotation.ApiAccessLog;
import static cn.iocoder.yudao.framework.apilog.core.enums.OperateTypeEnum.*;

import cn.iocoder.yudao.module.job.controller.admin.positions.vo.*;
import cn.iocoder.yudao.module.job.dal.dataobject.positions.PositionsDO;
import cn.iocoder.yudao.module.job.service.positions.PositionsService;

@Tag(name = "管理后台 - 职位信息")
@RestController
@RequestMapping("/job/positions")
@Validated
public class PositionsController {

    @Resource
    private PositionsService positionsService;

    @PostMapping("/create")
    @Operation(summary = "创建职位信息")
    @PreAuthorize("@ss.hasPermission('job:positions:create')")
    public CommonResult<Integer> createPositions(@Valid @RequestBody PositionsSaveReqVO createReqVO) {
        return success(positionsService.createPositions(createReqVO));
    }

    @PutMapping("/update")
    @Operation(summary = "更新职位信息")
    @PreAuthorize("@ss.hasPermission('job:positions:update')")
    public CommonResult<Boolean> updatePositions(@Valid @RequestBody PositionsSaveReqVO updateReqVO) {
        positionsService.updatePositions(updateReqVO);
        return success(true);
    }

    @DeleteMapping("/delete")
    @Operation(summary = "删除职位信息")
    @Parameter(name = "id", description = "编号", required = true)
    @PreAuthorize("@ss.hasPermission('job:positions:delete')")
    public CommonResult<Boolean> deletePositions(@RequestParam("id") Integer id) {
        positionsService.deletePositions(id);
        return success(true);
    }

    @GetMapping("/get")
    @Operation(summary = "获得职位信息")
    @Parameter(name = "id", description = "编号", required = true, example = "1024")
    @PreAuthorize("@ss.hasPermission('job:positions:query')")
    public CommonResult<PositionsRespVO> getPositions(@RequestParam("id") Integer id) {
        PositionsDO positions = positionsService.getPositions(id);
        return success(BeanUtils.toBean(positions, PositionsRespVO.class));
    }

    @GetMapping("/page")
    @Operation(summary = "获得职位信息分页")
    @PreAuthorize("@ss.hasPermission('job:positions:query')")
    public CommonResult<PageResult<PositionsRespVO>> getPositionsPage(@Valid PositionsPageReqVO pageReqVO) {
        PageResult<PositionsDO> pageResult = positionsService.getPositionsPage(pageReqVO);
        return success(BeanUtils.toBean(pageResult, PositionsRespVO.class));
    }

    @GetMapping("/export-excel")
    @Operation(summary = "导出职位信息 Excel")
    @PreAuthorize("@ss.hasPermission('job:positions:export')")
    @ApiAccessLog(operateType = EXPORT)
    public void exportPositionsExcel(@Valid PositionsPageReqVO pageReqVO,
              HttpServletResponse response) throws IOException {
        pageReqVO.setPageSize(PageParam.PAGE_SIZE_NONE);
        List<PositionsDO> list = positionsService.getPositionsPage(pageReqVO).getList();
        // 导出 Excel
        ExcelUtils.write(response, "职位信息.xls", "数据", PositionsRespVO.class,
                        BeanUtils.toBean(list, PositionsRespVO.class));
    }

}