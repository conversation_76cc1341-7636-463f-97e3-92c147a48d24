package cn.iocoder.yudao.module.job.dal.dataobject.positions;

import lombok.*;
import java.util.*;
import java.time.LocalDateTime;
import java.time.LocalDateTime;
import java.time.LocalDateTime;
import java.time.LocalDateTime;
import com.baomidou.mybatisplus.annotation.*;
import cn.iocoder.yudao.framework.mybatis.core.dataobject.BaseDO;

/**
 * 职位信息 DO
 *
 * <AUTHOR>
 */
@TableName("job_positions")
@KeySequence("job_positions_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class PositionsDO extends BaseDO {

    /**
     * 职位ID
     */
    @TableId
    private Integer positionId;
    /**
     * 公司ID
     */
    private Integer companyId;
    /**
     * 公司名称
     */
    private String companyName;
    /**
     * 公告ID
     */
    private Integer annoId;
    /**
     * 公告名称
     */
    private String annoName;
    /**
     * 公告链接
     */
    private String annoLink;
    /**
     * 类型
     */
    private String type;
    /**
     * 职位名称
     */
    private String title;
    /**
     * 工作地点
     */
    private String location;
    /**
     * 链接
     */
    private String link;
    /**
     * 专业要求
     */
    private String specialty;
    /**
     * 学历要求
     */
    private String education;
    /**
     * 学历列表 (使用 JSON 类型存储)
     */
    private String educationCnlist;
    /**
     * 工作经验要求
     */
    private String experience;
    /**
     * 薪资范围
     */
    private String salaryRange;
    /**
     * 职位描述
     */
    private String content;
    /**
     * 职位开始时间
     */
    private LocalDateTime jobStartTime;
    /**
     * 职位结束时间
     */
    private LocalDateTime jobEndTime;
    /**
     * 职位开始时间 (中文格式)
     */
    private String jobStartTimeCn;
    /**
     * 职位结束时间 (中文格式)
     */
    private String jobEndTimeCn;
    /**
     * 是否被收藏 (布尔类型)
     */
    private Boolean isCollect;

}