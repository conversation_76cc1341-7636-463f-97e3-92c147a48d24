package cn.iocoder.yudao.module.job.controller.admin.question.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;
import java.util.*;
import org.springframework.format.annotation.DateTimeFormat;
import java.time.LocalDateTime;
import com.alibaba.excel.annotation.*;

@Schema(description = "管理后台 - 工作题库 Response VO")
@Data
@ExcelIgnoreUnannotated
public class QuestionRespVO {

    @Schema(description = "题库id", requiredMode = Schema.RequiredMode.REQUIRED, example = "9025")
    @ExcelProperty("题库id")
    private Long id;

    @Schema(description = "标题")
    @ExcelProperty("标题")
    private String title;

    @Schema(description = "描述", requiredMode = Schema.RequiredMode.REQUIRED, example = "你说的对")
    @ExcelProperty("描述")
    private String description;

    @Schema(description = "图片URL", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("图片URL")
    private String img;

    @Schema(description = "文件URL", requiredMode = Schema.RequiredMode.REQUIRED, example = "https://www.iocoder.cn")
    @ExcelProperty("文件URL")
    private String url;

    @Schema(description = "价格模式", example = "16008")
    @ExcelProperty("价格模式")
    private String price;

    @Schema(description = "类别")
    @ExcelProperty("类别")
    private String category;

    @Schema(description = "创建时间", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("创建时间")
    private LocalDateTime createTime;

}