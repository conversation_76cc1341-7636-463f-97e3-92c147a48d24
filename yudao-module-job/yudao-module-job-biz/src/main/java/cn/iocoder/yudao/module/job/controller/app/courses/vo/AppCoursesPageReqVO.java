package cn.iocoder.yudao.module.job.controller.app.courses.vo;

import lombok.*;
import java.util.*;
import io.swagger.v3.oas.annotations.media.Schema;
import cn.iocoder.yudao.framework.common.pojo.PageParam;
import org.springframework.format.annotation.DateTimeFormat;
import java.time.LocalDateTime;

import static cn.iocoder.yudao.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;

@Schema(description = "用户 APP - 工作课程分页 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class AppCoursesPageReqVO extends PageParam {

    @Schema(description = "标题")
    private String title;

    @Schema(description = "描述", example = "随便")
    private String description;

    @Schema(description = "图片URL")
    private String img;

    @Schema(description = "价格模式", example = "19343")
    private String price;

    @Schema(description = "课时")
    private String lessons;

    @Schema(description = "创建时间")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime[] createTime;

}