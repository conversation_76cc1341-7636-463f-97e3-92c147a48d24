package cn.iocoder.yudao.module.job.controller.app.courses;

import cn.iocoder.yudao.module.job.controller.admin.courses.vo.CoursesPageReqVO;
import org.springframework.web.bind.annotation.*;
import javax.annotation.Resource;
import org.springframework.validation.annotation.Validated;

import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.Operation;

import javax.validation.constraints.*;
import javax.validation.*;
import javax.servlet.http.*;
import java.util.*;
import java.io.IOException;

import cn.iocoder.yudao.framework.common.pojo.PageParam;
import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.common.pojo.CommonResult;
import cn.iocoder.yudao.framework.common.util.object.BeanUtils;
import static cn.iocoder.yudao.framework.common.pojo.CommonResult.success;

import cn.iocoder.yudao.framework.excel.core.util.ExcelUtils;

import cn.iocoder.yudao.framework.apilog.core.annotation.ApiAccessLog;
import static cn.iocoder.yudao.framework.apilog.core.enums.OperateTypeEnum.*;

import cn.iocoder.yudao.module.job.controller.app.courses.vo.*;
import cn.iocoder.yudao.module.job.dal.dataobject.courses.CoursesDO;
import cn.iocoder.yudao.module.job.dal.dataobject.courses.CoursesLessonsDO;
import cn.iocoder.yudao.module.job.service.courses.CoursesService;

@Tag(name = "用户 APP - 工作课程")
@RestController
@RequestMapping("/job/courses")
@Validated
public class AppCoursesController {

    @Resource
    private CoursesService coursesService;

    @GetMapping("/get")
    @Operation(summary = "获得工作课程")
    @Parameter(name = "id", description = "编号", required = true, example = "1024")
    public CommonResult<AppCoursesRespVO> getCourses(@RequestParam("id") Long id) {
        CoursesDO courses = coursesService.getCourses(id);
        return success(BeanUtils.toBean(courses, AppCoursesRespVO.class));
    }

    @PostMapping("/page")
    @Operation(summary = "获得工作课程分页")
    public CommonResult<PageResult<AppCoursesRespVO>> getCoursesPage(@Valid CoursesPageReqVO pageReqVO) {
        PageResult<CoursesDO> pageResult = coursesService.getCoursesPage(pageReqVO);
        return success(BeanUtils.toBean(pageResult, AppCoursesRespVO.class));
    }


    // ==================== 子表（工作课程课时） ====================

    @PostMapping("/courses-lessons/page")
    @Operation(summary = "获得工作课程课时分页")
    @Parameter(name = "coursesId", description = "课程id")
    public CommonResult<PageResult<CoursesLessonsDO>> getCoursesLessonsPage(PageParam pageReqVO,
                                                                                        @RequestParam("coursesId") Long coursesId) {
        return success(coursesService.getCoursesLessonsPage(pageReqVO, coursesId));
    }

	@GetMapping("/courses-lessons/get")
	@Operation(summary = "获得工作课程课时")
	@Parameter(name = "id", description = "编号", required = true)
	public CommonResult<CoursesLessonsDO> getCoursesLessons(@RequestParam("id") Long id) {
	    return success(coursesService.getCoursesLessons(id));
	}

}