package cn.iocoder.yudao.module.job.dal.mysql.courses;

import java.util.*;

import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.common.pojo.PageParam;
import cn.iocoder.yudao.framework.mybatis.core.query.LambdaQueryWrapperX;
import cn.iocoder.yudao.framework.mybatis.core.mapper.BaseMapperX;
import cn.iocoder.yudao.module.job.dal.dataobject.courses.CoursesLessonsDO;
import org.apache.ibatis.annotations.Mapper;

/**
 * 工作课程课时 Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface CoursesLessonsMapper extends BaseMapperX<CoursesLessonsDO> {

    default PageResult<CoursesLessonsDO> selectPage(PageParam reqVO, Long coursesId) {
        return selectPage(reqVO, new LambdaQueryWrapperX<CoursesLessonsDO>()
            .eq(CoursesLessonsDO::getCoursesId, coursesId)
            .orderByDesc(CoursesLessonsDO::getId));
    }

    default int deleteByCoursesId(Long coursesId) {
        return delete(CoursesLessonsDO::getCoursesId, coursesId);
    }

}