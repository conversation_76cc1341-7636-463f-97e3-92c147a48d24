package cn.iocoder.yudao.module.job.dal.mysql.question;

import java.util.*;

import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.mybatis.core.query.LambdaQueryWrapperX;
import cn.iocoder.yudao.framework.mybatis.core.mapper.BaseMapperX;
import cn.iocoder.yudao.module.job.dal.dataobject.question.QuestionDO;
import org.apache.ibatis.annotations.Mapper;
import cn.iocoder.yudao.module.job.controller.admin.question.vo.*;

/**
 * 工作题库 Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface QuestionMapper extends BaseMapperX<QuestionDO> {

    default PageResult<QuestionDO> selectPage(QuestionPageReqVO reqVO) {
        return selectPage(reqVO, new LambdaQueryWrapperX<QuestionDO>()
                .likeIfPresent(QuestionDO::getTitle, reqVO.getTitle())
                .likeIfPresent(QuestionDO::getDescription, reqVO.getDescription())
                .eqIfPresent(QuestionDO::getPrice, reqVO.getPrice())
                .eqIfPresent(QuestionDO::getCategory, reqVO.getCategory())
                .betweenIfPresent(QuestionDO::getCreateTime, reqVO.getCreateTime())
                .orderByDesc(QuestionDO::getId));
    }

}