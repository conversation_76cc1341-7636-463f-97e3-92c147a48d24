package cn.iocoder.yudao.module.job.dal.mysql.positions;

import java.util.*;

import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.mybatis.core.query.LambdaQueryWrapperX;
import cn.iocoder.yudao.framework.mybatis.core.mapper.BaseMapperX;
import cn.iocoder.yudao.module.job.dal.dataobject.positions.PositionsDO;
import org.apache.ibatis.annotations.Mapper;
import cn.iocoder.yudao.module.job.controller.admin.positions.vo.*;

/**
 * 职位信息 Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface PositionsMapper extends BaseMapperX<PositionsDO> {

    default PageResult<PositionsDO> selectPage(PositionsPageReqVO reqVO) {
        return selectPage(reqVO, new LambdaQueryWrapperX<PositionsDO>()
                .eqIfPresent(PositionsDO::getCompanyId, reqVO.getCompanyId())
                .likeIfPresent(PositionsDO::getCompanyName, reqVO.getCompanyName())
                .eqIfPresent(PositionsDO::getAnnoId, reqVO.getAnnoId())
                .likeIfPresent(PositionsDO::getAnnoName, reqVO.getAnnoName())
                .eqIfPresent(PositionsDO::getAnnoLink, reqVO.getAnnoLink())
                .eqIfPresent(PositionsDO::getType, reqVO.getType())
                .eqIfPresent(PositionsDO::getTitle, reqVO.getTitle())
                .eqIfPresent(PositionsDO::getLocation, reqVO.getLocation())
                .eqIfPresent(PositionsDO::getLink, reqVO.getLink())
                .likeIfPresent(PositionsDO::getSpecialty, reqVO.getSpecialty())
                .likeIfPresent(PositionsDO::getEducation, reqVO.getEducation())
                .likeIfPresent(PositionsDO::getEducationCnlist, reqVO.getEducationCnlist())
                .likeIfPresent(PositionsDO::getExperience, reqVO.getExperience())
                .betweenIfPresent(PositionsDO::getSalaryRange, reqVO.getSalaryRange())
                .likeIfPresent(PositionsDO::getContent, reqVO.getContent())
                .betweenIfPresent(PositionsDO::getJobStartTime, reqVO.getJobStartTime())
                .betweenIfPresent(PositionsDO::getJobEndTime, reqVO.getJobEndTime())
                .eqIfPresent(PositionsDO::getJobStartTimeCn, reqVO.getJobStartTimeCn())
                .eqIfPresent(PositionsDO::getJobEndTimeCn, reqVO.getJobEndTimeCn())
                .eqIfPresent(PositionsDO::getIsCollect, reqVO.getIsCollect())
                .betweenIfPresent(PositionsDO::getCreateTime, reqVO.getCreateTime())
                .orderByDesc(PositionsDO::getPositionId));
    }

}