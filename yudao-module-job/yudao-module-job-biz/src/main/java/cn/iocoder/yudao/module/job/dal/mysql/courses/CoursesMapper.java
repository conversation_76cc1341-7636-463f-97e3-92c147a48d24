package cn.iocoder.yudao.module.job.dal.mysql.courses;

import java.util.*;

import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.mybatis.core.query.LambdaQueryWrapperX;
import cn.iocoder.yudao.framework.mybatis.core.mapper.BaseMapperX;
import cn.iocoder.yudao.module.job.dal.dataobject.courses.CoursesDO;
import org.apache.ibatis.annotations.Mapper;
import cn.iocoder.yudao.module.job.controller.admin.courses.vo.*;

/**
 * 工作课程 Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface CoursesMapper extends BaseMapperX<CoursesDO> {

    default PageResult<CoursesDO> selectPage(CoursesPageReqVO reqVO) {
        return selectPage(reqVO, new LambdaQueryWrapperX<CoursesDO>()
                .likeIfPresent(CoursesDO::getTitle, reqVO.getTitle())
                .likeIfPresent(CoursesDO::getDescription, reqVO.getDescription())
                .eqIfPresent(CoursesDO::getPrice, reqVO.getPrice())
                .betweenIfPresent(CoursesDO::getCreateTime, reqVO.getCreateTime())
                .orderByDesc(CoursesDO::getId));
    }

}