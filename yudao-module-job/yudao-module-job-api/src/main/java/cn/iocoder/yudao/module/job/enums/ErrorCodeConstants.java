package cn.iocoder.yudao.module.job.enums;

import cn.iocoder.yudao.framework.common.exception.ErrorCode;

/**
 * job 错误码枚举类
 * <p>
 * job 系统，使用 1-901-000-000 段
 */
public interface ErrorCodeConstants {
    // ========== 职位信息 1-901-001-000 ============
    ErrorCode POSITIONS_NOT_EXISTS = new ErrorCode(1_901_001_000, "职位信息不存在");

    // ========== 课程信息 1-901-002-000 ============
    ErrorCode COURSES_NOT_EXISTS = new ErrorCode(1_901_002_000, "工作课程不存在");
    // ========== 课时信息 1-901-003-000 ============
    ErrorCode COURSES_LESSONS_NOT_EXISTS = new ErrorCode(1_901_003_000, "工作课程课时不存在");

    // ========== 题库信息 1-901-004-000 ============
    ErrorCode QUESTION_NOT_EXISTS = new ErrorCode(1_901_004_000, "工作题库不存在");
}
