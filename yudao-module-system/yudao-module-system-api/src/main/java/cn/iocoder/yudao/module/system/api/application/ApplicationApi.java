package cn.iocoder.yudao.module.system.api.application;

import cn.iocoder.yudao.module.system.api.application.dto.FileConfigRespDTO;
import cn.iocoder.yudao.module.system.api.application.dto.MemberInviterRespDTO;

public interface ApplicationApi {
    /**
     * 通过appcode获得 会员邀请注册
     * @param appCode
     * @return
     */
    MemberInviterRespDTO getMemberInviterRespDTO(String appCode);
    /**
     * 通过appcode获得 文件配置
     * @param appCode
     * @return
     */
    FileConfigRespDTO getFileConfigRespDTO(String appCode);

    String getApiSignatureSecret(String appCode);

    /**
     * 记录邀请码
     * @param userId 用户id
     * @return 应用入口地址
     */
    String recordInvitationCode(String appCode,Long userId);
}
