package cn.iocoder.yudao.module.system.dal.mysql.application;

import java.util.*;

import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.mybatis.core.query.LambdaQueryWrapperX;
import cn.iocoder.yudao.framework.mybatis.core.mapper.BaseMapperX;
import cn.iocoder.yudao.module.system.dal.dataobject.application.ApplicationDO;
import org.apache.ibatis.annotations.Mapper;
import cn.iocoder.yudao.module.system.controller.admin.application.vo.*;

/**
 * 系统应用 Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface ApplicationMapper extends BaseMapperX<ApplicationDO> {

    default ApplicationDO selectByAppCode(String appCode){
        return selectOne(new LambdaQueryWrapperX<ApplicationDO>()
                .eq(ApplicationDO::getAppCode, appCode));
    }

    default PageResult<ApplicationDO> selectPage(ApplicationPageReqVO reqVO) {
        return selectPage(reqVO, new LambdaQueryWrapperX<ApplicationDO>()
                .likeIfPresent(ApplicationDO::getAppName, reqVO.getAppName())
                .eqIfPresent(ApplicationDO::getAppCode, reqVO.getAppCode())
                .likeIfPresent(ApplicationDO::getAppDescription, reqVO.getAppDescription())
                .eqIfPresent(ApplicationDO::getForceInvitation, reqVO.getForceInvitation())
                .betweenIfPresent(ApplicationDO::getCreateTime, reqVO.getCreateTime())
                .orderByDesc(ApplicationDO::getId));
    }

}