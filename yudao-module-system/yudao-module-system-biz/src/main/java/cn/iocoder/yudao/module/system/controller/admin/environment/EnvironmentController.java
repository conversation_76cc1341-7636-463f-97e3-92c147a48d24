package cn.iocoder.yudao.module.system.controller.admin.environment;

import org.springframework.web.bind.annotation.*;
import javax.annotation.Resource;
import org.springframework.validation.annotation.Validated;
import org.springframework.security.access.prepost.PreAuthorize;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.Operation;

import javax.validation.constraints.*;
import javax.validation.*;
import javax.servlet.http.*;
import java.util.*;
import java.io.IOException;

import cn.iocoder.yudao.framework.common.pojo.PageParam;
import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.common.pojo.CommonResult;
import cn.iocoder.yudao.framework.common.util.object.BeanUtils;
import static cn.iocoder.yudao.framework.common.pojo.CommonResult.success;

import cn.iocoder.yudao.framework.excel.core.util.ExcelUtils;

import cn.iocoder.yudao.framework.apilog.core.annotation.ApiAccessLog;
import static cn.iocoder.yudao.framework.apilog.core.enums.OperateTypeEnum.*;

import cn.iocoder.yudao.module.system.controller.admin.environment.vo.*;
import cn.iocoder.yudao.module.system.dal.dataobject.environment.EnvironmentDO;
import cn.iocoder.yudao.module.system.service.environment.EnvironmentService;

@Tag(name = "管理后台 - 租户配置")
@RestController
@RequestMapping("/system/environment")
@Validated
public class EnvironmentController {

    @Resource
    private EnvironmentService environmentService;

    @PostMapping("/create")
    @Operation(summary = "创建租户配置")
    @PreAuthorize("@ss.hasPermission('system:environment:create')")
    public CommonResult<Long> createEnvironment(@Valid @RequestBody EnvironmentSaveReqVO createReqVO) {
        return success(environmentService.createEnvironment(createReqVO));
    }

    @PutMapping("/update")
    @Operation(summary = "更新租户配置")
    @PreAuthorize("@ss.hasPermission('system:environment:update')")
    public CommonResult<Boolean> updateEnvironment(@Valid @RequestBody EnvironmentSaveReqVO updateReqVO) {
        environmentService.updateEnvironment(updateReqVO);
        return success(true);
    }

    @DeleteMapping("/delete")
    @Operation(summary = "删除租户配置")
    @Parameter(name = "id", description = "编号", required = true)
    @PreAuthorize("@ss.hasPermission('system:environment:delete')")
    public CommonResult<Boolean> deleteEnvironment(@RequestParam("id") Long id) {
        environmentService.deleteEnvironment(id);
        return success(true);
    }

    @GetMapping("/get")
    @Operation(summary = "获得租户配置")
    @Parameter(name = "id", description = "编号", required = true, example = "1024")
    @PreAuthorize("@ss.hasPermission('system:environment:query')")
    public CommonResult<EnvironmentRespVO> getEnvironment(@RequestParam("id") Long id) {
        EnvironmentDO environment = environmentService.getEnvironment(id);
        return success(BeanUtils.toBean(environment, EnvironmentRespVO.class));
    }

    @GetMapping("/page")
    @Operation(summary = "获得租户配置分页")
    @PreAuthorize("@ss.hasPermission('system:environment:query')")
    public CommonResult<PageResult<EnvironmentRespVO>> getEnvironmentPage(@Valid EnvironmentPageReqVO pageReqVO) {
        PageResult<EnvironmentDO> pageResult = environmentService.getEnvironmentPage(pageReqVO);
        return success(BeanUtils.toBean(pageResult, EnvironmentRespVO.class));
    }

    @GetMapping("/export-excel")
    @Operation(summary = "导出租户配置 Excel")
    @PreAuthorize("@ss.hasPermission('system:environment:export')")
    @ApiAccessLog(operateType = EXPORT)
    public void exportEnvironmentExcel(@Valid EnvironmentPageReqVO pageReqVO,
              HttpServletResponse response) throws IOException {
        pageReqVO.setPageSize(PageParam.PAGE_SIZE_NONE);
        List<EnvironmentDO> list = environmentService.getEnvironmentPage(pageReqVO).getList();
        // 导出 Excel
        ExcelUtils.write(response, "租户配置.xls", "数据", EnvironmentRespVO.class,
                        BeanUtils.toBean(list, EnvironmentRespVO.class));
    }

}