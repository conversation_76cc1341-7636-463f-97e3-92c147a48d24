package cn.iocoder.yudao.module.system.service.validity;

import java.time.Duration;

/**
 * 请求有效性 Service 接口
 *
 * <AUTHOR>
 */
public interface RequestValidityService {

    /**
     * 设置当前请求中的IP和User-Agent的有效期
     *
     * @param duration 有效期时长
     */
    void setRequestValidity(Duration duration);

    /**
     * 设置指定IP和User-Agent的有效期
     *
     * @param ip        IP地址
     * @param userAgent User-Agent
     * @param duration  有效期时长
     */
    void setRequestValidity(String ip, String userAgent, Duration duration);

    /**
     * 判断当前请求是否在有效期内
     *
     * @return true-在有效期内，false-不在有效期内
     */
    boolean isRequestValid();

    /**
     * 判断指定IP和User-Agent是否在有效期内
     *
     * @param ip        IP地址
     * @param userAgent User-Agent
     * @return true-在有效期内，false-不在有效期内
     */
    boolean isRequestValid(String ip, String userAgent);

    /**
     * 清除当前请求的有效期设置
     */
    void clearRequestValidity();

    /**
     * 清除指定IP和User-Agent的有效期设置
     *
     * @param ip        IP地址
     * @param userAgent User-Agent
     */
    void clearRequestValidity(String ip, String userAgent);

}
