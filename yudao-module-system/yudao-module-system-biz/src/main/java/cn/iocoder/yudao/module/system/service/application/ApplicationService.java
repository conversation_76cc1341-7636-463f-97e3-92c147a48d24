package cn.iocoder.yudao.module.system.service.application;

import java.util.*;
import javax.validation.*;

import cn.iocoder.yudao.module.system.api.application.dto.MemberInviterRespDTO;
import cn.iocoder.yudao.module.system.controller.admin.application.vo.*;
import cn.iocoder.yudao.module.system.dal.dataobject.application.ApplicationDO;
import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.common.pojo.PageParam;

/**
 * 系统应用 Service 接口
 *
 * <AUTHOR>
 */
public interface ApplicationService {
    MemberInviterRespDTO getMemberInviterRespDTO(String appCode);

    Long getInvitationCode(String appCode);

    /**
     * 记录邀请码
     * @param userId 用户id
     * @return 应用入口地址
     */
    String recordInvitationCode(String appCode,Long userId);
    /**
     * 创建系统应用
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    Long createApplication(@Valid ApplicationSaveReqVO createReqVO);

    /**
     * 更新系统应用
     *
     * @param updateReqVO 更新信息
     */
    void updateApplication(@Valid ApplicationSaveReqVO updateReqVO);

    /**
     * 删除系统应用
     *
     * @param id 编号
     */
    void deleteApplication(Long id);

    /**
     * 获得系统应用
     *
     * @param id 编号
     * @return 系统应用
     */
    ApplicationDO getApplication(Long id);

    /**
     * 获得系统应用分页
     *
     * @param pageReqVO 分页查询
     * @return 系统应用分页
     */
    PageResult<ApplicationDO> getApplicationPage(ApplicationPageReqVO pageReqVO);

    ApplicationDO getInfoByAppCode(String appCode);
}