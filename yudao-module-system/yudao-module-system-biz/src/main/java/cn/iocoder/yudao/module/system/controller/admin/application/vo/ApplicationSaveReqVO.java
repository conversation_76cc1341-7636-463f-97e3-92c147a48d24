package cn.iocoder.yudao.module.system.controller.admin.application.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;
import java.util.*;
import javax.validation.constraints.*;

@Schema(description = "管理后台 - 系统应用新增/修改 Request VO")
@Data
public class ApplicationSaveReqVO {

    @Schema(description = "应用ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "28728")
    private Long id;

    @Schema(description = "应用名称", requiredMode = Schema.RequiredMode.REQUIRED, example = "赵六")
    @NotEmpty(message = "应用名称不能为空")
    private String appName;

    @Schema(description = "应用代码", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotEmpty(message = "应用代码不能为空")
    private String appCode;

    @Schema(description = "应用入口地址", example = "https://www.iocoder.cn")
    private String appPortalUrl;

    @Schema(description = "应用描述", example = "你猜")
    private String appDescription;

    @Schema(description = "强制邀请", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "强制邀请不能为空")
    private Boolean forceInvitation;

    @Schema(description = "邀请积分", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "邀请积分不能为空")
    private Integer inviterPoints;

    @Schema(description = "注册积分")
    private Integer newcomerPoints;

    @Schema(description = "文件配置编号", example = "18078")
    private Long fileConfigId;

    @Schema(description = "文件临时时间")
    private Integer fileTempDuration;

    @Schema(description = "API签名密钥")
    private String apiSignatureSecret;

}