package cn.iocoder.yudao.module.system.controller.app.environment;

import cn.hutool.core.collection.CollectionUtil;
import cn.iocoder.yudao.framework.common.pojo.CommonResult;
import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.common.util.object.BeanUtils;
import cn.iocoder.yudao.module.system.controller.admin.environment.vo.EnvironmentPageReqVO;
import cn.iocoder.yudao.module.system.controller.admin.environment.vo.EnvironmentRespVO;
import cn.iocoder.yudao.module.system.dal.dataobject.environment.EnvironmentDO;
import cn.iocoder.yudao.module.system.service.environment.EnvironmentService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.annotation.security.PermitAll;
import javax.validation.Valid;

import java.util.List;
import java.util.Map;

import static cn.iocoder.yudao.framework.common.pojo.CommonResult.success;

@Tag(name = "管理后台 - 租户配置")
@RestController
@RequestMapping("/system/environment")
@Validated
public class AppEnvironmentController {

    @Resource
    private EnvironmentService environmentService;


    @GetMapping("/get")
    @Operation(summary = "获得租户配置")
    @Parameter(name = "id", description = "编号", required = true, example = "1024")
    @PermitAll
    public CommonResult<EnvironmentRespVO> getEnvironment(@RequestParam("id") Long id) {
        EnvironmentDO environment = environmentService.getEnvironment(id);
        return success(BeanUtils.toBean(environment, EnvironmentRespVO.class));
    }

    @GetMapping("/page")
    @Operation(summary = "获得租户配置分页")
    @PermitAll
    public CommonResult<PageResult<EnvironmentRespVO>> getEnvironmentPage(@Valid EnvironmentPageReqVO pageReqVO) {
        PageResult<EnvironmentDO> pageResult = environmentService.getEnvironmentPage(pageReqVO);
        return success(BeanUtils.toBean(pageResult, EnvironmentRespVO.class));
    }


    @GetMapping(value = "/get-value-by-category")
    @Operation(summary = "根据分组获得配置对象", description = "不可见的配置，不允许返回给前端。不建议将敏感内容配置到这里")
    @Parameter(name = "category", description = "参数键", required = true, example = "test2")
    @PermitAll
    public CommonResult<Map<String, String>> getConfigKeyByCategory(@RequestParam("category") String category) {
        List<EnvironmentDO> configDOS = environmentService.selectListByTypeAndCategoryAndVisible( category, true);
        Map<String, String> map = CollectionUtil.fieldValueAsMap(configDOS, "configKey", "value");
        return success(map);
    }
}