package cn.iocoder.yudao.module.system.service.sensitiveword;

import cn.hutool.core.collection.CollUtil;
import cn.iocoder.yudao.framework.common.exception.util.ServiceExceptionUtil;
import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.module.system.controller.admin.sensitiveword.vo.SensitiveWordPageReqVO;
import cn.iocoder.yudao.module.system.controller.admin.sensitiveword.vo.SensitiveWordSaveVO;
import cn.iocoder.yudao.module.system.dal.dataobject.sensitiveword.SensitiveWordDO;
import cn.iocoder.yudao.module.system.enums.ErrorCodeConstants;
import com.github.houbb.sensitive.word.core.SensitiveWordHelper;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.util.Assert;
import org.springframework.validation.annotation.Validated;

import java.util.*;

/**
 * 敏感词 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Slf4j
@Validated
public class SensitiveWordServiceImpl implements SensitiveWordService {

    /**
     * 是否开启敏感词功能
     */
    public static Boolean ENABLED = false;

    /**
     * 敏感词列表缓存
     */
    @Getter
    private volatile List<SensitiveWordDO> sensitiveWordCache = Collections.emptyList();
    /**
     * 敏感词标签缓存
     * key：敏感词编号 {@link SensitiveWordDO#getId()}
     * <p>
     * 这里声明 volatile 修饰的原因是，每次刷新时，直接修改指向
     */
    @Getter
    private volatile Set<String> sensitiveWordTagsCache = Collections.emptySet();


    @Override
    public Long createSensitiveWord(SensitiveWordSaveVO createReqVO) {
        throw  ServiceExceptionUtil.exception(ErrorCodeConstants.SENSITIVE_WORD_NOT_SUPPORT);
    }

    @Override
    public void updateSensitiveWord(SensitiveWordSaveVO updateReqVO) {
        throw  ServiceExceptionUtil.exception(ErrorCodeConstants.SENSITIVE_WORD_NOT_SUPPORT);
    }

    @Override
    public void deleteSensitiveWord(Long id) {
        throw  ServiceExceptionUtil.exception(ErrorCodeConstants.SENSITIVE_WORD_NOT_SUPPORT);
    }


    @Override
    public SensitiveWordDO getSensitiveWord(Long id) {
        throw  ServiceExceptionUtil.exception(ErrorCodeConstants.SENSITIVE_WORD_NOT_SUPPORT);
    }

    @Override
    public List<SensitiveWordDO> getSensitiveWordList() {
        throw  ServiceExceptionUtil.exception(ErrorCodeConstants.SENSITIVE_WORD_NOT_SUPPORT);
    }

    @Override
    public PageResult<SensitiveWordDO> getSensitiveWordPage(SensitiveWordPageReqVO pageReqVO) {
        throw  ServiceExceptionUtil.exception(ErrorCodeConstants.SENSITIVE_WORD_NOT_SUPPORT);
    }

    @Override
    public Set<String> getSensitiveWordTagSet() {
        return sensitiveWordTagsCache;
    }

    @Override
    public List<String> validateText(String text, List<String> tags) {
        return SensitiveWordHelper.findAll(text);
    }

    @Override
    public boolean isTextValid(String text, List<String> tags) {
        return SensitiveWordHelper.contains(text);
    }

}
