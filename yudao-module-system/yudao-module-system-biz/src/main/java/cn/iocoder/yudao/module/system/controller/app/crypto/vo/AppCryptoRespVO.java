package cn.iocoder.yudao.module.system.controller.app.crypto.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Schema(description = "用户 App - 加密 Response VO")
@Data
@NoArgsConstructor
@AllArgsConstructor
public class AppCryptoRespVO {

    @Schema(description = "标识", requiredMode = Schema.RequiredMode.REQUIRED, example = "1024")
    private String flag;

    @Schema(description = "密钥", requiredMode = Schema.RequiredMode.REQUIRED, example = "芋道")
    private String secretKey;


}
