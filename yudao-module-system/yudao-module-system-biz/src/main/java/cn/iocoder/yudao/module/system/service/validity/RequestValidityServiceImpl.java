package cn.iocoder.yudao.module.system.service.validity;

import cn.hutool.core.util.StrUtil;
import cn.hutool.crypto.SecureUtil;
import cn.hutool.extra.servlet.ServletUtil;
import cn.iocoder.yudao.framework.common.util.servlet.ServletUtils;
import com.github.benmanes.caffeine.cache.Cache;
import com.github.benmanes.caffeine.cache.Caffeine;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.validation.annotation.Validated;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.time.Duration;
import java.util.concurrent.TimeUnit;

import static cn.iocoder.yudao.framework.web.core.util.WebFrameworkUtils.getRequest;

/**
 * 请求有效性 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
@Slf4j
public class RequestValidityServiceImpl implements RequestValidityService {

    /**
     * 请求有效性缓存 KEY 格式
     * KEY 格式：request_validity:%s // 参数为 MD5(ip + userAgent)
     * VALUE 格式：String (过期时间戳)
     */
    private static final String REQUEST_VALIDITY_KEY = "request_validity:%s";

    /**
     * 本地缓存配置
     */
    private static final int LOCAL_CACHE_MAX_SIZE = 10000; // 最大缓存条目数
    private static final int LOCAL_CACHE_EXPIRE_MINUTES = 5; // 本地缓存过期时间（分钟）
    private static final int LOCAL_CACHE_REFRESH_SECONDS = 30; // 缓存刷新间隔（秒）

    @Resource
    private StringRedisTemplate stringRedisTemplate;

    /**
     * 本地缓存 - 存储有效期时间戳
     * Key: cacheKey (MD5), Value: 过期时间戳
     */
    private Cache<String, Long> localValidityCache;

    /**
     * 本地缓存 - 存储无效的请求（避免频繁查询Redis）
     * Key: cacheKey (MD5), Value: 检查时间戳
     */
    private Cache<String, Long> localInvalidCache;

    @PostConstruct
    public void initCache() {
        // 初始化有效期缓存
        localValidityCache = Caffeine.newBuilder()
                .maximumSize(LOCAL_CACHE_MAX_SIZE)
                .expireAfterWrite(LOCAL_CACHE_EXPIRE_MINUTES, TimeUnit.MINUTES)
                .recordStats() // 启用统计功能
                .build();

        // 初始化无效缓存（较短的过期时间，避免误判）
        localInvalidCache = Caffeine.newBuilder()
                .maximumSize(LOCAL_CACHE_MAX_SIZE / 2) // 无效缓存容量较小
                .expireAfterWrite(LOCAL_CACHE_REFRESH_SECONDS, TimeUnit.SECONDS)
                .recordStats() // 启用统计功能
                .build();

        log.info("[initCache][本地缓存初始化完成：maxSize={}, expireMinutes={}, refreshSeconds={}]",
                LOCAL_CACHE_MAX_SIZE, LOCAL_CACHE_EXPIRE_MINUTES, LOCAL_CACHE_REFRESH_SECONDS);
    }

    @Override
    public void setRequestValidity(Duration duration) {
        String ip = getAllClientIPString();
        String userAgent = ServletUtils.getUserAgent();
        setRequestValidity(ip, userAgent, duration);
    }

    @Override
    public void setRequestValidity(String ip, String userAgent, Duration duration) {
        if (StrUtil.isBlank(ip) || StrUtil.isBlank(userAgent) || duration == null) {
            log.warn("[setRequestValidity][参数无效：ip={}, userAgent={}, duration={}]", ip, userAgent, duration);
            return;
        }

        String cacheKey = buildCacheKey(ip, userAgent);
        String redisKey = buildRedisKey(ip, userAgent);
        long expireTime = System.currentTimeMillis() + duration.toMillis();

        // 设置Redis键值，过期时间比实际有效期稍长一些，避免时间差问题
        stringRedisTemplate.opsForValue().set(redisKey, String.valueOf(expireTime),
                duration.getSeconds() + 60, TimeUnit.SECONDS);

        // 更新本地缓存
        localValidityCache.put(cacheKey, expireTime);
        // 从无效缓存中移除（如果存在）
        localInvalidCache.invalidate(cacheKey);

        log.info("[setRequestValidity][设置请求有效期成功：ip={}, userAgent={}, duration={}, expireTime={}]",
                ip, userAgent, duration, expireTime);
    }

    @Override
    public boolean isRequestValid() {
        String ip = getAllClientIPString();

        String userAgent = ServletUtils.getUserAgent();
        return isRequestValid(ip, userAgent);
    }

    @Override
    public boolean isRequestValid(String ip, String userAgent) {
        if (StrUtil.isBlank(ip) || StrUtil.isBlank(userAgent)) {
            log.warn("[isRequestValid][参数无效：ip={}, userAgent={}]", ip, userAgent);
            return false;
        }

        String cacheKey = buildCacheKey(ip, userAgent);
        long currentTime = System.currentTimeMillis();

        // 1. 先检查本地有效缓存
        Long cachedExpireTime = localValidityCache.getIfPresent(cacheKey);
        if (cachedExpireTime != null) {
            boolean isValid = currentTime <= cachedExpireTime;
            if (isValid) {
                log.debug("[isRequestValid][本地缓存命中-有效：ip={}, userAgent={}, expireTime={}]",
                        ip, userAgent, cachedExpireTime);
                return true;
            } else {
                // 本地缓存已过期，清除
                localValidityCache.invalidate(cacheKey);
                log.debug("[isRequestValid][本地缓存过期，已清除：ip={}, userAgent={}]", ip, userAgent);
            }
        }

        // 2. 检查本地无效缓存（避免频繁查询Redis）
        Long lastInvalidCheck = localInvalidCache.getIfPresent(cacheKey);
        if (lastInvalidCheck != null) {
            log.debug("[isRequestValid][本地无效缓存命中：ip={}, userAgent={}, lastCheck={}]",
                    ip, userAgent, lastInvalidCheck);
            return false;
        }

        // 3. 查询Redis
        String redisKey = buildRedisKey(ip, userAgent);
        String expireTimeStr = stringRedisTemplate.opsForValue().get(redisKey);

        if (StrUtil.isBlank(expireTimeStr)) {
            log.debug("[isRequestValid][Redis未找到有效期记录：ip={}, userAgent={}]", ip, userAgent);
            // 缓存到无效缓存中
            localInvalidCache.put(cacheKey, currentTime);
            return false;
        }

        try {
            long expireTime = Long.parseLong(expireTimeStr);
            boolean isValid = currentTime <= expireTime;

            log.debug("[isRequestValid][Redis检查结果：ip={}, userAgent={}, isValid={}, expireTime={}, currentTime={}]",
                    ip, userAgent, isValid, expireTime, currentTime);

            if (isValid) {
                // 缓存到本地有效缓存中
                localValidityCache.put(cacheKey, expireTime);
            } else {
                // 已过期，删除Redis键并缓存到无效缓存中
                stringRedisTemplate.delete(redisKey);
                localInvalidCache.put(cacheKey, currentTime);
            }

            return isValid;
        } catch (NumberFormatException e) {
            log.error("[isRequestValid][解析过期时间失败：ip={}, userAgent={}, expireTimeStr={}]",
                    ip, userAgent, expireTimeStr, e);
            // 数据格式错误，删除Redis键并缓存到无效缓存中
            stringRedisTemplate.delete(redisKey);
            localInvalidCache.put(cacheKey, currentTime);
            return false;
        }
    }

    @Override
    public void clearRequestValidity() {
        String ip = getAllClientIPString();
        String userAgent = ServletUtils.getUserAgent();
        clearRequestValidity(ip, userAgent);
    }

    @Override
    public void clearRequestValidity(String ip, String userAgent) {
        if (StrUtil.isBlank(ip) || StrUtil.isBlank(userAgent)) {
            log.warn("[clearRequestValidity][参数无效：ip={}, userAgent={}]", ip, userAgent);
            return;
        }

        String cacheKey = buildCacheKey(ip, userAgent);
        String redisKey = buildRedisKey(ip, userAgent);

        // 删除Redis键
        Boolean deleted = stringRedisTemplate.delete(redisKey);

        // 清除本地缓存
        localValidityCache.invalidate(cacheKey);
        localInvalidCache.invalidate(cacheKey);

        log.info("[clearRequestValidity][清除请求有效期：ip={}, userAgent={}, redisDeleted={}, localCacheCleared=true]",
                ip, userAgent, deleted);
    }

    /**
     * 构建缓存键（用于本地缓存）
     *
     * @param ip        IP地址
     * @param userAgent User-Agent
     * @return 缓存键
     */
    private String buildCacheKey(String ip, String userAgent) {
        // 使用MD5避免键过长，同时保证唯一性
        String combined = ip + "|" + userAgent;
        return SecureUtil.md5(combined);
    }

    /**
     * 构建Redis键
     *
     * @param ip        IP地址
     * @param userAgent User-Agent
     * @return Redis键
     */
    private String buildRedisKey(String ip, String userAgent) {
        String cacheKey = buildCacheKey(ip, userAgent);
        return String.format(REQUEST_VALIDITY_KEY, cacheKey);
    }

    /**
     * 获取缓存统计信息（用于监控和调试）
     *
     * @return 缓存统计信息
     */
    public String getCacheStats() {
        return String.format(
                "ValidityCache[size=%d, hitRate=%.2f%%, evictionCount=%d], " +
                "InvalidCache[size=%d, hitRate=%.2f%%, evictionCount=%d]",
                localValidityCache.estimatedSize(),
                localValidityCache.stats().hitRate() * 100,
                localValidityCache.stats().evictionCount(),
                localInvalidCache.estimatedSize(),
                localInvalidCache.stats().hitRate() * 100,
                localInvalidCache.stats().evictionCount()
        );
    }

    /**
     * 清空所有本地缓存（用于管理和调试）
     */
    public void clearAllLocalCache() {
        localValidityCache.invalidateAll();
        localInvalidCache.invalidateAll();
        log.info("[clearAllLocalCache][已清空所有本地缓存]");
    }

    private String getAllClientIPString(){
        HttpServletRequest request = getRequest();
        if (request == null) {
            return null;
        }
        StringBuilder sb = new StringBuilder(request.getRemoteAddr());
        String[] headers = {"X-Forwarded-For", "X-Real-IP", "Proxy-Client-IP", "WL-Proxy-Client-IP", "HTTP_CLIENT_IP", "HTTP_X_FORWARDED_FOR"};
        for (String header : headers) {
            sb.append("-")
                    .append(header)
                    .append(":")
                    .append(ServletUtil.getHeaderIgnoreCase(request,header));
        }
        return sb.toString();
    }
}
