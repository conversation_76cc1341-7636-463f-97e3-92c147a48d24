package cn.iocoder.yudao.module.system.api.application;

import cn.hutool.core.util.StrUtil;
import cn.iocoder.yudao.framework.common.util.object.BeanUtils;
import cn.iocoder.yudao.module.system.api.application.dto.FileConfigRespDTO;
import cn.iocoder.yudao.module.system.api.application.dto.MemberInviterRespDTO;
import cn.iocoder.yudao.module.system.dal.dataobject.application.ApplicationDO;
import cn.iocoder.yudao.module.system.service.application.ApplicationService;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

@Service
public class ApplicationApiImpl implements   ApplicationApi{
    @Resource
    private ApplicationService applicationService;
    @Override
    public MemberInviterRespDTO getMemberInviterRespDTO(String appCode) {
        return applicationService.getMemberInviterRespDTO(appCode);
    }

    @Override
    public FileConfigRespDTO getFileConfigRespDTO(String appCode) {
        ApplicationDO applicationDO = applicationService.getInfoByAppCode(appCode);
        return BeanUtils.toBean(applicationDO,FileConfigRespDTO.class);
    }

    @Override
    public String getApiSignatureSecret(String appCode) {
        if(StrUtil.isBlank(appCode)){
            return null;
        }
        ApplicationDO applicationDO = applicationService.getInfoByAppCode(appCode);
        return applicationDO.getApiSignatureSecret();
    }

    @Override
    public String recordInvitationCode(String appCode, Long userId) {
        return applicationService.recordInvitationCode(appCode,userId);
    }
}
