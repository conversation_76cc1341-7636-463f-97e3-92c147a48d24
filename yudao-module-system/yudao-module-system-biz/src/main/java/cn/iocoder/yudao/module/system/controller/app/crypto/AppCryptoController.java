package cn.iocoder.yudao.module.system.controller.app.crypto;

import cn.iocoder.yudao.framework.common.pojo.CommonResult;
import cn.iocoder.yudao.module.system.controller.app.crypto.vo.AppCryptoRespVO;
import cn.iocoder.yudao.module.system.service.crypto.CryptoService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.annotation.security.PermitAll;
import java.io.IOException;

import static cn.iocoder.yudao.framework.common.pojo.CommonResult.success;

@Tag(name = "用户 App - 加密")
@RestController
@RequestMapping("/system/crypto")
@Validated
public class AppCryptoController {
    @Resource
    private CryptoService cryptoService;

    @GetMapping("/{beanName}/get")
    @Operation(summary = "获得密钥")
    @Parameter(name = "beanName", description = "应用code", required = true)
    @PermitAll
    public CommonResult<AppCryptoRespVO> getInvitationCode(@PathVariable("beanName") String beanName) throws IOException {
        return success(cryptoService.generateSecretKey(beanName));
    }



}
