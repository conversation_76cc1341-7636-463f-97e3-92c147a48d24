package cn.iocoder.yudao.module.system.api.crypto;

import cn.iocoder.yudao.module.system.service.crypto.CryptoService;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
@Service
public class CryptoApiImpl implements  CryptoApi {
    @Resource
    private CryptoService cryptoService;

    @Override
    public String getPrivateKey(String flag) {
        return cryptoService.getPrivateKey(flag);
    }
}
