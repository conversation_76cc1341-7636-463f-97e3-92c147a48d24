package cn.iocoder.yudao.module.system.service.environment;

import org.springframework.stereotype.Service;
import javax.annotation.Resource;
import org.springframework.validation.annotation.Validated;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import cn.iocoder.yudao.module.system.controller.admin.environment.vo.*;
import cn.iocoder.yudao.module.system.dal.dataobject.environment.EnvironmentDO;
import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.common.pojo.PageParam;
import cn.iocoder.yudao.framework.common.util.object.BeanUtils;

import cn.iocoder.yudao.module.system.dal.mysql.environment.EnvironmentMapper;

import static cn.iocoder.yudao.framework.common.exception.util.ServiceExceptionUtil.exception;
import static cn.iocoder.yudao.module.system.enums.ErrorCodeConstants.*;

/**
 * 租户配置 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class EnvironmentServiceImpl implements EnvironmentService {

    @Resource
    private EnvironmentMapper environmentMapper;

    @Override
    public Long createEnvironment(EnvironmentSaveReqVO createReqVO) {
        // 插入
        EnvironmentDO environment = BeanUtils.toBean(createReqVO, EnvironmentDO.class);
        environmentMapper.insert(environment);
        // 返回
        return environment.getId();
    }

    @Override
    public void updateEnvironment(EnvironmentSaveReqVO updateReqVO) {
        // 校验存在
        validateEnvironmentExists(updateReqVO.getId());
        // 更新
        EnvironmentDO updateObj = BeanUtils.toBean(updateReqVO, EnvironmentDO.class);
        environmentMapper.updateById(updateObj);
    }

    @Override
    public void deleteEnvironment(Long id) {
        // 校验存在
        validateEnvironmentExists(id);
        // 删除
        environmentMapper.deleteById(id);
    }

    private void validateEnvironmentExists(Long id) {
        if (environmentMapper.selectById(id) == null) {
            throw exception(ENVIRONMENT_NOT_EXISTS);
        }
    }

    @Override
    public EnvironmentDO getEnvironment(Long id) {
        return environmentMapper.selectById(id);
    }

    @Override
    public PageResult<EnvironmentDO> getEnvironmentPage(EnvironmentPageReqVO pageReqVO) {
        return environmentMapper.selectPage(pageReqVO);
    }

    @Override
    public List<EnvironmentDO> selectListByTypeAndCategoryAndVisible(String category, Boolean visible) {
         return environmentMapper.selectListByTypeAndCategoryAndVisible(category,visible);
    }

}