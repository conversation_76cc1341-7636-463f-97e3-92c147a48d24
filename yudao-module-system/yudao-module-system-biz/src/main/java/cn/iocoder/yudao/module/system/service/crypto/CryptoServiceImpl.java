package cn.iocoder.yudao.module.system.service.crypto;

import cn.hutool.core.util.IdUtil;
import cn.iocoder.yudao.framework.common.util.spring.SpringUtils;
import cn.iocoder.yudao.framework.crypto.core.EncryptionService;
import cn.iocoder.yudao.framework.crypto.core.SecretKeyBO;
import cn.iocoder.yudao.framework.redis.tool.RedisStringValueGetter;
import cn.iocoder.yudao.module.system.controller.app.crypto.vo.AppCryptoRespVO;
import org.springframework.stereotype.Service;
import org.springframework.validation.annotation.Validated;

import javax.annotation.Resource;
import java.time.Duration;

import static cn.iocoder.yudao.framework.common.exception.util.ServiceExceptionUtil.exception;
import static cn.iocoder.yudao.framework.common.util.servlet.ServletUtils.getClientIP;
import static cn.iocoder.yudao.module.system.dal.redis.RedisKeyConstants.CRYPTO_SECRET_KEY;

/**
 * 系统应用 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class CryptoServiceImpl implements CryptoService {

    @Resource
    private RedisStringValueGetter redisStringValueGetter;

    @Override
    public AppCryptoRespVO generateSecretKey(String beanName) {
        AppCryptoRespVO appCryptoRespVO = new AppCryptoRespVO();
        EncryptionService encryptionService = SpringUtils.getBean(beanName, EncryptionService.class);
        if(encryptionService != null){
            SecretKeyBO secretKeyBO = encryptionService.generateSecretKey();
            //
            String publicKey = secretKeyBO.getPublicKey();
            String privateKey = secretKeyBO.getPrivateKey();
            String uuid = IdUtil.fastSimpleUUID();
            //
            appCryptoRespVO.setFlag(uuid);
            appCryptoRespVO.setSecretKey(publicKey);
            //
            redisStringValueGetter.opsForValue().set(formatKey(uuid),privateKey, Duration.ofDays(365));
        }

        return appCryptoRespVO;
    }
    @Override
    public String getPrivateKey(String flag){
        return redisStringValueGetter.getStr(formatKey(flag));
    }

    private static String formatKey(String appCode) {
        return String.format(CRYPTO_SECRET_KEY, appCode);
    }
}