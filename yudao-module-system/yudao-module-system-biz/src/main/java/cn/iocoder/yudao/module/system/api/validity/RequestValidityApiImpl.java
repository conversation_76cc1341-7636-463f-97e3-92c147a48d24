package cn.iocoder.yudao.module.system.api.validity;

import cn.iocoder.yudao.module.system.service.validity.RequestValidityService;
import org.springframework.stereotype.Service;
import org.springframework.validation.annotation.Validated;

import javax.annotation.Resource;
import java.time.Duration;

/**
 * 请求有效性 API 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class RequestValidityApiImpl implements RequestValidityApi {

    @Resource
    private RequestValidityService requestValidityService;

    @Override
    public void setRequestValidity(Duration duration) {
        requestValidityService.setRequestValidity(duration);
    }

    @Override
    public void setRequestValidity(String ip, String userAgent, Duration duration) {
        requestValidityService.setRequestValidity(ip, userAgent, duration);
    }

    @Override
    public boolean isRequestValid() {
        return requestValidityService.isRequestValid();
    }

    @Override
    public boolean isRequestValid(String ip, String userAgent) {
        return requestValidityService.isRequestValid(ip, userAgent);
    }

    @Override
    public void clearRequestValidity() {
        requestValidityService.clearRequestValidity();
    }

    @Override
    public void clearRequestValidity(String ip, String userAgent) {
        requestValidityService.clearRequestValidity(ip, userAgent);
    }

}
