package cn.iocoder.yudao.module.system.dal.dataobject.application;

import lombok.*;
import java.util.*;
import java.time.LocalDateTime;
import java.time.LocalDateTime;
import com.baomidou.mybatisplus.annotation.*;
import cn.iocoder.yudao.framework.mybatis.core.dataobject.BaseDO;

/**
 * 系统应用 DO
 *
 * <AUTHOR>
 */
@TableName("system_application")
@KeySequence("system_application_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ApplicationDO extends BaseDO {

    /**
     * 应用ID
     */
    @TableId
    private Long id;
    /**
     * 应用名称
     */
    private String appName;
    /**
     * 应用代码
     */
    private String appCode;
    /**
     * 应用入口地址
     */
    private String appPortalUrl;
    /**
     * 应用描述
     */
    private String appDescription;
    /**
     * 强制邀请
     *
     * 枚举 {@link TODO infra_boolean_string 对应的类}
     */
    private Boolean forceInvitation;
    /**
     * 邀请积分
     */
    private Integer inviterPoints;
    /**
     * 注册积分
     */
    private Integer newcomerPoints;
    /**
     * 文件配置编号
     */
    private Long fileConfigId;
    /**
     * 文件临时时间
     */
    private Integer fileTempDuration;
    /**
     * API签名密钥
     */
    private String apiSignatureSecret;

}