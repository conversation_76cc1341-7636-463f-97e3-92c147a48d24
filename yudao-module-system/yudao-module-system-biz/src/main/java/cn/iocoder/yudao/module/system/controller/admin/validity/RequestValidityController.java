package cn.iocoder.yudao.module.system.controller.admin.validity;

import cn.iocoder.yudao.framework.common.pojo.CommonResult;
import cn.iocoder.yudao.module.system.api.validity.RequestValidityApi;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.time.Duration;

import static cn.iocoder.yudao.framework.common.pojo.CommonResult.success;

/**
 * 请求有效性控制器 - 示例用法
 *
 * <AUTHOR>
 */
//@Tag(name = "管理后台 - 请求有效性")
//@RestController
//@RequestMapping("/system/request-validity")
//@Validated
public class RequestValidityController {

    @Resource
    private RequestValidityApi requestValidityApi;

    @PostMapping("/set")
    @Operation(summary = "设置当前请求的有效期")
    @Parameter(name = "minutes", description = "有效期分钟数", required = true)
    public CommonResult<Boolean> setRequestValidity(@RequestParam("minutes") Integer minutes) {
        requestValidityApi.setRequestValidity(Duration.ofMinutes(minutes));
        return success(true);
    }

    @PostMapping("/set-specific")
    @Operation(summary = "设置指定IP和User-Agent的有效期")
    public CommonResult<Boolean> setSpecificRequestValidity(
            @RequestParam("ip") String ip,
            @RequestParam("userAgent") String userAgent,
            @RequestParam("minutes") Integer minutes) {
        requestValidityApi.setRequestValidity(ip, userAgent, Duration.ofMinutes(minutes));
        return success(true);
    }

    @GetMapping("/check")
    @Operation(summary = "检查当前请求是否在有效期内")
    public CommonResult<Boolean> checkRequestValidity() {
        boolean isValid = requestValidityApi.isRequestValid();
        return success(isValid);
    }

    @GetMapping("/check-specific")
    @Operation(summary = "检查指定IP和User-Agent是否在有效期内")
    public CommonResult<Boolean> checkSpecificRequestValidity(
            @RequestParam("ip") String ip,
            @RequestParam("userAgent") String userAgent) {
        boolean isValid = requestValidityApi.isRequestValid(ip, userAgent);
        return success(isValid);
    }

    @DeleteMapping("/clear")
    @Operation(summary = "清除当前请求的有效期")
    public CommonResult<Boolean> clearRequestValidity() {
        requestValidityApi.clearRequestValidity();
        return success(true);
    }

    @DeleteMapping("/clear-specific")
    @Operation(summary = "清除指定IP和User-Agent的有效期")
    public CommonResult<Boolean> clearSpecificRequestValidity(
            @RequestParam("ip") String ip,
            @RequestParam("userAgent") String userAgent) {
        requestValidityApi.clearRequestValidity(ip, userAgent);
        return success(true);
    }

}
