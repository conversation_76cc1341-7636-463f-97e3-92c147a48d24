package cn.iocoder.yudao.module.system.controller.admin.application.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;
import java.util.*;
import org.springframework.format.annotation.DateTimeFormat;
import java.time.LocalDateTime;
import com.alibaba.excel.annotation.*;
import cn.iocoder.yudao.framework.excel.core.annotations.DictFormat;
import cn.iocoder.yudao.framework.excel.core.convert.DictConvert;

import static cn.iocoder.yudao.module.infra.enums.DictTypeConstants.BOOLEAN_STRING;

@Schema(description = "管理后台 - 系统应用 Response VO")
@Data
@ExcelIgnoreUnannotated
public class ApplicationRespVO {

    @Schema(description = "应用ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "28728")
    @ExcelProperty("应用ID")
    private Long id;

    @Schema(description = "应用名称", requiredMode = Schema.RequiredMode.REQUIRED, example = "赵六")
    @ExcelProperty("应用名称")
    private String appName;

    @Schema(description = "应用代码", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("应用代码")
    private String appCode;

    @Schema(description = "应用入口地址", example = "https://www.iocoder.cn")
    @ExcelProperty("应用入口地址")
    private String appPortalUrl;

    @Schema(description = "应用描述", example = "你猜")
    @ExcelProperty("应用描述")
    private String appDescription;

    @Schema(description = "强制邀请", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty(value = "强制邀请", converter = DictConvert.class)
    @DictFormat(BOOLEAN_STRING)
    private Boolean forceInvitation;

    @Schema(description = "邀请积分", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("邀请积分")
    private Integer inviterPoints;

    @Schema(description = "注册积分")
    @ExcelProperty("注册积分")
    private Integer newcomerPoints;

    @Schema(description = "文件配置编号", example = "18078")
    @ExcelProperty("文件配置编号")
    private Long fileConfigId;

    @Schema(description = "文件临时时间")
    @ExcelProperty("文件临时时间")
    private Integer fileTempDuration;

    @Schema(description = "创建时间", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("创建时间")
    private LocalDateTime createTime;

}