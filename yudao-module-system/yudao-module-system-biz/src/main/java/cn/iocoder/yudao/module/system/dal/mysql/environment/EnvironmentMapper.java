package cn.iocoder.yudao.module.system.dal.mysql.environment;

import java.util.*;

import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.mybatis.core.query.LambdaQueryWrapperX;
import cn.iocoder.yudao.framework.mybatis.core.mapper.BaseMapperX;
import cn.iocoder.yudao.module.system.dal.dataobject.environment.EnvironmentDO;
import org.apache.ibatis.annotations.Mapper;
import cn.iocoder.yudao.module.system.controller.admin.environment.vo.*;

/**
 * 租户配置 Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface EnvironmentMapper extends BaseMapperX<EnvironmentDO> {

    default PageResult<EnvironmentDO> selectPage(EnvironmentPageReqVO reqVO) {
        return selectPage(reqVO, new LambdaQueryWrapperX<EnvironmentDO>()
                .eqIfPresent(EnvironmentDO::getCategory, reqVO.getCategory())
                .likeIfPresent(EnvironmentDO::getName, reqVO.getName())
                .eqIfPresent(EnvironmentDO::getConfigKey, reqVO.getConfigKey())
                .eqIfPresent(EnvironmentDO::getValue, reqVO.getValue())
                .eqIfPresent(EnvironmentDO::getVisible, reqVO.getVisible())
                .eqIfPresent(EnvironmentDO::getRemark, reqVO.getRemark())
                .betweenIfPresent(EnvironmentDO::getCreateTime, reqVO.getCreateTime())
                .orderByDesc(EnvironmentDO::getId));
    }


    default List<EnvironmentDO> selectListByTypeAndCategoryAndVisible( String category, Boolean visible) {
        return selectList( new LambdaQueryWrapperX<EnvironmentDO>()
                .eqIfPresent(EnvironmentDO::getCategory, category)
                .eqIfPresent(EnvironmentDO::getVisible, visible)
        );
    }
}