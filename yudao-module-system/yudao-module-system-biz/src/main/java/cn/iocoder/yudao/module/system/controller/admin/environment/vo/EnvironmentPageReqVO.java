package cn.iocoder.yudao.module.system.controller.admin.environment.vo;

import lombok.*;
import java.util.*;
import io.swagger.v3.oas.annotations.media.Schema;
import cn.iocoder.yudao.framework.common.pojo.PageParam;
import org.springframework.format.annotation.DateTimeFormat;
import java.time.LocalDateTime;

import static cn.iocoder.yudao.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;

@Schema(description = "管理后台 - 租户配置分页 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class EnvironmentPageReqVO extends PageParam {

    @Schema(description = "参数分组")
    private String category;

    @Schema(description = "参数名称", example = "芋艿")
    private String name;

    @Schema(description = "参数键名")
    private String configKey;

    @Schema(description = "参数键值")
    private String value;

    @Schema(description = "是否可见")
    private Boolean visible;

    @Schema(description = "备注", example = "你说的对")
    private String remark;

    @Schema(description = "创建时间")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime[] createTime;

}