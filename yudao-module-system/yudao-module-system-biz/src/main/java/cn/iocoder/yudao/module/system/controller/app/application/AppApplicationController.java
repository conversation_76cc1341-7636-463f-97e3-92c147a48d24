package cn.iocoder.yudao.module.system.controller.app.application;

import cn.iocoder.yudao.framework.common.enums.CommonStatusEnum;
import cn.iocoder.yudao.framework.common.pojo.CommonResult;
import cn.iocoder.yudao.framework.common.util.object.BeanUtils;
import cn.iocoder.yudao.module.system.controller.admin.environment.vo.EnvironmentRespVO;
import cn.iocoder.yudao.module.system.controller.app.dict.vo.AppDictDataRespVO;
import cn.iocoder.yudao.module.system.dal.dataobject.dict.DictDataDO;
import cn.iocoder.yudao.module.system.service.application.ApplicationService;
import cn.iocoder.yudao.module.system.service.dict.DictDataService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.annotation.security.PermitAll;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.List;

import static cn.iocoder.yudao.framework.common.pojo.CommonResult.success;
import static cn.iocoder.yudao.framework.common.util.servlet.ServletUtils.getClientIP;

@Tag(name = "用户 App - 字典数据")
@RestController
@RequestMapping("/system/application")
@Validated
public class AppApplicationController {
    @Resource
    private ApplicationService applicationService;

    @GetMapping("/invite/{appCode}")
    @Operation(summary = "记录邀请码")
    @Parameter(name = "appCode", description = "应用code", required = true)
    @Parameter(name = "code", description = "邀请码", required = true)
    @PermitAll
    public void recordInvitationCode(@PathVariable("appCode") String appCode, @RequestParam("code") Long code, HttpServletResponse response ) throws IOException {
        String appPortalUrl = applicationService.recordInvitationCode(appCode,code);
        // 重定向到其他对应的页面去啦。
        response.sendRedirect(appPortalUrl);
    }


    @GetMapping("/getInvite/{appCode}")
    @Operation(summary = "获得邀请码")
    @Parameter(name = "appCode", description = "应用code", required = true)
    @PermitAll
    public CommonResult<Long> getInvitationCode(@PathVariable("appCode") String appCode) throws IOException {
        Long invitationCode = applicationService.getInvitationCode(appCode);
        return success(invitationCode);
    }



}
