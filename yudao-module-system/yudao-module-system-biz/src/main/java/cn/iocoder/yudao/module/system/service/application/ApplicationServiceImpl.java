package cn.iocoder.yudao.module.system.service.application;

import cn.hutool.core.lang.Assert;
import cn.iocoder.yudao.framework.redis.tool.RedisStringValueGetter;
import cn.iocoder.yudao.module.system.api.application.dto.MemberInviterRespDTO;
import org.springframework.stereotype.Service;
import javax.annotation.Resource;
import org.springframework.validation.annotation.Validated;

import java.util.concurrent.TimeUnit;

import cn.iocoder.yudao.module.system.controller.admin.application.vo.*;
import cn.iocoder.yudao.module.system.dal.dataobject.application.ApplicationDO;
import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.common.util.object.BeanUtils;

import cn.iocoder.yudao.module.system.dal.mysql.application.ApplicationMapper;

import static cn.iocoder.yudao.framework.common.exception.util.ServiceExceptionUtil.exception;
import static cn.iocoder.yudao.framework.common.util.servlet.ServletUtils.getClientIP;
import static cn.iocoder.yudao.module.system.dal.redis.RedisKeyConstants.APPLICATION_RECORD_INVITATION;
import static cn.iocoder.yudao.module.system.enums.ErrorCodeConstants.*;

/**
 * 系统应用 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class ApplicationServiceImpl implements ApplicationService {

    @Resource
    private ApplicationMapper applicationMapper;
    @Resource
    private RedisStringValueGetter redisStringValueGetter;

    @Override
    public MemberInviterRespDTO getMemberInviterRespDTO(String appCode){
        ApplicationDO applicationDO = getInfoByAppCode(appCode);
        if(applicationDO == null){
            throw exception(APPLICATION_NOT_EXISTS);
        }
        MemberInviterRespDTO memberInviterRespDTO = BeanUtils.toBean(applicationDO,MemberInviterRespDTO.class);
        String redisKey = formatKey(appCode,getClientIP());
        Long inviter = redisStringValueGetter.getLong(redisKey);
        memberInviterRespDTO.setInviter(inviter);
        return memberInviterRespDTO;
    }
    @Override
    public Long getInvitationCode(String appCode) {
        String redisKey = formatKey(appCode,getClientIP());
        return redisStringValueGetter.getLong(redisKey);
    }
    @Override
    public String recordInvitationCode(String appCode,Long userId) {
        ApplicationDO applicationDO = getInfoByAppCode(appCode);
        if(applicationDO == null){
            throw exception(APPLICATION_NOT_EXISTS);
        }
        Assert.notNull(userId, "邀请码为空");
        String redisKey = formatKey(appCode,getClientIP());
        redisStringValueGetter.opsForValue().set(redisKey, userId.toString(), 30, TimeUnit.MINUTES);
        return applicationDO.getAppPortalUrl();
    }

    @Override
    public Long createApplication(ApplicationSaveReqVO createReqVO) {
        validateAppCode(createReqVO.getAppCode());
        // 插入
        ApplicationDO application = BeanUtils.toBean(createReqVO, ApplicationDO.class);
        applicationMapper.insert(application);
        // 返回
        return application.getId();
    }

    @Override
    public void updateApplication(ApplicationSaveReqVO updateReqVO) {
        // 校验存在
        validateApplicationExists(updateReqVO.getId());
        // 更新
        ApplicationDO updateObj = BeanUtils.toBean(updateReqVO, ApplicationDO.class);
        applicationMapper.updateById(updateObj);
    }

    @Override
    public void deleteApplication(Long id) {
        // 校验存在
        validateApplicationExists(id);
        // 删除
        applicationMapper.deleteById(id);
    }

    private void validateApplicationExists(Long id) {
        if (applicationMapper.selectById(id) == null) {
            throw exception(APPLICATION_NOT_EXISTS);
        }
    }

    @Override
    public ApplicationDO getApplication(Long id) {
        return applicationMapper.selectById(id);
    }

    @Override
    public PageResult<ApplicationDO> getApplicationPage(ApplicationPageReqVO pageReqVO) {
        return applicationMapper.selectPage(pageReqVO);
    }

    /**
     * 校验 应用代码 是唯一的
     *
     * @param appCode 应用代码
     */
    void validateAppCode(String appCode) {
        ApplicationDO applicationDO = getInfoByAppCode(appCode);
        if (applicationDO != null) {
            throw exception(APPLICATION_NAME_USED, appCode);
        }
    }
    @Override
    public ApplicationDO getInfoByAppCode(String appCode) {
        return applicationMapper.selectByAppCode(appCode);
    }

    private static String formatKey(String appCode, String accessToken) {
        return String.format(APPLICATION_RECORD_INVITATION, appCode, accessToken);
    }
}