package cn.iocoder.yudao.module.system.service.mail;

import cn.hutool.core.date.LocalDateTimeUtil;
import cn.hutool.core.lang.Assert;
import cn.hutool.core.map.MapUtil;
import cn.iocoder.yudao.module.system.api.mail.dto.code.MailCodeSendReqDTO;
import cn.iocoder.yudao.module.system.api.mail.dto.code.MailCodeUseReqDTO;
import cn.iocoder.yudao.module.system.api.mail.dto.code.MailCodeValidateReqDTO;
import cn.iocoder.yudao.module.system.dal.dataobject.mail.MailCodeDO;
import cn.iocoder.yudao.module.system.dal.mysql.mail.MailCodeMapper;
import cn.iocoder.yudao.module.system.enums.mail.MailSceneEnum;
import cn.iocoder.yudao.module.system.framework.mail.config.MailCodeProperties;
import cn.iocoder.yudao.module.system.service.mail.MailSendService;
import org.springframework.stereotype.Service;
import org.springframework.validation.annotation.Validated;

import javax.annotation.Resource;
import java.time.LocalDateTime;

import cn.hutool.core.date.DateUtil;
import static cn.hutool.core.util.RandomUtil.randomInt;
import static cn.iocoder.yudao.framework.common.exception.util.ServiceExceptionUtil.exception;
import static cn.iocoder.yudao.module.system.enums.ErrorCodeConstants.*;

/**
 * 邮件验证码 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class MailCodeServiceImpl implements MailCodeService {

    @Resource
    private MailCodeProperties mailCodeProperties;

    @Resource
    private MailCodeMapper mailCodeMapper;

    @Resource
    private MailSendService mailSendService;

    @Override
    public void sendMailCode(MailCodeSendReqDTO reqDTO) {
        MailSceneEnum sceneEnum = MailSceneEnum.getCodeByScene(reqDTO.getScene());
        Assert.notNull(sceneEnum, "验证码场景({}) 查找不到配置", reqDTO.getScene());
        // 创建验证码
        String code = createMailCode(reqDTO.getEmail(), reqDTO.getScene(), reqDTO.getCreateIp());
        // 发送验证码
        mailSendService.sendSingleMail(reqDTO.getEmail(), null, null,
                sceneEnum.getTemplateCode(), MapUtil.of("code", code));
    }

    private String createMailCode(String email, Integer scene, String ip) {
        // 校验是否可以发送验证码，不用筛选场景
        MailCodeDO lastMailCode = mailCodeMapper.selectLastByEmail(email, null, null);
        if (lastMailCode != null) {
            if (LocalDateTimeUtil.between(lastMailCode.getCreateTime(), LocalDateTime.now()).toMillis()
                    < mailCodeProperties.getSendFrequency().toMillis()) { // 发送过于频繁
                throw exception(MAIL_CODE_SEND_TOO_FAST);
            }
            if (DateUtil.isSameDay(DateUtil.date(lastMailCode.getCreateTime()), DateUtil.date()) && // 必须是今天，才能计算超过当天的上限
                    lastMailCode.getTodayIndex() >= mailCodeProperties.getSendMaximumQuantityPerDay()) { // 超过当天发送的上限。
                throw exception(MAIL_CODE_EXCEED_SEND_MAXIMUM_QUANTITY_PER_DAY);
            }
            // TODO 芋艿：提升，每个 IP 每天可发送数量
            // TODO 芋艿：提升，每个 IP 每小时可发送数量
        }

        // 创建验证码记录
        String code = String.format("%0" + mailCodeProperties.getEndCode().toString().length() + "d",
                randomInt(mailCodeProperties.getBeginCode(), mailCodeProperties.getEndCode() + 1));
        MailCodeDO newMailCode = MailCodeDO.builder().email(email).code(code).scene(scene)
                .todayIndex(lastMailCode != null && DateUtil.isSameDay(DateUtil.date(lastMailCode.getCreateTime()), DateUtil.date()) ? lastMailCode.getTodayIndex() + 1 : 1)
                .createIp(ip).used(false).build();
        mailCodeMapper.insert(newMailCode);
        return code;
    }

    @Override
    public void useMailCode(MailCodeUseReqDTO reqDTO) {
        // 检测验证码是否有效
        MailCodeDO lastMailCode = validateMailCode0(reqDTO.getEmail(), reqDTO.getCode(), reqDTO.getScene());
        // 使用验证码
        mailCodeMapper.updateById(MailCodeDO.builder().id(lastMailCode.getId())
                .used(true).usedTime(LocalDateTime.now()).usedIp(reqDTO.getUsedIp()).build());
    }

    @Override
    public void validateMailCode(MailCodeValidateReqDTO reqDTO) {
        validateMailCode0(reqDTO.getEmail(), reqDTO.getCode(), reqDTO.getScene());
    }

    /**
     * 检查验证码是否有效
     *
     * @param email 邮箱
     * @param code 验证码
     * @param scene 发送场景
     * @return 邮件验证码
     */
    public MailCodeDO validateMailCode0(String email, String code, Integer scene) {
        // 校验验证码
        MailCodeDO mailCode = mailCodeMapper.selectByEmailAndCode(email, code, scene);
        // 不存在
        if (mailCode == null) {
            throw exception(MAIL_CODE_NOT_FOUND);
        }
        // 已使用
        if (mailCode.getUsed()) {
            throw exception(MAIL_CODE_USED);
        }
        // 已过期
        if (LocalDateTimeUtil.between(mailCode.getCreateTime(), LocalDateTime.now()).toMillis()
                >= mailCodeProperties.getExpireTimes().toMillis()) {
            throw exception(MAIL_CODE_EXPIRED);
        }
        return mailCode;
    }

}
