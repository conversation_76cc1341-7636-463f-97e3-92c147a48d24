package cn.iocoder.yudao.module.system.service.crypto;

import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.module.system.api.application.dto.MemberInviterRespDTO;
import cn.iocoder.yudao.module.system.controller.admin.application.vo.ApplicationPageReqVO;
import cn.iocoder.yudao.module.system.controller.admin.application.vo.ApplicationSaveReqVO;
import cn.iocoder.yudao.module.system.controller.app.crypto.vo.AppCryptoRespVO;
import cn.iocoder.yudao.module.system.dal.dataobject.application.ApplicationDO;

import javax.validation.Valid;

/**
 * 系统应用 Service 接口
 *
 * <AUTHOR>
 */
public interface CryptoService {
    AppCryptoRespVO generateSecretKey(String beanName);

    String getPrivateKey(String flag);
}