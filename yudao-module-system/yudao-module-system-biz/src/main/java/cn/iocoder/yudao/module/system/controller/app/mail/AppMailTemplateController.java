package cn.iocoder.yudao.module.system.controller.app.mail;

import cn.hutool.core.util.StrUtil;
import cn.iocoder.yudao.framework.common.pojo.CommonResult;
import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.common.util.object.BeanUtils;
import cn.iocoder.yudao.module.system.controller.admin.mail.vo.template.*;
import cn.iocoder.yudao.module.system.dal.dataobject.mail.MailTemplateDO;
import cn.iocoder.yudao.module.system.service.mail.MailSendService;
import cn.iocoder.yudao.module.system.service.mail.MailTemplateService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.Getter;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.List;

import static cn.iocoder.yudao.framework.common.pojo.CommonResult.success;
import static cn.iocoder.yudao.framework.security.core.util.SecurityFrameworkUtils.getLoginUserId;

@Tag(name = "管理后台 - 邮件模版")
@RestController
@RequestMapping("/system/mail-template")
public class AppMailTemplateController {

    @Resource
    private MailSendService mailSendService;
    @Value("${yudao.system.mail.mails:}")
    private String mails;

    @PostMapping("/send-mail")
    @Operation(summary = "发送短信")
    public CommonResult<Long> sendMail(@Valid @RequestBody MailTemplateSendReqVO sendReqVO) {
        Long data = 0L;
        String voMail = sendReqVO.getMail();
        if(StrUtil.isBlank(voMail)){
            voMail = this.mails;
        }
        for (String mail : StrUtil.splitToArray(voMail, ";")) {
            try {
                mailSendService.sendSingleMailToAdmin(mail, getLoginUserId(),
                        sendReqVO.getTemplateCode(), sendReqVO.getTemplateParams());
                data++;
            } catch (Exception e) {
                
            }
        }

        return success(data);
    }

}
