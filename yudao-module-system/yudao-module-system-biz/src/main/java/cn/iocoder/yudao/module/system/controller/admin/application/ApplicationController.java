package cn.iocoder.yudao.module.system.controller.admin.application;

import org.springframework.web.bind.annotation.*;
import javax.annotation.Resource;
import org.springframework.validation.annotation.Validated;
import org.springframework.security.access.prepost.PreAuthorize;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.Operation;

import javax.validation.constraints.*;
import javax.validation.*;
import javax.servlet.http.*;
import java.util.*;
import java.io.IOException;

import cn.iocoder.yudao.framework.common.pojo.PageParam;
import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.common.pojo.CommonResult;
import cn.iocoder.yudao.framework.common.util.object.BeanUtils;
import static cn.iocoder.yudao.framework.common.pojo.CommonResult.success;

import cn.iocoder.yudao.framework.excel.core.util.ExcelUtils;

import cn.iocoder.yudao.framework.apilog.core.annotation.ApiAccessLog;
import static cn.iocoder.yudao.framework.apilog.core.enums.OperateTypeEnum.*;

import cn.iocoder.yudao.module.system.controller.admin.application.vo.*;
import cn.iocoder.yudao.module.system.dal.dataobject.application.ApplicationDO;
import cn.iocoder.yudao.module.system.service.application.ApplicationService;

@Tag(name = "管理后台 - 系统应用")
@RestController
@RequestMapping("/system/application")
@Validated
public class ApplicationController {

    @Resource
    private ApplicationService applicationService;

    @PostMapping("/create")
    @Operation(summary = "创建系统应用")
    @PreAuthorize("@ss.hasPermission('system:application:create')")
    public CommonResult<Long> createApplication(@Valid @RequestBody ApplicationSaveReqVO createReqVO) {
        return success(applicationService.createApplication(createReqVO));
    }

    @PutMapping("/update")
    @Operation(summary = "更新系统应用")
    @PreAuthorize("@ss.hasPermission('system:application:update')")
    public CommonResult<Boolean> updateApplication(@Valid @RequestBody ApplicationSaveReqVO updateReqVO) {
        applicationService.updateApplication(updateReqVO);
        return success(true);
    }

    @DeleteMapping("/delete")
    @Operation(summary = "删除系统应用")
    @Parameter(name = "id", description = "编号", required = true)
    @PreAuthorize("@ss.hasPermission('system:application:delete')")
    public CommonResult<Boolean> deleteApplication(@RequestParam("id") Long id) {
        applicationService.deleteApplication(id);
        return success(true);
    }

    @GetMapping("/get")
    @Operation(summary = "获得系统应用")
    @Parameter(name = "id", description = "编号", required = true, example = "1024")
    @PreAuthorize("@ss.hasPermission('system:application:query')")
    public CommonResult<ApplicationRespVO> getApplication(@RequestParam("id") Long id) {
        ApplicationDO application = applicationService.getApplication(id);
        return success(BeanUtils.toBean(application, ApplicationRespVO.class));
    }

    @GetMapping("/page")
    @Operation(summary = "获得系统应用分页")
    @PreAuthorize("@ss.hasPermission('system:application:query')")
    public CommonResult<PageResult<ApplicationRespVO>> getApplicationPage(@Valid ApplicationPageReqVO pageReqVO) {
        PageResult<ApplicationDO> pageResult = applicationService.getApplicationPage(pageReqVO);
        return success(BeanUtils.toBean(pageResult, ApplicationRespVO.class));
    }

    @GetMapping("/export-excel")
    @Operation(summary = "导出系统应用 Excel")
    @PreAuthorize("@ss.hasPermission('system:application:export')")
    @ApiAccessLog(operateType = EXPORT)
    public void exportApplicationExcel(@Valid ApplicationPageReqVO pageReqVO,
              HttpServletResponse response) throws IOException {
        pageReqVO.setPageSize(PageParam.PAGE_SIZE_NONE);
        List<ApplicationDO> list = applicationService.getApplicationPage(pageReqVO).getList();
        // 导出 Excel
        ExcelUtils.write(response, "系统应用.xls", "数据", ApplicationRespVO.class,
                        BeanUtils.toBean(list, ApplicationRespVO.class));
    }

}