package cn.iocoder.yudao.module.system.controller.admin.application.vo;

import lombok.*;
import java.util.*;
import io.swagger.v3.oas.annotations.media.Schema;
import cn.iocoder.yudao.framework.common.pojo.PageParam;
import org.springframework.format.annotation.DateTimeFormat;
import java.time.LocalDateTime;

import static cn.iocoder.yudao.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;

@Schema(description = "管理后台 - 系统应用分页 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class ApplicationPageReqVO extends PageParam {

    @Schema(description = "应用名称", example = "赵六")
    private String appName;

    @Schema(description = "应用代码")
    private String appCode;

    @Schema(description = "应用入口地址", example = "https://www.iocoder.cn")
    private String appPortalUrl;

    @Schema(description = "应用描述", example = "你猜")
    private String appDescription;

    @Schema(description = "强制邀请")
    private Boolean forceInvitation;

    @Schema(description = "邀请积分")
    private Integer inviterPoints;

    @Schema(description = "API签名密钥")
    private String apiSignatureSecret;

    @Schema(description = "文件配置编号", example = "18078")
    private Long fileConfigId;

    @Schema(description = "文件临时时间")
    private Integer fileTempDuration;

    @Schema(description = "创建时间")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime[] createTime;

}