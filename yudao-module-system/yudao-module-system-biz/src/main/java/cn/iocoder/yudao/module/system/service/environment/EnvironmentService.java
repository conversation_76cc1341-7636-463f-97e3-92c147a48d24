package cn.iocoder.yudao.module.system.service.environment;

import java.util.*;
import javax.validation.*;
import cn.iocoder.yudao.module.system.controller.admin.environment.vo.*;
import cn.iocoder.yudao.module.system.dal.dataobject.environment.EnvironmentDO;
import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.common.pojo.PageParam;

/**
 * 租户配置 Service 接口
 *
 * <AUTHOR>
 */
public interface EnvironmentService {

    /**
     * 创建租户配置
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    Long createEnvironment(@Valid EnvironmentSaveReqVO createReqVO);

    /**
     * 更新租户配置
     *
     * @param updateReqVO 更新信息
     */
    void updateEnvironment(@Valid EnvironmentSaveReqVO updateReqVO);

    /**
     * 删除租户配置
     *
     * @param id 编号
     */
    void deleteEnvironment(Long id);

    /**
     * 获得租户配置
     *
     * @param id 编号
     * @return 租户配置
     */
    EnvironmentDO getEnvironment(Long id);

    /**
     * 获得租户配置分页
     *
     * @param pageReqVO 分页查询
     * @return 租户配置分页
     */
    PageResult<EnvironmentDO> getEnvironmentPage(EnvironmentPageReqVO pageReqVO);

    /**
     * 获得参数配置列表
     * @param category
     * @param visible
     * @return
     */
    public List<EnvironmentDO> selectListByTypeAndCategoryAndVisible( String category, Boolean visible);

}