# 去重文件API使用说明

## 📖 概述

去重文件API是一个独立的文件管理服务，专门为第三方模块提供智能去重的文件上传、管理和访问功能。

## 🎯 核心特性

- **智能去重**：自动检测重复文件，节约存储空间
- **自动压缩**：定时压缩文件，进一步节约空间
- **引用计数**：支持多个业务引用同一个物理文件
- **批量操作**：支持批量上传、查询、删除
- **统计信息**：提供详细的去重和压缩统计

## 🔌 API接口

### 1. 上传文件

```java
@Resource
private DeduplicationFileApi deduplicationFileApi;

// 上传文件
DeduplicationFileUploadReqDTO uploadReq = new DeduplicationFileUploadReqDTO();
uploadReq.setName("example.jpg");
uploadReq.setContent(fileBytes);
uploadReq.setType("image/jpeg");

DeduplicationFileRespDTO result = deduplicationFileApi.uploadFile(uploadReq);
```

### 2. 获取文件信息

```java
// 单个文件
DeduplicationFileRespDTO file = deduplicationFileApi.getFile(fileId);

// 批量获取
List<Long> fileIds = Arrays.asList(1L, 2L, 3L);
Map<Long, DeduplicationFileRespDTO> files = deduplicationFileApi.getFiles(fileIds);
```

### 3. 获取文件URL

```java
// 单个URL
String url = deduplicationFileApi.getFileUrl(fileId);

// 批量获取URL
Map<Long, String> urls = deduplicationFileApi.getFileUrls(fileIds);
```

### 4. 删除文件

```java
// 单个删除
Boolean success = deduplicationFileApi.deleteFile(fileId);

// 批量删除
Integer deletedCount = deduplicationFileApi.deleteFiles(fileIds);
```

### 5. 获取统计信息

```java
DeduplicationStatsRespDTO stats = deduplicationFileApi.getDeduplicationStats();
System.out.println("去重率: " + stats.getDeduplicationRate() + "%");
System.out.println("存储节省率: " + stats.getStorageSaveRate() + "%");
```

## 📊 响应数据结构

### DeduplicationFileRespDTO

```json
{
  "id": 1,
  "name": "example.jpg",
  "path": "2024/1/15/1705123456789_example.jpg",
  "url": "https://example.com/2024/1/15/1705123456789_example.jpg",
  "type": "image/jpeg",
  "size": 102400,
  "originalSize": 204800,
  "isCompressed": true,
  "compressType": "image_avif",
  "compressRatio": 50.00,
  "savedSpace": 102400,
  "refCount": 3,
  "isDuplicated": true,
  "createTime": "2024-01-15T10:30:56",
  "updateTime": "2024-01-15T10:30:56"
}
```

### DeduplicationStatsRespDTO

```json
{
  "totalFileCount": 1000,
  "deduplicatedFileCount": 300,
  "deduplicationRate": 30.00,
  "totalStorageSize": 1073741824,
  "savedStorageSize": 536870912,
  "compressSavedSize": 268435456,
  "totalSavedSize": 805306368,
  "storageSaveRate": 75.00,
  "compressedFileCount": 800,
  "compressionRate": 80.00
}
```

## 🚀 使用场景

### 1. 用户头像管理

```java
// 用户上传头像
public String uploadAvatar(Long userId, MultipartFile file) {
    DeduplicationFileUploadReqDTO uploadReq = new DeduplicationFileUploadReqDTO();
    uploadReq.setName("avatar_" + userId + "_" + file.getOriginalFilename());
    uploadReq.setContent(file.getBytes());
    uploadReq.setType(file.getContentType());
    
    DeduplicationFileRespDTO result = deduplicationFileApi.uploadFile(uploadReq);
    
    // 保存用户头像关联
    userService.updateAvatar(userId, result.getId());
    
    return result.getUrl();
}
```

### 2. 商品图片管理

```java
// 批量上传商品图片
public List<String> uploadProductImages(Long productId, MultipartFile[] files) {
    List<String> imageUrls = new ArrayList<>();
    
    for (MultipartFile file : files) {
        DeduplicationFileUploadReqDTO uploadReq = new DeduplicationFileUploadReqDTO();
        uploadReq.setName("product_" + productId + "_" + file.getOriginalFilename());
        uploadReq.setContent(file.getBytes());
        uploadReq.setType(file.getContentType());
        
        DeduplicationFileRespDTO result = deduplicationFileApi.uploadFile(uploadReq);
        imageUrls.add(result.getUrl());
        
        // 保存商品图片关联
        productImageService.addImage(productId, result.getId());
    }
    
    return imageUrls;
}
```

### 3. 文档管理

```java
// 获取文档列表
public List<DocumentVO> getDocuments(List<Long> documentIds) {
    // 批量获取文件信息
    Map<Long, DeduplicationFileRespDTO> files = deduplicationFileApi.getFiles(documentIds);
    
    return documentIds.stream()
        .map(id -> {
            DeduplicationFileRespDTO file = files.get(id);
            if (file != null) {
                DocumentVO doc = new DocumentVO();
                doc.setId(id);
                doc.setName(file.getName());
                doc.setUrl(file.getUrl());
                doc.setSize(file.getSize());
                doc.setIsCompressed(file.getIsCompressed());
                doc.setSavedSpace(file.getSavedSpace());
                return doc;
            }
            return null;
        })
        .filter(Objects::nonNull)
        .collect(Collectors.toList());
}
```

## ⚡ 性能优势

1. **存储节约**：通过去重和压缩，可节约50-80%的存储空间
2. **上传加速**：重复文件秒传，大幅提升用户体验
3. **带宽节约**：压缩文件减少网络传输时间
4. **成本降低**：显著降低存储和带宽成本

## 🔧 配置说明

### 定时压缩任务

系统会自动执行定时压缩任务，无需手动配置。如需调整频率，可修改：

```java
@Scheduled(cron = "0 0 * * * ?") // 每小时执行一次
public void executeCompression() {
    // 压缩逻辑
}
```

### 压缩策略

- **图片文件**：自动转换为AVIF格式，压缩率30-70%
- **视频文件**：H.264编码优化，压缩率20-50%
- **文档文件**：PDF压缩，压缩率10-30%

## 📈 监控指标

通过统计API可以监控以下指标：

- 去重率：重复文件占总文件的比例
- 压缩率：已压缩文件占总文件的比例
- 存储节省率：节省的存储空间占原始空间的比例
- 引用计数分布：了解文件复用情况

## 🛡️ 注意事项

1. **删除机制**：删除文件时会减少引用计数，只有引用计数为0时才会物理删除
2. **压缩不可逆**：文件压缩后会替换原文件，无法恢复原始文件
3. **并发安全**：所有操作都是线程安全的，支持高并发访问
4. **事务保证**：文件操作支持事务，确保数据一致性

