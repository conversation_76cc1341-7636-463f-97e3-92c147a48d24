# 水印问题修复说明

## 🐛 问题分析

### 错误信息
```
[Parsed_drawbox_1 @ 0x7f04d8011a40] [Eval @ 0x7f04e84c4260] Undefined constant or missing '(' in 'W-w-10'
[Parsed_drawbox_1 @ 0x7f04d8011a40] [Eval @ 0x7f04e84c4260] Undefined constant or missing '(' in 'H-h)/2'
```

### 问题根因
**drawbox滤镜不支持overlay滤镜的变量**（W、H、w、h）。

在FFmpeg的滤镜链中：
- `drawbox`滤镜在执行时还没有overlay的上下文
- 无法识别`W`（主图宽度）、`H`（主图高度）、`w`（水印宽度）、`h`（水印高度）等变量
- 这些变量只在`overlay`滤镜中有效

### 错误的命令
```bash
-filter_complex "[0:v]scale='if(gt(iw,2048),2048,iw)':-1,drawbox=W-w-10:(H-h)/2:200:200:color=white@0.8:thickness=fill[main];[1:v]scale=160:-1,format=rgba,colorchannelmixer=aa=0.6[watermark];[main][watermark]overlay=W-w-10:(H-h)/2"
```

## ✅ 修复方案

### 临时修复：移除logo覆盖功能
为了先解决水印显示问题，暂时移除了logo覆盖功能：

```java
// 暂时移除logo覆盖功能，专注于修复水印问题
// TODO: 后续优化logo覆盖功能
```

### 修复后的命令
```bash
# 只有水印，没有覆盖
ffmpeg -y -i "input.png" -i "/root/caddy/www/company-logo.png" \
  -filter_complex "[0:v]scale='if(gt(iw,2048),2048,iw)':-1[main];[1:v]scale=200:-1,format=rgba,colorchannelmixer=aa=0.8[watermark];[main][watermark]overlay=W-w-10:10" \
  "output.avif"
```

## 🔧 后续优化方案

### 方案1：使用固定坐标
```bash
# 使用固定坐标，适合已知尺寸的图片
-filter_complex "[0:v]scale='if(gt(iw,2048),2048,iw)':-1,drawbox=814:10:200:100:color=white@0.8:thickness=fill[main];[1:v]scale=200:-1,format=rgba,colorchannelmixer=aa=0.8[watermark];[main][watermark]overlay=814:10"
```

### 方案2：分步处理
```bash
# 第一步：创建覆盖层
ffmpeg -f lavfi -i "color=white@0.8:size=200x100:duration=1" -vf "format=rgba" cover.png

# 第二步：先覆盖原logo
ffmpeg -i input.png -i cover.png -filter_complex "[0:v]scale='if(gt(iw,2048),2048,iw)':-1[main];[main][1:v]overlay=814:10" temp.png

# 第三步：添加水印
ffmpeg -i temp.png -i watermark.png -filter_complex "[0:v]null[main];[1:v]scale=200:-1,format=rgba,colorchannelmixer=aa=0.8[watermark];[main][watermark]overlay=814:10" output.avif
```

### 方案3：使用pad滤镜
```bash
# 使用pad滤镜创建覆盖效果
-filter_complex "[0:v]scale='if(gt(iw,2048),2048,iw)':-1,pad=iw+200:ih:200:0:white[padded];[padded]crop=iw-200:ih:0:0[main];[1:v]scale=200:-1,format=rgba,colorchannelmixer=aa=0.8[watermark];[main][watermark]overlay=W-w-10:10"
```

## 🎯 当前状态

### ✅ 已修复
- 水印功能正常工作
- 支持9宫格位置定位
- 支持固定宽度缩放
- 支持透明度控制

### ⏳ 待优化
- Logo覆盖功能（需要重新设计实现方案）
- 动态坐标计算
- 更灵活的覆盖策略

## 🧪 测试命令

### 测试水印功能
```bash
# 测试右上角水印
ffmpeg -y -i "input.png" -i "/root/caddy/www/company-logo.png" \
  -filter_complex "[0:v]scale='if(gt(iw,2048),2048,iw)':-1[main];[1:v]scale=200:-1,format=rgba,colorchannelmixer=aa=0.8[watermark];[main][watermark]overlay=W-w-10:10" \
  "output.avif"

# 测试中心水印
ffmpeg -y -i "input.png" -i "/root/caddy/www/company-logo.png" \
  -filter_complex "[0:v]scale='if(gt(iw,2048),2048,iw)':-1[main];[1:v]scale=200:-1,format=rgba,colorchannelmixer=aa=0.8[watermark];[main][watermark]overlay=(W-w)/2:(H-h)/2" \
  "output.avif"
```

现在水印功能应该可以正常工作了！Logo覆盖功能将在后续版本中重新实现。
