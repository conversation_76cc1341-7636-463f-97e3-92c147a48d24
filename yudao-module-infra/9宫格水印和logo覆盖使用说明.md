# 9宫格水印和Logo覆盖功能使用说明

## 🎯 功能概述

系统现在支持：
1. **9宫格水印定位** - 支持数字1-9或名称定位水印位置
2. **Logo覆盖功能** - 在添加水印前先覆盖原有logo
3. **灵活组合使用** - 可以只覆盖、只水印、或两者结合

## 📊 9宫格位置示意图

```
+---+---+---+
| 1 | 2 | 3 |  1=top-left, 2=top-center, 3=top-right
+---+---+---+
| 4 | 5 | 6 |  4=middle-left, 5=center, 6=middle-right  
+---+---+---+
| 7 | 8 | 9 |  7=bottom-left, 8=bottom-center, 9=bottom-right
+---+---+---+
```

## 🔧 支持的位置格式

### 数字格式（推荐）
- `"1"` - 左上角
- `"2"` - 顶部居中
- `"3"` - 右上角
- `"4"` - 左侧居中
- `"5"` - 正中央
- `"6"` - 右侧居中
- `"7"` - 左下角
- `"8"` - 底部居中
- `"9"` - 右下角

### 名称格式
- `"top-left"` / `"topleft"` - 左上角
- `"top-center"` / `"topcenter"` - 顶部居中
- `"top-right"` / `"topright"` - 右上角
- `"middle-left"` / `"middleleft"` / `"left"` - 左侧居中
- `"center"` / `"middle"` - 正中央
- `"middle-right"` / `"middleright"` / `"right"` - 右侧居中
- `"bottom-left"` / `"bottomleft"` - 左下角
- `"bottom-center"` / `"bottomcenter"` - 底部居中
- `"bottom-right"` / `"bottomright"` - 右下角

### 自定义坐标
- `"100:50"` - 自定义x:y坐标

## 🎨 使用场景示例

### 场景1：覆盖原logo并添加新水印
```java
DeduplicationFileUploadReqDTO uploadReq = new DeduplicationFileUploadReqDTO();
// 水印配置
uploadReq.setWatermarkPath("/root/caddy/www/company-logo.png");
uploadReq.setWatermarkPosition("3"); // 右上角添加新水印
uploadReq.setWatermarkOpacity(0.8);
uploadReq.setWatermarkScale(0.1);

// 覆盖原logo配置
uploadReq.setCoverPosition("1"); // 覆盖左上角的原logo
uploadReq.setCoverSize("200:100"); // 覆盖区域200x100像素
```

### 场景2：只覆盖原logo，不添加水印
```java
DeduplicationFileUploadReqDTO uploadReq = new DeduplicationFileUploadReqDTO();
// 不设置水印相关参数
uploadReq.setWatermarkPath(null);

// 只设置覆盖配置
uploadReq.setCoverPosition("9"); // 覆盖右下角的原logo
uploadReq.setCoverSize("150:80"); // 覆盖区域150x80像素
```

### 场景3：只添加水印，不覆盖
```java
DeduplicationFileUploadReqDTO uploadReq = new DeduplicationFileUploadReqDTO();
// 只设置水印配置
uploadReq.setWatermarkPath("/root/caddy/www/company-logo.png");
uploadReq.setWatermarkPosition("5"); // 正中央添加水印
uploadReq.setWatermarkOpacity(1.0); // 完全不透明
uploadReq.setWatermarkScale(0.15); // 较大尺寸(300px)

// 不设置覆盖参数
uploadReq.setCoverPosition(null);
uploadReq.setCoverSize(null);
```

## 📋 数据库字段说明

### 新增字段
```sql
-- 水印位置（支持9宫格）
watermark_position VARCHAR(20) DEFAULT 'top-right' COMMENT '水印位置：支持9宫格(1-9)或名称'

-- logo覆盖相关
cover_position VARCHAR(20) NULL COMMENT '需要覆盖的原logo位置：支持9宫格(1-9)或名称'
cover_size VARCHAR(20) NULL COMMENT '覆盖区域大小，格式width:height，如200:100'
```

## 🎬 FFmpeg命令示例

### 图片处理（覆盖+水印）
```bash
ffmpeg -y -i "input.png" -i "/root/caddy/www/company-logo.png" \
  -filter_complex "[0:v]scale='if(gt(iw,2048),2048,iw)':-1,drawbox=10:10:200:100:color=white@0.8:thickness=fill[main];[1:v]scale=200:-1,format=rgba,colorchannelmixer=aa=0.8[watermark];[main][watermark]overlay=W-w-10:10" \
  "output.avif"
```

### 视频处理（覆盖+水印）
```bash
ffmpeg -y -i "input.mp4" -i "/root/caddy/www/company-logo.png" \
  -filter_complex "[0:v]scale=1920x1080,drawbox=10:10:200:100:color=black@0.8:thickness=fill[main];[1:v]scale=200:-1,format=rgba,colorchannelmixer=aa=0.8[watermark];[main][watermark]overlay=W-w-10:10" \
  -c:v libx264 -b:v 1500k -c:a aac -b:a 128k \
  "output.mp4"
```

## 🔍 覆盖效果说明

### 覆盖颜色
- **图片**: 使用半透明白色 `color=white@0.8`
- **视频**: 使用半透明黑色 `color=black@0.8`

### 覆盖大小
- 默认: `200:100` (宽200px，高100px)
- 可自定义: 如 `300:150`, `100:50` 等
- 建议根据原logo实际大小调整

## ⚠️ 注意事项

1. **位置冲突**: 避免覆盖位置和水印位置相同
2. **尺寸合理**: 覆盖区域不要过大，避免影响主体内容
3. **颜色选择**: 
   - 图片用白色覆盖适合深色背景
   - 视频用黑色覆盖适合浅色背景
4. **透明度**: 覆盖透明度固定为0.8，既能遮盖原logo又不会太突兀

## 🚀 最佳实践

### 推荐配置组合
```java
// 企业标准配置
watermarkPosition = "3";      // 右上角水印
watermarkOpacity = 0.8;       // 适中透明度
watermarkScale = 0.1;         // 标准大小200px
coverPosition = "1";          // 覆盖左上角原logo
coverSize = "180:90";         // 略小于水印的覆盖区域
```

### 测试建议
1. 先测试只覆盖功能，确认覆盖位置和大小
2. 再测试只水印功能，确认水印效果
3. 最后组合测试，调整位置避免冲突

现在你可以灵活地处理各种logo覆盖和水印需求了！
