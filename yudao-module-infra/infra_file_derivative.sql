-- 文件衍生版本表
CREATE TABLE `infra_file_derivative` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '衍生版本ID',
  `original_file_id` bigint NOT NULL COMMENT '原始文件ID',
  `derivative_type` varchar(50) NOT NULL COMMENT '衍生类型：COMPRESSED-压缩版本，WATERMARKED-水印版本，COMPRESSED_WATERMARKED-压缩+水印版本，HLS-HLS切片版本',
  `file_id` bigint NOT NULL COMMENT '衍生文件ID（关联infra_file_index表）',
  `processing_params` json COMMENT '处理参数JSON',
  `file_size` bigint DEFAULT NULL COMMENT '文件大小（字节）',
  `quality` varchar(20) DEFAULT NULL COMMENT '质量等级：ORIGINAL-原始，HIGH-高清，MEDIUM-标清，LOW-低清',
  `resolution` varchar(20) DEFAULT NULL COMMENT '分辨率：如1920x1080',
  `bitrate` int DEFAULT NULL COMMENT '比特率（kbps）',
  `duration` int DEFAULT NULL COMMENT '时长（秒，视频文件）',
  `format` varchar(10) DEFAULT NULL COMMENT '格式：mp4,avif,webp,m3u8等',
  `status` tinyint NOT NULL DEFAULT '0' COMMENT '状态：0-处理中，1-成功，2-失败',
  `error_message` text COMMENT '错误信息',
  `creator` varchar(64) DEFAULT '' COMMENT '创建者',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updater` varchar(64) DEFAULT '' COMMENT '更新者',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `deleted` bit(1) NOT NULL DEFAULT b'0' COMMENT '是否删除',
  `tenant_id` bigint NOT NULL DEFAULT '0' COMMENT '租户编号',
  PRIMARY KEY (`id`),
  KEY `idx_original_file_id` (`original_file_id`),
  KEY `idx_derivative_type` (`derivative_type`),
  KEY `idx_quality` (`quality`),
  KEY `idx_status` (`status`),
  KEY `idx_tenant_id` (`tenant_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='文件衍生版本表';

-- 用户云盘表
CREATE TABLE `infra_user_cloud_disk` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '云盘记录ID',
  `user_id` bigint NOT NULL COMMENT '用户ID',
  `file_id` bigint NOT NULL COMMENT '文件ID（关联infra_file_index表）',
  `parent_id` bigint DEFAULT '0' COMMENT '父目录ID，0表示根目录',
  `file_name` varchar(255) NOT NULL COMMENT '用户自定义文件名',
  `file_path` varchar(1000) DEFAULT NULL COMMENT '文件路径',
  `is_folder` bit(1) NOT NULL DEFAULT b'0' COMMENT '是否为文件夹',
  `folder_type` varchar(20) DEFAULT NULL COMMENT '文件夹类型：ROOT-根目录，CUSTOM-自定义目录',
  `file_size` bigint DEFAULT NULL COMMENT '文件大小（字节）',
  `file_type` varchar(50) DEFAULT NULL COMMENT '文件类型',
  `tags` json DEFAULT NULL COMMENT '文件标签',
  `description` varchar(500) DEFAULT NULL COMMENT '文件描述',
  `is_favorite` bit(1) NOT NULL DEFAULT b'0' COMMENT '是否收藏',
  `is_shared` bit(1) NOT NULL DEFAULT b'0' COMMENT '是否共享',
  `share_code` varchar(32) DEFAULT NULL COMMENT '分享码',
  `share_expire_time` datetime DEFAULT NULL COMMENT '分享过期时间',
  `download_count` int NOT NULL DEFAULT '0' COMMENT '下载次数',
  `view_count` int NOT NULL DEFAULT '0' COMMENT '查看次数',
  `creator` varchar(64) DEFAULT '' COMMENT '创建者',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updater` varchar(64) DEFAULT '' COMMENT '更新者',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `deleted` bit(1) NOT NULL DEFAULT b'0' COMMENT '是否删除',
  `tenant_id` bigint NOT NULL DEFAULT '0' COMMENT '租户编号',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_user_file_path` (`user_id`, `file_path`, `deleted`),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_parent_id` (`parent_id`),
  KEY `idx_file_id` (`file_id`),
  KEY `idx_is_folder` (`is_folder`),
  KEY `idx_is_favorite` (`is_favorite`),
  KEY `idx_share_code` (`share_code`),
  KEY `idx_tenant_id` (`tenant_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='用户云盘表';

-- 文件处理任务表
CREATE TABLE `infra_file_processing_task` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '任务ID',
  `task_id` varchar(64) NOT NULL COMMENT '任务唯一标识',
  `original_file_id` bigint NOT NULL COMMENT '原始文件ID',
  `processing_types` json NOT NULL COMMENT '处理类型数组：["COMPRESS", "WATERMARK", "HLS"]',
  `processing_params` json COMMENT '处理参数JSON',
  `status` tinyint NOT NULL DEFAULT '0' COMMENT '状态：0-待处理，1-处理中，2-成功，3-失败',
  `progress` int NOT NULL DEFAULT '0' COMMENT '进度百分比',
  `result_file_ids` json DEFAULT NULL COMMENT '结果文件ID数组',
  `error_message` text COMMENT '错误信息',
  `start_time` datetime DEFAULT NULL COMMENT '开始时间',
  `end_time` datetime DEFAULT NULL COMMENT '结束时间',
  `creator` varchar(64) DEFAULT '' COMMENT '创建者',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updater` varchar(64) DEFAULT '' COMMENT '更新者',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `deleted` bit(1) NOT NULL DEFAULT b'0' COMMENT '是否删除',
  `tenant_id` bigint NOT NULL DEFAULT '0' COMMENT '租户编号',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_task_id` (`task_id`),
  KEY `idx_original_file_id` (`original_file_id`),
  KEY `idx_status` (`status`),
  KEY `idx_tenant_id` (`tenant_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='文件处理任务表';
