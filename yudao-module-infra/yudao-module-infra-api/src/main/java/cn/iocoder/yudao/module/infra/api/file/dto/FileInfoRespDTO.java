package cn.iocoder.yudao.module.infra.api.file.dto;

import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 文件信息 Response DTO
 *
 * <AUTHOR>
 */
@Data
public class FileInfoRespDTO {

    /**
     * 文件ID
     */
    private Long id;

    /**
     * 文件名称
     */
    private String name;

    /**
     * 文件路径
     */
    private String path;

    /**
     * 文件访问URL
     */
    private String url;

    /**
     * 文件类型
     */
    private String type;

    /**
     * 文件大小（字节）
     */
    private Long size;

    /**
     * 原始文件大小（字节）
     */
    private Long originalSize;

    /**
     * 是否已压缩
     */
    private Boolean isCompressed;

    /**
     * 压缩类型
     */
    private String compressType;

    /**
     * 压缩比例（百分比）
     */
    private BigDecimal compressRatio;

    /**
     * 节省的存储空间（字节）
     */
    private Long savedSpace;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    private LocalDateTime updateTime;

}
