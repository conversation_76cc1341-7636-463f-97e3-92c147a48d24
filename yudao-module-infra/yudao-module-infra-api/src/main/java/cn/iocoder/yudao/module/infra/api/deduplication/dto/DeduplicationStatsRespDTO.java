package cn.iocoder.yudao.module.infra.api.deduplication.dto;

import lombok.Data;

import java.math.BigDecimal;

/**
 * 去重统计信息 Response DTO
 *
 * <AUTHOR>
 */
@Data
public class DeduplicationStatsRespDTO {

    /**
     * 总文件数量
     */
    private Long totalFileCount;

    /**
     * 去重文件数量（引用计数>1的文件）
     */
    private Long deduplicatedFileCount;

    /**
     * 去重率（百分比）
     */
    private BigDecimal deduplicationRate;

    /**
     * 总存储大小（字节）
     */
    private Long totalStorageSize;

    /**
     * 去重节省的存储空间（字节）
     */
    private Long savedStorageSize;

    /**
     * 压缩节省的存储空间（字节）
     */
    private Long compressSavedSize;

    /**
     * 总节省的存储空间（字节）
     */
    private Long totalSavedSize;

    /**
     * 存储节省率（百分比）
     */
    private BigDecimal storageSaveRate;

    /**
     * 已压缩文件数量
     */
    private Long compressedFileCount;

    /**
     * 压缩率（百分比）
     */
    private BigDecimal compressionRate;

}
