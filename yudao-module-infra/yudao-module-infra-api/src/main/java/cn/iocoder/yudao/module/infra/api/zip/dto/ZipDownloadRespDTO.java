package cn.iocoder.yudao.module.infra.api.zip.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;
import java.util.List;

/**
 * ZIP下载响应DTO
 *
 * <AUTHOR>
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ZipDownloadRespDTO {

//    @Schema(description = "ZIP文件路径")
    private String zipFilePath;

//    @Schema(description = "ZIP文件大小（字节）")
    private Long zipFileSize;

//    @Schema(description = "ZIP文件名")
    private String zipFileName;

//    @Schema(description = "下载成功的文件数量")
    private Integer successCount;

//    @Schema(description = "下载失败的文件数量")
    private Integer failedCount;

//    @Schema(description = "总文件数量")
    private Integer totalCount;

//    @Schema(description = "下载开始时间")
    private LocalDateTime startTime;

//    @Schema(description = "下载结束时间")
    private LocalDateTime endTime;

//    @Schema(description = "总耗时（毫秒）")
    private Long totalTimeMs;

//    @Schema(description = "成功下载的文件列表")
    private List<FileDownloadResult> successFiles;

//    @Schema(description = "失败下载的文件列表")
    private List<FileDownloadResult> failedFiles;

    /**
     * 文件下载结果
     */
//    @Schema(description = "文件下载结果")
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class FileDownloadResult {

//        @Schema(description = "原始URL")
        private String url;

//        @Schema(description = "ZIP内文件名")
        private String fileName;

//        @Schema(description = "文件大小（字节）")
        private Long fileSize;

//        @Schema(description = "下载状态：SUCCESS-成功，FAILED-失败")
        private String status;

//        @Schema(description = "错误信息（失败时）")
        private String errorMessage;

//        @Schema(description = "下载耗时（毫秒）")
        private Long downloadTimeMs;

    }

    /**
     * 计算成功率
     */
    public double getSuccessRate() {
        if (totalCount == null || totalCount == 0) {
            return 0.0;
        }
        return (double) successCount / totalCount * 100;
    }

    /**
     * 获取平均下载速度（KB/s）
     */
    public double getAverageSpeed() {
        if (totalTimeMs == null || totalTimeMs == 0 || zipFileSize == null) {
            return 0.0;
        }
        return (double) zipFileSize / 1024 / (totalTimeMs / 1000.0);
    }

}
