package cn.iocoder.yudao.module.infra.api.deduplication.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;

/**
 * 去重文件上传响应DTO
 *
 * <AUTHOR>
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class DeduplicationFileUploadRespDTO {

    /**
     * 文件ID
     */
    private Long fileId;

    /**
     * 文件URL
     */
    private String url;

    /**
     * 文件名
     */
    private String name;

    /**
     * 文件大小
     */
    private Long size;

    /**
     * 文件类型
     */
    private String type;

    /**
     * 是否为去重文件
     */
    private Boolean isDuplicate;

    /**
     * 原始文件ID
     */
    private Long originalFileId;

    /**
     * 处理任务ID（异步处理）
     */
    private String processingTaskId;

    /**
     * 云盘记录ID（如果保存到云盘）
     */
    private Long cloudDiskId;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

}
