package cn.iocoder.yudao.module.infra.api.file;

import cn.iocoder.yudao.module.infra.api.file.dto.FileConfigRespDTO;

import java.time.Duration;

/**
 * 文件 API 接口
 *
 * <AUTHOR>
 */
public interface FileApi {

    /**
     * 获得指定编号的文件客户端
     *
     * @param id 配置编号
     * @return 文件客户端
     */
    FileConfigRespDTO getFileConfig(Long id);


    /**
     * 保存文件，并返回文件的访问路径
     *
     * @param content 文件内容
     * @return 文件路径
     */
    default String createFile(byte[] content) {
        return createFile(null, null, content);
    }

    /**
     * 保存文件，并返回文件的访问路径
     *
     * @param path 文件路径
     * @param content 文件内容
     * @return 文件路径
     */
    default String createFile(String path, byte[] content) {
        return createFile(null, path, content);
    }

    /**
     * 保存文件，并返回文件的访问路径
     *
     * @param name 文件名称
     * @param path 文件路径
     * @param content 文件内容
     * @return 文件路径
     */
    String createFile(String name, String path, byte[] content);

    /**
     * 获得文件内容
     *
     * @param configId 配置编号
     * @param path     文件路径
     * @return 文件内容
     */
    byte[] getFileContent(Long configId, String path) throws Exception;

    /**
     * 生成有效期内可重复下载文件url<br/>
     * @param configId 配置编号
     * @param path     原来文件路径
     * @param timeout  有效期
     * @return 临时下载url
     */
    String genRepeatTempDownloadUrl(Long configId, String path, Duration timeout) ;

    /**
     * 生成有效期内一次性下载文件url<br/>
     * @param configId 配置编号
     * @param path     原来文件路径
     * @param timeout  有效期
     * @return 临时下载url
     */
    String genOnceTempDownloadUrl(Long configId, String path, Duration timeout);

}
