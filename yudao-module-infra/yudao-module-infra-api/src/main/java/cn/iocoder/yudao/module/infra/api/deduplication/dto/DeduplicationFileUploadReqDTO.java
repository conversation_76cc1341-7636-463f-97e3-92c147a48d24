package cn.iocoder.yudao.module.infra.api.deduplication.dto;

import lombok.Data;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;

/**
 * 去重文件上传 Request DTO
 *
 * <AUTHOR>
 */
@Data
public class DeduplicationFileUploadReqDTO {

    /**
     * 文件名称
     */
    @NotEmpty(message = "文件名称不能为空")
    private String name;

    /**
     * 文件路径
     */
    private String path;

    /**
     * 文件内容
     */
    @NotNull(message = "文件内容不能为空")
    private byte[] content;

    /**
     * 文件类型
     */
    private String type;

    /**
     * 存储配置ID（可选，不传则使用默认配置）
     */
    private Long configId;

}
