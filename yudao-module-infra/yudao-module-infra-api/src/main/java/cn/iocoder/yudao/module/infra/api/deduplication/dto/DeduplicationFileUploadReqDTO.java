package cn.iocoder.yudao.module.infra.api.deduplication.dto;

import lombok.Data;

import javax.validation.constraints.NotEmpty;
import java.io.InputStream;

/**
 * 去重文件上传 Request DTO
 * 支持流式上传，避免大文件OOM
 *
 * <AUTHOR>
 */
@Data
public class DeduplicationFileUploadReqDTO {

    /**
     * 文件名称
     */
    @NotEmpty(message = "文件名称不能为空")
    private String name;

    /**
     * 文件路径
     */
    private String path;

    /**
     * 文件输入流（优先使用，支持大文件）
     */
    private InputStream inputStream;

    /**
     * 文件内容（小文件可用，大文件建议使用inputStream）
     */
    private byte[] content;

    /**
     * 文件大小（字节）
     */
    private Long contentLength;

    /**
     * 文件类型
     */
    private String type;

    /**
     * 存储配置ID（可选，不传则使用默认配置）
     */
    private Long configId;

    /**
     * 是否强制使用流式上传（即使有content也使用inputStream）
     */
    private Boolean forceStreamUpload = false;

    /**
     * 是否同步压缩（上传后立即压缩）
     */
    private Boolean syncCompress = false;

    /**
     * 压缩类型（可选，不指定则自动判断）
     */
    private String compressType;

    /**
     * 压缩配置参数（JSON格式）
     */
    private String compressConfig;

    /**
     * 获取有效的输入流
     */
    public InputStream getEffectiveInputStream() {
        if (inputStream != null) {
            return inputStream;
        }
        if (content != null) {
            return new java.io.ByteArrayInputStream(content);
        }
        return null;
    }

    /**
     * 获取有效的文件大小
     */
    public Long getEffectiveContentLength() {
        if (contentLength != null) {
            return contentLength;
        }
        if (content != null) {
            return (long) content.length;
        }
        return null;
    }

    /**
     * 是否应该使用流式上传
     */
    public boolean shouldUseStreamUpload() {
        // 强制流式上传
        if (Boolean.TRUE.equals(forceStreamUpload)) {
            return true;
        }
        // 有输入流时使用流式上传
        if (inputStream != null) {
            return true;
        }
        // 大文件（>10MB）使用流式上传
        if (content != null && content.length > 10 * 1024 * 1024) {
            return true;
        }
        return false;
    }

}
