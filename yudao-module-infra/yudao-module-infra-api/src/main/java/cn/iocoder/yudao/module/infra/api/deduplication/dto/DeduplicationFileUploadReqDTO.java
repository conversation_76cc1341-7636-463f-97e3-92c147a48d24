package cn.iocoder.yudao.module.infra.api.deduplication.dto;

import cn.hutool.core.io.FileUtil;
import cn.hutool.core.io.IoUtil;
import cn.hutool.core.util.IdUtil;
import cn.iocoder.yudao.framework.common.util.io.FileUtils;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.SneakyThrows;

import javax.validation.constraints.NotEmpty;
import java.io.File;
import java.io.FileInputStream;
import java.io.InputStream;
import java.util.function.Function;
import java.util.function.Supplier;

/**
 * 去重文件上传 Request DTO
 * 支持流式上传，避免大文件OOM
 * +---+---+---+
 * | 1 | 2 | 3 |  1=top-left, 2=top-center, 3=top-right
 * +---+---+---+
 * | 4 | 5 | 6 |  4=middle-left, 5=center, 6=middle-right
 * +---+---+---+
 * | 7 | 8 | 9 |  7=bottom-left, 8=bottom-center, 9=bottom-right
 * +---+---+---+
 * <AUTHOR>
 */
@Data
public class DeduplicationFileUploadReqDTO {

    /**
     * 文件名称
     */
    @NotEmpty(message = "文件名称不能为空")
    private String name;

    /**
     * 文件路径
     */
    private String path;

    /**
     * 文件输入流（优先使用，支持大文件）
     */
//    private InputStream inputStream;

    /**
     * 文件内容（小文件可用，大文件建议使用inputStream）
     */
//    private byte[] content;
    /**
     * 统一用这个：临时文件（可重复使用inputStream,且不会内存溢出）
     */
    private java.io.File tempFile;

    /**
     * 文件大小（字节）
     */
    private Long contentLength;

    /**
     * 文件类型
     */
    private String type;

    /**
     * 存储配置ID（可选，不传则使用默认配置）
     */
    private Long configId;

    /**
     * 是否自动清理临时上传文件。 成功删除,失败保留
     */
    private Boolean autoCleanTempFile = true;

    /**
     * 是否强制使用流式上传（即使有content也使用inputStream）
     */
    private Boolean forceStreamUpload = false;

    /**
     * 是否同步压缩（上传后立即压缩）
     */
    private Boolean syncCompress = false;

    /**
     * 压缩类型（可选，不指定则自动判断）
     */
    private String compressType;

    /**
     * 压缩配置参数（JSON格式）
     */
    private String compressConfig;

    /**
     * 水印图片路径（可选）
     */
    private String watermarkPath;

    /**
     * 水印位置（可选）：top-left, top-right, bottom-left, bottom-right, center
     * 默认为 top-right
     */
    private String watermarkPosition = "top-right";

    /**
     * 水印透明度（0.0-1.0，默认0.5）
     */
    private Double watermarkOpacity = 0.5;

    /**
     * 水印大小比例（相对于原图的比例，默认0.1即10%）
     */
    private Double watermarkScale = 0.1;

    /**
     * 需要覆盖的原logo位置（9宫格：1-9 或 top-left等）
     */
    private String coverPosition;

    /**
     * 覆盖区域大小，格式"width:height"，如"200:100"
     */
    private String coverSize;

    /**
     * 文件处理选项
     */
    private FileProcessingOptions processingOptions;

    /**
     * 用户云盘选项
     */
    private CloudDiskOptions cloudDiskOptions;

    /**
     * 获取有效的输入流
     */
    public InputStream getEffectiveInputStream() {
        return IoUtil.toStream(this.tempFile);
    }

    /**
     * 获取有效的文件大小
     */
    public Long getEffectiveContentLength() {
        if (contentLength != null) {
            return contentLength;
        }
        return FileUtil.size(this.tempFile);
    }

    public void setInputStream(InputStream inputStream){
        this.setInputStream(inputStream,true);
    }
    public void setInputStream(InputStream inputStream, boolean isCloseIn){
        this.tempFile = FileUtil.file(FileUtil.getTmpDir(), name);
        // 标记 JVM 退出时，自动删除
        this.tempFile.deleteOnExit();
        //
        FileUtil.writeFromStream(inputStream,this.tempFile,isCloseIn);
    }

    public void setContent(byte[] content){
        this.tempFile = FileUtil.file(FileUtil.getTmpDir(), name);
        // 标记 JVM 退出时，自动删除
        this.tempFile.deleteOnExit();
        //
        FileUtil.writeBytes(content,this.tempFile);
    }

    /**
     * 文件处理选项
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class FileProcessingOptions {

        /**
         * 是否启用压缩
         */
        private Boolean enableCompress = false;

        /**
         * 是否启用水印
         */
        private Boolean enableWatermark = false;

        /**
         * 是否启用HLS切片（视频文件）
         */
        private Boolean enableHls = false;

        /**
         * 是否异步处理
         */
        private Boolean asyncProcessing = true;

        /**
         * HLS质量配置
         */
        private HlsQualityConfig hlsConfig;
    }

    /**
     * HLS质量配置
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class HlsQualityConfig {

        /**
         * 是否生成高清版本
         */
        private Boolean enableHigh = true;

        /**
         * 是否生成标清版本
         */
        private Boolean enableMedium = true;

        /**
         * 是否生成低清版本
         */
        private Boolean enableLow = false;

        /**
         * 高清分辨率
         */
        private String highResolution = "1920x1080";

        /**
         * 标清分辨率
         */
        private String mediumResolution = "1280x720";

        /**
         * 低清分辨率
         */
        private String lowResolution = "854x480";

        /**
         * 高清比特率
         */
        private Integer highBitrate = 3000;

        /**
         * 标清比特率
         */
        private Integer mediumBitrate = 1500;

        /**
         * 低清比特率
         */
        private Integer lowBitrate = 800;
    }

    /**
     * 云盘选项
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class CloudDiskOptions {

        /**
         * 是否保存到用户云盘
         */
        private Boolean saveToCloudDisk = false;

        /**
         * 云盘文件名
         */
        private String cloudFileName;

        /**
         * 云盘文件路径
         */
        private String cloudFilePath = "/";

        /**
         * 父目录ID
         */
        private Long parentId = 0L;

        /**
         * 文件描述
         */
        private String description;

        /**
         * 文件标签
         */
        private String[] tags;
    }

}
