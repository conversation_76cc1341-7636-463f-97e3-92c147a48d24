package cn.iocoder.yudao.module.infra.api.deduplication.dto;

import lombok.Data;

/**
 * 压缩配置 DTO
 *
 * <AUTHOR>
 */
@Data
public class CompressConfigDTO {

    /**
     * 视频压缩配置
     */
    @Data
    public static class VideoCompressConfig {
        /**
         * 视频码率（kbps）
         */
        private Integer videoBitrate = 1000;

        /**
         * 音频码率（kbps）
         */
        private Integer audioBitrate = 128;

        /**
         * 分辨率（如：1280x720）
         */
        private String resolution = "1280x720";

        /**
         * 帧率
         */
        private Integer frameRate;

        /**
         * 是否保持原始宽高比
         */
        private Boolean keepAspectRatio = true;
    }

    /**
     * 图片压缩配置
     */
    @Data
    public static class ImageCompressConfig {
        /**
         * 图片质量（0-100）
         */
        private Integer quality = 80;

        /**
         * 最大宽度
         */
        private Integer maxWidth;

        /**
         * 最大高度
         */
        private Integer maxHeight;

        /**
         * 是否保持原始宽高比
         */
        private Boolean keepAspectRatio = true;

        /**
         * 输出格式（如：avif、webp、jpeg）
         */
        private String outputFormat = "avif";
    }

}
