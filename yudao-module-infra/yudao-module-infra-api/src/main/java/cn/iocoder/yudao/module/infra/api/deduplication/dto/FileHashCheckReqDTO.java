package cn.iocoder.yudao.module.infra.api.deduplication.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * 文件哈希校验请求DTO
 *
 * <AUTHOR>
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class FileHashCheckReqDTO {

    /**
     * 文件SHA-256哈希值
     */
    @NotBlank(message = "文件哈希值不能为空")
    private String sha256Hash;

    /**
     * 文件大小（字节）
     */
    @NotNull(message = "文件大小不能为空")
    private Long fileSize;

    /**
     * 文件名
     */
    private String fileName;

    /**
     * 文件类型
     */
    private String contentType;

    /**
     * 用户ID（可选，用于用户级别的去重检查）
     */
    private Long userId;

    /**
     * 是否检查用户云盘中的文件
     */
    private Boolean checkUserCloudDisk = false;

}
