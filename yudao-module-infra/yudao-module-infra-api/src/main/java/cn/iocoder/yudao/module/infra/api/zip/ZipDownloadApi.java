package cn.iocoder.yudao.module.infra.api.zip;

import cn.iocoder.yudao.framework.common.pojo.CommonResult;
import cn.iocoder.yudao.module.infra.api.zip.dto.ZipDownloadReqDTO;
import cn.iocoder.yudao.module.infra.api.zip.dto.ZipDownloadRespDTO;
import org.apache.skywalking.apm.toolkit.trace.Tag;

import javax.validation.Valid;
import java.io.OutputStream;

/**
 * ZIP下载 API 接口
 *
 * <AUTHOR>
 */
public interface ZipDownloadApi {

    /**
     * 下载文件并打包成ZIP
     *
     * @param reqDTO 下载请求
     * @return ZIP下载结果
     */
    CommonResult<ZipDownloadRespDTO> downloadZip(@Valid ZipDownloadReqDTO reqDTO);

    /**
     * 下载文件并直接返回ZIP流
     *
     * @param reqDTO 下载请求
     * @param outputStream 输出流
     */
    void downloadZipStream(@Valid ZipDownloadReqDTO reqDTO, OutputStream outputStream);

    /**
     * 异步下载文件并打包成ZIP
     *
     * @param reqDTO 下载请求
     * @return 任务ID
     */
    CommonResult<String> downloadZipAsync(@Valid ZipDownloadReqDTO reqDTO);

    /**
     * 查询异步下载任务状态
     *
     * @param taskId 任务ID
     * @return 任务状态和结果
     */
    CommonResult<ZipDownloadRespDTO> getAsyncTaskStatus(String taskId);

}
