package cn.iocoder.yudao.module.infra.api.zip;

import cn.iocoder.yudao.framework.common.pojo.CommonResult;
import cn.iocoder.yudao.module.infra.api.zip.dto.ZipDownloadReqDTO;
import cn.iocoder.yudao.module.infra.api.zip.dto.ZipDownloadRespDTO;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;

import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;

/**
 * ZIP下载 API 接口
 *
 * <AUTHOR>
 */
@Tag(name = "ZIP下载 API", description = "通用ZIP下载功能")
public interface ZipDownloadApi {

    /**
     * 下载文件并打包成ZIP
     *
     * @param reqDTO 下载请求
     * @return ZIP下载结果
     */
    @Operation(summary = "下载文件并打包成ZIP", description = "根据URL列表下载文件并打包成ZIP文件")
    CommonResult<ZipDownloadRespDTO> downloadZip(@Valid ZipDownloadReqDTO reqDTO);

    /**
     * 下载文件并直接返回ZIP流
     *
     * @param reqDTO 下载请求
     * @param response HTTP响应
     */
    @Operation(summary = "下载文件并直接返回ZIP流", description = "根据URL列表下载文件并直接返回ZIP文件流")
    void downloadZipStream(@Valid ZipDownloadReqDTO reqDTO, HttpServletResponse response);

    /**
     * 异步下载文件并打包成ZIP
     *
     * @param reqDTO 下载请求
     * @return 任务ID
     */
    @Operation(summary = "异步下载文件并打包成ZIP", description = "异步处理大量文件下载，返回任务ID用于查询进度")
    CommonResult<String> downloadZipAsync(@Valid ZipDownloadReqDTO reqDTO);

    /**
     * 查询异步下载任务状态
     *
     * @param taskId 任务ID
     * @return 任务状态和结果
     */
    @Operation(summary = "查询异步下载任务状态", description = "根据任务ID查询异步下载任务的状态和结果")
    CommonResult<ZipDownloadRespDTO> getAsyncTaskStatus(String taskId);

}
