package cn.iocoder.yudao.module.infra.api.zip.dto;

import lombok.Data;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * ZIP下载请求DTO
 *
 * <AUTHOR>
 */
@Data
public class ZipDownloadReqDTO {

    @NotEmpty(message = "文件下载列表不能为空")
    private List<FileDownloadItem> files;



//    @Schema(description = "ZIP文件名", requiredMode = Schema.RequiredMode.REQUIRED, example = "download.zip")
    @NotNull(message = "ZIP文件名不能为空")
    private String zipFileName;

//    @Schema(description = "ZIP内文件编码格式", example = "UTF-8")
    private String encoding = "UTF-8";

//    @Schema(description = "ZIP压缩级别，0-9，0不压缩，9最大压缩", example = "6")
    private Integer compressionLevel = 6;

//    @Schema(description = "ZIP解压密码", example = "123456")
    private String password;

//    @Schema(description = "是否创建目录结构", example = "false")
    private Boolean createDirectories = false;

//    @Schema(description = "目录前缀，当createDirectories为true时有效", example = "files/")
    private String directoryPrefix;

//    @Schema(description = "下载超时时间（秒）", example = "30")
    private Integer downloadTimeout = 30;

//    @Schema(description = "最大文件大小（MB）", example = "100")
    private Integer maxFileSizeMB = 100;

//    @Schema(description = "最大文件数量", example = "50")
    private Integer maxFileCount = 50;

//    @Schema(description = "是否跳过下载失败的文件", example = "true")
    private Boolean skipFailedFiles = true;

//    @Schema(description = "用户代理", example = "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36")
    private String userAgent = "YudaoDownloader/1.0";

//    @Schema(description = "请求头信息")
    private Map<String, String> headers;

}
