package cn.iocoder.yudao.module.infra.api.zip.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.util.List;
import java.util.Map;

/**
 * ZIP下载请求DTO
 *
 * <AUTHOR>
 */
@Schema(description = "ZIP下载请求")
@Data
public class ZipDownloadReqDTO {

    @Schema(description = "URL集合", requiredMode = Schema.RequiredMode.REQUIRED, example = "[\"https://example.com/file1.jpg\", \"https://example.com/file2.pdf\"]")
    @NotEmpty(message = "URL集合不能为空")
    private List<String> urls;

    @Schema(description = "URL到文件名的映射，key为URL，value为ZIP内的文件名", example = "{\"https://example.com/file1.jpg\": \"image1.jpg\"}")
    private Map<String, String> urlToFileNameMap;

    @Schema(description = "ZIP文件名", requiredMode = Schema.RequiredMode.REQUIRED, example = "download.zip")
    @NotNull(message = "ZIP文件名不能为空")
    private String zipFileName;

    @Schema(description = "ZIP内文件编码格式", example = "UTF-8")
    private String encoding = "UTF-8";

    @Schema(description = "ZIP压缩级别，0-9，0不压缩，9最大压缩", example = "6")
    private Integer compressionLevel = 6;

    @Schema(description = "ZIP解压密码", example = "123456")
    private String password;

    @Schema(description = "是否创建目录结构", example = "false")
    private Boolean createDirectories = false;

    @Schema(description = "目录前缀，当createDirectories为true时有效", example = "files/")
    private String directoryPrefix;

    @Schema(description = "下载超时时间（秒）", example = "30")
    private Integer downloadTimeout = 30;

    @Schema(description = "最大文件大小（MB）", example = "100")
    private Integer maxFileSizeMB = 100;

    @Schema(description = "最大文件数量", example = "50")
    private Integer maxFileCount = 50;

    @Schema(description = "是否跳过下载失败的文件", example = "true")
    private Boolean skipFailedFiles = true;

    @Schema(description = "用户代理", example = "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36")
    private String userAgent = "YudaoDownloader/1.0";

    @Schema(description = "请求头信息")
    private Map<String, String> headers;

    /**
     * 获取指定URL的文件名
     */
    public String getFileNameForUrl(String url) {
        if (urlToFileNameMap != null && urlToFileNameMap.containsKey(url)) {
            return urlToFileNameMap.get(url);
        }
        
        // 从URL中提取文件名
        String fileName = url.substring(url.lastIndexOf('/') + 1);
        
        // 移除查询参数
        int queryIndex = fileName.indexOf('?');
        if (queryIndex > 0) {
            fileName = fileName.substring(0, queryIndex);
        }
        
        // 如果没有扩展名，添加默认扩展名
        if (!fileName.contains(".")) {
            fileName += ".file";
        }
        
        return fileName;
    }

    /**
     * 获取完整的文件路径（包含目录前缀）
     */
    public String getFullFilePath(String url) {
        String fileName = getFileNameForUrl(url);
        
        if (createDirectories && directoryPrefix != null && !directoryPrefix.trim().isEmpty()) {
            return directoryPrefix + (directoryPrefix.endsWith("/") ? "" : "/") + fileName;
        }
        
        return fileName;
    }

}
