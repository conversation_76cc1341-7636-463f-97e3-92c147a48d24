package cn.iocoder.yudao.module.infra.api.deduplication.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 文件哈希校验响应DTO
 *
 * <AUTHOR>
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class FileHashCheckRespDTO {

    /**
     * 是否存在重复文件
     */
    private Boolean isDuplicate;

    /**
     * 重复文件ID
     */
    private Long duplicateFileId;

    /**
     * 重复文件信息
     */
    private DuplicateFileInfo duplicateFileInfo;

    /**
     * 可用的衍生版本列表
     */
    private List<DerivativeVersionInfo> availableVersions;

    /**
     * 用户云盘中的重复文件
     */
    private List<CloudDiskFileInfo> cloudDiskFiles;

    /**
     * 是否可以跳过上传
     */
    private Boolean canSkipUpload;

    /**
     * 建议的处理方式
     */
    private String suggestedAction;

    /**
     * 重复文件信息
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class DuplicateFileInfo {
        
        /**
         * 文件ID
         */
        private Long fileId;
        
        /**
         * 文件名
         */
        private String fileName;
        
        /**
         * 文件大小
         */
        private Long fileSize;
        
        /**
         * 文件类型
         */
        private String contentType;
        
        /**
         * 文件URL
         */
        private String fileUrl;
        
        /**
         * 创建时间
         */
        private LocalDateTime createTime;
        
        /**
         * 引用次数
         */
        private Integer referenceCount;
    }

    /**
     * 衍生版本信息
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class DerivativeVersionInfo {
        
        /**
         * 衍生版本ID
         */
        private Long derivativeId;
        
        /**
         * 衍生类型
         */
        private String derivativeType;
        
        /**
         * 文件ID
         */
        private Long fileId;
        
        /**
         * 质量等级
         */
        private String quality;
        
        /**
         * 分辨率
         */
        private String resolution;
        
        /**
         * 文件大小
         */
        private Long fileSize;
        
        /**
         * 文件URL
         */
        private String fileUrl;
        
        /**
         * 状态
         */
        private Integer status;
    }

    /**
     * 云盘文件信息
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class CloudDiskFileInfo {
        
        /**
         * 云盘记录ID
         */
        private Long cloudDiskId;
        
        /**
         * 用户自定义文件名
         */
        private String fileName;
        
        /**
         * 文件路径
         */
        private String filePath;
        
        /**
         * 文件大小
         */
        private Long fileSize;
        
        /**
         * 是否收藏
         */
        private Boolean isFavorite;
        
        /**
         * 创建时间
         */
        private LocalDateTime createTime;
    }

    /**
     * 建议的处理方式枚举
     */
    public static class SuggestedAction {
        public static final String SKIP_UPLOAD = "SKIP_UPLOAD";           // 跳过上传
        public static final String UPLOAD_NEW = "UPLOAD_NEW";             // 上传新文件
        public static final String USE_EXISTING = "USE_EXISTING";         // 使用现有文件
        public static final String ADD_TO_CLOUD_DISK = "ADD_TO_CLOUD_DISK"; // 添加到云盘
    }

}
