-- 扩展 infra_file_index 表支持水印功能和logo覆盖功能
-- 执行时间：请在维护窗口执行

-- 1. 添加水印相关字段
ALTER TABLE infra_file_index
ADD COLUMN cover_position VARCHAR(20) NULL COMMENT '需要覆盖的原logo位置：支持9宫格(1-9)或名称' AFTER watermark_processed_at,
ADD COLUMN cover_size VARCHAR(20) NULL COMMENT '覆盖区域大小，格式width:height，如200:100' AFTER cover_position;

ADD COLUMN watermark_path VARCHAR(512) NULL COMMENT '水印图片路径' AFTER compress_ratio,
ADD COLUMN watermark_position VARCHAR(20) DEFAULT 'top-right' COMMENT '水印位置：支持9宫格(1-9)或名称(top-left等)' AFTER watermark_path,
ADD COLUMN watermark_opacity DECIMAL(3,2) DEFAULT 0.80 COMMENT '水印透明度(0.0-1.0)' AFTER watermark_position,
ADD COLUMN watermark_scale DECIMAL(3,2) DEFAULT 0.10 COMMENT '水印大小比例(0.0-1.0)' AFTER watermark_opacity,
ADD COLUMN has_watermark TINYINT(1) DEFAULT 0 COMMENT '是否已添加水印：0-否，1-是' AFTER watermark_scale,
ADD COLUMN watermark_status TINYINT(1) DEFAULT 0 COMMENT '水印处理状态：0-未处理，1-处理中，2-成功，3-失败' AFTER has_watermark,
ADD COLUMN watermark_error_msg VARCHAR(500) NULL COMMENT '水印处理错误信息' AFTER watermark_status,
ADD COLUMN watermark_processed_at DATETIME NULL COMMENT '水印处理完成时间' AFTER watermark_error_msg,
ADD COLUMN cover_position VARCHAR(20) NULL COMMENT '需要覆盖的原logo位置：支持9宫格(1-9)或名称' AFTER watermark_processed_at,
ADD COLUMN cover_size VARCHAR(20) NULL COMMENT '覆盖区域大小，格式width:height，如200:100' AFTER cover_position;

-- 2. 添加索引优化查询性能
ALTER TABLE infra_file_index 
ADD INDEX idx_watermark_status (watermark_status, has_watermark),
ADD INDEX idx_watermark_processed_at (watermark_processed_at);

-- 3. 添加字段注释
ALTER TABLE infra_file_index 
MODIFY COLUMN watermark_path VARCHAR(512) NULL COMMENT '水印图片路径（绝对路径）',
MODIFY COLUMN watermark_position VARCHAR(20) DEFAULT 'top-right' COMMENT '水印位置：支持9宫格(1-9)或名称(top-left,top-center,top-right,middle-left,center,middle-right,bottom-left,bottom-center,bottom-right)',
MODIFY COLUMN watermark_opacity DECIMAL(3,2) DEFAULT 0.80 COMMENT '水印透明度，范围0.0-1.0，0.0完全透明，1.0完全不透明，默认0.8',
MODIFY COLUMN watermark_scale DECIMAL(3,2) DEFAULT 0.10 COMMENT '水印大小比例，范围0.0-1.0，对应固定宽度50px-500px，默认0.1对应200px',
MODIFY COLUMN has_watermark TINYINT(1) DEFAULT 0 COMMENT '是否已添加水印：0-否，1-是',
MODIFY COLUMN watermark_status TINYINT(1) DEFAULT 0 COMMENT '水印处理状态：0-未处理，1-处理中，2-处理成功，3-处理失败',
MODIFY COLUMN watermark_error_msg VARCHAR(500) NULL COMMENT '水印处理失败时的错误信息',
MODIFY COLUMN watermark_processed_at DATETIME NULL COMMENT '水印处理完成时间（成功或失败）',
MODIFY COLUMN cover_position VARCHAR(20) NULL COMMENT '需要覆盖的原logo位置：支持9宫格(1-9)或名称，如3或top-right',
MODIFY COLUMN cover_size VARCHAR(20) NULL COMMENT '覆盖区域大小，格式width:height，如200:100，用于覆盖原有logo';

-- 4. 验证表结构
SELECT 
    COLUMN_NAME,
    COLUMN_TYPE,
    IS_NULLABLE,
    COLUMN_DEFAULT,
    COLUMN_COMMENT
FROM INFORMATION_SCHEMA.COLUMNS 
WHERE TABLE_SCHEMA = DATABASE() 
  AND TABLE_NAME = 'infra_file_index' 
  AND (COLUMN_NAME LIKE '%watermark%' OR COLUMN_NAME LIKE '%cover%')
ORDER BY ORDINAL_POSITION;

-- 5. 查看索引
SHOW INDEX FROM infra_file_index WHERE Key_name LIKE '%watermark%';

-- 6. 9宫格位置说明
/*
9宫格位置示意图：
+---+---+---+
| 1 | 2 | 3 |  1=top-left, 2=top-center, 3=top-right
+---+---+---+
| 4 | 5 | 6 |  4=middle-left, 5=center, 6=middle-right  
+---+---+---+
| 7 | 8 | 9 |  7=bottom-left, 8=bottom-center, 9=bottom-right
+---+---+---+

使用示例：
- watermark_position: '3' 或 'top-right' (水印放在右上角)
- cover_position: '1' 或 'top-left' (覆盖左上角的原logo)
- cover_size: '200:100' (覆盖区域宽200px，高100px)
*/
