package cn.iocoder.yudao.module.infra.service.file;

import cn.iocoder.yudao.framework.test.core.ut.BaseDbUnitTest;
import cn.iocoder.yudao.module.infra.dal.dataobject.file.FileCompressTaskDO;
import cn.iocoder.yudao.module.infra.dal.dataobject.file.FileDO;
import cn.iocoder.yudao.module.infra.dal.mysql.file.FileCompressTaskMapper;
import cn.iocoder.yudao.module.infra.enums.file.FileCompressTaskStatusEnum;
import cn.iocoder.yudao.module.infra.enums.file.FileCompressTypeEnum;
import org.junit.jupiter.api.Test;
import org.springframework.context.annotation.Import;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.List;
import java.util.Map;

import static cn.iocoder.yudao.framework.test.core.util.AssertUtils.assertPojoEquals;
import static cn.iocoder.yudao.framework.test.core.util.RandomUtils.*;
import static org.junit.jupiter.api.Assertions.*;

/**
 * {@link FileCompressTaskManager} 的单元测试类
 *
 * <AUTHOR>
 */
@Import({FileCompressTaskManager.class, FileCompressTaskMapper.class})
public class FileCompressTaskManagerTest extends BaseDbUnitTest {

    @Resource
    private FileCompressTaskManager taskManager;

    @Resource
    private FileCompressTaskMapper fileCompressTaskMapper;

    @Test
    public void testCreateTask() {
        // 准备参数
        FileDO file = randomFileDO();
        FileCompressTypeEnum compressType = FileCompressTypeEnum.MP4_COMPRESS;
        Integer priority = 3;
        String configParams = "{\"videoBitrate\":1000}";

        // 调用方法
        Long taskId = taskManager.createTask(file, compressType, priority, configParams);

        // 验证结果
        assertNotNull(taskId);
        
        FileCompressTaskDO task = fileCompressTaskMapper.selectById(taskId);
        assertNotNull(task);
        assertEquals(file.getId(), task.getFileId());
        assertEquals(file.getPath(), task.getFilePath());
        assertEquals(file.getSize(), task.getFileSize());
        assertEquals(compressType.getFileType(), task.getFileType());
        assertEquals(compressType.getCode(), task.getCompressType());
        assertEquals(FileCompressTaskStatusEnum.PENDING.getStatus(), task.getStatus());
        assertEquals(0, task.getProgress());
        assertEquals(priority, task.getPriority());
        assertEquals(configParams, task.getConfigParams());
    }

    @Test
    public void testCreateTask_ExistingTask() {
        // 准备已存在的任务
        FileDO file = randomFileDO();
        FileCompressTypeEnum compressType = FileCompressTypeEnum.MP4_COMPRESS;
        
        FileCompressTaskDO existingTask = randomFileCompressTaskDO();
        existingTask.setFileId(file.getId());
        existingTask.setCompressType(compressType.getCode());
        existingTask.setStatus(FileCompressTaskStatusEnum.PROCESSING.getStatus());
        fileCompressTaskMapper.insert(existingTask);

        // 调用方法
        Long taskId = taskManager.createTask(file, compressType, 5, null);

        // 验证结果 - 应该返回现有任务ID
        assertEquals(existingTask.getId(), taskId);
    }

    @Test
    public void testStartProcessing() {
        // 准备任务
        FileCompressTaskDO task = randomFileCompressTaskDO();
        task.setStatus(FileCompressTaskStatusEnum.PENDING.getStatus());
        fileCompressTaskMapper.insert(task);

        // 调用方法
        boolean success = taskManager.startProcessing(task.getId());

        // 验证结果
        assertTrue(success);
        
        FileCompressTaskDO updatedTask = fileCompressTaskMapper.selectById(task.getId());
        assertEquals(FileCompressTaskStatusEnum.PROCESSING.getStatus(), updatedTask.getStatus());
        assertNotNull(updatedTask.getStartTime());
    }

    @Test
    public void testStartProcessing_WrongStatus() {
        // 准备任务（状态不是待处理）
        FileCompressTaskDO task = randomFileCompressTaskDO();
        task.setStatus(FileCompressTaskStatusEnum.PROCESSING.getStatus());
        fileCompressTaskMapper.insert(task);

        // 调用方法
        boolean success = taskManager.startProcessing(task.getId());

        // 验证结果
        assertFalse(success);
    }

    @Test
    public void testCompleteSuccess() {
        // 准备任务
        FileCompressTaskDO task = randomFileCompressTaskDO();
        task.setStatus(FileCompressTaskStatusEnum.PROCESSING.getStatus());
        task.setFileSize(1000L);
        fileCompressTaskMapper.insert(task);

        // 调用方法
        String compressedPath = "/compressed/test.mp4";
        Long compressedSize = 500L;
        taskManager.completeSuccess(task.getId(), compressedPath, compressedSize, task.getFileSize());

        // 验证结果
        FileCompressTaskDO updatedTask = fileCompressTaskMapper.selectById(task.getId());
        assertEquals(FileCompressTaskStatusEnum.SUCCESS.getStatus(), updatedTask.getStatus());
        assertEquals(100, updatedTask.getProgress());
        assertEquals(compressedPath, updatedTask.getCompressedPath());
        assertEquals(compressedSize, updatedTask.getCompressedSize());
        assertEquals(new BigDecimal("50.0000"), updatedTask.getCompressRatio());
        assertNotNull(updatedTask.getEndTime());
    }

    @Test
    public void testCompleteFailed() {
        // 准备任务
        FileCompressTaskDO task = randomFileCompressTaskDO();
        task.setStatus(FileCompressTaskStatusEnum.PROCESSING.getStatus());
        task.setRetryCount(0);
        fileCompressTaskMapper.insert(task);

        // 调用方法
        String errorMessage = "压缩失败";
        taskManager.completeFailed(task.getId(), errorMessage);

        // 验证结果
        FileCompressTaskDO updatedTask = fileCompressTaskMapper.selectById(task.getId());
        assertEquals(FileCompressTaskStatusEnum.FAILED.getStatus(), updatedTask.getStatus());
        assertEquals(errorMessage, updatedTask.getErrorMessage());
        assertEquals(1, updatedTask.getRetryCount());
        assertNotNull(updatedTask.getEndTime());
    }

    @Test
    public void testGetPendingTasks() {
        // 准备任务
        FileCompressTaskDO task1 = randomFileCompressTaskDO();
        task1.setStatus(FileCompressTaskStatusEnum.PENDING.getStatus());
        task1.setPriority(1);
        fileCompressTaskMapper.insert(task1);

        FileCompressTaskDO task2 = randomFileCompressTaskDO();
        task2.setStatus(FileCompressTaskStatusEnum.PENDING.getStatus());
        task2.setPriority(2);
        fileCompressTaskMapper.insert(task2);

        FileCompressTaskDO task3 = randomFileCompressTaskDO();
        task3.setStatus(FileCompressTaskStatusEnum.PROCESSING.getStatus());
        fileCompressTaskMapper.insert(task3);

        // 调用方法
        List<FileCompressTaskDO> pendingTasks = taskManager.getPendingTasks(10);

        // 验证结果
        assertEquals(2, pendingTasks.size());
        // 验证按优先级排序
        assertEquals(task1.getId(), pendingTasks.get(0).getId());
        assertEquals(task2.getId(), pendingTasks.get(1).getId());
    }

    @Test
    public void testGetRetryableTasks() {
        // 准备任务
        FileCompressTaskDO task1 = randomFileCompressTaskDO();
        task1.setStatus(FileCompressTaskStatusEnum.FAILED.getStatus());
        task1.setRetryCount(1);
        task1.setMaxRetryCount(3);
        fileCompressTaskMapper.insert(task1);

        FileCompressTaskDO task2 = randomFileCompressTaskDO();
        task2.setStatus(FileCompressTaskStatusEnum.FAILED.getStatus());
        task2.setRetryCount(3);
        task2.setMaxRetryCount(3);
        fileCompressTaskMapper.insert(task2);

        // 调用方法
        List<FileCompressTaskDO> retryableTasks = taskManager.getRetryableTasks(10);

        // 验证结果
        assertEquals(1, retryableTasks.size());
        assertEquals(task1.getId(), retryableTasks.get(0).getId());
    }

    @Test
    public void testCancelTask() {
        // 准备任务
        FileCompressTaskDO task = randomFileCompressTaskDO();
        task.setStatus(FileCompressTaskStatusEnum.PENDING.getStatus());
        fileCompressTaskMapper.insert(task);

        // 调用方法
        boolean success = taskManager.cancelTask(task.getId());

        // 验证结果
        assertTrue(success);
        
        FileCompressTaskDO updatedTask = fileCompressTaskMapper.selectById(task.getId());
        assertEquals(FileCompressTaskStatusEnum.CANCELLED.getStatus(), updatedTask.getStatus());
    }

    @Test
    public void testCancelTask_WrongStatus() {
        // 准备任务（状态不允许取消）
        FileCompressTaskDO task = randomFileCompressTaskDO();
        task.setStatus(FileCompressTaskStatusEnum.PROCESSING.getStatus());
        fileCompressTaskMapper.insert(task);

        // 调用方法
        boolean success = taskManager.cancelTask(task.getId());

        // 验证结果
        assertFalse(success);
    }

    @Test
    public void testRetryTask() {
        // 准备任务
        FileCompressTaskDO task = randomFileCompressTaskDO();
        task.setStatus(FileCompressTaskStatusEnum.FAILED.getStatus());
        task.setRetryCount(1);
        task.setMaxRetryCount(3);
        fileCompressTaskMapper.insert(task);

        // 调用方法
        boolean success = taskManager.retryTask(task.getId());

        // 验证结果
        assertTrue(success);
        
        FileCompressTaskDO updatedTask = fileCompressTaskMapper.selectById(task.getId());
        assertEquals(FileCompressTaskStatusEnum.PENDING.getStatus(), updatedTask.getStatus());
        assertEquals(0, updatedTask.getProgress());
    }

    @Test
    public void testGetStatusStatistics() {
        // 准备任务
        FileCompressTaskDO task1 = randomFileCompressTaskDO();
        task1.setStatus(FileCompressTaskStatusEnum.PENDING.getStatus());
        fileCompressTaskMapper.insert(task1);

        FileCompressTaskDO task2 = randomFileCompressTaskDO();
        task2.setStatus(FileCompressTaskStatusEnum.SUCCESS.getStatus());
        fileCompressTaskMapper.insert(task2);

        FileCompressTaskDO task3 = randomFileCompressTaskDO();
        task3.setStatus(FileCompressTaskStatusEnum.SUCCESS.getStatus());
        fileCompressTaskMapper.insert(task3);

        // 调用方法
        Map<Integer, Long> statistics = taskManager.getStatusStatistics();

        // 验证结果
        assertEquals(1L, statistics.get(FileCompressTaskStatusEnum.PENDING.getStatus()));
        assertEquals(2L, statistics.get(FileCompressTaskStatusEnum.SUCCESS.getStatus()));
    }

    // ========== 工具方法 ==========

    private FileDO randomFileDO() {
        return FileDO.builder()
            .id(randomLong())
            .configId(randomLong())
            .name(randomString())
            .path(randomString())
            .url(randomString())
            .type(randomString())
            .size(randomLong())
            .build();
    }

    private FileCompressTaskDO randomFileCompressTaskDO() {
        return FileCompressTaskDO.builder()
            .fileId(randomLong())
            .filePath(randomString())
            .fileSize(randomLong())
            .fileType("video")
            .compressType("mp4_compress")
            .status(FileCompressTaskStatusEnum.PENDING.getStatus())
            .progress(0)
            .retryCount(0)
            .maxRetryCount(3)
            .priority(5)
            .build();
    }

}
