package cn.iocoder.yudao.module.infra.framework.file.core.utils;

import org.junit.jupiter.api.Test;

import java.io.ByteArrayInputStream;
import java.io.InputStream;

import static org.junit.jupiter.api.Assertions.*;

/**
 * {@link FileHashUtils} 的单元测试
 *
 * <AUTHOR>
 */
class FileHashUtilsTest {

    @Test
    void testCalculateSha256_WithByteArray() {
        // 准备参数
        byte[] content = "Hello, World!".getBytes();
        
        // 调用方法
        String hash = FileHashUtils.calculateSha256(content);
        
        // 断言结果
        assertNotNull(hash);
        assertEquals(64, hash.length());
        // "Hello, World!" 的 SHA-256 哈希值
        assertEquals("dffd6021bb2bd5b0af676290809ec3a53191dd81c7f70a4b28688a362182986f", hash);
    }

    @Test
    void testCalculateSha256_WithInputStream() throws Exception {
        // 准备参数
        byte[] content = "Hello, World!".getBytes();
        InputStream inputStream = new ByteArrayInputStream(content);
        
        // 调用方法
        String hash = FileHashUtils.calculateSha256(inputStream);
        
        // 断言结果
        assertNotNull(hash);
        assertEquals(64, hash.length());
        assertEquals("dffd6021bb2bd5b0af676290809ec3a53191dd81c7f70a4b28688a362182986f", hash);
    }

    @Test
    void testCalculateSha256_WithEmptyContent() {
        // 准备参数
        byte[] content = new byte[0];
        
        // 调用方法并断言异常
        assertThrows(IllegalArgumentException.class, () -> {
            FileHashUtils.calculateSha256(content);
        });
    }

    @Test
    void testCalculateSha256_WithNullContent() {
        // 调用方法并断言异常
        assertThrows(IllegalArgumentException.class, () -> {
            FileHashUtils.calculateSha256((byte[]) null);
        });
    }

    @Test
    void testCalculateSha256_WithNullInputStream() {
        // 调用方法并断言异常
        assertThrows(IllegalArgumentException.class, () -> {
            FileHashUtils.calculateSha256((InputStream) null);
        });
    }

    @Test
    void testVerifyHash_WithByteArray_Success() {
        // 准备参数
        byte[] content = "Hello, World!".getBytes();
        String expectedHash = "dffd6021bb2bd5b0af676290809ec3a53191dd81c7f70a4b28688a362182986f";
        
        // 调用方法
        boolean result = FileHashUtils.verifyHash(content, expectedHash);
        
        // 断言结果
        assertTrue(result);
    }

    @Test
    void testVerifyHash_WithByteArray_Failure() {
        // 准备参数
        byte[] content = "Hello, World!".getBytes();
        String expectedHash = "wrong_hash";
        
        // 调用方法
        boolean result = FileHashUtils.verifyHash(content, expectedHash);
        
        // 断言结果
        assertFalse(result);
    }

    @Test
    void testVerifyHash_WithInputStream_Success() throws Exception {
        // 准备参数
        byte[] content = "Hello, World!".getBytes();
        InputStream inputStream = new ByteArrayInputStream(content);
        String expectedHash = "dffd6021bb2bd5b0af676290809ec3a53191dd81c7f70a4b28688a362182986f";
        
        // 调用方法
        boolean result = FileHashUtils.verifyHash(inputStream, expectedHash);
        
        // 断言结果
        assertTrue(result);
    }

    @Test
    void testVerifyHash_WithNullParameters() {
        // 测试空参数
        assertFalse(FileHashUtils.verifyHash((byte[]) null, "hash"));
        assertFalse(FileHashUtils.verifyHash("test".getBytes(), null));
        assertFalse(FileHashUtils.verifyHash((InputStream) null, "hash"));
        assertFalse(FileHashUtils.verifyHash(new ByteArrayInputStream("test".getBytes()), null));
    }

    @Test
    void testIsValidSha256Hash() {
        // 测试有效的哈希值
        assertTrue(FileHashUtils.isValidSha256Hash("dffd6021bb2bd5b0af676290809ec3a53191dd81c7f70a4b28688a362182986f"));
        assertTrue(FileHashUtils.isValidSha256Hash("DFFD6021BB2BD5B0AF676290809EC3A53191DD81C7F70A4B28688A362182986F"));
        
        // 测试无效的哈希值
        assertFalse(FileHashUtils.isValidSha256Hash(null));
        assertFalse(FileHashUtils.isValidSha256Hash(""));
        assertFalse(FileHashUtils.isValidSha256Hash("short"));
        assertFalse(FileHashUtils.isValidSha256Hash("dffd6021bb2bd5b0af676290809ec3a53191dd81c7f70a4b28688a362182986f1")); // 65位
        assertFalse(FileHashUtils.isValidSha256Hash("dffd6021bb2bd5b0af676290809ec3a53191dd81c7f70a4b28688a362182986")); // 63位
        assertFalse(FileHashUtils.isValidSha256Hash("dffd6021bb2bd5b0af676290809ec3a53191dd81c7f70a4b28688a362182986g")); // 包含非十六进制字符
    }

    @Test
    void testCalculateHashAndSize() {
        // 准备参数
        byte[] content = "Hello, World!".getBytes();
        
        // 调用方法
        FileHashUtils.FileHashResult result = FileHashUtils.calculateHashAndSize(content);
        
        // 断言结果
        assertNotNull(result);
        assertEquals("dffd6021bb2bd5b0af676290809ec3a53191dd81c7f70a4b28688a362182986f", result.getHash());
        assertEquals(content.length, result.getSize());
    }

    @Test
    void testCalculateHashAndSize_WithNullContent() {
        // 调用方法并断言异常
        assertThrows(IllegalArgumentException.class, () -> {
            FileHashUtils.calculateHashAndSize(null);
        });
    }

    @Test
    void testFileHashResult() {
        // 准备参数
        String hash = "test_hash";
        long size = 1024L;
        
        // 创建对象
        FileHashUtils.FileHashResult result = new FileHashUtils.FileHashResult(hash, size);
        
        // 断言结果
        assertEquals(hash, result.getHash());
        assertEquals(size, result.getSize());
        assertTrue(result.toString().contains(hash));
        assertTrue(result.toString().contains(String.valueOf(size)));
    }

    @Test
    void testCalculateSha256_WithLargeContent() {
        // 准备大文件内容（1MB）
        byte[] content = new byte[1024 * 1024];
        for (int i = 0; i < content.length; i++) {
            content[i] = (byte) (i % 256);
        }
        
        // 调用方法
        String hash = FileHashUtils.calculateSha256(content);
        
        // 断言结果
        assertNotNull(hash);
        assertEquals(64, hash.length());
        assertTrue(FileHashUtils.isValidSha256Hash(hash));
    }

    @Test
    void testCalculateSha256_ConsistentResults() {
        // 准备参数
        byte[] content = "Test content for consistency".getBytes();

        // 多次调用方法
        String hash1 = FileHashUtils.calculateSha256(content);
        String hash2 = FileHashUtils.calculateSha256(content);
        String hash3 = FileHashUtils.calculateSha256(content);

        // 断言结果一致
        assertEquals(hash1, hash2);
        assertEquals(hash2, hash3);
    }

    @Test
    void testCalculateSha256_DifferentContent() {
        // 准备不同的内容
        byte[] content1 = "Content 1".getBytes();
        byte[] content2 = "Content 2".getBytes();

        // 调用方法
        String hash1 = FileHashUtils.calculateSha256(content1);
        String hash2 = FileHashUtils.calculateSha256(content2);

        // 断言结果不同
        assertNotEquals(hash1, hash2);
    }

}
