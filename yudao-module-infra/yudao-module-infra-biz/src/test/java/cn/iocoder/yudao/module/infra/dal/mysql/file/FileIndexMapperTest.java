package cn.iocoder.yudao.module.infra.dal.mysql.file;

import cn.iocoder.yudao.framework.test.core.ut.BaseDbUnitTest;
import cn.iocoder.yudao.module.infra.dal.dataobject.file.FileIndexDO;
import org.junit.jupiter.api.Test;
import org.springframework.context.annotation.Import;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.List;

import static cn.iocoder.yudao.framework.test.core.util.AssertUtils.assertPojoEquals;
import static cn.iocoder.yudao.framework.test.core.util.RandomUtils.*;
import static org.junit.jupiter.api.Assertions.*;

/**
 * {@link FileIndexMapper} 的单元测试类
 *
 * <AUTHOR>
 */
@Import(FileIndexMapper.class)
public class FileIndexMapperTest extends BaseDbUnitTest {

    @Resource
    private FileIndexMapper fileIndexMapper;

    @Test
    public void testSelectBySha256AndSize() {
        // mock 数据
        FileIndexDO fileIndex = randomFileIndexDO();
        fileIndexMapper.insert(fileIndex);
        
        // 准备参数
        String sha256 = fileIndex.getSha256();
        Long size = fileIndex.getSize();
        
        // 调用
        FileIndexDO result = fileIndexMapper.selectBySha256AndSize(sha256, size);
        
        // 断言
        assertPojoEquals(fileIndex, result);
    }

    @Test
    public void testSelectBySha256AndSize_NotFound() {
        // 调用
        FileIndexDO result = fileIndexMapper.selectBySha256AndSize("nonexistent", 1024L);
        
        // 断言
        assertNull(result);
    }

    @Test
    public void testIncrementRefCount() {
        // mock 数据
        FileIndexDO fileIndex = randomFileIndexDO();
        fileIndex.setRefCount(1);
        fileIndexMapper.insert(fileIndex);
        
        // 调用
        int updated = fileIndexMapper.incrementRefCount(fileIndex.getId());
        
        // 断言
        assertEquals(1, updated);
        
        // 验证引用计数增加
        FileIndexDO updatedFileIndex = fileIndexMapper.selectById(fileIndex.getId());
        assertEquals(2, updatedFileIndex.getRefCount());
    }

    @Test
    public void testIncrementRefCount_NotFound() {
        // 调用
        int updated = fileIndexMapper.incrementRefCount(999L);
        
        // 断言
        assertEquals(0, updated);
    }

    @Test
    public void testDecrementRefCount() {
        // mock 数据
        FileIndexDO fileIndex = randomFileIndexDO();
        fileIndex.setRefCount(2);
        fileIndexMapper.insert(fileIndex);
        
        // 调用
        int updated = fileIndexMapper.decrementRefCount(fileIndex.getId());
        
        // 断言
        assertEquals(1, updated);
        
        // 验证引用计数减少
        FileIndexDO updatedFileIndex = fileIndexMapper.selectById(fileIndex.getId());
        assertEquals(1, updatedFileIndex.getRefCount());
    }

    @Test
    public void testDecrementRefCount_ZeroRefCount() {
        // mock 数据
        FileIndexDO fileIndex = randomFileIndexDO();
        fileIndex.setRefCount(0);
        fileIndexMapper.insert(fileIndex);
        
        // 调用
        int updated = fileIndexMapper.decrementRefCount(fileIndex.getId());
        
        // 断言 - 引用计数为0时不应该减少
        assertEquals(0, updated);
        
        // 验证引用计数未变化
        FileIndexDO updatedFileIndex = fileIndexMapper.selectById(fileIndex.getId());
        assertEquals(0, updatedFileIndex.getRefCount());
    }

    @Test
    public void testSelectListByConfigId() {
        // mock 数据
        FileIndexDO fileIndex1 = randomFileIndexDO();
        fileIndex1.setConfigId(1L);
        fileIndexMapper.insert(fileIndex1);
        
        FileIndexDO fileIndex2 = randomFileIndexDO();
        fileIndex2.setConfigId(1L);
        fileIndexMapper.insert(fileIndex2);
        
        FileIndexDO fileIndex3 = randomFileIndexDO();
        fileIndex3.setConfigId(2L);
        fileIndexMapper.insert(fileIndex3);
        
        // 调用
        List<FileIndexDO> result = fileIndexMapper.selectListByConfigId(1L);
        
        // 断言
        assertEquals(2, result.size());
        assertTrue(result.stream().allMatch(item -> item.getConfigId().equals(1L)));
    }

    @Test
    public void testSelectListByRefCountZero() {
        // mock 数据
        FileIndexDO fileIndex1 = randomFileIndexDO();
        fileIndex1.setRefCount(0);
        fileIndexMapper.insert(fileIndex1);
        
        FileIndexDO fileIndex2 = randomFileIndexDO();
        fileIndex2.setRefCount(1);
        fileIndexMapper.insert(fileIndex2);
        
        FileIndexDO fileIndex3 = randomFileIndexDO();
        fileIndex3.setRefCount(0);
        fileIndexMapper.insert(fileIndex3);
        
        // 调用
        List<FileIndexDO> result = fileIndexMapper.selectListByRefCountZero();
        
        // 断言
        assertEquals(2, result.size());
        assertTrue(result.stream().allMatch(item -> item.getRefCount().equals(0)));
    }

    @Test
    public void testSelectByStoragePathAndConfigId() {
        // mock 数据
        FileIndexDO fileIndex = randomFileIndexDO();
        fileIndexMapper.insert(fileIndex);
        
        // 调用
        FileIndexDO result = fileIndexMapper.selectByStoragePathAndConfigId(
            fileIndex.getStoragePath(), fileIndex.getConfigId());
        
        // 断言
        assertPojoEquals(fileIndex, result);
    }

    @Test
    public void testSelectSavedStorageSize() {
        // mock 数据
        FileIndexDO fileIndex1 = randomFileIndexDO();
        fileIndex1.setSize(1000L);
        fileIndex1.setRefCount(3); // 节省 2 * 1000 = 2000 字节
        fileIndexMapper.insert(fileIndex1);
        
        FileIndexDO fileIndex2 = randomFileIndexDO();
        fileIndex2.setSize(500L);
        fileIndex2.setRefCount(2); // 节省 1 * 500 = 500 字节
        fileIndexMapper.insert(fileIndex2);
        
        FileIndexDO fileIndex3 = randomFileIndexDO();
        fileIndex3.setSize(200L);
        fileIndex3.setRefCount(1); // 节省 0 字节
        fileIndexMapper.insert(fileIndex3);
        
        // 调用
        Long savedSize = fileIndexMapper.selectSavedStorageSize();
        
        // 断言 - 总节省 2500 字节
        assertEquals(2500L, savedSize);
    }

    @Test
    public void testSelectDeduplicatedFileCount() {
        // mock 数据
        FileIndexDO fileIndex1 = randomFileIndexDO();
        fileIndex1.setRefCount(3); // 去重文件
        fileIndexMapper.insert(fileIndex1);
        
        FileIndexDO fileIndex2 = randomFileIndexDO();
        fileIndex2.setRefCount(1); // 非去重文件
        fileIndexMapper.insert(fileIndex2);
        
        FileIndexDO fileIndex3 = randomFileIndexDO();
        fileIndex3.setRefCount(2); // 去重文件
        fileIndexMapper.insert(fileIndex3);
        
        // 调用
        Long count = fileIndexMapper.selectDeduplicatedFileCount();
        
        // 断言
        assertEquals(2L, count);
    }

    @Test
    public void testSelectTotalFileCount() {
        // mock 数据
        fileIndexMapper.insert(randomFileIndexDO());
        fileIndexMapper.insert(randomFileIndexDO());
        fileIndexMapper.insert(randomFileIndexDO());
        
        // 调用
        Long count = fileIndexMapper.selectTotalFileCount();
        
        // 断言
        assertEquals(3L, count);
    }

    @Test
    public void testSelectTotalStorageSize() {
        // mock 数据
        FileIndexDO fileIndex1 = randomFileIndexDO();
        fileIndex1.setSize(1000L);
        fileIndexMapper.insert(fileIndex1);
        
        FileIndexDO fileIndex2 = randomFileIndexDO();
        fileIndex2.setSize(500L);
        fileIndexMapper.insert(fileIndex2);
        
        FileIndexDO fileIndex3 = randomFileIndexDO();
        fileIndex3.setSize(200L);
        fileIndexMapper.insert(fileIndex3);
        
        // 调用
        Long totalSize = fileIndexMapper.selectTotalStorageSize();
        
        // 断言
        assertEquals(1700L, totalSize);
    }

    // ========== 工具方法 ==========

    private FileIndexDO randomFileIndexDO() {
        return FileIndexDO.builder()
            .sha256(randomString(64))
            .size(randomLong(1L, 10000L))
            .storagePath(randomString(100))
            .configId(randomLong(1L, 10L))
            .refCount(randomInt(1, 5))
            .build();
    }

}
