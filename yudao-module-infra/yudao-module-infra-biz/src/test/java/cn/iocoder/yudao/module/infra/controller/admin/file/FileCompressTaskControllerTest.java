package cn.iocoder.yudao.module.infra.controller.admin.file;

import cn.iocoder.yudao.framework.common.pojo.CommonResult;
import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.test.core.ut.BaseDbUnitTest;
import cn.iocoder.yudao.module.infra.controller.admin.file.vo.compress.*;
import cn.iocoder.yudao.module.infra.dal.dataobject.file.FileCompressTaskDO;
import cn.iocoder.yudao.module.infra.enums.file.FileCompressTaskStatusEnum;
import cn.iocoder.yudao.module.infra.service.file.AsyncFileCompressProcessor;
import cn.iocoder.yudao.module.infra.service.file.FileCompressTaskManager;
import cn.iocoder.yudao.module.infra.service.file.FileService;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.context.annotation.Import;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.Map;

import static cn.iocoder.yudao.framework.test.core.util.AssertUtils.assertPojoEquals;
import static cn.iocoder.yudao.framework.test.core.util.RandomUtils.*;
import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

/**
 * {@link FileCompressTaskController} 的单元测试类
 *
 * <AUTHOR>
 */
@Import(FileCompressTaskController.class)
public class FileCompressTaskControllerTest extends BaseDbUnitTest {

    @Resource
    private FileCompressTaskController fileCompressTaskController;

    @MockBean
    private FileCompressTaskManager taskManager;

    @MockBean
    private FileService fileService;

    @MockBean
    private AsyncFileCompressProcessor asyncFileCompressProcessor;

    @Test
    public void testCreateCompressTask() {
        // 准备参数
        FileCompressTaskCreateReqVO createReqVO = new FileCompressTaskCreateReqVO();
        createReqVO.setFileId(1024L);
        createReqVO.setCompressType("mp4_compress");
        createReqVO.setPriority(3);
        createReqVO.setConfigParams("{\"videoBitrate\":1000}");

        // mock 方法
        Long mockTaskId = 2048L;
        when(fileService.createCompressTask(
            createReqVO.getFileId(),
            createReqVO.getCompressType(),
            createReqVO.getPriority(),
            createReqVO.getConfigParams()
        )).thenReturn(mockTaskId);

        // 调用方法
        CommonResult<Long> result = fileCompressTaskController.createCompressTask(createReqVO);

        // 验证结果
        assertTrue(result.isSuccess());
        assertEquals(mockTaskId, result.getData());

        // 验证调用
        verify(fileService).createCompressTask(
            createReqVO.getFileId(),
            createReqVO.getCompressType(),
            createReqVO.getPriority(),
            createReqVO.getConfigParams()
        );
    }

    @Test
    public void testGetCompressTaskPage() {
        // 准备参数
        FileCompressTaskPageReqVO pageReqVO = new FileCompressTaskPageReqVO();
        pageReqVO.setPageNo(1);
        pageReqVO.setPageSize(10);
        pageReqVO.setStatus(FileCompressTaskStatusEnum.SUCCESS.getStatus());

        // mock 数据
        FileCompressTaskDO mockTask = randomFileCompressTaskDO();
        PageResult<FileCompressTaskDO> mockPageResult = new PageResult<>(
            java.util.Collections.singletonList(mockTask), 1L);

        when(taskManager.getTaskPage(pageReqVO)).thenReturn(mockPageResult);

        // 调用方法
        CommonResult<PageResult<FileCompressTaskRespVO>> result = 
            fileCompressTaskController.getCompressTaskPage(pageReqVO);

        // 验证结果
        assertTrue(result.isSuccess());
        assertNotNull(result.getData());
        assertEquals(1, result.getData().getList().size());
        assertEquals(1L, result.getData().getTotal());

        // 验证调用
        verify(taskManager).getTaskPage(pageReqVO);
    }

    @Test
    public void testGetCompressTask() {
        // 准备参数
        Long taskId = 1024L;

        // mock 数据
        FileCompressTaskDO mockTask = randomFileCompressTaskDO();
        mockTask.setId(taskId);
        when(taskManager.getTask(taskId)).thenReturn(mockTask);

        // 调用方法
        CommonResult<FileCompressTaskRespVO> result = 
            fileCompressTaskController.getCompressTask(taskId);

        // 验证结果
        assertTrue(result.isSuccess());
        assertNotNull(result.getData());
        assertEquals(taskId, result.getData().getId());

        // 验证调用
        verify(taskManager).getTask(taskId);
    }

    @Test
    public void testRetryCompressTask() {
        // 准备参数
        Long taskId = 1024L;

        // mock 方法
        when(taskManager.retryTask(taskId)).thenReturn(true);

        // 调用方法
        CommonResult<Boolean> result = fileCompressTaskController.retryCompressTask(taskId);

        // 验证结果
        assertTrue(result.isSuccess());
        assertTrue(result.getData());

        // 验证调用
        verify(taskManager).retryTask(taskId);
        verify(asyncFileCompressProcessor).processCompressTask(taskId);
    }

    @Test
    public void testRetryCompressTask_Failed() {
        // 准备参数
        Long taskId = 1024L;

        // mock 方法
        when(taskManager.retryTask(taskId)).thenReturn(false);

        // 调用方法
        CommonResult<Boolean> result = fileCompressTaskController.retryCompressTask(taskId);

        // 验证结果
        assertTrue(result.isSuccess());
        assertFalse(result.getData());

        // 验证调用
        verify(taskManager).retryTask(taskId);
        verify(asyncFileCompressProcessor, never()).processCompressTask(any());
    }

    @Test
    public void testCancelCompressTask() {
        // 准备参数
        Long taskId = 1024L;

        // mock 方法
        when(taskManager.cancelTask(taskId)).thenReturn(true);

        // 调用方法
        CommonResult<Boolean> result = fileCompressTaskController.cancelCompressTask(taskId);

        // 验证结果
        assertTrue(result.isSuccess());
        assertTrue(result.getData());

        // 验证调用
        verify(taskManager).cancelTask(taskId);
    }

    @Test
    public void testGetCompressStats() {
        // mock 数据
        Map<Integer, Long> mockStatusCount = new HashMap<>();
        mockStatusCount.put(FileCompressTaskStatusEnum.PENDING.getStatus(), 10L);
        mockStatusCount.put(FileCompressTaskStatusEnum.PROCESSING.getStatus(), 2L);
        mockStatusCount.put(FileCompressTaskStatusEnum.SUCCESS.getStatus(), 100L);
        mockStatusCount.put(FileCompressTaskStatusEnum.FAILED.getStatus(), 5L);
        mockStatusCount.put(FileCompressTaskStatusEnum.CANCELLED.getStatus(), 3L);

        when(taskManager.getStatusStatistics()).thenReturn(mockStatusCount);
        when(taskManager.getSavedStorageSize()).thenReturn(1073741824L);
        when(taskManager.getAvgCompressRatio()).thenReturn(new BigDecimal("65.50"));

        // 调用方法
        CommonResult<FileCompressStatsRespVO> result = 
            fileCompressTaskController.getCompressStats();

        // 验证结果
        assertTrue(result.isSuccess());
        assertNotNull(result.getData());

        FileCompressStatsRespVO stats = result.getData();
        assertEquals(mockStatusCount, stats.getStatusCount());
        assertEquals(10L, stats.getPendingCount());
        assertEquals(2L, stats.getProcessingCount());
        assertEquals(100L, stats.getSuccessCount());
        assertEquals(5L, stats.getFailedCount());
        assertEquals(3L, stats.getCancelledCount());
        assertEquals(120L, stats.getTotalCount());
        assertEquals(new BigDecimal("83.33"), stats.getSuccessRate());
        assertEquals(1073741824L, stats.getSavedStorageSize());
        assertEquals(new BigDecimal("65.50"), stats.getAvgCompressRatio());

        // 验证调用
        verify(taskManager).getStatusStatistics();
        verify(taskManager).getSavedStorageSize();
        verify(taskManager).getAvgCompressRatio();
    }

    @Test
    public void testAutoCreateCompressTasks() {
        // 准备参数
        Long fileId = 1024L;

        // mock 方法
        when(fileService.autoCreateCompressTasks(fileId)).thenReturn(1);

        // 调用方法
        CommonResult<Integer> result = 
            fileCompressTaskController.autoCreateCompressTasks(fileId);

        // 验证结果
        assertTrue(result.isSuccess());
        assertEquals(1, result.getData());

        // 验证调用
        verify(fileService).autoCreateCompressTasks(fileId);
    }

    // ========== 工具方法 ==========

    private FileCompressTaskDO randomFileCompressTaskDO() {
        return FileCompressTaskDO.builder()
            .id(randomLong())
            .fileId(randomLong())
            .filePath(randomString())
            .fileSize(randomLong())
            .fileType("video")
            .compressType("mp4_compress")
            .status(FileCompressTaskStatusEnum.SUCCESS.getStatus())
            .progress(100)
            .compressedPath(randomString())
            .compressedSize(randomLong())
            .compressRatio(new BigDecimal("50.00"))
            .startTime(LocalDateTime.now().minusMinutes(10))
            .endTime(LocalDateTime.now())
            .retryCount(0)
            .maxRetryCount(3)
            .priority(5)
            .build();
    }

}
