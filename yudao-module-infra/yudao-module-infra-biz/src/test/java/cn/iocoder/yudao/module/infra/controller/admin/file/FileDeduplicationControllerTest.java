package cn.iocoder.yudao.module.infra.controller.admin.file;

import cn.iocoder.yudao.framework.common.pojo.CommonResult;
import cn.iocoder.yudao.framework.test.core.ut.BaseDbUnitTest;
import cn.iocoder.yudao.module.infra.controller.admin.file.vo.file.FileDeduplicationStatsRespVO;
import cn.iocoder.yudao.module.infra.service.file.FileService;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.context.annotation.Import;

import javax.annotation.Resource;

import static cn.iocoder.yudao.framework.test.core.util.AssertUtils.assertPojoEquals;
import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;

/**
 * {@link FileDeduplicationController} 的单元测试类
 *
 * <AUTHOR>
 */
@Import(FileDeduplicationController.class)
public class FileDeduplicationControllerTest extends BaseDbUnitTest {

    @Resource
    private FileDeduplicationController fileDeduplicationController;

    @MockBean
    private FileService fileService;

    @Test
    public void testGetDeduplicationStats() {
        // mock 数据
        FileDeduplicationStatsRespVO mockStats = new FileDeduplicationStatsRespVO();
        mockStats.setTotalFileCount(100L);
        mockStats.setDeduplicatedFileCount(30L);
        mockStats.setTotalStorageSize(1048576L);
        mockStats.setSavedStorageSize(314572L);
        mockStats.setDeduplicationRate(30.0);
        mockStats.setStorageSavingRate(23.08);
        
        when(fileService.getDeduplicationStats()).thenReturn(mockStats);
        
        // 调用方法
        CommonResult<FileDeduplicationStatsRespVO> result = fileDeduplicationController.getDeduplicationStats();
        
        // 验证结果
        assertNotNull(result);
        assertTrue(result.isSuccess());
        assertPojoEquals(mockStats, result.getData());
        
        // 验证调用
        verify(fileService).getDeduplicationStats();
    }

    @Test
    public void testGetDeduplicationStats_EmptyData() {
        // mock 数据
        FileDeduplicationStatsRespVO mockStats = new FileDeduplicationStatsRespVO();
        mockStats.setTotalFileCount(0L);
        mockStats.setDeduplicatedFileCount(0L);
        mockStats.setTotalStorageSize(0L);
        mockStats.setSavedStorageSize(0L);
        mockStats.setDeduplicationRate(0.0);
        mockStats.setStorageSavingRate(0.0);
        
        when(fileService.getDeduplicationStats()).thenReturn(mockStats);
        
        // 调用方法
        CommonResult<FileDeduplicationStatsRespVO> result = fileDeduplicationController.getDeduplicationStats();
        
        // 验证结果
        assertNotNull(result);
        assertTrue(result.isSuccess());
        assertPojoEquals(mockStats, result.getData());
        
        // 验证调用
        verify(fileService).getDeduplicationStats();
    }

    @Test
    public void testCleanupUnreferencedFiles() {
        // mock 数据
        int mockCleanedCount = 15;
        when(fileService.cleanupUnreferencedFiles()).thenReturn(mockCleanedCount);
        
        // 调用方法
        CommonResult<Integer> result = fileDeduplicationController.cleanupUnreferencedFiles();
        
        // 验证结果
        assertNotNull(result);
        assertTrue(result.isSuccess());
        assertEquals(mockCleanedCount, result.getData());
        
        // 验证调用
        verify(fileService).cleanupUnreferencedFiles();
    }

    @Test
    public void testCleanupUnreferencedFiles_NoFilesToClean() {
        // mock 数据
        int mockCleanedCount = 0;
        when(fileService.cleanupUnreferencedFiles()).thenReturn(mockCleanedCount);
        
        // 调用方法
        CommonResult<Integer> result = fileDeduplicationController.cleanupUnreferencedFiles();
        
        // 验证结果
        assertNotNull(result);
        assertTrue(result.isSuccess());
        assertEquals(0, result.getData());
        
        // 验证调用
        verify(fileService).cleanupUnreferencedFiles();
    }

    @Test
    public void testCleanupUnreferencedFiles_LargeCount() {
        // mock 数据
        int mockCleanedCount = 1000;
        when(fileService.cleanupUnreferencedFiles()).thenReturn(mockCleanedCount);
        
        // 调用方法
        CommonResult<Integer> result = fileDeduplicationController.cleanupUnreferencedFiles();
        
        // 验证结果
        assertNotNull(result);
        assertTrue(result.isSuccess());
        assertEquals(1000, result.getData());
        
        // 验证调用
        verify(fileService).cleanupUnreferencedFiles();
    }

}
