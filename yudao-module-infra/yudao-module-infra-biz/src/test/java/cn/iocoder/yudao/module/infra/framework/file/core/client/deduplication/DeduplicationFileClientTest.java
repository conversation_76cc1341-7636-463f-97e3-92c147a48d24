package cn.iocoder.yudao.module.infra.framework.file.core.client.deduplication;

import cn.iocoder.yudao.framework.test.core.ut.BaseDbUnitTest;
import cn.iocoder.yudao.module.infra.dal.dataobject.file.FileIndexDO;
import cn.iocoder.yudao.module.infra.dal.mysql.file.FileIndexMapper;
import cn.iocoder.yudao.module.infra.framework.file.core.client.FileClient;
import cn.iocoder.yudao.module.infra.framework.file.core.client.local.LocalFileClient;
import cn.iocoder.yudao.module.infra.framework.file.core.client.local.LocalFileClientConfig;
import cn.iocoder.yudao.module.infra.framework.file.core.enums.FileStorageEnum;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.springframework.context.annotation.Import;

import javax.annotation.Resource;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

/**
 * {@link DeduplicationFileClient} 的单元测试类
 *
 * <AUTHOR>
 */
@Import({FileIndexMapper.class})
public class DeduplicationFileClientTest extends BaseDbUnitTest {

    @Resource
    private FileIndexMapper fileIndexMapper;

    @Mock
    private FileClient mockUnderlyingClient;

    private DeduplicationFileClient deduplicationFileClient;
    private DeduplicationFileClientConfig config;

    @BeforeEach
    void setUp() {
        MockitoAnnotations.openMocks(this);
        
        // 创建配置
        config = new DeduplicationFileClientConfig();
        config.setDomain("http://test.example.com");
        config.setUnderlyingStorage(FileStorageEnum.LOCAL.getStorage());
        config.setEnableDeduplication(true);
        config.setEnableHashVerification(true);
        
        LocalFileClientConfig underlyingConfig = new LocalFileClientConfig();
        underlyingConfig.setBasePath("/tmp/test");
        underlyingConfig.setDomain("http://test.example.com");
        config.setUnderlyingConfig(underlyingConfig);
        
        // 创建客户端
        deduplicationFileClient = new DeduplicationFileClient(1L, config);
        
        // 手动设置底层客户端（避免初始化问题）
        deduplicationFileClient = spy(deduplicationFileClient);
        doReturn(mockUnderlyingClient).when(deduplicationFileClient).getUnderlyingClient();
    }

    @Test
    void testUpload_NewFile() throws Exception {
        // 准备参数
        byte[] content = "Hello, World!".getBytes();
        String path = "/test/file.txt";
        String type = "text/plain";
        String expectedUrl = "http://test.example.com/test/file.txt";
        
        // mock 底层客户端
        when(mockUnderlyingClient.upload(content, path, type)).thenReturn(expectedUrl);
        
        // 调用方法
        String result = deduplicationFileClient.upload(content, path, type);
        
        // 验证结果
        assertEquals(expectedUrl, result);
        
        // 验证数据库记录
        FileIndexDO fileIndex = fileIndexMapper.selectBySha256AndSize(
            "dffd6021bb2bd5b0af676290809ec3a53191dd81c7f70a4b28688a362182986f", 
            (long) content.length
        );
        assertNotNull(fileIndex);
        assertEquals(path, fileIndex.getStoragePath());
        assertEquals(1, fileIndex.getRefCount());
        
        // 验证底层客户端被调用
        verify(mockUnderlyingClient).upload(content, path, type);
    }

    @Test
    void testUpload_ExistingFile() throws Exception {
        // 准备已存在的文件索引
        String sha256 = "dffd6021bb2bd5b0af676290809ec3a53191dd81c7f70a4b28688a362182986f";
        FileIndexDO existingIndex = FileIndexDO.builder()
            .sha256(sha256)
            .size(13L)
            .storagePath("/existing/file.txt")
            .configId(1L)
            .refCount(1)
            .build();
        fileIndexMapper.insert(existingIndex);
        
        // 准备参数
        byte[] content = "Hello, World!".getBytes();
        String path = "/test/file.txt";
        String type = "text/plain";
        
        // 调用方法
        String result = deduplicationFileClient.upload(content, path, type);
        
        // 验证结果 - 应该返回现有文件的URL
        assertEquals("http://test.example.com/existing/file.txt", result);
        
        // 验证引用计数增加
        FileIndexDO updatedIndex = fileIndexMapper.selectById(existingIndex.getId());
        assertEquals(2, updatedIndex.getRefCount());
        
        // 验证底层客户端未被调用
        verify(mockUnderlyingClient, never()).upload(any(), any(), any());
    }

    @Test
    void testUpload_DeduplicationDisabled() throws Exception {
        // 禁用去重
        config.setEnableDeduplication(false);
        
        // 准备参数
        byte[] content = "Hello, World!".getBytes();
        String path = "/test/file.txt";
        String type = "text/plain";
        String expectedUrl = "http://test.example.com/test/file.txt";
        
        // mock 底层客户端
        when(mockUnderlyingClient.upload(content, path, type)).thenReturn(expectedUrl);
        
        // 调用方法
        String result = deduplicationFileClient.upload(content, path, type);
        
        // 验证结果
        assertEquals(expectedUrl, result);
        
        // 验证底层客户端被调用
        verify(mockUnderlyingClient).upload(content, path, type);
        
        // 验证没有创建文件索引
        FileIndexDO fileIndex = fileIndexMapper.selectBySha256AndSize(
            "dffd6021bb2bd5b0af676290809ec3a53191dd81c7f70a4b28688a362182986f", 
            (long) content.length
        );
        assertNull(fileIndex);
    }

    @Test
    void testDelete_ExistingFile_LastReference() throws Exception {
        // 准备文件索引
        FileIndexDO fileIndex = FileIndexDO.builder()
            .sha256("test_hash")
            .size(1024L)
            .storagePath("/test/file.txt")
            .configId(1L)
            .refCount(1)
            .build();
        fileIndexMapper.insert(fileIndex);
        
        // 调用方法
        deduplicationFileClient.delete("/test/file.txt");
        
        // 验证物理文件被删除
        verify(mockUnderlyingClient).delete("/test/file.txt");
        
        // 验证文件索引被删除
        FileIndexDO deletedIndex = fileIndexMapper.selectById(fileIndex.getId());
        assertNull(deletedIndex);
    }

    @Test
    void testDelete_ExistingFile_HasOtherReferences() throws Exception {
        // 准备文件索引（有多个引用）
        FileIndexDO fileIndex = FileIndexDO.builder()
            .sha256("test_hash")
            .size(1024L)
            .storagePath("/test/file.txt")
            .configId(1L)
            .refCount(3)
            .build();
        fileIndexMapper.insert(fileIndex);
        
        // 调用方法
        deduplicationFileClient.delete("/test/file.txt");
        
        // 验证物理文件未被删除
        verify(mockUnderlyingClient, never()).delete(any());
        
        // 验证引用计数减少
        FileIndexDO updatedIndex = fileIndexMapper.selectById(fileIndex.getId());
        assertNotNull(updatedIndex);
        assertEquals(2, updatedIndex.getRefCount());
    }

    @Test
    void testDelete_NonExistentFile() throws Exception {
        // 调用方法
        deduplicationFileClient.delete("/nonexistent/file.txt");
        
        // 验证尝试直接删除物理文件
        verify(mockUnderlyingClient).delete("/nonexistent/file.txt");
    }

    @Test
    void testDelete_DeduplicationDisabled() throws Exception {
        // 禁用去重
        config.setEnableDeduplication(false);
        
        // 调用方法
        deduplicationFileClient.delete("/test/file.txt");
        
        // 验证直接调用底层客户端
        verify(mockUnderlyingClient).delete("/test/file.txt");
    }

    @Test
    void testGetContent() throws Exception {
        // 准备参数
        String path = "/test/file.txt";
        byte[] expectedContent = "file content".getBytes();
        
        // mock 底层客户端
        when(mockUnderlyingClient.getContent(path)).thenReturn(expectedContent);
        
        // 调用方法
        byte[] result = deduplicationFileClient.getContent(path);
        
        // 验证结果
        assertArrayEquals(expectedContent, result);
        verify(mockUnderlyingClient).getContent(path);
    }

    @Test
    void testCleanupUnreferencedFiles() throws Exception {
        // 准备引用计数为0的文件索引
        FileIndexDO fileIndex1 = FileIndexDO.builder()
            .sha256("hash1")
            .size(1024L)
            .storagePath("/test/file1.txt")
            .configId(1L)
            .refCount(0)
            .build();
        fileIndexMapper.insert(fileIndex1);
        
        FileIndexDO fileIndex2 = FileIndexDO.builder()
            .sha256("hash2")
            .size(2048L)
            .storagePath("/test/file2.txt")
            .configId(1L)
            .refCount(0)
            .build();
        fileIndexMapper.insert(fileIndex2);
        
        FileIndexDO fileIndex3 = FileIndexDO.builder()
            .sha256("hash3")
            .size(512L)
            .storagePath("/test/file3.txt")
            .configId(1L)
            .refCount(1) // 有引用，不应该被清理
            .build();
        fileIndexMapper.insert(fileIndex3);
        
        // 调用方法
        int cleanedCount = deduplicationFileClient.cleanupUnreferencedFiles();
        
        // 验证结果
        assertEquals(2, cleanedCount);
        
        // 验证物理文件被删除
        verify(mockUnderlyingClient).delete("/test/file1.txt");
        verify(mockUnderlyingClient).delete("/test/file2.txt");
        verify(mockUnderlyingClient, never()).delete("/test/file3.txt");
        
        // 验证文件索引被删除
        assertNull(fileIndexMapper.selectById(fileIndex1.getId()));
        assertNull(fileIndexMapper.selectById(fileIndex2.getId()));
        assertNotNull(fileIndexMapper.selectById(fileIndex3.getId()));
    }

}
