package cn.iocoder.yudao.module.infra.service.file;

import cn.iocoder.yudao.framework.test.core.ut.BaseDbUnitTest;
import cn.iocoder.yudao.module.infra.controller.admin.file.vo.file.FileDeduplicationStatsRespVO;
import cn.iocoder.yudao.module.infra.dal.dataobject.file.FileConfigDO;
import cn.iocoder.yudao.module.infra.dal.dataobject.file.FileIndexDO;
import cn.iocoder.yudao.module.infra.dal.mysql.file.FileConfigMapper;
import cn.iocoder.yudao.module.infra.dal.mysql.file.FileIndexMapper;
import cn.iocoder.yudao.module.infra.framework.file.core.client.deduplication.DeduplicationFileClient;
import cn.iocoder.yudao.module.infra.service.file.FileConfigService;
import org.junit.jupiter.api.Test;
import org.mockito.Mock;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.context.annotation.Import;

import javax.annotation.Resource;
import java.util.Arrays;

import static cn.iocoder.yudao.framework.test.core.util.RandomUtils.*;
import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;

/**
 * {@link FileServiceImpl} 去重功能的单元测试类
 *
 * <AUTHOR>
 */
@Import({FileServiceImpl.class, FileIndexMapper.class, FileConfigMapper.class})
public class FileServiceDeduplicationTest extends BaseDbUnitTest {

    @Resource
    private FileService fileService;

    @Resource
    private FileIndexMapper fileIndexMapper;

    @Resource
    private FileConfigMapper fileConfigMapper;

    @MockBean
    private FileConfigService fileConfigService;

    @Mock
    private DeduplicationFileClient mockDeduplicationClient;

    @Test
    public void testGetDeduplicationStats() {
        // mock 数据
        // 文件1：引用计数3，大小1000，节省2000字节
        FileIndexDO fileIndex1 = randomFileIndexDO();
        fileIndex1.setSize(1000L);
        fileIndex1.setRefCount(3);
        fileIndexMapper.insert(fileIndex1);
        
        // 文件2：引用计数2，大小500，节省500字节
        FileIndexDO fileIndex2 = randomFileIndexDO();
        fileIndex2.setSize(500L);
        fileIndex2.setRefCount(2);
        fileIndexMapper.insert(fileIndex2);
        
        // 文件3：引用计数1，大小200，节省0字节
        FileIndexDO fileIndex3 = randomFileIndexDO();
        fileIndex3.setSize(200L);
        fileIndex3.setRefCount(1);
        fileIndexMapper.insert(fileIndex3);
        
        // 调用方法
        FileDeduplicationStatsRespVO stats = fileService.getDeduplicationStats();
        
        // 验证结果
        assertNotNull(stats);
        assertEquals(3L, stats.getTotalFileCount()); // 总文件数
        assertEquals(2L, stats.getDeduplicatedFileCount()); // 去重文件数（引用计数>1）
        assertEquals(1700L, stats.getTotalStorageSize()); // 总存储大小
        assertEquals(2500L, stats.getSavedStorageSize()); // 节省的存储大小
        assertEquals(66.67, stats.getDeduplicationRate()); // 去重率 2/3 * 100
        assertEquals(59.52, stats.getStorageSavingRate()); // 存储节省率 2500/(1700+2500) * 100
    }

    @Test
    public void testGetDeduplicationStats_NoData() {
        // 调用方法
        FileDeduplicationStatsRespVO stats = fileService.getDeduplicationStats();
        
        // 验证结果
        assertNotNull(stats);
        assertEquals(0L, stats.getTotalFileCount());
        assertEquals(0L, stats.getDeduplicatedFileCount());
        assertEquals(0L, stats.getTotalStorageSize());
        assertEquals(0L, stats.getSavedStorageSize());
        assertEquals(0.0, stats.getDeduplicationRate());
        assertEquals(0.0, stats.getStorageSavingRate());
    }

    @Test
    public void testGetDeduplicationStats_OnlyNonDeduplicatedFiles() {
        // mock 数据 - 只有引用计数为1的文件
        FileIndexDO fileIndex1 = randomFileIndexDO();
        fileIndex1.setSize(1000L);
        fileIndex1.setRefCount(1);
        fileIndexMapper.insert(fileIndex1);
        
        FileIndexDO fileIndex2 = randomFileIndexDO();
        fileIndex2.setSize(500L);
        fileIndex2.setRefCount(1);
        fileIndexMapper.insert(fileIndex2);
        
        // 调用方法
        FileDeduplicationStatsRespVO stats = fileService.getDeduplicationStats();
        
        // 验证结果
        assertNotNull(stats);
        assertEquals(2L, stats.getTotalFileCount());
        assertEquals(0L, stats.getDeduplicatedFileCount());
        assertEquals(1500L, stats.getTotalStorageSize());
        assertEquals(0L, stats.getSavedStorageSize());
        assertEquals(0.0, stats.getDeduplicationRate());
        assertEquals(0.0, stats.getStorageSavingRate());
    }

    @Test
    public void testCleanupUnreferencedFiles() {
        // mock 文件配置
        FileConfigDO config1 = randomFileConfigDO();
        config1.setId(1L);
        fileConfigMapper.insert(config1);
        
        FileConfigDO config2 = randomFileConfigDO();
        config2.setId(2L);
        fileConfigMapper.insert(config2);
        
        // mock 去重客户端
        DeduplicationFileClient deduplicationClient1 = mock(DeduplicationFileClient.class);
        DeduplicationFileClient deduplicationClient2 = mock(DeduplicationFileClient.class);
        DeduplicationFileClient normalClient = mock(DeduplicationFileClient.class);
        
        when(deduplicationClient1.cleanupUnreferencedFiles()).thenReturn(3);
        when(deduplicationClient2.cleanupUnreferencedFiles()).thenReturn(2);
        
        // mock FileConfigService
        when(fileConfigService.getFileClient(1L)).thenReturn(deduplicationClient1);
        when(fileConfigService.getFileClient(2L)).thenReturn(normalClient); // 非去重客户端
        
        // 调用方法
        int cleanedCount = fileService.cleanupUnreferencedFiles();
        
        // 验证结果
        assertEquals(3, cleanedCount); // 只有去重客户端的清理数量
        
        // 验证调用
        verify(deduplicationClient1).cleanupUnreferencedFiles();
        verify(normalClient, never()).cleanupUnreferencedFiles();
    }

    @Test
    public void testCleanupUnreferencedFiles_NoDeduplicationClients() {
        // mock 文件配置
        FileConfigDO config = randomFileConfigDO();
        config.setId(1L);
        fileConfigMapper.insert(config);
        
        // mock 非去重客户端
        Object normalClient = mock(Object.class);
        when(fileConfigService.getFileClient(1L)).thenReturn(normalClient);
        
        // 调用方法
        int cleanedCount = fileService.cleanupUnreferencedFiles();
        
        // 验证结果
        assertEquals(0, cleanedCount);
    }

    @Test
    public void testCleanupUnreferencedFiles_WithException() {
        // mock 文件配置
        FileConfigDO config1 = randomFileConfigDO();
        config1.setId(1L);
        fileConfigMapper.insert(config1);
        
        FileConfigDO config2 = randomFileConfigDO();
        config2.setId(2L);
        fileConfigMapper.insert(config2);
        
        // mock 去重客户端
        DeduplicationFileClient deduplicationClient1 = mock(DeduplicationFileClient.class);
        DeduplicationFileClient deduplicationClient2 = mock(DeduplicationFileClient.class);
        
        when(deduplicationClient1.cleanupUnreferencedFiles()).thenThrow(new RuntimeException("清理失败"));
        when(deduplicationClient2.cleanupUnreferencedFiles()).thenReturn(5);
        
        // mock FileConfigService
        when(fileConfigService.getFileClient(1L)).thenReturn(deduplicationClient1);
        when(fileConfigService.getFileClient(2L)).thenReturn(deduplicationClient2);
        
        // 调用方法
        int cleanedCount = fileService.cleanupUnreferencedFiles();
        
        // 验证结果 - 即使有异常，也应该继续处理其他配置
        assertEquals(5, cleanedCount);
        
        // 验证调用
        verify(deduplicationClient1).cleanupUnreferencedFiles();
        verify(deduplicationClient2).cleanupUnreferencedFiles();
    }

    // ========== 工具方法 ==========

    private FileIndexDO randomFileIndexDO() {
        return FileIndexDO.builder()
            .sha256(randomString(64))
            .size(randomLong(1L, 10000L))
            .storagePath(randomString(100))
            .configId(randomLong(1L, 10L))
            .refCount(randomInt(1, 5))
            .build();
    }

    private FileConfigDO randomFileConfigDO() {
        return FileConfigDO.builder()
            .name(randomString(10))
            .storage(randomInt(1, 20))
            .remark(randomString(50))
            .master(randomBoolean())
            .config(randomString(200))
            .build();
    }

}
