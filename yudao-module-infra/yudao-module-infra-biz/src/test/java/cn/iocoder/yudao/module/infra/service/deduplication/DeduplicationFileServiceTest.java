package cn.iocoder.yudao.module.infra.service.deduplication;

import cn.iocoder.yudao.framework.test.core.ut.BaseDbUnitTest;
import cn.iocoder.yudao.module.infra.api.deduplication.dto.DeduplicationStatsRespDTO;
import cn.iocoder.yudao.module.infra.dal.mysql.file.FileIndexMapper;
import cn.iocoder.yudao.module.infra.service.deduplication.impl.DeduplicationFileServiceImpl;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.context.annotation.Import;

import javax.annotation.Resource;

import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.mockito.Mockito.when;

/**
 * 去重文件服务测试
 *
 * <AUTHOR>
 */
@Import(DeduplicationFileServiceImpl.class)
public class DeduplicationFileServiceTest extends BaseDbUnitTest {

    @Resource
    private DeduplicationFileService deduplicationFileService;

    @MockBean
    private FileIndexMapper fileIndexMapper;

    @Test
    public void testGetDeduplicationStats() {
        // 准备参数
        when(fileIndexMapper.selectTotalFileCount()).thenReturn(100L);
        when(fileIndexMapper.selectTotalStorageSize()).thenReturn(1024000L);
        when(fileIndexMapper.selectDeduplicatedFileCount()).thenReturn(20L);
        when(fileIndexMapper.selectSavedStorageSize()).thenReturn(204800L);

        // 调用
        DeduplicationStatsRespDTO result = deduplicationFileService.getDeduplicationStats();

        // 断言
        assertNotNull(result);
        assertNotNull(result.getTotalFileCount());
        assertNotNull(result.getTotalStorageSize());
    }

}
