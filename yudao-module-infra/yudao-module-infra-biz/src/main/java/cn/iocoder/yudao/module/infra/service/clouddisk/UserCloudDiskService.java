package cn.iocoder.yudao.module.infra.service.clouddisk;

import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.module.infra.controller.app.clouddisk.vo.*;
import cn.iocoder.yudao.module.infra.dal.dataobject.file.UserCloudDiskDO;

import java.util.List;
import java.util.Map;

/**
 * 用户云盘服务接口
 *
 * <AUTHOR>
 */
public interface UserCloudDiskService {

    /**
     * 创建云盘文件记录
     *
     * @param userId 用户ID
     * @param createReq 创建请求
     * @return 云盘记录ID
     */
    Long createCloudDiskFile(Long userId, AppUserCloudDiskCreateReqVO createReq);

    /**
     * 创建文件夹
     *
     * @param userId 用户ID
     * @param createReq 创建请求
     * @return 文件夹ID
     */
    Long createFolder(Long userId, AppUserCloudDiskCreateFolderReqVO createReq);

    /**
     * 获取云盘文件详情
     *
     * @param id 云盘记录ID
     * @return 云盘文件
     */
    UserCloudDiskDO getCloudDiskFile(Long id);

    /**
     * 获取用户文件列表
     *
     * @param userId 用户ID
     * @param parentId 父目录ID
     * @return 文件列表
     */
    List<UserCloudDiskDO> getFileList(Long userId, Long parentId);

    /**
     * 分页获取云盘文件
     *
     * @param reqVO 分页请求
     * @return 分页结果
     */
    PageResult<UserCloudDiskDO> getCloudDiskFilePage(AppUserCloudDiskPageReqVO reqVO);

    /**
     * 更新云盘文件
     *
     * @param userId 用户ID
     * @param updateReq 更新请求
     */
    void updateCloudDiskFile(Long userId, AppUserCloudDiskUpdateReqVO updateReq);

    /**
     * 删除云盘文件
     *
     * @param userId 用户ID
     * @param id 云盘记录ID
     */
    void deleteCloudDiskFile(Long userId, Long id);

    /**
     * 批量删除云盘文件
     *
     * @param userId 用户ID
     * @param ids 云盘记录ID列表
     * @return 删除数量
     */
    Integer batchDeleteCloudDiskFiles(Long userId, List<Long> ids);

    /**
     * 收藏/取消收藏文件
     *
     * @param userId 用户ID
     * @param id 云盘记录ID
     */
    void toggleFavorite(Long userId, Long id);

    /**
     * 获取收藏文件列表
     *
     * @param userId 用户ID
     * @return 收藏文件列表
     */
    List<UserCloudDiskDO> getFavoriteFiles(Long userId);

    /**
     * 搜索文件
     *
     * @param userId 用户ID
     * @param keyword 关键词
     * @param fileType 文件类型
     * @return 搜索结果
     */
    List<UserCloudDiskDO> searchFiles(Long userId, String keyword, String fileType);

    /**
     * 获取云盘统计信息
     *
     * @param userId 用户ID
     * @return 统计信息
     */
    Map<String, Object> getCloudDiskStats(Long userId);

    /**
     * 分享文件
     *
     * @param userId 用户ID
     * @param id 云盘记录ID
     * @param shareReq 分享请求
     * @return 分享码
     */
    String shareFile(Long userId, Long id, AppUserCloudDiskShareReqVO shareReq);

    /**
     * 获取分享文件
     *
     * @param shareCode 分享码
     * @return 分享文件
     */
    UserCloudDiskDO getSharedFile(String shareCode);

}
