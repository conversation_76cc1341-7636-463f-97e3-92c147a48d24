package cn.iocoder.yudao.module.infra.job;

import cn.iocoder.yudao.framework.quartz.core.handler.JobHandler;
import cn.iocoder.yudao.framework.tenant.core.job.TenantJob;
import cn.iocoder.yudao.module.infra.dal.dataobject.file.FileCompressTaskDO;
import cn.iocoder.yudao.module.infra.enums.file.FileCompressTaskStatusEnum;
import cn.iocoder.yudao.module.infra.service.file.AsyncFileCompressProcessor;
import cn.iocoder.yudao.module.infra.service.file.FileCompressTaskManager;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;

/**
 * 文件压缩任务调度器
 * 定时处理待处理的压缩任务和失败重试任务
 *
 * <AUTHOR>
 */
@Component
@Slf4j
public class FileCompressTaskJob implements JobHandler {

    @Resource
    private FileCompressTaskManager taskManager;

    @Resource
    private AsyncFileCompressProcessor asyncFileCompressProcessor;

    /**
     * 每分钟执行一次，处理待处理的压缩任务
     */
    @Override
    @TenantJob
    public String execute(String param) throws Exception {
        log.debug("[execute][开始执行文件压缩任务调度]");
        
        int processedCount = 0;
        
        try {
            // 处理待处理任务
            processedCount += processPendingTasks();
            
            // 处理重试任务
            processedCount += processRetryTasks();
            
            // 处理超时任务
            processedCount += processTimeoutTasks();
            
            log.info("[execute][文件压缩任务调度完成] processedCount: {}", processedCount);
            return String.format("处理任务数量: %d", processedCount);
            
        } catch (Exception e) {
            log.error("[execute][文件压缩任务调度异常]", e);
            throw e;
        }
    }

    /**
     * 处理待处理任务
     *
     * @return 处理的任务数量
     */
    private int processPendingTasks() {
        // 每次最多处理10个待处理任务
        List<FileCompressTaskDO> pendingTasks = taskManager.getPendingTasks(10);
        
        int processedCount = 0;
        for (FileCompressTaskDO task : pendingTasks) {
            try {
                log.debug("[processPendingTasks][开始处理待处理任务] taskId: {}", task.getId());
                
                // 异步执行压缩任务
                asyncFileCompressProcessor.processCompressTask(task.getId());
                processedCount++;
                
            } catch (Exception e) {
                log.error("[processPendingTasks][处理待处理任务失败] taskId: {}", task.getId(), e);
                
                // 标记任务为失败
                try {
                    taskManager.completeFailed(task.getId(), "调度器处理失败: " + e.getMessage());
                } catch (Exception ex) {
                    log.error("[processPendingTasks][标记任务失败异常] taskId: {}", task.getId(), ex);
                }
            }
        }
        
        if (processedCount > 0) {
            log.info("[processPendingTasks][处理待处理任务完成] processedCount: {}", processedCount);
        }
        
        return processedCount;
    }

    /**
     * 处理重试任务
     *
     * @return 处理的任务数量
     */
    private int processRetryTasks() {
        // 每次最多处理5个重试任务
        List<FileCompressTaskDO> retryTasks = taskManager.getRetryableTasks(5);
        
        int processedCount = 0;
        for (FileCompressTaskDO task : retryTasks) {
            try {
                log.info("[processRetryTasks][开始重试任务] taskId: {}, retryCount: {}", 
                    task.getId(), task.getRetryCount());
                
                // 重置任务状态为待处理
                if (taskManager.retryTask(task.getId())) {
                    // 异步执行压缩任务
                    asyncFileCompressProcessor.processCompressTask(task.getId());
                    processedCount++;
                }
                
            } catch (Exception e) {
                log.error("[processRetryTasks][重试任务失败] taskId: {}", task.getId(), e);
            }
        }
        
        if (processedCount > 0) {
            log.info("[processRetryTasks][处理重试任务完成] processedCount: {}", processedCount);
        }
        
        return processedCount;
    }

    /**
     * 处理超时任务
     *
     * @return 处理的任务数量
     */
    private int processTimeoutTasks() {
        // 查找超过30分钟仍在处理中的任务
        List<FileCompressTaskDO> timeoutTasks = taskManager.getTimeoutTasks(30, 10);
        
        int processedCount = 0;
        for (FileCompressTaskDO task : timeoutTasks) {
            try {
                log.warn("[processTimeoutTasks][发现超时任务] taskId: {}, startTime: {}", 
                    task.getId(), task.getStartTime());
                
                // 将超时任务标记为失败
                taskManager.completeFailed(task.getId(), "任务处理超时（超过30分钟）");
                processedCount++;
                
            } catch (Exception e) {
                log.error("[processTimeoutTasks][处理超时任务失败] taskId: {}", task.getId(), e);
            }
        }
        
        if (processedCount > 0) {
            log.warn("[processTimeoutTasks][处理超时任务完成] processedCount: {}", processedCount);
        }
        
        return processedCount;
    }

}
