package cn.iocoder.yudao.module.infra.framework.file.core.client.deduplication;

import cn.iocoder.yudao.module.infra.framework.file.core.client.FileClientConfig;
import com.fasterxml.jackson.annotation.JsonTypeInfo;
import lombok.Data;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import org.hibernate.validator.constraints.URL;

/**
 * 去重文件客户端配置类
 *
 * <AUTHOR>
 */
@Data
public class DeduplicationFileClientConfig implements FileClientConfig {

    /**
     * 自定义域名
     */
    @NotEmpty(message = "domain 不能为空")
    @URL(message = "domain 必须是 URL 格式")
    private String domain;

    /**
     * 底层存储器类型
     * 枚举 {@link cn.iocoder.yudao.module.infra.framework.file.core.enums.FileStorageEnum}
     */
    @NotNull(message = "底层存储器类型不能为空")
    private Integer underlyingStorage;

    /**
     * 底层存储器配置
     */
    @NotNull(message = "底层存储器配置不能为空")
    @JsonTypeInfo(use = JsonTypeInfo.Id.CLASS, property = "@class")
    private FileClientConfig underlyingConfig;

    /**
     * 是否启用去重
     * 默认为 true，如果设置为 false，则直接使用底层存储器，不进行去重
     */
    private Boolean enableDeduplication = true;

    /**
     * 是否启用哈希验证
     * 默认为 true，上传时会验证文件内容与哈希值是否匹配
     */
    private Boolean enableHashVerification = true;

}
