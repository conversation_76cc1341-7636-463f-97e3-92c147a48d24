package cn.iocoder.yudao.module.infra.framework.file.core.utils;

import cn.hutool.core.io.IoUtil;
import cn.hutool.core.util.StrUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpStatus;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.File;
import java.io.IOException;
import java.io.OutputStream;
import java.io.RandomAccessFile;
import java.nio.file.Files;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * 视频流式播放工具类
 * 支持 HTTP Range 请求，实现视频的边下边播功能
 */
@Slf4j
public class VideoStreamUtils {

    /**
     * 默认缓冲区大小
     */
    private static final int DEFAULT_BUFFER_SIZE = 8192;

    /**
     * 缓存时间常量 - 1小时
     */
    public static final int CACHE_1_HOUR = 60 * 60;

    /**
     * 缓存时间常量 - 1天
     */
    public static final int CACHE_1_DAY = 24 * 60 * 60;

    /**
     * 缓存时间常量 - 7天
     */
    public static final int CACHE_7_DAYS = 7 * 24 * 60 * 60;

    /**
     * 缓存时间常量 - 30天
     */
    public static final int CACHE_30_DAYS = 30 * 24 * 60 * 60;

    /**
     * 默认缓存时间（秒）- 7天
     */
    private static final int DEFAULT_CACHE_MAX_AGE = CACHE_7_DAYS;

    /**
     * 范围请求正则表达式
     */
    private static final Pattern RANGE_PATTERN = Pattern.compile("bytes=(\\d*)-(\\d*)");

    /**
     * 处理视频流请求
     *
     * @param request  HTTP请求
     * @param response HTTP响应
     * @param content  视频内容
     * @param filename 文件名
     * @throws IOException IO异常
     */
    public static void streamVideoContent(HttpServletRequest request, HttpServletResponse response,
                                          byte[] content, String filename) throws IOException {
        streamVideoContent(request, response, content, filename, DEFAULT_CACHE_MAX_AGE);
    }

    /**
     * 处理视频流请求（可自定义缓存时间）
     *
     * @param request    HTTP请求
     * @param response   HTTP响应
     * @param content    视频内容
     * @param filename   文件名
     * @param cacheMaxAge 缓存最大时间（秒）
     * @throws IOException IO异常
     */
    public static void streamVideoContent(HttpServletRequest request, HttpServletResponse response,
                                          byte[] content, String filename, int cacheMaxAge) throws IOException {
        // 如果内容为空，返回404
        if (content == null || content.length == 0) {
            response.setStatus(HttpStatus.NOT_FOUND.value());
            return;
        }

        // 检查响应是否已经被提交，如果已提交则不能修改头部
        if (response.isCommitted()) {
            log.warn("[streamVideoContent][响应已提交，无法设置视频流响应头] filename: {}", filename);
            return;
        }

        try {
            // 设置基本响应头
            response.setContentType("video/mp4");
            response.setHeader("Accept-Ranges", "bytes");
            response.setHeader("Content-Disposition", "inline; filename=\"" + filename + "\"");

            // 设置缓存控制头，提高视频播放性能
            setCacheHeaders(response, cacheMaxAge);

            // 生成ETag用于缓存验证（基于内容长度和文件名的简单hash）
            String etag = "\"" + Integer.toHexString((filename + content.length).hashCode()) + "\"";
            response.setHeader("ETag", etag);

            // 设置Last-Modified（使用当前时间减去1小时，避免时钟偏差问题）
            long lastModified = System.currentTimeMillis() - 3600000; // 1小时前
            response.setDateHeader("Last-Modified", lastModified);

            // 检查客户端缓存
            String ifNoneMatch = request.getHeader("If-None-Match");
            if (etag.equals(ifNoneMatch)) {
                response.setStatus(HttpStatus.NOT_MODIFIED.value());
                return;
            }

            // 获取Range请求头
            String rangeHeader = request.getHeader("Range");
            if (StrUtil.isBlank(rangeHeader)) {
                // 如果没有Range头，返回整个文件
                response.setContentLength(content.length);
                response.setStatus(HttpStatus.OK.value());
                IoUtil.write(response.getOutputStream(), false, content);
                return;
            }

            // 解析Range头
            long[] range = parseRange(rangeHeader, content.length);
            long rangeStart = range[0];
            long rangeEnd = range[1];
            long contentLength = rangeEnd - rangeStart + 1;

            // 设置部分内容响应头
            response.setStatus(HttpStatus.PARTIAL_CONTENT.value());
            response.setHeader("Content-Range", "bytes " + rangeStart + "-" + rangeEnd + "/" + content.length);
            response.setContentLength((int) contentLength);

            // 写入部分内容
            try (OutputStream os = response.getOutputStream()) {
                os.write(content, (int) rangeStart, (int) contentLength);
                os.flush();
            }
        } catch (Exception e) {
            // 检查是否是客户端断开连接的异常
            if (isClientDisconnectedException(e)) {
                // 客户端断开连接是正常情况，只记录debug级别日志
                log.debug("[streamVideoContent][客户端断开连接] filename: {}, error: {}", filename, e.getMessage());
                return; // 不抛出异常，避免传播到全局异常处理器
            }

            // 其他异常记录错误日志
            log.error("[streamVideoContent][处理视频流时发生异常] filename: {}", filename, e);
            // 如果响应还没有提交，尝试重置Content-Type
            if (!response.isCommitted()) {
                response.reset();
                response.setStatus(HttpStatus.INTERNAL_SERVER_ERROR.value());
                response.setContentType("application/json;charset=UTF-8");
            }
            throw e;
        }
    } // http://127.0.0.1:48080/31/get/

    /**
     * 处理视频流请求（从文件）
     *
     * @param request  HTTP请求
     * @param response HTTP响应
     * @param file     视频文件
     * @throws IOException IO异常
     */
    public static void streamVideoFile(HttpServletRequest request, HttpServletResponse response,
                                       File file) throws IOException {
        streamVideoFile(request, response, file, DEFAULT_CACHE_MAX_AGE);
    }

    /**
     * 处理视频流请求（从文件，可自定义缓存时间）
     *
     * @param request     HTTP请求
     * @param response    HTTP响应
     * @param file        视频文件
     * @param cacheMaxAge 缓存最大时间（秒）
     * @throws IOException IO异常
     */
    public static void streamVideoFile(HttpServletRequest request, HttpServletResponse response,
                                       File file, int cacheMaxAge) throws IOException {
        // 如果文件不存在，返回404
        if (file == null || !file.exists() || !file.isFile() || !file.canRead()) {
            response.setStatus(HttpStatus.NOT_FOUND.value());
            return;
        }

        // 检查响应是否已经被提交，如果已提交则不能修改头部
        if (response.isCommitted()) {
            log.warn("[streamVideoFile][响应已提交，无法设置视频流响应头] file: {}", file.getName());
            return;
        }

        try {
            // 获取文件长度
            long fileLength = file.length();

            // 设置基本响应头
            response.setContentType("video/mp4");
            response.setHeader("Accept-Ranges", "bytes");
            response.setHeader("Content-Disposition", "inline; filename=\"" + file.getName() + "\"");

            // 设置缓存控制头，提高视频播放性能
            setCacheHeaders(response, cacheMaxAge);

            // 生成ETag用于缓存验证（基于文件路径和大小的hash）
            String etag = "\"" + Integer.toHexString((file.getAbsolutePath() + fileLength).hashCode()) + "\"";
            response.setHeader("ETag", etag);

            // 设置Last-Modified为文件的最后修改时间
            response.setDateHeader("Last-Modified", file.lastModified());

            // 检查客户端缓存
            String ifNoneMatch = request.getHeader("If-None-Match");
            if (etag.equals(ifNoneMatch)) {
                response.setStatus(HttpStatus.NOT_MODIFIED.value());
                return;
            }

            // 获取Range请求头
            String rangeHeader = request.getHeader("Range");

            if (StrUtil.isBlank(rangeHeader)) {
                // 如果没有Range头，返回整个文件
                // 如果 file.length() 结果超出了 int 范围（最大值为 2,147,483,647），会变成 负数。 代理服务器就会报错。
                response.setContentLengthLong( fileLength);
                response.setStatus(HttpStatus.OK.value());
                Files.copy(file.toPath(), response.getOutputStream());
                return;
            }

            // 解析Range头
            long[] range = parseRange(rangeHeader, fileLength);
            long rangeStart = range[0];
            long rangeEnd = range[1];
            long contentLength = rangeEnd - rangeStart + 1;

            // 设置部分内容响应头
            response.setStatus(HttpStatus.PARTIAL_CONTENT.value());
            response.setHeader("Content-Range", "bytes " + rangeStart + "-" + rangeEnd + "/" + fileLength);
            // 如果 file.length() 结果超出了 int 范围（最大值为 2,147,483,647），会变成 负数。 代理服务器就会报错。
            response.setContentLengthLong(contentLength);

            // 使用RandomAccessFile读取文件的指定部分
            try (RandomAccessFile raf = new RandomAccessFile(file, "r");
                 OutputStream os = response.getOutputStream()) {

                byte[] buffer = new byte[DEFAULT_BUFFER_SIZE];
                raf.seek(rangeStart);

                // 计算需要读取的总字节数
                long remaining = contentLength;
                int read;

                while (remaining > 0) {
                    // 计算本次读取的字节数
                    int bytesToRead = (int) Math.min(buffer.length, remaining);
                    read = raf.read(buffer, 0, bytesToRead);

                    if (read == -1) {
                        break;
                    }

                    os.write(buffer, 0, read);
                    remaining -= read;
                }

                os.flush();
            }
        } catch (Exception e) {
            // 检查是否是客户端断开连接的异常
            if (isClientDisconnectedException(e)) {
                // 客户端断开连接是正常情况，只记录debug级别日志
                log.debug("[streamVideoFile][客户端断开连接] file: {}, error: {}", file.getName(), e.getMessage());
                return; // 不抛出异常，避免传播到全局异常处理器
            }

            // 其他异常记录错误日志
            log.error("[streamVideoFile][处理视频文件流时发生异常] file: {}", file.getName(), e);
            // 如果响应还没有提交，尝试重置Content-Type
            if (!response.isCommitted()) {
                response.reset();
                response.setStatus(HttpStatus.INTERNAL_SERVER_ERROR.value());
                response.setContentType("application/json;charset=UTF-8");
            }
            throw e;
        }
    }

    /**
     * 解析Range头
     *
     * @param rangeHeader Range请求头
     * @param fileLength  文件长度
     * @return 范围数组 [开始位置, 结束位置]
     */
    private static long[] parseRange(String rangeHeader, long fileLength) {
        Matcher matcher = RANGE_PATTERN.matcher(rangeHeader);

        if (matcher.find()) {
            String startStr = matcher.group(1);
            String endStr = matcher.group(2);

            long start = 0;
            long end = fileLength - 1;

            if (StrUtil.isNotEmpty(startStr)) {
                start = Long.parseLong(startStr);
            }

            if (StrUtil.isNotEmpty(endStr)) {
                end = Long.parseLong(endStr);
            }

            // 确保范围有效
            if (start > end || start >= fileLength || end >= fileLength) {
                start = 0;
                end = fileLength - 1;
            }

            return new long[]{start, end};
        }

        // 默认返回整个文件
        return new long[]{0, fileLength - 1};
    }

    /**
     * 设置缓存相关的HTTP响应头
     *
     * @param response HTTP响应
     * @param maxAge   缓存最大时间（秒）
     */
    private static void setCacheHeaders(HttpServletResponse response, int maxAge) {
        // 设置Cache-Control头
        response.setHeader("Cache-Control", "public, max-age=" + maxAge + ", immutable");

        // 动态计算过期时间（当前时间 + maxAge秒）
        long expiresTime = System.currentTimeMillis() + (maxAge * 1000L);
        response.setDateHeader("Expires", expiresTime);
    }

    /**
     * 检查是否是客户端断开连接的异常
     *
     * @param e 异常对象
     * @return 是否是客户端断开连接异常
     */
    public static boolean isClientDisconnectedException(Exception e) {
        if (e == null) {
            return false;
        }

        String message = e.getMessage();
        if (message == null) {
            message = "";
        }

        String className = e.getClass().getName();

        // 排除明确不是客户端断开连接的异常
        if (isBackendServiceException(className, message)) {
            return false;
        }

        // 1. 明确的客户端断开连接异常类型
        if (className.contains("AsyncRequestNotUsableException") ||
            className.contains("ClientAbortException") ||
            className.contains("EofException")) {
            return true;
        }

        // 2. 检查是否是与HTTP响应输出相关的IOException
        if (e instanceof java.io.IOException) {
            // 检查堆栈信息，确保是在HTTP响应输出过程中发生的异常
            StackTraceElement[] stackTrace = e.getStackTrace();
            boolean isHttpResponseRelated = false;

            for (StackTraceElement element : stackTrace) {
                String elementClassName = element.getClassName();
                String methodName = element.getMethodName();

                // 检查是否在HTTP响应输出相关的类和方法中
                if ((elementClassName.contains("ServletOutputStream") ||
                     elementClassName.contains("ResponseFacade") ||
                     elementClassName.contains("CoyoteOutputStream") ||
                     elementClassName.contains("HttpServletResponse") ||
                     elementClassName.contains("StandardServletAsyncWebRequest")) &&
                    (methodName.contains("write") ||
                     methodName.contains("flush") ||
                     methodName.contains("close"))) {
                    isHttpResponseRelated = true;
                    break;
                }
            }

            // 只有在HTTP响应输出过程中的IOException才可能是客户端断开连接
            if (isHttpResponseRelated) {
                return message.contains("Connection reset by peer") ||
                       message.contains("Broken pipe") ||
                       message.contains("Connection aborted") ||
                       message.contains("Connection closed") ||
                       message.contains("ServletOutputStream failed to write") ||
                       message.contains("An established connection was aborted") ||
                       message.contains("远程主机强迫关闭了一个现有的连接") ||
                       message.contains("你的主机中的软件中止了一个已建立的连接") ||
                       message.contains("Socket closed") ||
                       message.contains("Connection reset") ||
                       message.contains("Broken pipe");
            }
        }

        return false;
    }

    /**
     * 检查是否是后端服务异常（数据库、Redis、消息队列等）
     * 这些异常不应该被误判为客户端断开连接
     *
     * @param className 异常类名
     * @param message   异常消息
     * @return 是否是后端服务异常
     */
    private static boolean isBackendServiceException(String className, String message) {
        // 数据库相关异常
        if (className.contains("SQLException") ||
            className.contains("DataAccessException") ||
            className.contains("JdbcException") ||
            className.contains("HikariPool") ||
            className.contains("ConnectionPool") ||
            message.contains("database") ||
            message.contains("jdbc") ||
            message.contains("mysql") ||
            message.contains("postgresql") ||
            message.contains("oracle")) {
            return true;
        }

        // Redis相关异常
        if (className.contains("RedisException") ||
            className.contains("JedisException") ||
            className.contains("LettuceException") ||
            message.contains("redis") ||
            message.contains("jedis") ||
            message.contains("lettuce")) {
            return true;
        }

        // 消息队列相关异常
        if (className.contains("RabbitMQ") ||
            className.contains("KafkaException") ||
            className.contains("JmsException") ||
            message.contains("rabbitmq") ||
            message.contains("kafka") ||
            message.contains("activemq")) {
            return true;
        }

        // HTTP客户端调用异常（调用其他服务）
        if (className.contains("HttpClientException") ||
            className.contains("RestClientException") ||
            className.contains("FeignException") ||
            className.contains("WebClientException") ||
            message.contains("feign") ||
            message.contains("RestTemplate") ||
            message.contains("WebClient")) {
            return true;
        }

        // 文件系统异常
        if (className.contains("FileNotFoundException") ||
            className.contains("FileSystemException") ||
            message.contains("No such file") ||
            message.contains("Permission denied") ||
            message.contains("Disk full")) {
            return true;
        }

        return false;
    }
}
