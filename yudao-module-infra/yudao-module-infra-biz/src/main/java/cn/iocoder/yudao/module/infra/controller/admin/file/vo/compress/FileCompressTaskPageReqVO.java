package cn.iocoder.yudao.module.infra.controller.admin.file.vo.compress;

import cn.iocoder.yudao.framework.common.pojo.PageParam;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import org.springframework.format.annotation.DateTimeFormat;

import java.time.LocalDateTime;

import static cn.iocoder.yudao.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;

/**
 * 管理后台 - 文件压缩任务分页 Request VO
 *
 * <AUTHOR>
 */
@Schema(description = "管理后台 - 文件压缩任务分页 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class FileCompressTaskPageReqVO extends PageParam {

    @Schema(description = "文件ID", example = "1024")
    private Long fileId;

    @Schema(description = "任务状态", example = "1")
    private Integer status;

    @Schema(description = "压缩类型", example = "mp4_compress")
    private String compressType;

    @Schema(description = "文件类型", example = "video")
    private String fileType;

    @Schema(description = "创建时间")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime[] createTime;

}
