package cn.iocoder.yudao.module.infra.controller.admin.file.vo.compress;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Map;

/**
 * 管理后台 - 文件压缩统计 Response VO
 *
 * <AUTHOR>
 */
@Schema(description = "管理后台 - 文件压缩统计 Response VO")
@Data
public class FileCompressStatsRespVO {

    @Schema(description = "各状态任务数量统计", requiredMode = Schema.RequiredMode.REQUIRED)
    private Map<Integer, Long> statusCount;

    @Schema(description = "待处理任务数量", requiredMode = Schema.RequiredMode.REQUIRED, example = "10")
    private Long pendingCount;

    @Schema(description = "处理中任务数量", requiredMode = Schema.RequiredMode.REQUIRED, example = "2")
    private Long processingCount;

    @Schema(description = "成功任务数量", requiredMode = Schema.RequiredMode.REQUIRED, example = "100")
    private Long successCount;

    @Schema(description = "失败任务数量", requiredMode = Schema.RequiredMode.REQUIRED, example = "5")
    private Long failedCount;

    @Schema(description = "已取消任务数量", requiredMode = Schema.RequiredMode.REQUIRED, example = "3")
    private Long cancelledCount;

    @Schema(description = "总任务数量", requiredMode = Schema.RequiredMode.REQUIRED, example = "120")
    private Long totalCount;

    @Schema(description = "成功率（百分比）", requiredMode = Schema.RequiredMode.REQUIRED, example = "83.33")
    private BigDecimal successRate;

    @Schema(description = "节省的存储空间（字节）", requiredMode = Schema.RequiredMode.REQUIRED, example = "1073741824")
    private Long savedStorageSize;

    @Schema(description = "平均压缩比例（百分比）", requiredMode = Schema.RequiredMode.REQUIRED, example = "65.50")
    private BigDecimal avgCompressRatio;

}
