package cn.iocoder.yudao.module.infra.service.file;

import cn.hutool.core.util.StrUtil;
import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.module.infra.controller.admin.file.vo.compress.FileCompressTaskPageReqVO;
import cn.iocoder.yudao.module.infra.dal.dataobject.file.FileCompressTaskDO;
import cn.iocoder.yudao.module.infra.dal.dataobject.file.FileDO;
import cn.iocoder.yudao.module.infra.dal.mysql.file.FileCompressTaskMapper;
import cn.iocoder.yudao.module.infra.enums.file.FileCompressTaskStatusEnum;
import cn.iocoder.yudao.module.infra.enums.file.FileCompressTypeEnum;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

/**
 * 文件压缩任务管理器
 *
 * <AUTHOR>
 */
@Service
@Slf4j
public class FileCompressTaskManager {

    @Resource
    private FileCompressTaskMapper fileCompressTaskMapper;

    // ==================== 任务创建 ====================

    /**
     * 创建压缩任务
     *
     * @param file 文件信息
     * @param compressType 压缩类型
     * @param priority 优先级
     * @param configParams 配置参数
     * @return 任务ID
     */
    @Transactional(rollbackFor = Exception.class)
    public Long createTask(FileDO file, FileCompressTypeEnum compressType, Integer priority, String configParams) {
        // 检查是否已存在相同的任务
        FileCompressTaskDO existingTask = fileCompressTaskMapper.selectByFileIdAndCompressType(
            file.getId(), compressType.getCode());
        if (existingTask != null && !existingTask.isCompleted()) {
            log.warn("[createTask][文件({})的压缩任务({})已存在，跳过创建]", file.getId(), compressType.getCode());
            return existingTask.getId();
        }

        // 获取文件扩展名
        String fileExtension = getFileExtension(file.getPath());
        
        // 创建任务
        FileCompressTaskDO task = FileCompressTaskDO.builder()
            .fileId(file.getId())
            .filePath(file.getPath())
            .fileSize(file.getSize())
            .fileType(compressType.getFileType())
            .compressType(compressType.getCode())
            .status(FileCompressTaskStatusEnum.PENDING.getStatus())
            .progress(0)
            .retryCount(0)
            .maxRetryCount(3)
            .priority(priority != null ? priority : 5)
            .configParams(configParams)
            .build();

        fileCompressTaskMapper.insert(task);
        
        log.info("[createTask][创建压缩任务成功] taskId: {}, fileId: {}, compressType: {}", 
            task.getId(), file.getId(), compressType.getCode());
        
        return task.getId();
    }

    /**
     * 批量创建压缩任务
     *
     * @param files 文件列表
     * @param compressType 压缩类型
     * @param priority 优先级
     * @return 创建的任务数量
     */
    @Transactional(rollbackFor = Exception.class)
    public int createBatchTasks(List<FileDO> files, FileCompressTypeEnum compressType, Integer priority) {
        int createdCount = 0;
        for (FileDO file : files) {
            try {
                createTask(file, compressType, priority, null);
                createdCount++;
            } catch (Exception e) {
                log.error("[createBatchTasks][创建任务失败] fileId: {}, compressType: {}", 
                    file.getId(), compressType.getCode(), e);
            }
        }
        return createdCount;
    }

    // ==================== 任务执行 ====================

    /**
     * 开始处理任务
     *
     * @param taskId 任务ID
     * @return 是否成功开始
     */
    @Transactional(rollbackFor = Exception.class)
    public boolean startProcessing(Long taskId) {
        int updated = fileCompressTaskMapper.startProcessing(
            taskId, 
            FileCompressTaskStatusEnum.PROCESSING.getStatus(),
            FileCompressTaskStatusEnum.PENDING.getStatus()
        );
        
        if (updated > 0) {
            log.info("[startProcessing][开始处理任务] taskId: {}", taskId);
            return true;
        } else {
            log.warn("[startProcessing][任务状态不正确，无法开始处理] taskId: {}", taskId);
            return false;
        }
    }

    /**
     * 更新任务进度
     *
     * @param taskId 任务ID
     * @param progress 进度（0-100）
     */
    public void updateProgress(Long taskId, Integer progress) {
        fileCompressTaskMapper.updateStatus(taskId, FileCompressTaskStatusEnum.PROCESSING.getStatus(), progress);
        log.debug("[updateProgress][更新任务进度] taskId: {}, progress: {}%", taskId, progress);
    }

    /**
     * 完成任务处理（成功）
     *
     * @param taskId 任务ID
     * @param compressedPath 压缩后文件路径
     * @param compressedSize 压缩后文件大小
     * @param originalSize 原始文件大小
     */
    @Transactional(rollbackFor = Exception.class)
    public void completeSuccess(Long taskId, String compressedPath, Long compressedSize, Long originalSize) {
        // 计算压缩比例
        BigDecimal compressRatio = null;
        if (originalSize != null && originalSize > 0 && compressedSize != null) {
            compressRatio = BigDecimal.valueOf(compressedSize)
                .divide(BigDecimal.valueOf(originalSize), 4, RoundingMode.HALF_UP)
                .multiply(BigDecimal.valueOf(100));
        }

        fileCompressTaskMapper.completeProcessing(
            taskId,
            FileCompressTaskStatusEnum.SUCCESS.getStatus(),
            compressedPath,
            compressedSize,
            compressRatio,
            null
        );

        log.info("[completeSuccess][任务处理成功] taskId: {}, compressedPath: {}, " +
                "originalSize: {}, compressedSize: {}, ratio: {}%", 
                taskId, compressedPath, originalSize, compressedSize, compressRatio);
    }

    /**
     * 完成任务处理（失败）
     *
     * @param taskId 任务ID
     * @param errorMessage 错误信息
     */
    @Transactional(rollbackFor = Exception.class)
    public void completeFailed(Long taskId, String errorMessage) {
        // 增加重试次数
        fileCompressTaskMapper.incrementRetryCount(taskId);
        
        // 更新任务状态为失败
        fileCompressTaskMapper.completeProcessing(
            taskId,
            FileCompressTaskStatusEnum.FAILED.getStatus(),
            null,
            null,
            null,
            errorMessage
        );

        log.error("[completeFailed][任务处理失败] taskId: {}, error: {}", taskId, errorMessage);
    }

    // ==================== 任务查询 ====================

    /**
     * 获取任务详情
     *
     * @param taskId 任务ID
     * @return 任务信息
     */
    public FileCompressTaskDO getTask(Long taskId) {
        return fileCompressTaskMapper.selectById(taskId);
    }

    /**
     * 分页查询任务
     *
     * @param pageReqVO 分页请求
     * @return 任务分页结果
     */
    public PageResult<FileCompressTaskDO> getTaskPage(FileCompressTaskPageReqVO pageReqVO) {
        return fileCompressTaskMapper.selectPage(pageReqVO);
    }

    /**
     * 获取待处理任务列表
     *
     * @param limit 限制数量
     * @return 待处理任务列表
     */
    public List<FileCompressTaskDO> getPendingTasks(int limit) {
        return fileCompressTaskMapper.selectPendingTasks(limit);
    }

    /**
     * 获取需要重试的任务列表
     *
     * @param limit 限制数量
     * @return 需要重试的任务列表
     */
    public List<FileCompressTaskDO> getRetryableTasks(int limit) {
        return fileCompressTaskMapper.selectRetryableTasks(limit);
    }

    /**
     * 获取超时任务列表
     *
     * @param timeoutMinutes 超时分钟数
     * @param limit 限制数量
     * @return 超时任务列表
     */
    public List<FileCompressTaskDO> getTimeoutTasks(int timeoutMinutes, int limit) {
        return fileCompressTaskMapper.selectTimeoutTasks(timeoutMinutes, limit);
    }

    // ==================== 任务管理 ====================

    /**
     * 取消任务
     *
     * @param taskId 任务ID
     * @return 是否成功取消
     */
    @Transactional(rollbackFor = Exception.class)
    public boolean cancelTask(Long taskId) {
        FileCompressTaskDO task = fileCompressTaskMapper.selectById(taskId);
        if (task == null) {
            return false;
        }

        // 只能取消待处理或失败的任务
        if (task.getStatus().equals(FileCompressTaskStatusEnum.PENDING.getStatus()) ||
            task.getStatus().equals(FileCompressTaskStatusEnum.FAILED.getStatus())) {
            
            fileCompressTaskMapper.updateStatus(taskId, FileCompressTaskStatusEnum.CANCELLED.getStatus(), 0);
            log.info("[cancelTask][取消任务成功] taskId: {}", taskId);
            return true;
        }

        log.warn("[cancelTask][任务状态不允许取消] taskId: {}, status: {}", taskId, task.getStatus());
        return false;
    }

    /**
     * 重试任务
     *
     * @param taskId 任务ID
     * @return 是否成功重试
     */
    @Transactional(rollbackFor = Exception.class)
    public boolean retryTask(Long taskId) {
        FileCompressTaskDO task = fileCompressTaskMapper.selectById(taskId);
        if (task == null || !task.canRetry()) {
            return false;
        }

        // 重置任务状态
        fileCompressTaskMapper.updateStatus(taskId, FileCompressTaskStatusEnum.PENDING.getStatus(), 0);
        log.info("[retryTask][重试任务成功] taskId: {}", taskId);
        return true;
    }

    // ==================== 统计信息 ====================

    /**
     * 获取任务状态统计
     *
     * @return 状态统计
     */
    public Map<Integer, Long> getStatusStatistics() {
        return fileCompressTaskMapper.selectStatusCount();
    }

    /**
     * 获取压缩节省的存储空间
     *
     * @return 节省的字节数
     */
    public Long getSavedStorageSize() {
        return fileCompressTaskMapper.selectSavedStorageSize();
    }

    /**
     * 获取平均压缩比例
     *
     * @return 平均压缩比例
     */
    public BigDecimal getAvgCompressRatio() {
        return fileCompressTaskMapper.selectAvgCompressRatio();
    }

    // ==================== 工具方法 ====================

    /**
     * 获取文件扩展名
     *
     * @param filePath 文件路径
     * @return 扩展名（不含点号）
     */
    private String getFileExtension(String filePath) {
        if (StrUtil.isBlank(filePath)) {
            return "";
        }
        int lastDotIndex = filePath.lastIndexOf('.');
        if (lastDotIndex > 0 && lastDotIndex < filePath.length() - 1) {
            return filePath.substring(lastDotIndex + 1).toLowerCase();
        }
        return "";
    }

}
