package cn.iocoder.yudao.module.infra.service.file;

import cn.iocoder.yudao.module.infra.api.deduplication.dto.DeduplicationFileUploadReqDTO;
import cn.iocoder.yudao.module.infra.dal.dataobject.file.FileProcessingTaskDO;

import java.util.List;
import java.util.Map;

/**
 * 文件处理服务接口
 *
 * <AUTHOR>
 */
public interface FileProcessingService {

    /**
     * 异步处理文件（压缩、水印、HLS等）
     *
     * @param originalFileId 原始文件ID
     * @param processingOptions 处理选项
     * @return 任务ID
     */
    String processFileAsync(Long originalFileId, DeduplicationFileUploadReqDTO.FileProcessingOptions processingOptions);

    /**
     * 批量处理文件
     *
     * @param fileIds 文件ID列表
     * @param processingTypes 处理类型列表
     * @param processingParams 处理参数
     * @return 任务ID
     */
    String batchProcessFiles(List<Long> fileIds, List<String> processingTypes, Map<String, Object> processingParams);

    /**
     * 查询处理任务状态
     *
     * @param taskId 任务ID
     * @return 任务状态信息
     */
    Map<String, Object> getTaskStatus(String taskId);

    /**
     * 获取文件的衍生版本列表
     *
     * @param originalFileId 原始文件ID
     * @return 衍生版本列表
     */
    List<Map<String, Object>> getFileDerivatives(Long originalFileId);

    /**
     * 处理单个文件（同步）
     *
     * @param originalFileId 原始文件ID
     * @param processingType 处理类型
     * @param processingParams 处理参数
     * @return 衍生文件ID
     */
    Long processSingleFile(Long originalFileId, String processingType, Map<String, Object> processingParams);

    /**
     * 生成HLS多清晰度版本
     *
     * @param originalFileId 原始视频文件ID
     * @param hlsConfig HLS配置
     * @return HLS版本信息映射
     */
    Map<String, Long> generateHlsVersions(Long originalFileId, DeduplicationFileUploadReqDTO.HlsQualityConfig hlsConfig);

    /**
     * 清理处理任务和临时文件
     *
     * @param taskId 任务ID
     */
    void cleanupTask(String taskId);

    /**
     * 重试失败的处理任务
     *
     * @param taskId 任务ID
     * @return 新的任务ID
     */
    String retryTask(String taskId);

    /**
     * 取消处理任务
     *
     * @param taskId 任务ID
     * @return 是否成功
     */
    Boolean cancelTask(String taskId);

}
