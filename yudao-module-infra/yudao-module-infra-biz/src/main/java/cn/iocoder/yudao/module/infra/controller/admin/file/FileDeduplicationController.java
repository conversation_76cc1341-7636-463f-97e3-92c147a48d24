package cn.iocoder.yudao.module.infra.controller.admin.file;

import cn.iocoder.yudao.framework.common.pojo.CommonResult;
import cn.iocoder.yudao.module.infra.controller.admin.file.vo.file.FileDeduplicationStatsRespVO;
import cn.iocoder.yudao.module.infra.service.file.FileService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;

import static cn.iocoder.yudao.framework.common.pojo.CommonResult.success;

/**
 * 管理后台 - 文件去重管理
 *
 * <AUTHOR>
 */
@Tag(name = "管理后台 - 文件去重管理")
@RestController
@RequestMapping("/infra/file-deduplication")
@Validated
@Slf4j
public class FileDeduplicationController {

    @Resource
    private FileService fileService;

    @GetMapping("/stats")
    @Operation(summary = "获取文件去重统计信息")
    @PreAuthorize("@ss.hasPermission('infra:file:query')")
    public CommonResult<FileDeduplicationStatsRespVO> getDeduplicationStats() {
        FileDeduplicationStatsRespVO stats = fileService.getDeduplicationStats();
        return success(stats);
    }

    @PostMapping("/cleanup")
    @Operation(summary = "清理引用计数为0的文件")
    @PreAuthorize("@ss.hasPermission('infra:file:delete')")
    public CommonResult<Integer> cleanupUnreferencedFiles() {
        int cleanedCount = fileService.cleanupUnreferencedFiles();
        log.info("[cleanupUnreferencedFiles][清理完成] cleanedCount: {}", cleanedCount);
        return success(cleanedCount);
    }

}
