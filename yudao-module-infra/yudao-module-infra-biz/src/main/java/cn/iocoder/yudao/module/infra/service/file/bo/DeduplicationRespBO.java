package cn.iocoder.yudao.module.infra.service.file.bo;

import cn.iocoder.yudao.module.infra.dal.dataobject.file.FileIndexDO;
import cn.iocoder.yudao.module.infra.service.file.ReplacementDeduplicationService;

/**
 * 去重结果
 */

public class DeduplicationRespBO {
    private final boolean found;
    private final boolean isCompressed;
    private final FileIndexDO fileIndex;

    private DeduplicationRespBO(boolean found, boolean isCompressed, FileIndexDO fileIndex) {
        this.found = found;
        this.isCompressed = isCompressed;
        this.fileIndex = fileIndex;
    }

    public static DeduplicationRespBO notFound() {
        return new DeduplicationRespBO(false, false, null);
    }

    public static DeduplicationRespBO foundOriginal(FileIndexDO fileIndex) {
        return new DeduplicationRespBO(true, false, fileIndex);
    }

    public static DeduplicationRespBO foundCompressed(FileIndexDO fileIndex) {
        return new DeduplicationRespBO(true, true, fileIndex);
    }

    // Getters
    public boolean isFound() { return found; }
    public boolean isCompressed() { return isCompressed; }
    public FileIndexDO getFileIndex() { return fileIndex; }

    /**
     * 获取文件路径
     */
    public String getFilePath() {
        return found ? fileIndex.getStoragePath() : null;
    }

    /**
     * 获取文件大小
     */
    public Long getFileSize() {
        return found ? fileIndex.getCurrentSize() : null;
    }

    /**
     * 获取压缩节省的空间
     */
    public Long getSavedSpace() {
        return found && isCompressed ? fileIndex.getSavedSpace() : 0L;
    }
}
