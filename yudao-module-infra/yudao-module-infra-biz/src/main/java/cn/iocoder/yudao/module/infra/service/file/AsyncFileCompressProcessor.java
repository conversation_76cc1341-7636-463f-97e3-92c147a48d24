package cn.iocoder.yudao.module.infra.service.file;

import cn.hutool.core.io.FileUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import cn.iocoder.yudao.module.infra.dal.dataobject.file.FileCompressTaskDO;
import cn.iocoder.yudao.module.infra.dal.dataobject.file.FileDO;
import cn.iocoder.yudao.module.infra.enums.file.FileCompressTypeEnum;
import cn.iocoder.yudao.module.infra.framework.file.core.client.FileClient;
import cn.iocoder.yudao.module.infra.service.file.dto.FileCompressConfigDTO;
import cn.iocoder.yudao.module.tv.util.FFmpegVideoUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.io.File;
import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;

/**
 * 异步文件压缩处理器
 * 基于现有的 ProcessService，扩展支持多种压缩格式
 *
 * <AUTHOR>
 */
@Service
@Slf4j
public class AsyncFileCompressProcessor {

    @Resource
    private FileCompressTaskManager taskManager;

    @Resource
    private FileConfigService fileConfigService;

    @Resource
    private FileService fileService;

    /**
     * 异步处理压缩任务
     *
     * @param taskId 任务ID
     */
    @Async
    public void processCompressTask(Long taskId) {
        FileCompressTaskDO task = taskManager.getTask(taskId);
        if (task == null) {
            log.error("[processCompressTask][任务不存在] taskId: {}", taskId);
            return;
        }

        // 检查任务状态
        if (!taskManager.startProcessing(taskId)) {
            log.warn("[processCompressTask][任务状态不正确，跳过处理] taskId: {}", taskId);
            return;
        }

        try {
            log.info("[processCompressTask][开始处理压缩任务] taskId: {}, compressType: {}", 
                taskId, task.getCompressType());

            // 下载原始文件到临时目录
            File originalFile = downloadFileToTemp(task);
            if (originalFile == null) {
                throw new RuntimeException("下载原始文件失败");
            }

            try {
                // 根据压缩类型执行相应的压缩处理
                File compressedFile = executeCompress(task, originalFile);
                if (compressedFile == null) {
                    throw new RuntimeException("压缩处理失败");
                }

                // 上传压缩后的文件
                String compressedPath = uploadCompressedFile(task, compressedFile);
                
                // 完成任务
                taskManager.completeSuccess(taskId, compressedPath, compressedFile.length(), originalFile.length());
                
                log.info("[processCompressTask][压缩任务处理成功] taskId: {}, originalSize: {}, compressedSize: {}", 
                    taskId, originalFile.length(), compressedFile.length());

            } finally {
                // 清理临时文件
                cleanupTempFile(originalFile);
            }

        } catch (Exception e) {
            log.error("[processCompressTask][压缩任务处理失败] taskId: {}", taskId, e);
            taskManager.completeFailed(taskId, e.getMessage());
        }
    }

    /**
     * 下载文件到临时目录
     *
     * @param task 压缩任务
     * @return 临时文件
     */
    private File downloadFileToTemp(FileCompressTaskDO task) {
        try {
            // 获取文件客户端
            FileClient fileClient = fileConfigService.getMasterFileClient();
            
            // 下载文件内容
            byte[] fileContent = fileClient.getContent(task.getFilePath());
            if (fileContent == null || fileContent.length == 0) {
                log.error("[downloadFileToTemp][文件内容为空] filePath: {}", task.getFilePath());
                return null;
            }

            // 创建临时文件
            String fileName = getFileName(task.getFilePath());
            Path tempDir = Paths.get(System.getProperty("java.io.tmpdir"), "file-compress");
            Files.createDirectories(tempDir);
            
            File tempFile = tempDir.resolve(task.getId() + "_" + fileName).toFile();
            FileUtil.writeBytes(fileContent, tempFile);
            
            log.debug("[downloadFileToTemp][下载文件到临时目录成功] tempFile: {}", tempFile.getAbsolutePath());
            return tempFile;

        } catch (Exception e) {
            log.error("[downloadFileToTemp][下载文件失败] filePath: {}", task.getFilePath(), e);
            return null;
        }
    }

    /**
     * 执行压缩处理
     *
     * @param task 压缩任务
     * @param originalFile 原始文件
     * @return 压缩后的文件
     */
    private File executeCompress(FileCompressTaskDO task, File originalFile) {
        FileCompressTypeEnum compressType = task.getCompressTypeEnum();
        if (compressType == null) {
            throw new RuntimeException("不支持的压缩类型: " + task.getCompressType());
        }

        // 解析配置参数
        FileCompressConfigDTO config = parseConfig(task.getConfigParams());

        switch (compressType) {
            case MP4_COMPRESS:
                return compressVideo(task, originalFile, config);
            case IMAGE_AVIF:
                return compressImageToAvif(task, originalFile, config);
            case IMAGE_WEBP:
                return compressImageToWebp(task, originalFile, config);
            default:
                throw new RuntimeException("暂不支持的压缩类型: " + compressType.getName());
        }
    }

    /**
     * 压缩视频文件
     *
     * @param task 压缩任务
     * @param originalFile 原始文件
     * @param config 配置参数
     * @return 压缩后的文件
     */
    private File compressVideo(FileCompressTaskDO task, File originalFile, FileCompressConfigDTO config) {
        try {
            taskManager.updateProgress(task.getId(), 20);

            // 生成输出文件名
            String outputFileName = getFileNameWithoutExt(originalFile.getName()) + "_compressed.mp4";
            File outputFile = new File(originalFile.getParent(), outputFileName);

            // 使用FFmpeg压缩视频
            boolean success = FFmpegVideoUtils.compressVideo(
                originalFile, 
                outputFile, 
                config.getVideoBitrate(), 
                config.getAudioBitrate(),
                config.getVideoScale()
            );

            if (!success || !outputFile.exists()) {
                throw new RuntimeException("视频压缩失败");
            }

            taskManager.updateProgress(task.getId(), 90);
            return outputFile;

        } catch (Exception e) {
            log.error("[compressVideo][视频压缩失败] file: {}", originalFile.getName(), e);
            throw new RuntimeException("视频压缩失败: " + e.getMessage());
        }
    }

    /**
     * 压缩图片为AVIF格式
     *
     * @param task 压缩任务
     * @param originalFile 原始文件
     * @param config 配置参数
     * @return 压缩后的文件
     */
    private File compressImageToAvif(FileCompressTaskDO task, File originalFile, FileCompressConfigDTO config) {
        try {
            taskManager.updateProgress(task.getId(), 30);

            // 使用FFmpeg转换图片为AVIF
            File outputFile = FFmpegVideoUtils.optimizeImageToAvif(originalFile, originalFile.getParent());
            
            if (outputFile == null || !outputFile.exists()) {
                throw new RuntimeException("图片转AVIF失败");
            }

            taskManager.updateProgress(task.getId(), 90);
            return outputFile;

        } catch (Exception e) {
            log.error("[compressImageToAvif][图片转AVIF失败] file: {}", originalFile.getName(), e);
            throw new RuntimeException("图片转AVIF失败: " + e.getMessage());
        }
    }

    /**
     * 压缩图片为WebP格式
     *
     * @param task 压缩任务
     * @param originalFile 原始文件
     * @param config 配置参数
     * @return 压缩后的文件
     */
    private File compressImageToWebp(FileCompressTaskDO task, File originalFile, FileCompressConfigDTO config) {
        try {
            taskManager.updateProgress(task.getId(), 30);

            // 生成输出文件名
            String outputFileName = getFileNameWithoutExt(originalFile.getName()) + "_compressed.webp";
            File outputFile = new File(originalFile.getParent(), outputFileName);

            // 使用FFmpeg转换图片为WebP
            boolean success = FFmpegVideoUtils.convertImageToWebp(originalFile, outputFile, config.getImageQuality());
            
            if (!success || !outputFile.exists()) {
                throw new RuntimeException("图片转WebP失败");
            }

            taskManager.updateProgress(task.getId(), 90);
            return outputFile;

        } catch (Exception e) {
            log.error("[compressImageToWebp][图片转WebP失败] file: {}", originalFile.getName(), e);
            throw new RuntimeException("图片转WebP失败: " + e.getMessage());
        }
    }

    /**
     * 上传压缩后的文件
     *
     * @param task 压缩任务
     * @param compressedFile 压缩后的文件
     * @return 上传后的文件路径
     */
    private String uploadCompressedFile(FileCompressTaskDO task, File compressedFile) {
        try {
            // 读取文件内容
            byte[] fileContent = FileUtil.readBytes(compressedFile);
            
            // 生成新的文件路径
            String originalPath = task.getFilePath();
            String compressedPath = generateCompressedPath(originalPath, task.getCompressType());
            
            // 获取文件客户端并上传
            FileClient fileClient = fileConfigService.getMasterFileClient();
            String url = fileClient.upload(fileContent, compressedPath, getContentType(compressedFile));
            
            log.debug("[uploadCompressedFile][上传压缩文件成功] path: {}", compressedPath);
            return compressedPath;

        } catch (Exception e) {
            log.error("[uploadCompressedFile][上传压缩文件失败] file: {}", compressedFile.getName(), e);
            throw new RuntimeException("上传压缩文件失败: " + e.getMessage());
        }
    }

    /**
     * 解析配置参数
     *
     * @param configParams 配置参数JSON字符串
     * @return 配置对象
     */
    private FileCompressConfigDTO parseConfig(String configParams) {
        if (StrUtil.isBlank(configParams)) {
            return new FileCompressConfigDTO();
        }
        
        try {
            return JSONUtil.toBean(configParams, FileCompressConfigDTO.class);
        } catch (Exception e) {
            log.warn("[parseConfig][解析配置参数失败，使用默认配置] configParams: {}", configParams, e);
            return new FileCompressConfigDTO();
        }
    }

    /**
     * 生成压缩后的文件路径
     *
     * @param originalPath 原始路径
     * @param compressType 压缩类型
     * @return 压缩后的路径
     */
    private String generateCompressedPath(String originalPath, String compressType) {
        String dir = FileUtil.getParent(originalPath, 1);
        String fileName = FileUtil.getPrefix(originalPath);
        String extension = getCompressedExtension(compressType);
        
        return dir + "/" + fileName + "_compressed." + extension;
    }

    /**
     * 获取压缩后的文件扩展名
     *
     * @param compressType 压缩类型
     * @return 扩展名
     */
    private String getCompressedExtension(String compressType) {
        switch (compressType) {
            case "mp4_compress":
                return "mp4";
            case "image_avif":
                return "avif";
            case "image_webp":
                return "webp";
            default:
                return "bin";
        }
    }

    /**
     * 获取文件名（含扩展名）
     *
     * @param filePath 文件路径
     * @return 文件名
     */
    private String getFileName(String filePath) {
        return Paths.get(filePath).getFileName().toString();
    }

    /**
     * 获取文件名（不含扩展名）
     *
     * @param fileName 文件名
     * @return 不含扩展名的文件名
     */
    private String getFileNameWithoutExt(String fileName) {
        int lastDotIndex = fileName.lastIndexOf('.');
        return lastDotIndex > 0 ? fileName.substring(0, lastDotIndex) : fileName;
    }

    /**
     * 获取文件的Content-Type
     *
     * @param file 文件
     * @return Content-Type
     */
    private String getContentType(File file) {
        try {
            return Files.probeContentType(file.toPath());
        } catch (IOException e) {
            return "application/octet-stream";
        }
    }

    /**
     * 清理临时文件
     *
     * @param tempFile 临时文件
     */
    private void cleanupTempFile(File tempFile) {
        if (tempFile != null && tempFile.exists()) {
            try {
                FileUtil.del(tempFile);
                log.debug("[cleanupTempFile][清理临时文件成功] file: {}", tempFile.getAbsolutePath());
            } catch (Exception e) {
                log.warn("[cleanupTempFile][清理临时文件失败] file: {}", tempFile.getAbsolutePath(), e);
            }
        }
    }

}
