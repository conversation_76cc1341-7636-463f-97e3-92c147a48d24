package cn.iocoder.yudao.module.infra.api.file;

import cn.iocoder.yudao.framework.common.util.object.BeanUtils;
import cn.iocoder.yudao.module.infra.api.file.dto.FileConfigRespDTO;
import cn.iocoder.yudao.module.infra.dal.dataobject.file.FileConfigDO;
import cn.iocoder.yudao.module.infra.service.file.FileConfigService;
import cn.iocoder.yudao.module.infra.service.file.FileService;
import org.springframework.stereotype.Service;
import org.springframework.validation.annotation.Validated;

import javax.annotation.Resource;
import java.time.Duration;

/**
 * 文件 API 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class FileApiImpl implements FileApi {

    @Resource
    private FileService fileService;
    @Resource
    private FileConfigService fileConfigService;

    @Override
    public FileConfigRespDTO getFileConfig(Long id) {
        FileConfigRespDTO fileConfigRespDTO = null;
        FileConfigDO config = fileConfigService.getFileConfig(id);
        if (config != null) {
            fileConfigRespDTO = BeanUtils.toBean(config.getConfig(),FileConfigRespDTO.class);
        }
        return fileConfigRespDTO;
    }

    @Override
    public String createFile(String name, String path, byte[] content) {
        return fileService.createFile(name, path, content);
    }

    @Override
    public byte[] getFileContent(Long configId, String path) throws Exception {
        return fileService.getFileContent(configId,path);
    }

    @Override
    public String genRepeatTempDownloadUrl(Long configId, String path, Duration timeout) {
        return genRepeatTempDownloadUrl(configId, path, timeout, "/app-api/infra/file/pull/");
    }

    @Override
    public String genOnceTempDownloadUrl(Long configId, String path, Duration timeout) {
        return genRepeatTempDownloadUrl(configId, path, timeout, "/app-api/infra/file/fetch/");
    }

    private String genRepeatTempDownloadUrl(Long configId, String path, Duration timeout, String api) {
        String format = fileService.genTempDownloadFormat(configId, path, timeout);
        return String.format(format, api);
    }
}
