package cn.iocoder.yudao.module.infra.api.file;

import cn.iocoder.yudao.framework.common.util.object.BeanUtils;
import cn.iocoder.yudao.module.infra.api.file.dto.FileConfigRespDTO;
import cn.iocoder.yudao.module.infra.api.file.dto.FileInfoRespDTO;
import cn.iocoder.yudao.module.infra.dal.dataobject.file.FileConfigDO;
import cn.iocoder.yudao.module.infra.dal.dataobject.file.FileDO;
import cn.iocoder.yudao.module.infra.service.file.FileConfigService;
import cn.iocoder.yudao.module.infra.service.file.FileService;
import org.springframework.stereotype.Service;
import org.springframework.validation.annotation.Validated;

import javax.annotation.Resource;
import java.time.Duration;

/**
 * 文件 API 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class FileApiImpl implements FileApi {

    @Resource
    private FileService fileService;
    @Resource
    private FileConfigService fileConfigService;

    @Override
    public FileConfigRespDTO getFileConfig(Long id) {
        FileConfigRespDTO fileConfigRespDTO = null;
        FileConfigDO config = fileConfigService.getFileConfig(id);
        if (config != null) {
            fileConfigRespDTO = BeanUtils.toBean(config.getConfig(),FileConfigRespDTO.class);
        }
        return fileConfigRespDTO;
    }

    @Override
    public String createFile(String name, String path, byte[] content) {
        return fileService.createFile(name, path, content);
    }

    @Override
    public byte[] getFileContent(Long configId, String path) throws Exception {
        return fileService.getFileContent(configId,path);
    }

    @Override
    public String genRepeatTempDownloadUrl(Long configId, String path, Duration timeout) {
        return genRepeatTempDownloadUrl(configId, path, timeout, "/app-api/infra/file/pull/");
    }

    @Override
    public String genOnceTempDownloadUrl(Long configId, String path, Duration timeout) {
        return genRepeatTempDownloadUrl(configId, path, timeout, "/app-api/infra/file/fetch/");
    }

    private String genRepeatTempDownloadUrl(Long configId, String path, Duration timeout, String api) {
        String format = fileService.genTempDownloadFormat(configId, path, timeout);
        return String.format(format, api);
    }

    // ==================== 新增的文件管理API实现 ====================

    @Override
    public String getFileUrl(Long fileId) {
        FileDO file = fileService.getFile(fileId);
        return file != null ? file.getUrl() : null;
    }

    @Override
    public java.util.Map<Long, String> getFileUrls(java.util.List<Long> fileIds) {
        java.util.Map<Long, String> result = new java.util.HashMap<>();
        if (fileIds == null || fileIds.isEmpty()) {
            return result;
        }

        for (Long fileId : fileIds) {
            String url = getFileUrl(fileId);
            if (url != null) {
                result.put(fileId, url);
            }
        }
        return result;
    }

    @Override
    public FileInfoRespDTO getFileInfo(Long fileId) {
        FileDO file = fileService.getFile(fileId);
        return file != null ? convertToFileInfo(file) : null;
    }

    @Override
    public java.util.Map<Long, FileInfoRespDTO> getFileInfos(java.util.List<Long> fileIds) {
        java.util.Map<Long, FileInfoRespDTO> result = new java.util.HashMap<>();
        if (fileIds == null || fileIds.isEmpty()) {
            return result;
        }

        for (Long fileId : fileIds) {
            FileInfoRespDTO fileInfo = getFileInfo(fileId);
            if (fileInfo != null) {
                result.put(fileId, fileInfo);
            }
        }
        return result;
    }

    @Override
    public Boolean deleteFile(Long fileId) {
        try {
            fileService.deleteFile(fileId);
            return true;
        } catch (Exception e) {
            return false;
        }
    }

    @Override
    public Integer deleteFiles(java.util.List<Long> fileIds) {
        if (fileIds == null || fileIds.isEmpty()) {
            return 0;
        }

        int deletedCount = 0;
        for (Long fileId : fileIds) {
            if (deleteFile(fileId)) {
                deletedCount++;
            }
        }
        return deletedCount;
    }

    /**
     * 转换为文件信息DTO
     */
    private FileInfoRespDTO convertToFileInfo(FileDO file) {
        FileInfoRespDTO fileInfo = BeanUtils.toBean(file, FileInfoRespDTO.class);

        // 设置文件大小相关信息
        fileInfo.setSize(file.getSize().longValue());

        // 如果有文件索引信息，设置压缩相关信息
        // TODO: 这里需要查询文件索引表获取压缩信息
        // 暂时设置默认值
        fileInfo.setOriginalSize(file.getSize().longValue());
        fileInfo.setIsCompressed(false);
        fileInfo.setSavedSpace(0L);

        return fileInfo;
    }

}
