package cn.iocoder.yudao.module.infra.dal.mysql.file;

import cn.iocoder.yudao.framework.mybatis.core.mapper.BaseMapperX;
import cn.iocoder.yudao.framework.mybatis.core.query.LambdaQueryWrapperX;
import cn.iocoder.yudao.module.infra.dal.dataobject.file.FileIndexDO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;

/**
 * 文件索引 Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface FileIndexMapper extends BaseMapperX<FileIndexDO> {

    /**
     * 根据哈希值和大小查找文件索引（兼容旧版本）
     *
     * @param sha256 文件SHA-256哈希值
     * @param size   文件大小
     * @return 文件索引记录
     */
    default FileIndexDO selectBySha256AndSize(String sha256, Long size) {
        return selectByHashAndSize(sha256, size);
    }

    /**
     * 根据哈希和大小查找文件索引（支持双哈希查询）
     *
     * @param hash 文件哈希
     * @param size 文件大小
     * @return 文件索引记录
     */
    default FileIndexDO selectByHashAndSize(String hash, Long size) {
        return selectOne(new LambdaQueryWrapperX<FileIndexDO>()
                .and(wrapper -> wrapper
                    .eq(FileIndexDO::getOriginalHash, hash).eq(FileIndexDO::getOriginalSize, size)
                    .or()
                    .eq(FileIndexDO::getCurrentHash, hash).eq(FileIndexDO::getCurrentSize, size)
                ));
    }

    /**
     * 根据原文件哈希和大小查找
     *
     * @param originalHash 原文件哈希
     * @param originalSize 原文件大小
     * @return 文件索引记录
     */
    default FileIndexDO selectByOriginalHashAndSize(String originalHash, Long originalSize) {
        return selectOne(FileIndexDO::getOriginalHash, originalHash,
                        FileIndexDO::getOriginalSize, originalSize);
    }

    /**
     * 根据当前文件哈希和大小查找
     *
     * @param currentHash 当前文件哈希
     * @param currentSize 当前文件大小
     * @return 文件索引记录
     */
    default FileIndexDO selectByCurrentHashAndSize(String currentHash, Long currentSize) {
        return selectOne(FileIndexDO::getCurrentHash, currentHash,
                        FileIndexDO::getCurrentSize, currentSize);
    }

    /**
     * 增加引用计数
     *
     * @param id 文件索引ID
     * @return 影响的行数
     */
    @Update("UPDATE infra_file_index SET ref_count = ref_count + 1, update_time = NOW() WHERE id = #{id} AND deleted = 0")
    int incrementRefCount(@Param("id") Long id);

    /**
     * 减少引用计数
     *
     * @param id 文件索引ID
     * @return 影响的行数
     */
    @Update("UPDATE infra_file_index SET ref_count = ref_count - 1, update_time = NOW() WHERE id = #{id} AND deleted = 0 AND ref_count > 0")
    int decrementRefCount(@Param("id") Long id);

    /**
     * 根据配置ID查询文件索引列表
     *
     * @param configId 配置ID
     * @return 文件索引列表
     */
    default java.util.List<FileIndexDO> selectListByConfigId(Long configId) {
        return selectList(FileIndexDO::getConfigId, configId);
    }

    /**
     * 查询引用计数为0的文件索引列表（用于清理）
     *
     * @return 引用计数为0的文件索引列表
     */
    default java.util.List<FileIndexDO> selectListByRefCountZero() {
        return selectList(FileIndexDO::getRefCount, 0);
    }

    /**
     * 根据存储路径查找文件索引
     *
     * @param storagePath 存储路径
     * @param configId    配置ID
     * @return 文件索引记录
     */
    default FileIndexDO selectByStoragePathAndConfigId(String storagePath, Long configId) {
        return selectOne(FileIndexDO::getStoragePath, storagePath, FileIndexDO::getConfigId, configId);
    }



    /**
     * 统计总文件数量
     *
     * @return 总文件数量
     */
    default Long selectTotalFileCount() {
        return selectCount();
    }

    /**
     * 统计总存储大小
     *
     * @return 总存储大小（字节）
     */
    @Select("SELECT COALESCE(SUM(current_size), 0) FROM infra_file_index WHERE deleted = 0")
    Long selectTotalStorageSize();

    /**
     * 查找未压缩的文件
     *
     * @param limit 限制数量
     * @return 未压缩的文件列表
     */
    default java.util.List<FileIndexDO> selectUncompressedFiles(int limit) {
        return selectList(new LambdaQueryWrapperX<FileIndexDO>()
                .eq(FileIndexDO::getIsCompressed, false)
                .orderByAsc(FileIndexDO::getCreateTime)
                .last("LIMIT " + limit));
    }

    /**
     * 统计压缩节省的存储空间
     *
     * @return 节省的字节数
     */
    @Select("SELECT COALESCE(SUM(original_size - current_size), 0) FROM infra_file_index WHERE deleted = 0 AND is_compressed = 1")
    Long selectCompressSavedSize();

    /**
     * 统计去重文件数量（引用计数>1的文件）
     *
     * @return 去重文件数量
     */
    @Select("SELECT COUNT(*) FROM infra_file_index WHERE deleted = 0 AND ref_count > 1")
    Long selectDeduplicatedFileCount();

    /**
     * 统计去重节省的存储空间
     *
     * @return 节省的字节数
     */
    @Select("SELECT COALESCE(SUM(current_size * (ref_count - 1)), 0) FROM infra_file_index WHERE deleted = 0 AND ref_count > 1")
    Long selectSavedStorageSize();

    /**
     * 统计已压缩文件数量
     *
     * @return 已压缩文件数量
     */
    @Select("SELECT COUNT(*) FROM infra_file_index WHERE deleted = 0 AND is_compressed = 1")
    Long selectCompressedFileCount();



}
