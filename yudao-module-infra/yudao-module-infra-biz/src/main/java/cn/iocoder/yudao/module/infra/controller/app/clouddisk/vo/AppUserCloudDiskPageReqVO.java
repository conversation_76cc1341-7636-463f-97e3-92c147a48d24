package cn.iocoder.yudao.module.infra.controller.app.clouddisk.vo;

import cn.iocoder.yudao.framework.common.pojo.PageParam;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 用户云盘文件分页请求VO
 *
 * <AUTHOR>
 */
@Schema(description = "用户 APP - 云盘文件分页请求VO")
@Data
@EqualsAndHashCode(callSuper = true)
public class AppUserCloudDiskPageReqVO extends PageParam {

    @Schema(description = "用户ID（内部使用）", hidden = true)
    private Long userId;

    @Schema(description = "父目录ID", example = "0")
    private Long parentId;

    @Schema(description = "文件名（模糊搜索）", example = "avatar")
    private String fileName;

    @Schema(description = "文件类型", example = "image/jpeg")
    private String fileType;

    @Schema(description = "是否为文件夹", example = "false")
    private Boolean isFolder;

    @Schema(description = "是否收藏", example = "false")
    private Boolean isFavorite;

}
