package cn.iocoder.yudao.module.infra.framework.file.core.client;

import cn.iocoder.yudao.module.infra.framework.file.core.client.s3.FilePresignedUrlRespDTO;

import java.io.File;

/**
 * 文件客户端
 *
 * <AUTHOR>
 */
public interface FileClient {

    /**
     * 获得客户端编号
     *
     * @return 客户端编号
     */
    Long getId();

    /**
     * 上传文件
     *
     * @param content 文件流
     * @param path    相对路径
     * @return 完整路径，即 HTTP 访问地址
     * @throws Exception 上传文件时，抛出 Exception 异常
     */
    String upload(byte[] content, String path, String type) throws Exception;

    /**
     * 删除文件
     *
     * @param path 相对路径
     * @throws Exception 删除文件时，抛出 Exception 异常
     */
    void delete(String path) throws Exception;

    /**
     * 获得文件的内容
     *
     * @param path 相对路径
     * @return 文件的内容
     */
    byte[] getContent(String path) throws Exception;

    /**
     * 获得文件
     *
     * @param path 相对路径
     * @return 文件的内容
     */
    File getFile(String path) throws Exception;

    /**
     * 获得文件预签名地址
     *
     * @param path 相对路径
     * @return 文件预签名地址
     */
    default FilePresignedUrlRespDTO getPresignedObjectUrl(String path) throws Exception {
        throw new UnsupportedOperationException("不支持的操作");
    }

    /**
     * 流式上传文件
     *
     * @param inputStream 文件输入流
     * @param path 相对路径
     * @param type 文件类型
     * @return 完整URL，即 HTTP 访问地址
     */
    default String uploadStream(java.io.InputStream inputStream, String path, String type) throws Exception {
        // 默认实现：将流转换为字节数组（兼容旧实现）
        java.io.ByteArrayOutputStream baos = new java.io.ByteArrayOutputStream();
        byte[] buffer = new byte[8192];
        int bytesRead;
        while ((bytesRead = inputStream.read(buffer)) != -1) {
            baos.write(buffer, 0, bytesRead);
        }
        byte[] content = baos.toByteArray();
        return upload(content, path, type);
    }

    /**
     * 流式上传文件（带文件大小）
     *
     * @param inputStream 文件输入流
     * @param path 相对路径
     * @param type 文件类型
     * @param contentLength 文件大小（字节）
     * @return 完整URL，即 HTTP 访问地址
     */
    default String uploadStream(java.io.InputStream inputStream, String path, String type, long contentLength) throws Exception {
        // 默认实现：调用不带大小的流式上传
        return uploadStream(inputStream, path, type);
    }

}
