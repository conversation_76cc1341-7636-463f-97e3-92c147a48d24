package cn.iocoder.yudao.module.infra.controller.admin.file.vo.compress;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 管理后台 - 文件压缩任务 Response VO
 *
 * <AUTHOR>
 */
@Schema(description = "管理后台 - 文件压缩任务 Response VO")
@Data
public class FileCompressTaskRespVO {

    @Schema(description = "任务ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "1024")
    private Long id;

    @Schema(description = "文件ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "2048")
    private Long fileId;

    @Schema(description = "原始文件路径", requiredMode = Schema.RequiredMode.REQUIRED, example = "/upload/2024/01/test.mp4")
    private String filePath;

    @Schema(description = "原始文件大小（字节）", requiredMode = Schema.RequiredMode.REQUIRED, example = "10485760")
    private Long fileSize;

    @Schema(description = "文件类型", requiredMode = Schema.RequiredMode.REQUIRED, example = "video")
    private String fileType;

    @Schema(description = "压缩类型", requiredMode = Schema.RequiredMode.REQUIRED, example = "mp4_compress")
    private String compressType;

    @Schema(description = "任务状态", requiredMode = Schema.RequiredMode.REQUIRED, example = "2")
    private Integer status;

    @Schema(description = "任务状态名称", requiredMode = Schema.RequiredMode.REQUIRED, example = "成功")
    private String statusName;

    @Schema(description = "处理进度（0-100）", requiredMode = Schema.RequiredMode.REQUIRED, example = "100")
    private Integer progress;

    @Schema(description = "压缩后文件路径", example = "/upload/2024/01/test_compressed.mp4")
    private String compressedPath;

    @Schema(description = "压缩后文件大小（字节）", example = "5242880")
    private Long compressedSize;

    @Schema(description = "压缩比例（百分比）", example = "50.00")
    private BigDecimal compressRatio;

    @Schema(description = "开始处理时间")
    private LocalDateTime startTime;

    @Schema(description = "结束处理时间")
    private LocalDateTime endTime;

    @Schema(description = "错误信息")
    private String errorMessage;

    @Schema(description = "重试次数", requiredMode = Schema.RequiredMode.REQUIRED, example = "0")
    private Integer retryCount;

    @Schema(description = "最大重试次数", requiredMode = Schema.RequiredMode.REQUIRED, example = "3")
    private Integer maxRetryCount;

    @Schema(description = "任务优先级", requiredMode = Schema.RequiredMode.REQUIRED, example = "5")
    private Integer priority;

    @Schema(description = "压缩配置参数")
    private String configParams;

    @Schema(description = "创建时间", requiredMode = Schema.RequiredMode.REQUIRED)
    private LocalDateTime createTime;

    @Schema(description = "更新时间", requiredMode = Schema.RequiredMode.REQUIRED)
    private LocalDateTime updateTime;

    @Schema(description = "处理耗时（毫秒）", example = "30000")
    private Long processingDuration;

    @Schema(description = "是否可以重试", example = "true")
    private Boolean canRetry;

    @Schema(description = "是否已完成", example = "true")
    private Boolean isCompleted;

}
