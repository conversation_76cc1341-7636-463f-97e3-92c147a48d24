package cn.iocoder.yudao.module.infra.job;

import cn.iocoder.yudao.framework.quartz.core.handler.JobHandler;
import cn.iocoder.yudao.framework.tenant.core.job.TenantJob;
import cn.iocoder.yudao.module.infra.dal.dataobject.file.FileIndexDO;
import cn.iocoder.yudao.module.infra.dal.mysql.file.FileIndexMapper;
import cn.iocoder.yudao.module.infra.enums.file.WatermarkStatusEnum;
import cn.iocoder.yudao.module.infra.service.file.ReplacementCompressProcessor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 水印处理定时任务
 * 定期扫描需要添加水印的文件并进行异步处理
 *
 * <AUTHOR>
 */
@Component
@Slf4j
public class WatermarkProcessJob implements JobHandler {

    @Resource
    private FileIndexMapper fileIndexMapper;

    @Resource
    private ReplacementCompressProcessor compressProcessor;

    @Override
    @TenantJob
    public String execute(String param) throws Exception {
        log.info("[WatermarkProcessJob][开始执行水印处理定时任务]");

        try {
            // 1. 查询需要处理水印的文件
            List<FileIndexDO> pendingFiles = findPendingWatermarkFiles();
            
            if (pendingFiles.isEmpty()) {
                log.debug("[WatermarkProcessJob][没有需要处理水印的文件]");
                return "没有需要处理水印的文件";
            }

            log.info("[WatermarkProcessJob][找到需要处理水印的文件数量: {}]", pendingFiles.size());

            // 2. 批量处理水印
            int processedCount = 0;
            int failedCount = 0;

            for (FileIndexDO fileIndex : pendingFiles) {
                try {
                    // 异步处理水印
                    compressProcessor.processFileWatermark(fileIndex);
                    processedCount++;
                    
                    log.debug("[WatermarkProcessJob][提交水印处理任务] fileId: {}, path: {}", 
                        fileIndex.getId(), fileIndex.getStoragePath());
                        
                } catch (Exception e) {
                    failedCount++;
                    log.error("[WatermarkProcessJob][提交水印处理任务失败] fileId: {}", fileIndex.getId(), e);
                    
                    // 更新失败状态
                    updateWatermarkStatus(fileIndex.getId(), WatermarkStatusEnum.FAILED, 
                        "提交处理任务失败: " + e.getMessage());
                }
            }

            String result = String.format("水印处理任务执行完成，总数: %d, 成功提交: %d, 失败: %d", 
                pendingFiles.size(), processedCount, failedCount);
            
            log.info("[WatermarkProcessJob][{}]", result);
            return result;

        } catch (Exception e) {
            log.error("[WatermarkProcessJob][水印处理定时任务执行异常]", e);
            throw e;
        }
    }

    /**
     * 查询需要处理水印的文件
     */
    private List<FileIndexDO> findPendingWatermarkFiles() {
        // 查询条件：
        // 1. 有水印路径配置
        // 2. 未添加水印
        // 3. 水印状态为未处理或处理失败（超过1小时）
        return fileIndexMapper.selectPendingWatermarkFiles();
    }

    /**
     * 更新水印状态
     */
    private void updateWatermarkStatus(Long fileId, WatermarkStatusEnum status, String errorMsg) {
        try {
            FileIndexDO updateDO = new FileIndexDO();
            updateDO.setId(fileId);
            updateDO.setWatermarkStatus(status.getCode());
            updateDO.setWatermarkErrorMsg(errorMsg);
            updateDO.setWatermarkProcessedAt(LocalDateTime.now());
            
            fileIndexMapper.updateById(updateDO);
            
        } catch (Exception e) {
            log.error("[updateWatermarkStatus][更新水印状态失败] fileId: {}, status: {}", fileId, status, e);
        }
    }

}
