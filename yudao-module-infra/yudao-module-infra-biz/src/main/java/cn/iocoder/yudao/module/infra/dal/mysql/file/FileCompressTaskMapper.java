package cn.iocoder.yudao.module.infra.dal.mysql.file;

import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.mybatis.core.mapper.BaseMapperX;
import cn.iocoder.yudao.framework.mybatis.core.query.LambdaQueryWrapperX;
import cn.iocoder.yudao.module.infra.controller.admin.file.vo.compress.FileCompressTaskPageReqVO;
import cn.iocoder.yudao.module.infra.dal.dataobject.file.FileCompressTaskDO;
import cn.iocoder.yudao.module.infra.enums.file.FileCompressTaskStatusEnum;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Update;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 文件压缩任务 Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface FileCompressTaskMapper extends BaseMapperX<FileCompressTaskDO> {

    default PageResult<FileCompressTaskDO> selectPage(FileCompressTaskPageReqVO reqVO) {
        return selectPage(reqVO, new LambdaQueryWrapperX<FileCompressTaskDO>()
                .eqIfPresent(FileCompressTaskDO::getFileId, reqVO.getFileId())
                .eqIfPresent(FileCompressTaskDO::getStatus, reqVO.getStatus())
                .eqIfPresent(FileCompressTaskDO::getCompressType, reqVO.getCompressType())
                .eqIfPresent(FileCompressTaskDO::getFileType, reqVO.getFileType())
                .betweenIfPresent(FileCompressTaskDO::getCreateTime, reqVO.getCreateTime())
                .orderByDesc(FileCompressTaskDO::getId));
    }

    /**
     * 查询待处理的任务列表（按优先级和创建时间排序）
     *
     * @param limit 限制数量
     * @return 待处理任务列表
     */
    default List<FileCompressTaskDO> selectPendingTasks(int limit) {
        return selectList(new LambdaQueryWrapperX<FileCompressTaskDO>()
                .eq(FileCompressTaskDO::getStatus, FileCompressTaskStatusEnum.PENDING.getStatus())
                .orderByAsc(FileCompressTaskDO::getPriority)
                .orderByAsc(FileCompressTaskDO::getCreateTime)
                .last("LIMIT " + limit));
    }

    /**
     * 查询需要重试的失败任务
     *
     * @param limit 限制数量
     * @return 需要重试的任务列表
     */
    default List<FileCompressTaskDO> selectRetryableTasks(int limit) {
        return selectList(new LambdaQueryWrapperX<FileCompressTaskDO>()
                .eq(FileCompressTaskDO::getStatus, FileCompressTaskStatusEnum.FAILED.getStatus())
                .apply("retry_count < max_retry_count")
                .orderByAsc(FileCompressTaskDO::getPriority)
                .orderByAsc(FileCompressTaskDO::getUpdateTime)
                .last("LIMIT " + limit));
    }

    /**
     * 查询超时的处理中任务
     *
     * @param timeoutMinutes 超时分钟数
     * @param limit 限制数量
     * @return 超时任务列表
     */
    default List<FileCompressTaskDO> selectTimeoutTasks(int timeoutMinutes, int limit) {
        LocalDateTime timeoutTime = LocalDateTime.now().minusMinutes(timeoutMinutes);
        return selectList(new LambdaQueryWrapperX<FileCompressTaskDO>()
                .eq(FileCompressTaskDO::getStatus, FileCompressTaskStatusEnum.PROCESSING.getStatus())
                .lt(FileCompressTaskDO::getStartTime, timeoutTime)
                .last("LIMIT " + limit));
    }

    /**
     * 根据文件ID查询任务
     *
     * @param fileId 文件ID
     * @return 任务列表
     */
    default List<FileCompressTaskDO> selectByFileId(Long fileId) {
        return selectList(FileCompressTaskDO::getFileId, fileId);
    }

    /**
     * 根据文件ID和压缩类型查询任务
     *
     * @param fileId 文件ID
     * @param compressType 压缩类型
     * @return 任务记录
     */
    default FileCompressTaskDO selectByFileIdAndCompressType(Long fileId, String compressType) {
        return selectOne(FileCompressTaskDO::getFileId, fileId, 
                        FileCompressTaskDO::getCompressType, compressType);
    }

    /**
     * 更新任务状态
     *
     * @param id 任务ID
     * @param status 新状态
     * @param progress 进度
     * @return 影响行数
     */
    @Update("UPDATE infra_file_compress_task SET status = #{status}, progress = #{progress}, update_time = NOW() WHERE id = #{id}")
    int updateStatus(@Param("id") Long id, @Param("status") Integer status, @Param("progress") Integer progress);

    /**
     * 开始处理任务
     *
     * @param id 任务ID
     * @return 影响行数
     */
    @Update("UPDATE infra_file_compress_task SET status = #{status}, start_time = NOW(), update_time = NOW() WHERE id = #{id} AND status = #{oldStatus}")
    int startProcessing(@Param("id") Long id, 
                       @Param("status") Integer status, 
                       @Param("oldStatus") Integer oldStatus);

    /**
     * 完成任务处理
     *
     * @param id 任务ID
     * @param status 最终状态
     * @param compressedPath 压缩后文件路径
     * @param compressedSize 压缩后文件大小
     * @param compressRatio 压缩比例
     * @param errorMessage 错误信息（如果失败）
     * @return 影响行数
     */
    @Update("UPDATE infra_file_compress_task SET status = #{status}, progress = 100, " +
            "compressed_path = #{compressedPath}, compressed_size = #{compressedSize}, " +
            "compress_ratio = #{compressRatio}, error_message = #{errorMessage}, " +
            "end_time = NOW(), update_time = NOW() WHERE id = #{id}")
    int completeProcessing(@Param("id") Long id,
                          @Param("status") Integer status,
                          @Param("compressedPath") String compressedPath,
                          @Param("compressedSize") Long compressedSize,
                          @Param("compressRatio") java.math.BigDecimal compressRatio,
                          @Param("errorMessage") String errorMessage);

    /**
     * 增加重试次数
     *
     * @param id 任务ID
     * @return 影响行数
     */
    @Update("UPDATE infra_file_compress_task SET retry_count = retry_count + 1, update_time = NOW() WHERE id = #{id}")
    int incrementRetryCount(@Param("id") Long id);

    /**
     * 统计各状态任务数量
     *
     * @return 状态统计Map
     */
    default java.util.Map<Integer, Long> selectStatusCount() {
        return selectObjs("SELECT status, COUNT(*) FROM infra_file_compress_task WHERE deleted = 0 GROUP BY status")
                .stream()
                .collect(java.util.stream.Collectors.toMap(
                    obj -> ((Object[]) obj)[0] != null ? ((Number) ((Object[]) obj)[0]).intValue() : 0,
                    obj -> ((Object[]) obj)[1] != null ? ((Number) ((Object[]) obj)[1]).longValue() : 0L
                ));
    }

    /**
     * 统计压缩节省的存储空间
     *
     * @return 节省的字节数
     */
    default Long selectSavedStorageSize() {
        return selectObjs("SELECT SUM(file_size - compressed_size) FROM infra_file_compress_task " +
                         "WHERE deleted = 0 AND status = 2 AND compressed_size IS NOT NULL", Long.class)
                .stream()
                .findFirst()
                .orElse(0L);
    }

    /**
     * 统计平均压缩比例
     *
     * @return 平均压缩比例
     */
    default java.math.BigDecimal selectAvgCompressRatio() {
        return selectObjs("SELECT AVG(compress_ratio) FROM infra_file_compress_task " +
                         "WHERE deleted = 0 AND status = 2 AND compress_ratio IS NOT NULL", java.math.BigDecimal.class)
                .stream()
                .findFirst()
                .orElse(java.math.BigDecimal.ZERO);
    }

}
