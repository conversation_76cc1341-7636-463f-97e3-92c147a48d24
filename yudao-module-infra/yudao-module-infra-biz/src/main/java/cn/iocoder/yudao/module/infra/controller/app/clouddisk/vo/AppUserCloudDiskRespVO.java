package cn.iocoder.yudao.module.infra.controller.app.clouddisk.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * 用户云盘文件响应VO
 *
 * <AUTHOR>
 */
@Schema(description = "用户 APP - 云盘文件响应VO")
@Data
public class AppUserCloudDiskRespVO {

    @Schema(description = "云盘记录ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "1024")
    private Long id;

    @Schema(description = "文件ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "2048")
    private Long fileId;

    @Schema(description = "文件名", requiredMode = Schema.RequiredMode.REQUIRED, example = "avatar.jpg")
    private String fileName;

    @Schema(description = "文件大小（字节）", requiredMode = Schema.RequiredMode.REQUIRED, example = "1024000")
    private Long fileSize;

    @Schema(description = "文件类型", requiredMode = Schema.RequiredMode.REQUIRED, example = "image/jpeg")
    private String fileType;

    @Schema(description = "文件访问URL", requiredMode = Schema.RequiredMode.REQUIRED, example = "https://example.com/files/avatar.jpg")
    private String fileUrl;

    @Schema(description = "文件路径", requiredMode = Schema.RequiredMode.REQUIRED, example = "/cloud-disk/user-1/avatar.jpg")
    private String filePath;

    @Schema(description = "父目录ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "0")
    private Long parentId;

    @Schema(description = "是否为文件夹", requiredMode = Schema.RequiredMode.REQUIRED, example = "false")
    private Boolean isFolder;

    @Schema(description = "是否收藏", requiredMode = Schema.RequiredMode.REQUIRED, example = "false")
    private Boolean isFavorite;

    @Schema(description = "分享码", example = "abc123")
    private String shareCode;

    @Schema(description = "分享过期时间", example = "2023-12-31 23:59:59")
    private LocalDateTime shareExpireTime;

    @Schema(description = "下载次数", example = "10")
    private Integer downloadCount;

    @Schema(description = "查看次数", example = "20")
    private Integer viewCount;

    @Schema(description = "是否为去重文件", example = "true")
    private Boolean isDuplicate;

    @Schema(description = "处理任务ID（异步处理）", example = "task-123")
    private String processingTaskId;

    @Schema(description = "创建时间", requiredMode = Schema.RequiredMode.REQUIRED)
    private LocalDateTime createTime;

}
