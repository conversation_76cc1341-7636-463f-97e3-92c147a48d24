package cn.iocoder.yudao.module.infra.controller.app.clouddisk.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * 用户云盘文件创建请求VO
 *
 * <AUTHOR>
 */
@Schema(description = "用户 APP - 云盘文件创建请求VO")
@Data
public class AppUserCloudDiskCreateReqVO {

    @Schema(description = "文件ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "2048")
    @NotNull(message = "文件ID不能为空")
    private Long fileId;

    @Schema(description = "文件名", requiredMode = Schema.RequiredMode.REQUIRED, example = "avatar.jpg")
    @NotBlank(message = "文件名不能为空")
    private String fileName;

    @Schema(description = "文件大小（字节）", requiredMode = Schema.RequiredMode.REQUIRED, example = "1024000")
    @NotNull(message = "文件大小不能为空")
    private Long fileSize;

    @Schema(description = "文件类型", requiredMode = Schema.RequiredMode.REQUIRED, example = "image/jpeg")
    @NotBlank(message = "文件类型不能为空")
    private String fileType;

    @Schema(description = "文件路径", requiredMode = Schema.RequiredMode.REQUIRED, example = "/cloud-disk/user-1/avatar.jpg")
    @NotBlank(message = "文件路径不能为空")
    private String filePath;

    @Schema(description = "父目录ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "0")
    @NotNull(message = "父目录ID不能为空")
    private Long parentId;

}
