package cn.iocoder.yudao.module.infra.dal.dataobject.file;

import cn.iocoder.yudao.framework.mybatis.core.dataobject.BaseDO;
import com.baomidou.mybatisplus.annotation.KeySequence;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.*;

/**
 * 文件衍生版本 DO
 *
 * <AUTHOR>
 */
@TableName("infra_file_derivative")
@KeySequence("infra_file_derivative_seq")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class FileDerivativeDO extends BaseDO {

    /**
     * 衍生版本ID
     */
    @TableId
    private Long id;

    /**
     * 原始文件ID
     */
    private Long originalFileId;

    /**
     * 衍生类型
     */
    private String derivativeType;

    /**
     * 衍生文件ID
     */
    private Long fileId;

    /**
     * 处理参数JSON
     */
    private String processingParams;

    /**
     * 文件大小（字节）
     */
    private Long fileSize;

    /**
     * 质量等级
     */
    private String quality;

    /**
     * 分辨率
     */
    private String resolution;

    /**
     * 比特率（kbps）
     */
    private Integer bitrate;

    /**
     * 时长（秒）
     */
    private Integer duration;

    /**
     * 格式
     */
    private String format;

    /**
     * 状态
     */
    private Integer status;

    /**
     * 错误信息
     */
    private String errorMessage;

    /**
     * 衍生类型枚举
     */
    public static class DerivativeType {
        public static final String COMPRESSED = "COMPRESSED";
        public static final String WATERMARKED = "WATERMARKED";
        public static final String COMPRESSED_WATERMARKED = "COMPRESSED_WATERMARKED";
        public static final String HLS = "HLS";
    }

    /**
     * 质量等级枚举
     */
    public static class Quality {
        public static final String ORIGINAL = "ORIGINAL";
        public static final String HIGH = "HIGH";
        public static final String MEDIUM = "MEDIUM";
        public static final String LOW = "LOW";
    }

    /**
     * 状态枚举
     */
    public static class Status {
        public static final Integer PROCESSING = 0;
        public static final Integer SUCCESS = 1;
        public static final Integer FAILED = 2;
    }

}
