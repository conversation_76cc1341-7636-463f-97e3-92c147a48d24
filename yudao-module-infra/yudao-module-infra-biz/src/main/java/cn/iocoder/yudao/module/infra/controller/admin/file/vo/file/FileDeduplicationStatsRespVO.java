package cn.iocoder.yudao.module.infra.controller.admin.file.vo.file;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * 文件去重统计 Response VO
 *
 * <AUTHOR>
 */
@Schema(description = "管理后台 - 文件去重统计 Response VO")
@Data
public class FileDeduplicationStatsRespVO {

    @Schema(description = "总文件数量", requiredMode = Schema.RequiredMode.REQUIRED, example = "1000")
    private Long totalFileCount;

    @Schema(description = "去重文件数量", requiredMode = Schema.RequiredMode.REQUIRED, example = "200")
    private Long deduplicatedFileCount;

    @Schema(description = "总存储大小（字节）", requiredMode = Schema.RequiredMode.REQUIRED, example = "1048576")
    private Long totalStorageSize;

    @Schema(description = "节省的存储大小（字节）", requiredMode = Schema.RequiredMode.REQUIRED, example = "524288")
    private Long savedStorageSize;

    @Schema(description = "去重率（百分比）", requiredMode = Schema.RequiredMode.REQUIRED, example = "20.5")
    private Double deduplicationRate;

    @Schema(description = "存储节省率（百分比）", requiredMode = Schema.RequiredMode.REQUIRED, example = "50.0")
    private Double storageSavingRate;

}
