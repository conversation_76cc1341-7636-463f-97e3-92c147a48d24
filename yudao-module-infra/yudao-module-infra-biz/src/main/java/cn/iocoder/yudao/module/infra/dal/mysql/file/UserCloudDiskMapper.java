package cn.iocoder.yudao.module.infra.dal.mysql.file;

import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.mybatis.core.mapper.BaseMapperX;
import cn.iocoder.yudao.framework.mybatis.core.query.LambdaQueryWrapperX;
import cn.iocoder.yudao.module.infra.dal.dataobject.file.UserCloudDiskDO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;

import java.util.List;

/**
 * 用户云盘 Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface UserCloudDiskMapper extends BaseMapperX<UserCloudDiskDO> {

    /**
     * 根据用户ID和父目录ID查询文件列表
     *
     * @param userId 用户ID
     * @param parentId 父目录ID
     * @return 文件列表
     */
    default List<UserCloudDiskDO> selectByUserIdAndParentId(Long userId, Long parentId) {
        return selectList(new LambdaQueryWrapperX<UserCloudDiskDO>()
                .eq(UserCloudDiskDO::getUserId, userId)
                .eq(UserCloudDiskDO::getParentId, parentId)
                .orderByDesc(UserCloudDiskDO::getIsFolder)
                .orderByDesc(UserCloudDiskDO::getCreateTime));
    }

    /**
     * 根据用户ID和文件路径查询文件
     *
     * @param userId 用户ID
     * @param filePath 文件路径
     * @return 文件记录
     */
    default UserCloudDiskDO selectByUserIdAndFilePath(Long userId, String filePath) {
        return selectOne(new LambdaQueryWrapperX<UserCloudDiskDO>()
                .eq(UserCloudDiskDO::getUserId, userId)
                .eq(UserCloudDiskDO::getFilePath, filePath));
    }

    /**
     * 根据用户ID和文件ID查询文件
     *
     * @param userId 用户ID
     * @param fileId 文件ID
     * @return 文件记录
     */
    default UserCloudDiskDO selectByUserIdAndFileId(Long userId, Long fileId) {
        return selectOne(new LambdaQueryWrapperX<UserCloudDiskDO>()
                .eq(UserCloudDiskDO::getUserId, userId)
                .eq(UserCloudDiskDO::getFileId, fileId));
    }

    /**
     * 根据用户ID查询收藏文件列表
     *
     * @param userId 用户ID
     * @return 收藏文件列表
     */
    default List<UserCloudDiskDO> selectFavoritesByUserId(Long userId) {
        return selectList(new LambdaQueryWrapperX<UserCloudDiskDO>()
                .eq(UserCloudDiskDO::getUserId, userId)
                .eq(UserCloudDiskDO::getIsFavorite, true)
                .orderByDesc(UserCloudDiskDO::getCreateTime));
    }

    /**
     * 根据分享码查询文件
     *
     * @param shareCode 分享码
     * @return 文件记录
     */
    default UserCloudDiskDO selectByShareCode(String shareCode) {
        return selectOne(UserCloudDiskDO::getShareCode, shareCode);
    }

    /**
     * 搜索用户文件
     *
     * @param userId 用户ID
     * @param keyword 关键词
     * @param fileType 文件类型（可选）
     * @return 搜索结果
     */
    default List<UserCloudDiskDO> searchFiles(Long userId, String keyword, String fileType) {
        LambdaQueryWrapperX<UserCloudDiskDO> wrapper = new LambdaQueryWrapperX<UserCloudDiskDO>()
                .eq(UserCloudDiskDO::getUserId, userId)
                .like(UserCloudDiskDO::getFileName, keyword);
        
        if (fileType != null && !fileType.trim().isEmpty()) {
            wrapper.like(UserCloudDiskDO::getFileType, fileType);
        }
        
        return selectList(wrapper.orderByDesc(UserCloudDiskDO::getCreateTime));
    }

    /**
     * 分页查询用户文件
     *
     * @param userId 用户ID
     * @param parentId 父目录ID
     * @param pageNo 页码
     * @param pageSize 页大小
     * @return 分页结果
     */
    default PageResult<UserCloudDiskDO> selectPage(Long userId, Long parentId, int pageNo, int pageSize) {
        return selectPage(pageNo, pageSize, new LambdaQueryWrapperX<UserCloudDiskDO>()
                .eq(UserCloudDiskDO::getUserId, userId)
                .eq(UserCloudDiskDO::getParentId, parentId)
                .orderByDesc(UserCloudDiskDO::getIsFolder)
                .orderByDesc(UserCloudDiskDO::getCreateTime));
    }

    /**
     * 增加下载次数
     *
     * @param id 云盘记录ID
     * @return 影响行数
     */
    @Update("UPDATE infra_user_cloud_disk SET download_count = download_count + 1, update_time = NOW() WHERE id = #{id}")
    int incrementDownloadCount(@Param("id") Long id);

    /**
     * 增加查看次数
     *
     * @param id 云盘记录ID
     * @return 影响行数
     */
    @Update("UPDATE infra_user_cloud_disk SET view_count = view_count + 1, update_time = NOW() WHERE id = #{id}")
    int incrementViewCount(@Param("id") Long id);

    /**
     * 统计用户文件总数
     *
     * @param userId 用户ID
     * @return 文件总数
     */
    @Select("SELECT COUNT(*) FROM infra_user_cloud_disk WHERE user_id = #{userId} AND is_folder = 0 AND deleted = 0")
    Long countFilesByUserId(@Param("userId") Long userId);

    /**
     * 统计用户文件总大小
     *
     * @param userId 用户ID
     * @return 文件总大小（字节）
     */
    @Select("SELECT COALESCE(SUM(file_size), 0) FROM infra_user_cloud_disk WHERE user_id = #{userId} AND is_folder = 0 AND deleted = 0")
    Long sumFileSizeByUserId(@Param("userId") Long userId);

    /**
     * 统计用户文件夹数量
     *
     * @param userId 用户ID
     * @return 文件夹数量
     */
    @Select("SELECT COUNT(*) FROM infra_user_cloud_disk WHERE user_id = #{userId} AND is_folder = 1 AND deleted = 0")
    Long countFoldersByUserId(@Param("userId") Long userId);

    /**
     * 根据父目录ID查询子文件/文件夹列表
     *
     * @param parentId 父目录ID
     * @return 子文件/文件夹列表
     */
    default List<UserCloudDiskDO> selectByParentId(Long parentId) {
        return selectList(UserCloudDiskDO::getParentId, parentId);
    }

    /**
     * 批量删除用户文件
     *
     * @param userId 用户ID
     * @param ids ID列表
     * @return 影响行数
     */
    default int batchDeleteByUserIdAndIds(Long userId, List<Long> ids) {
        return delete(new LambdaQueryWrapperX<UserCloudDiskDO>()
                .eq(UserCloudDiskDO::getUserId, userId)
                .in(UserCloudDiskDO::getId, ids));
    }

}
