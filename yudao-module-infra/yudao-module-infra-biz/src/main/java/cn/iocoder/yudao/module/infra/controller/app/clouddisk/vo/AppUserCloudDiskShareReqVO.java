package cn.iocoder.yudao.module.infra.controller.app.clouddisk.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.Max;
import javax.validation.constraints.Min;

/**
 * 用户云盘文件分享请求VO
 *
 * <AUTHOR>
 */
@Schema(description = "用户 APP - 云盘文件分享请求VO")
@Data
public class AppUserCloudDiskShareReqVO {

    @Schema(description = "分享有效期（天）", example = "7")
    @Min(value = 1, message = "分享有效期最少1天")
    @Max(value = 365, message = "分享有效期最多365天")
    private Integer expireDays = 7;

    @Schema(description = "分享密码", example = "1234")
    private String sharePassword;

    @Schema(description = "允许下载", example = "true")
    private Boolean allowDownload = true;

}
