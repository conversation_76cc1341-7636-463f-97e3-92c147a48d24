package cn.iocoder.yudao.module.infra.enums.file;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 文件压缩任务状态枚举
 *
 * <AUTHOR>
 */
@AllArgsConstructor
@Getter
public enum FileCompressTaskStatusEnum {

    PENDING(0, "待处理"),
    PROCESSING(1, "处理中"),
    SUCCESS(2, "成功"),
    FAILED(3, "失败"),
    CANCELLED(4, "已取消");

    /**
     * 状态值
     */
    private final Integer status;
    
    /**
     * 状态名
     */
    private final String name;

    public static FileCompressTaskStatusEnum valueOf(Integer status) {
        for (FileCompressTaskStatusEnum statusEnum : values()) {
            if (statusEnum.getStatus().equals(status)) {
                return statusEnum;
            }
        }
        throw new IllegalArgumentException("未知的文件压缩任务状态: " + status);
    }

}
