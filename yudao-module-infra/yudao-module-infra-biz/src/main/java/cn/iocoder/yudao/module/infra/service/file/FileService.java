package cn.iocoder.yudao.module.infra.service.file;

import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.module.infra.controller.admin.file.vo.file.FileCreateReqVO;
import cn.iocoder.yudao.module.infra.controller.admin.file.vo.file.FilePageReqVO;
import cn.iocoder.yudao.module.infra.controller.admin.file.vo.file.FilePresignedUrlRespVO;
import cn.iocoder.yudao.module.infra.dal.dataobject.file.FileDO;
import cn.iocoder.yudao.module.infra.service.file.bo.TempFileRespBO;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.time.Duration;

/**
 * 文件 Service 接口
 *
 * <AUTHOR>
 */
public interface FileService {

    /**
     * 获得文件分页
     *
     * @param pageReqVO 分页查询
     * @return 文件分页
     */
    PageResult<FileDO> getFilePage(FilePageReqVO pageReqVO);

    /**
     * 保存文件，并返回文件的访问路径
     *
     * @param name    文件名称
     * @param path    文件路径
     * @param content 文件内容
     * @return 文件路径
     */
    String createFile(String name, String path, byte[] content);

    /**
     * 创建文件
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    Long createFile(FileCreateReqVO createReqVO);

    /**
     * 删除文件
     *
     * @param id 编号
     */
    void deleteFile(Long id) throws Exception;

    /**
     * 获得文件内容
     *
     * @param configId 配置编号
     * @param path     文件路径
     * @return 文件内容
     */
    byte[] getFileContent(Long configId, String path) throws Exception;

    /**
     * 写入 response 文件下载，支持流式播放
     * @param request 请求
     * @param configId 配置编号
     * @param path 文件路径
     * @param response 响应
     * @throws Exception 异常
     */
    void writeResponse(HttpServletRequest request, Long configId, String path, HttpServletResponse response) throws Exception;

    /**
     * 生成临时下载url，格式化的内容为<br/>
     * doman + %s + uuid
     * @param configId 配置编号
     * @param path     原来文件路径
     * @param timeout  有效期
     * @return 临时下载url
     * @see #getTempFileContent(String,boolean)
     */
    String genTempDownloadFormat(Long configId, String path, Duration timeout) ;

    /**
     * 获得临时文件内容
     *
     * @param uuid 配置编号
     * @param once 一次性
     * @return 文件内容
     * @see #genTempDownloadFormat(Long, String, Duration)
     */
    TempFileRespBO getTempFileContent(String uuid, boolean once) throws Exception;

    /**
     * 生成文件预签名地址信息
     *
     * @param path 文件路径
     * @return 预签名地址信息
     */
    FilePresignedUrlRespVO getFilePresignedUrl(String path) throws Exception;

}
