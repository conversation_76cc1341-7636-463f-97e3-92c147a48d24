package cn.iocoder.yudao.module.infra.framework.file.core.utils;

import cn.hutool.crypto.digest.DigestUtil;
import lombok.extern.slf4j.Slf4j;

import java.io.ByteArrayInputStream;
import java.io.IOException;
import java.io.InputStream;
import java.security.MessageDigest;
import java.security.NoSuchAlgorithmException;

/**
 * 文件哈希计算工具类
 * 
 * <AUTHOR>
 */
@Slf4j
public class FileHashUtils {

    /**
     * 计算文件内容的 SHA-256 哈希值
     *
     * @param content 文件内容
     * @return SHA-256 哈希值（64位十六进制字符串）
     */
    public static String calculateSha256(byte[] content) {
        if (content == null || content.length == 0) {
            throw new IllegalArgumentException("文件内容不能为空");
        }
        
        try {
            return DigestUtil.sha256Hex(content);
        } catch (Exception e) {
            log.error("[calculateSha256][计算文件哈希失败] content.length: {}", content.length, e);
            throw new RuntimeException("计算文件哈希失败", e);
        }
    }

    /**
     * 计算输入流的 SHA-256 哈希值
     *
     * @param inputStream 输入流
     * @return SHA-256 哈希值（64位十六进制字符串）
     */
    public static String calculateSha256(InputStream inputStream) {
        if (inputStream == null) {
            throw new IllegalArgumentException("输入流不能为空");
        }
        
        try {
            MessageDigest digest = MessageDigest.getInstance("SHA-256");
            byte[] buffer = new byte[8192];
            int bytesRead;
            
            while ((bytesRead = inputStream.read(buffer)) != -1) {
                digest.update(buffer, 0, bytesRead);
            }
            
            byte[] hashBytes = digest.digest();
            StringBuilder hexString = new StringBuilder();
            
            for (byte b : hashBytes) {
                String hex = Integer.toHexString(0xff & b);
                if (hex.length() == 1) {
                    hexString.append('0');
                }
                hexString.append(hex);
            }
            
            return hexString.toString();
        } catch (NoSuchAlgorithmException | IOException e) {
            log.error("[calculateSha256][计算输入流哈希失败]", e);
            throw new RuntimeException("计算文件哈希失败", e);
        }
    }

    /**
     * 验证文件内容与哈希值是否匹配
     *
     * @param content 文件内容
     * @param expectedHash 期望的哈希值
     * @return 是否匹配
     */
    public static boolean verifyHash(byte[] content, String expectedHash) {
        if (content == null || expectedHash == null) {
            return false;
        }
        
        try {
            String actualHash = calculateSha256(content);
            return expectedHash.equalsIgnoreCase(actualHash);
        } catch (Exception e) {
            log.error("[verifyHash][验证文件哈希失败] expectedHash: {}", expectedHash, e);
            return false;
        }
    }

    /**
     * 验证输入流与哈希值是否匹配
     *
     * @param inputStream 输入流
     * @param expectedHash 期望的哈希值
     * @return 是否匹配
     */
    public static boolean verifyHash(InputStream inputStream, String expectedHash) {
        if (inputStream == null || expectedHash == null) {
            return false;
        }
        
        try {
            String actualHash = calculateSha256(inputStream);
            return expectedHash.equalsIgnoreCase(actualHash);
        } catch (Exception e) {
            log.error("[verifyHash][验证输入流哈希失败] expectedHash: {}", expectedHash, e);
            return false;
        }
    }

    /**
     * 检查哈希值格式是否有效（64位十六进制字符串）
     *
     * @param hash 哈希值
     * @return 是否有效
     */
    public static boolean isValidSha256Hash(String hash) {
        if (hash == null || hash.length() != 64) {
            return false;
        }
        
        return hash.matches("^[a-fA-F0-9]{64}$");
    }

    /**
     * 计算文件内容的哈希值和大小
     *
     * @param content 文件内容
     * @return 包含哈希值和大小的结果对象
     */
    public static FileHashResult calculateHashAndSize(byte[] content) {
        if (content == null) {
            throw new IllegalArgumentException("文件内容不能为空");
        }
        
        String hash = calculateSha256(content);
        return new FileHashResult(hash, content.length);
    }

    /**
     * 文件哈希计算结果
     */
    public static class FileHashResult {
        private final String hash;
        private final long size;

        public FileHashResult(String hash, long size) {
            this.hash = hash;
            this.size = size;
        }

        public String getHash() {
            return hash;
        }

        public long getSize() {
            return size;
        }

        @Override
        public String toString() {
            return "FileHashResult{" +
                    "hash='" + hash + '\'' +
                    ", size=" + size +
                    '}';
        }
    }
}
