package cn.iocoder.yudao.module.infra.framework.file.core.utils;

import lombok.extern.slf4j.Slf4j;

import java.io.File;
import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.Executors;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.TimeUnit;

/**
 * 临时文件管理器
 * 负责临时文件的创建、清理和定期维护
 *
 * <AUTHOR>
 */
@Slf4j
public class TempFileManager {

    private static final String TEMP_DIR_PREFIX = "yudao-upload-";
    private static final long TEMP_FILE_MAX_AGE_HOURS = 24; // 临时文件最大保留24小时
    private static final long CLEANUP_INTERVAL_HOURS = 6; // 每6小时清理一次

    private static final ConcurrentHashMap<String, File> activeTempFiles = new ConcurrentHashMap<>();
    private static final ScheduledExecutorService cleanupExecutor = Executors.newSingleThreadScheduledExecutor(r -> {
        Thread t = new Thread(r, "temp-file-cleanup");
        t.setDaemon(true);
        return t;
    });

    private static volatile boolean initialized = false;

    /**
     * 初始化临时文件管理器
     */
    public static synchronized void initialize() {
        if (!initialized) {
            // 启动定期清理任务
            cleanupExecutor.scheduleWithFixedDelay(
                TempFileManager::cleanupExpiredTempFiles,
                CLEANUP_INTERVAL_HOURS,
                CLEANUP_INTERVAL_HOURS,
                TimeUnit.HOURS
            );
            initialized = true;
            log.info("[TempFileManager][初始化完成] 定期清理间隔: {}小时", CLEANUP_INTERVAL_HOURS);
        }
    }

    /**
     * 创建临时文件
     *
     * @param prefix 文件名前缀
     * @param suffix 文件名后缀
     * @return 临时文件
     */
    public static File createTempFile(String prefix, String suffix) {
        try {
            // 确保初始化
            if (!initialized) {
                initialize();
            }

            // 创建临时目录
            Path tempDir = createTempDirectory();
            
            // 生成唯一的文件名
            String timestamp = LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyyMMdd_HHmmss_SSS"));
            String fileName = prefix + "_" + timestamp + "_" + Thread.currentThread().getId() + suffix;
            
            File tempFile = new File(tempDir.toFile(), fileName);
            
            // 记录活跃的临时文件
            activeTempFiles.put(tempFile.getAbsolutePath(), tempFile);
            
            log.debug("[createTempFile][创建临时文件] file: {}", tempFile.getAbsolutePath());
            return tempFile;

        } catch (IOException e) {
            throw new RuntimeException("创建临时文件失败", e);
        }
    }

    /**
     * 创建临时目录
     */
    private static Path createTempDirectory() throws IOException {
        String tempDirName = TEMP_DIR_PREFIX + LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyyMMdd"));
        Path tempDir = Paths.get(System.getProperty("java.io.tmpdir"), tempDirName);
        
        if (!Files.exists(tempDir)) {
            Files.createDirectories(tempDir);
            log.debug("[createTempDirectory][创建临时目录] dir: {}", tempDir);
        }
        
        return tempDir;
    }

    /**
     * 删除临时文件
     *
     * @param tempFile 临时文件
     * @return 是否删除成功
     */
    public static boolean deleteTempFile(File tempFile) {
        if (tempFile == null) {
            return true;
        }

        try {
            // 从活跃列表中移除
            activeTempFiles.remove(tempFile.getAbsolutePath());
            
            if (tempFile.exists()) {
                boolean deleted = tempFile.delete();
                if (deleted) {
                    log.debug("[deleteTempFile][删除临时文件成功] file: {}", tempFile.getAbsolutePath());
                } else {
                    log.warn("[deleteTempFile][删除临时文件失败] file: {}", tempFile.getAbsolutePath());
                }
                return deleted;
            }
            return true;

        } catch (Exception e) {
            log.error("[deleteTempFile][删除临时文件异常] file: {}", tempFile.getAbsolutePath(), e);
            return false;
        }
    }

    /**
     * 清理过期的临时文件
     */
    public static void cleanupExpiredTempFiles() {
        try {
            log.info("[cleanupExpiredTempFiles][开始清理过期临时文件]");
            
            long currentTime = System.currentTimeMillis();
            long maxAge = TEMP_FILE_MAX_AGE_HOURS * 60 * 60 * 1000; // 转换为毫秒
            int cleanedCount = 0;

            // 清理活跃列表中的过期文件
            activeTempFiles.entrySet().removeIf(entry -> {
                File file = entry.getValue();
                if (!file.exists()) {
                    return true; // 文件已不存在，从列表中移除
                }
                
                if (currentTime - file.lastModified() > maxAge) {
                    if (file.delete()) {
                        log.debug("[cleanupExpiredTempFiles][删除过期文件] file: {}", file.getAbsolutePath());
                        return true;
                    }
                }
                return false;
            });

            // 清理临时目录中的过期文件
            String tempDirPath = System.getProperty("java.io.tmpdir");
            File tempDir = new File(tempDirPath);
            
            if (tempDir.exists() && tempDir.isDirectory()) {
                File[] files = tempDir.listFiles((dir, name) -> name.startsWith(TEMP_DIR_PREFIX));
                if (files != null) {
                    for (File dir : files) {
                        if (dir.isDirectory()) {
                            cleanedCount += cleanupDirectory(dir, maxAge);
                        }
                    }
                }
            }

            log.info("[cleanupExpiredTempFiles][清理完成] 清理文件数: {}, 活跃文件数: {}", 
                cleanedCount, activeTempFiles.size());

        } catch (Exception e) {
            log.error("[cleanupExpiredTempFiles][清理过期临时文件异常]", e);
        }
    }

    /**
     * 清理目录中的过期文件
     */
    private static int cleanupDirectory(File directory, long maxAge) {
        int cleanedCount = 0;
        long currentTime = System.currentTimeMillis();
        
        File[] files = directory.listFiles();
        if (files != null) {
            for (File file : files) {
                if (file.isFile() && currentTime - file.lastModified() > maxAge) {
                    if (file.delete()) {
                        cleanedCount++;
                        log.debug("[cleanupDirectory][删除过期文件] file: {}", file.getAbsolutePath());
                    }
                }
            }
            
            // 如果目录为空，尝试删除目录
            if (directory.list() != null && directory.list().length == 0) {
                if (directory.delete()) {
                    log.debug("[cleanupDirectory][删除空目录] dir: {}", directory.getAbsolutePath());
                }
            }
        }
        
        return cleanedCount;
    }

    /**
     * 获取活跃临时文件数量
     */
    public static int getActiveTempFileCount() {
        return activeTempFiles.size();
    }

    /**
     * 强制清理所有临时文件（用于测试或关闭时）
     */
    public static void forceCleanupAll() {
        log.info("[forceCleanupAll][强制清理所有临时文件] 当前活跃文件数: {}", activeTempFiles.size());
        
        activeTempFiles.values().forEach(file -> {
            try {
                if (file.exists() && file.delete()) {
                    log.debug("[forceCleanupAll][删除文件] file: {}", file.getAbsolutePath());
                }
            } catch (Exception e) {
                log.warn("[forceCleanupAll][删除文件失败] file: {}", file.getAbsolutePath(), e);
            }
        });
        
        activeTempFiles.clear();
    }

    /**
     * 关闭临时文件管理器
     */
    public static void shutdown() {
        log.info("[shutdown][关闭临时文件管理器]");
        forceCleanupAll();
        cleanupExecutor.shutdown();
        try {
            if (!cleanupExecutor.awaitTermination(5, TimeUnit.SECONDS)) {
                cleanupExecutor.shutdownNow();
            }
        } catch (InterruptedException e) {
            cleanupExecutor.shutdownNow();
            Thread.currentThread().interrupt();
        }
        initialized = false;
    }

}
