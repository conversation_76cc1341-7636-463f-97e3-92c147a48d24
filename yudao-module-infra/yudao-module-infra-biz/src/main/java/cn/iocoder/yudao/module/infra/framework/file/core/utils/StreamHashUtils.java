package cn.iocoder.yudao.module.infra.framework.file.core.utils;

import lombok.Data;
import lombok.extern.slf4j.Slf4j;

import java.io.IOException;
import java.io.InputStream;
import java.security.MessageDigest;
import java.security.NoSuchAlgorithmException;

/**
 * 流式哈希计算工具类
 * 支持大文件的哈希计算，避免OOM
 *
 * <AUTHOR>
 */
@Slf4j
public class StreamHashUtils {

    private static final int BUFFER_SIZE = 8192; // 8KB缓冲区

    /**
     * 计算输入流的SHA-256哈希值和大小
     *
     * @param inputStream 输入流
     * @return 哈希结果
     */
    public static StreamHashResult calculateHashAndSize(InputStream inputStream) {
        try {
            MessageDigest digest = MessageDigest.getInstance("SHA-256");
            byte[] buffer = new byte[BUFFER_SIZE];
            long totalSize = 0;
            int bytesRead;

            while ((bytesRead = inputStream.read(buffer)) != -1) {
                digest.update(buffer, 0, bytesRead);
                totalSize += bytesRead;
            }

            byte[] hashBytes = digest.digest();
            String hash = bytesToHex(hashBytes);

            return new StreamHashResult(hash, totalSize);

        } catch (NoSuchAlgorithmException e) {
            throw new RuntimeException("SHA-256算法不可用", e);
        } catch (IOException e) {
            throw new RuntimeException("读取输入流失败", e);
        }
    }

    /**
     * 计算输入流的SHA-256哈希值和大小（支持重置的流）
     * 计算完成后会尝试重置流到开始位置
     *
     * @param inputStream 支持重置的输入流
     * @return 哈希结果
     */
    public static StreamHashResult calculateHashAndSizeWithReset(InputStream inputStream) {
        if (!inputStream.markSupported()) {
            throw new IllegalArgumentException("输入流不支持mark/reset操作");
        }

        try {
            // 标记流的开始位置
            inputStream.mark(Integer.MAX_VALUE);
            
            // 计算哈希
            StreamHashResult result = calculateHashAndSize(inputStream);
            
            // 重置流到开始位置
            inputStream.reset();
            
            return result;

        } catch (IOException e) {
            throw new RuntimeException("重置输入流失败", e);
        }
    }

    /**
     * 计算输入流的哈希并写入临时文件
     * 真正的流式处理，避免大文件OOM
     *
     * @param inputStream 原始输入流
     * @return 哈希结果和临时文件
     */
    public static StreamHashAndTempFileResult calculateHashAndWriteToTempFile(InputStream inputStream) {
        java.io.File tempFile = null;
        try {
            // 使用临时文件管理器创建临时文件
            tempFile = TempFileManager.createTempFile("upload", ".tmp");

            MessageDigest digest = MessageDigest.getInstance("SHA-256");
            byte[] buffer = new byte[BUFFER_SIZE];
            long totalSize = 0;
            int bytesRead;

            // 同时写入临时文件和计算哈希
            try (java.io.FileOutputStream fos = new java.io.FileOutputStream(tempFile);
                 java.io.BufferedOutputStream bos = new java.io.BufferedOutputStream(fos)) {

                while ((bytesRead = inputStream.read(buffer)) != -1) {
                    // 更新哈希计算
                    digest.update(buffer, 0, bytesRead);
                    // 写入临时文件
                    bos.write(buffer, 0, bytesRead);
                    totalSize += bytesRead;
                }
                bos.flush();
            }

            byte[] hashBytes = digest.digest();
            String hash = bytesToHex(hashBytes);

            log.debug("[calculateHashAndWriteToTempFile][处理完成] hash: {}, size: {}, tempFile: {}",
                hash, totalSize, tempFile.getAbsolutePath());

            return new StreamHashAndTempFileResult(hash, totalSize, tempFile);

        } catch (Exception e) {
            // 出错时清理临时文件
            if (tempFile != null) {
                TempFileManager.deleteTempFile(tempFile);
            }
            throw new RuntimeException("计算哈希并写入临时文件失败", e);
        }
    }

    /**
     * 从临时文件创建新的输入流
     *
     * @param tempFile 临时文件
     * @return 新的文件输入流
     */
    public static InputStream createInputStreamFromTempFile(java.io.File tempFile) {
        try {
            return new java.io.FileInputStream(tempFile);
        } catch (java.io.FileNotFoundException e) {
            throw new RuntimeException("临时文件不存在: " + tempFile.getAbsolutePath(), e);
        }
    }

    /**
     * 清理临时文件
     *
     * @param tempFile 临时文件
     */
    public static void cleanupTempFile(java.io.File tempFile) {
        TempFileManager.deleteTempFile(tempFile);
    }

    /**
     * 将字节数组转换为十六进制字符串
     */
    private static String bytesToHex(byte[] bytes) {
        StringBuilder result = new StringBuilder();
        for (byte b : bytes) {
            result.append(String.format("%02x", b));
        }
        return result.toString();
    }

    /**
     * 流式哈希计算结果
     */
    @Data
    public static class StreamHashResult {
        /**
         * SHA-256哈希值
         */
        private final String hash;

        /**
         * 文件大小（字节）
         */
        private final Long size;

        public StreamHashResult(String hash, Long size) {
            this.hash = hash;
            this.size = size;
        }
    }

    /**
     * 流式哈希计算和临时文件结果
     */
    @Data
    public static class StreamHashAndTempFileResult {
        /**
         * SHA-256哈希值
         */
        private final String hash;

        /**
         * 文件大小（字节）
         */
        private final Long size;

        /**
         * 临时文件
         */
        private final java.io.File tempFile;

        public StreamHashAndTempFileResult(String hash, Long size, java.io.File tempFile) {
            this.hash = hash;
            this.size = size;
            this.tempFile = tempFile;
        }

        /**
         * 获取StreamHashResult
         */
        public StreamHashResult getHashResult() {
            return new StreamHashResult(hash, size);
        }

        /**
         * 创建新的输入流
         */
        public InputStream createInputStream() {
            return StreamHashUtils.createInputStreamFromTempFile(tempFile);
        }

        /**
         * 清理临时文件
         */
        public void cleanup() {
            StreamHashUtils.cleanupTempFile(tempFile);
        }
    }

}
