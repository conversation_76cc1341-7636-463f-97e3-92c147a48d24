package cn.iocoder.yudao.module.infra.framework.file.core.utils;

import lombok.Data;
import lombok.extern.slf4j.Slf4j;

import java.io.IOException;
import java.io.InputStream;
import java.security.MessageDigest;
import java.security.NoSuchAlgorithmException;

/**
 * 流式哈希计算工具类
 * 支持大文件的哈希计算，避免OOM
 *
 * <AUTHOR>
 */
@Slf4j
public class StreamHashUtils {

    private static final int BUFFER_SIZE = 8192; // 8KB缓冲区

    /**
     * 计算输入流的SHA-256哈希值和大小
     *
     * @param inputStream 输入流
     * @return 哈希结果
     */
    public static StreamHashResult calculateHashAndSize(InputStream inputStream) {
        try {
            MessageDigest digest = MessageDigest.getInstance("SHA-256");
            byte[] buffer = new byte[BUFFER_SIZE];
            long totalSize = 0;
            int bytesRead;

            while ((bytesRead = inputStream.read(buffer)) != -1) {
                digest.update(buffer, 0, bytesRead);
                totalSize += bytesRead;
            }

            byte[] hashBytes = digest.digest();
            String hash = bytesToHex(hashBytes);

            return new StreamHashResult(hash, totalSize);

        } catch (NoSuchAlgorithmException e) {
            throw new RuntimeException("SHA-256算法不可用", e);
        } catch (IOException e) {
            throw new RuntimeException("读取输入流失败", e);
        }
    }

    /**
     * 计算输入流的SHA-256哈希值和大小（支持重置的流）
     * 计算完成后会尝试重置流到开始位置
     *
     * @param inputStream 支持重置的输入流
     * @return 哈希结果
     */
    public static StreamHashResult calculateHashAndSizeWithReset(InputStream inputStream) {
        if (!inputStream.markSupported()) {
            throw new IllegalArgumentException("输入流不支持mark/reset操作");
        }

        try {
            // 标记流的开始位置
            inputStream.mark(Integer.MAX_VALUE);
            
            // 计算哈希
            StreamHashResult result = calculateHashAndSize(inputStream);
            
            // 重置流到开始位置
            inputStream.reset();
            
            return result;

        } catch (IOException e) {
            throw new RuntimeException("重置输入流失败", e);
        }
    }

    /**
     * 创建支持重置的输入流包装器
     * 对于不支持mark/reset的流，会先读取到内存中
     *
     * @param inputStream 原始输入流
     * @param maxSize 最大允许的文件大小（字节）
     * @return 支持重置的输入流
     */
    public static InputStream createResettableStream(InputStream inputStream, long maxSize) {
        if (inputStream.markSupported()) {
            return inputStream;
        }

        try {
            // 读取到字节数组
            java.io.ByteArrayOutputStream baos = new java.io.ByteArrayOutputStream();
            byte[] buffer = new byte[BUFFER_SIZE];
            long totalSize = 0;
            int bytesRead;

            while ((bytesRead = inputStream.read(buffer)) != -1) {
                totalSize += bytesRead;
                if (totalSize > maxSize) {
                    throw new IllegalArgumentException("文件大小超过限制: " + maxSize + " 字节");
                }
                baos.write(buffer, 0, bytesRead);
            }

            // 创建支持重置的字节数组输入流
            return new java.io.ByteArrayInputStream(baos.toByteArray());

        } catch (IOException e) {
            throw new RuntimeException("创建可重置流失败", e);
        }
    }

    /**
     * 将字节数组转换为十六进制字符串
     */
    private static String bytesToHex(byte[] bytes) {
        StringBuilder result = new StringBuilder();
        for (byte b : bytes) {
            result.append(String.format("%02x", b));
        }
        return result.toString();
    }

    /**
     * 流式哈希计算结果
     */
    @Data
    public static class StreamHashResult {
        /**
         * SHA-256哈希值
         */
        private final String hash;

        /**
         * 文件大小（字节）
         */
        private final Long size;

        public StreamHashResult(String hash, Long size) {
            this.hash = hash;
            this.size = size;
        }
    }

}
