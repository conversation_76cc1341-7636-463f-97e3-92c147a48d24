package cn.iocoder.yudao.module.infra.dal.dataobject.file;

import cn.iocoder.yudao.framework.mybatis.core.dataobject.BaseDO;
import com.baomidou.mybatisplus.annotation.KeySequence;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.*;

import java.time.LocalDateTime;

/**
 * 用户云盘 DO
 *
 * <AUTHOR>
 */
@TableName("infra_user_cloud_disk")
@KeySequence("infra_user_cloud_disk_seq")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class UserCloudDiskDO extends BaseDO {

    /**
     * 云盘记录ID
     */
    @TableId
    private Long id;

    /**
     * 用户ID
     */
    private Long userId;

    /**
     * 文件ID
     */
    private Long fileId;

    /**
     * 父目录ID
     */
    private Long parentId;

    /**
     * 用户自定义文件名
     */
    private String fileName;

    /**
     * 文件路径
     */
    private String filePath;

    /**
     * 是否为文件夹
     */
    private Boolean isFolder;

    /**
     * 文件夹类型
     */
    private String folderType;

    /**
     * 文件大小
     */
    private Long fileSize;

    /**
     * 文件类型
     */
    private String fileType;

    /**
     * 文件标签
     */
    private String tags;

    /**
     * 文件描述
     */
    private String description;

    /**
     * 是否收藏
     */
    private Boolean isFavorite;

    /**
     * 是否共享
     */
    private Boolean isShared;

    /**
     * 分享码
     */
    private String shareCode;

    /**
     * 分享过期时间
     */
    private LocalDateTime shareExpireTime;

    /**
     * 下载次数
     */
    private Integer downloadCount;

    /**
     * 查看次数
     */
    private Integer viewCount;

    /**
     * 文件夹类型枚举
     */
    public static class FolderType {
        public static final String ROOT = "ROOT";
        public static final String CUSTOM = "CUSTOM";
    }

}
