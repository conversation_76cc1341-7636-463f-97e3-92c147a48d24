package cn.iocoder.yudao.module.infra.service.deduplication;

import cn.iocoder.yudao.module.infra.api.deduplication.dto.DeduplicationFileUploadReqDTO;
import cn.iocoder.yudao.module.infra.api.deduplication.dto.DeduplicationFileRespDTO;
import cn.iocoder.yudao.module.infra.api.deduplication.dto.DeduplicationStatsRespDTO;

import java.util.List;
import java.util.Map;

/**
 * 去重文件服务接口
 *
 * <AUTHOR>
 */
public interface DeduplicationFileService {

    /**
     * 上传文件（支持去重）
     *
     * @param uploadReq 上传请求
     * @return 文件信息
     */
    DeduplicationFileRespDTO uploadFile(DeduplicationFileUploadReqDTO uploadReq);

    /**
     * 根据文件ID获取文件信息
     *
     * @param fileId 文件ID
     * @return 文件信息
     */
    DeduplicationFileRespDTO getFile(Long fileId);

    /**
     * 批量根据文件ID获取文件信息
     *
     * @param fileIds 文件ID列表
     * @return 文件ID -> 文件信息的映射
     */
    Map<Long, DeduplicationFileRespDTO> getFiles(List<Long> fileIds);

    /**
     * 根据文件ID获取访问URL
     *
     * @param fileId 文件ID
     * @return 访问URL
     */
    String getFileUrl(Long fileId);

    /**
     * 批量根据文件ID获取访问URL
     *
     * @param fileIds 文件ID列表
     * @return 文件ID -> 访问URL的映射
     */
    Map<Long, String> getFileUrls(List<Long> fileIds);

    /**
     * 删除文件（减少引用计数）
     *
     * @param fileId 文件ID
     * @return 是否删除成功
     */
    Boolean deleteFile(Long fileId);

    /**
     * 批量删除文件
     *
     * @param fileIds 文件ID列表
     * @return 删除成功的文件数量
     */
    Integer deleteFiles(List<Long> fileIds);

    /**
     * 获取去重统计信息
     *
     * @return 去重统计信息
     */
    DeduplicationStatsRespDTO getDeduplicationStats();

}
