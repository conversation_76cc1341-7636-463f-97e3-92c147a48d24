package cn.iocoder.yudao.module.infra.service.deduplication;

import cn.iocoder.yudao.module.infra.api.deduplication.dto.*;

import java.util.List;
import java.util.Map;

/**
 * 去重文件服务接口（重新设计）
 *
 * <AUTHOR>
 */
public interface DeduplicationFileService {

    /**
     * 前端文件哈希校验（防止无效上传）
     *
     * @param reqDTO 校验请求
     * @return 校验结果
     */
    FileHashCheckRespDTO checkFileHash(FileHashCheckReqDTO reqDTO);

    /**
     * 去重文件上传
     *
     * @param reqDTO 上传请求
     * @return 上传结果
     */
    DeduplicationFileUploadRespDTO uploadFile(DeduplicationFileUploadReqDTO reqDTO);

    /**
     * 获取文件信息
     *
     * @param fileId 文件ID
     * @return 文件信息
     */
    DeduplicationFileRespDTO getFileInfo(Long fileId);

    /**
     * 批量获取文件信息
     *
     * @param fileIds 文件ID列表
     * @return 文件信息映射
     */
    Map<Long, DeduplicationFileRespDTO> getFileInfoBatch(List<Long> fileIds);

    /**
     * 批量处理文件（压缩、水印、HLS等）
     *
     * @param fileIds 文件ID列表
     * @param processingTypes 处理类型列表
     * @param processingParams 处理参数
     * @return 处理任务ID
     */
    String batchProcessFiles(List<Long> fileIds, List<String> processingTypes, Map<String, Object> processingParams);

    /**
     * 查询文件处理任务状态
     *
     * @param taskId 任务ID
     * @return 任务状态信息
     */
    Map<String, Object> getProcessingTaskStatus(String taskId);

    /**
     * 获取文件的衍生版本列表
     *
     * @param fileId 原始文件ID
     * @return 衍生版本列表
     */
    List<Map<String, Object>> getFileDerivatives(Long fileId);

    /**
     * 获取去重统计信息
     *
     * @return 去重统计信息
     */
    DeduplicationStatsRespDTO getDeduplicationStats();

    /**
     * 删除文件（减少引用计数）
     *
     * @param fileId 文件ID
     * @return 是否成功
     */
    Boolean deleteFile(Long fileId);

    /**
     * 清理零引用文件
     *
     * @return 清理的文件数量
     */
    Integer cleanupZeroRefFiles();

}
