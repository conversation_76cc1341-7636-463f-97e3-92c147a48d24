package cn.iocoder.yudao.module.infra.dal.dataobject.file;

import cn.iocoder.yudao.framework.mybatis.core.dataobject.BaseDO;
import com.baomidou.mybatisplus.annotation.KeySequence;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.*;

import java.time.LocalDateTime;

/**
 * 文件处理任务 DO
 *
 * <AUTHOR>
 */
@TableName("infra_file_processing_task")
@KeySequence("infra_file_processing_task_seq")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class FileProcessingTaskDO extends BaseDO {

    /**
     * 任务ID
     */
    @TableId
    private Long id;

    /**
     * 任务唯一标识
     */
    private String taskId;

    /**
     * 原始文件ID
     */
    private Long originalFileId;

    /**
     * 处理类型数组JSON
     */
    private String processingTypes;

    /**
     * 处理参数JSON
     */
    private String processingParams;

    /**
     * 状态
     */
    private Integer status;

    /**
     * 进度百分比
     */
    private Integer progress;

    /**
     * 结果文件ID数组JSON
     */
    private String resultFileIds;

    /**
     * 错误信息
     */
    private String errorMessage;

    /**
     * 开始时间
     */
    private LocalDateTime startTime;

    /**
     * 结束时间
     */
    private LocalDateTime endTime;

    /**
     * 状态枚举
     */
    public static class Status {
        public static final Integer PENDING = 0;
        public static final Integer PROCESSING = 1;
        public static final Integer SUCCESS = 2;
        public static final Integer FAILED = 3;
    }

    /**
     * 处理类型枚举
     */
    public static class ProcessingType {
        public static final String COMPRESS = "COMPRESS";
        public static final String WATERMARK = "WATERMARK";
        public static final String HLS = "HLS";
    }

}
