package cn.iocoder.yudao.module.infra.dal.dataobject.file;

import cn.iocoder.yudao.framework.mybatis.core.dataobject.BaseDO;
import com.baomidou.mybatisplus.annotation.KeySequence;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.*;

/**
 * 文件索引表
 * 用于全局文件去重，记录文件的哈希值、存储路径和引用计数
 *
 * <AUTHOR>
 */
@TableName("infra_file_index")
@KeySequence("infra_file_index_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class FileIndexDO extends BaseDO {

    /**
     * 文件索引ID，数据库自增
     */
    private Long id;

    /**
     * 文件SHA-256哈希值
     * 64位十六进制字符串，用于文件去重
     */
    private String sha256;

    /**
     * 文件大小（字节）
     * 与哈希值一起作为唯一标识，防止哈希碰撞
     */
    private Long size;

    /**
     * 存储路径
     * 文件在存储系统中的实际路径
     */
    private String storagePath;

    /**
     * 存储配置ID
     * 关联 {@link FileConfigDO#getId()}
     */
    private Long configId;

    /**
     * 引用计数
     * 记录有多少个文件记录引用了这个物理文件
     * 当引用计数为0时，可以删除物理文件
     */
    private Integer refCount;

}
