package cn.iocoder.yudao.module.infra.dal.dataobject.file;

import cn.iocoder.yudao.framework.mybatis.core.dataobject.BaseDO;
import com.baomidou.mybatisplus.annotation.KeySequence;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.*;

/**
 * 文件索引 DO（简化版本）
 * 只保留核心字段，其他处理信息通过衍生版本表管理
 *
 * <AUTHOR>
 */
@TableName("infra_file_index")
@KeySequence("infra_file_index_seq")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class FileIndexDO extends BaseDO {

    /**
     * 文件ID
     */
    @TableId
    private Long id;

    /**
     * 文件配置编号
     */
    private Long configId;

    /**
     * 文件名
     */
    private String name;

    /**
     * 文件路径
     */
    private String path;

    /**
     * 文件 URL
     */
    private String url;

    /**
     * 文件类型
     */
    private String type;

    /**
     * 文件大小（字节）
     */
    private Long size;

    /**
     * SHA-256哈希值
     */
    private String sha256Hash;

    /**
     * 引用次数
     */
    private Integer referenceCount;

}