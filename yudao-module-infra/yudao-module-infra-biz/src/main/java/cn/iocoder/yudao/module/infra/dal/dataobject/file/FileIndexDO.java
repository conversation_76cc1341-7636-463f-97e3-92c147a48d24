package cn.iocoder.yudao.module.infra.dal.dataobject.file;

import cn.iocoder.yudao.framework.mybatis.core.dataobject.BaseDO;
import com.baomidou.mybatisplus.annotation.KeySequence;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.*;

/**
 * 文件索引表
 * 用于全局文件去重，记录文件的哈希值、存储路径和引用计数
 *
 * <AUTHOR>
 */
@TableName("infra_file_index")
@KeySequence("infra_file_index_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class FileIndexDO extends BaseDO {

    /**
     * 文件索引ID，数据库自增
     */
    private Long id;

    /**
     * 原文件SHA-256哈希值
     * 用于识别原始文件，即使文件被压缩替换也保持不变
     */
    private String originalHash;

    /**
     * 当前文件SHA-256哈希值
     * 当前实际存储文件的哈希值，压缩后会更新为压缩文件的哈希
     */
    private String currentHash;

    /**
     * 原文件大小（字节）
     */
    private Long originalSize;

    /**
     * 当前文件大小（字节）
     */
    private Long currentSize;

    /**
     * 存储路径
     * 文件在存储系统中的实际路径
     */
    private String storagePath;

    /**
     * 存储配置ID
     * 关联 {@link FileConfigDO#getId()}
     */
    private Long configId;

    /**
     * 引用计数
     * 记录有多少个文件记录引用了这个物理文件
     * 当引用计数为0时，可以删除物理文件
     */
    private Integer refCount;

    /**
     * 是否已压缩
     */
    private Boolean isCompressed;

    /**
     * 压缩类型
     * 枚举 {@link cn.iocoder.yudao.module.infra.enums.file.FileCompressTypeEnum}
     */
    private String compressType;

    /**
     * 压缩比例（百分比）
     */
    private java.math.BigDecimal compressRatio;

    /**
     * 水印图片路径
     */
    private String watermarkPath;

    /**
     * 水印位置：top-left,top-right,bottom-left,bottom-right,center
     */
    private String watermarkPosition;

    /**
     * 水印透明度(0.0-1.0)
     */
    private java.math.BigDecimal watermarkOpacity;

    /**
     * 水印大小比例(0.0-1.0)
     */
    private java.math.BigDecimal watermarkScale;

    /**
     * 是否已添加水印：0-否，1-是
     */
    private Boolean hasWatermark;

    /**
     * 水印处理状态：0-未处理，1-处理中，2-成功，3-失败
     */
    private Integer watermarkStatus;

    /**
     * 水印处理错误信息
     */
    private String watermarkErrorMsg;

    /**
     * 水印处理完成时间
     */
    private java.time.LocalDateTime watermarkProcessedAt;

    // ========== 便利方法 ==========

    /**
     * 获取有效的哈希值（兼容旧版本）
     */
    public String getSha256() {
        return currentHash != null ? currentHash : originalHash;
    }

    /**
     * 设置哈希值（兼容旧版本）
     */
    public void setSha256(String sha256) {
        if (this.originalHash == null) {
            this.originalHash = sha256;
        }
        this.currentHash = sha256;
    }

    /**
     * 获取有效的大小（兼容旧版本）
     */
    public Long getSize() {
        return currentSize != null ? currentSize : originalSize;
    }

    /**
     * 设置大小（兼容旧版本）
     */
    public void setSize(Long size) {
        if (this.originalSize == null) {
            this.originalSize = size;
        }
        this.currentSize = size;
    }

    /**
     * 是否已被压缩
     */
    public boolean isCompressed() {
        return Boolean.TRUE.equals(isCompressed);
    }

    /**
     * 获取压缩节省的空间
     */
    public Long getSavedSpace() {
        if (isCompressed() && originalSize != null && currentSize != null) {
            return originalSize - currentSize;
        }
        return 0L;
    }

}
