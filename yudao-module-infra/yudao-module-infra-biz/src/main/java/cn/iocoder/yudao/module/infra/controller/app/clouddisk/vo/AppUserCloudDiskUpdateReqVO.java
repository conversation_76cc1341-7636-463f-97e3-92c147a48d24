package cn.iocoder.yudao.module.infra.controller.app.clouddisk.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotNull;

/**
 * 用户云盘文件更新请求VO
 *
 * <AUTHOR>
 */
@Schema(description = "用户 APP - 云盘文件更新请求VO")
@Data
public class AppUserCloudDiskUpdateReqVO {

    @Schema(description = "云盘记录ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "1024")
    @NotNull(message = "云盘记录ID不能为空")
    private Long id;

    @Schema(description = "文件名", example = "新文件名.jpg")
    private String fileName;

    @Schema(description = "父目录ID", example = "1")
    private Long parentId;

}
