package cn.iocoder.yudao.module.infra.service.file;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.io.FileUtil;
import cn.hutool.core.lang.Assert;
import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.core.util.URLUtil;
import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.common.util.io.FileUtils;
import cn.iocoder.yudao.framework.common.util.number.NumberUtils;
import cn.iocoder.yudao.framework.common.util.object.BeanUtils;
import cn.iocoder.yudao.module.infra.dal.dataobject.file.FileConfigDO;
import cn.iocoder.yudao.module.infra.framework.file.core.client.FileClient;
import cn.iocoder.yudao.module.infra.framework.file.core.client.FileClientConfig;
import cn.iocoder.yudao.module.infra.framework.file.core.client.local.LocalFileClient;
import cn.iocoder.yudao.module.infra.framework.file.core.client.s3.FilePresignedUrlRespDTO;
import cn.iocoder.yudao.module.infra.framework.file.core.utils.FileTypeUtils;
import cn.iocoder.yudao.module.infra.controller.admin.file.vo.file.FileCreateReqVO;
import cn.iocoder.yudao.module.infra.controller.admin.file.vo.file.FilePageReqVO;
import cn.iocoder.yudao.module.infra.controller.admin.file.vo.file.FilePresignedUrlRespVO;
import cn.iocoder.yudao.module.infra.dal.dataobject.file.FileDO;
import cn.iocoder.yudao.module.infra.dal.mysql.file.FileMapper;
import cn.iocoder.yudao.module.infra.framework.file.core.utils.VideoStreamUtils;
import cn.iocoder.yudao.module.infra.service.file.bo.TempFileRespBO;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import java.time.Duration;

import static cn.iocoder.yudao.framework.common.enums.SeparatorConstants.NOT_IN_URL_CHARACTER_SET;
import static cn.iocoder.yudao.framework.common.exception.util.ServiceExceptionUtil.exception;
import static cn.iocoder.yudao.module.infra.enums.ErrorCodeConstants.FILE_NOT_EXISTS;
import static cn.iocoder.yudao.module.infra.framework.file.core.utils.FileTypeUtils.writeAttachment;

/**
 * 文件 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Slf4j
public class FileServiceImpl implements FileService {

    @Resource
    private FileConfigService fileConfigService;

    @Resource
    private FileMapper fileMapper;

    @Resource
    private StringRedisTemplate stringRedisTemplate;

    @Override
    public PageResult<FileDO> getFilePage(FilePageReqVO pageReqVO) {
        return fileMapper.selectPage(pageReqVO);
    }

    @Override
    @SneakyThrows
    public String createFile(String name, String path, byte[] content) {
        // 计算默认的 path 名
        String type = FileTypeUtils.getMineType(content, name);
        if (StrUtil.isEmpty(path)) {
            path = FileUtils.generatePath(content, name);
        }
        // 如果 name 为空，则使用 path 填充
        if (StrUtil.isEmpty(name)) {
            name = path;
        }

        // 上传到文件存储器
        FileClient client = fileConfigService.getMasterFileClient();
        Assert.notNull(client, "客户端(master) 不能为空");
        String url = client.upload(content, path, type);

        // 保存到数据库
        FileDO file = new FileDO();
        file.setConfigId(client.getId());
        file.setName(name);
        file.setPath(path);
        file.setUrl(url);
        file.setType(type);
        file.setSize(content.length);
        fileMapper.insert(file);
        return url;
    }

    @Override
    public Long createFile(FileCreateReqVO createReqVO) {
        FileDO file = BeanUtils.toBean(createReqVO, FileDO.class);
        fileMapper.insert(file);
        return file.getId();
    }

    @Override
    public void deleteFile(Long id) throws Exception {
        // 校验存在
        FileDO file = validateFileExists(id);

        // 从文件存储器中删除
        FileClient client = getFileClient(file.getConfigId());
        client.delete(file.getPath());

        // 删除记录
        fileMapper.deleteById(id);
    }

    private FileDO validateFileExists(Long id) {
        FileDO fileDO = fileMapper.selectById(id);
        if (fileDO == null) {
            throw exception(FILE_NOT_EXISTS);
        }
        return fileDO;
    }

    @Override
    public byte[] getFileContent(Long configId, String path) throws Exception {
        FileClient client = getFileClient(configId);
        return client.getContent(path);
    }

    private FileClient getFileClient(Long configId) {
        FileClient client = fileConfigService.getFileClient(configId);
        Assert.notNull(client, "客户端({}) 不能为空", configId);
        return client;
    }

    @Override
    public void writeResponse(HttpServletRequest request, Long configId, String path, HttpServletResponse response) throws Exception {
        // 获取请求的路径
        if (StrUtil.isEmpty(path)) {
            throw new IllegalArgumentException("结尾的 path 路径必须传递");
        }
        // 解码，解决中文路径的问题 https://gitee.com/zhijiantianya/ruoyi-vue-pro/pulls/807/
        path = URLUtil.decode(path);
        // 读取内容
        FileClient fileClient = getFileClient(configId);
        LocalFileClient localFileClient = null;
        byte[] content = null;
        if(fileClient instanceof LocalFileClient){
            localFileClient = (LocalFileClient) fileClient;
            if (!FileUtil.exist(localFileClient.getFile(path))) {
                log.warn("[getFileContent][configId({}) path({}) 文件不存在]", configId, path);
                response.setStatus(HttpStatus.NOT_FOUND.value());
                return;
            }
        }else{
            content = fileClient.getContent(path);
            if (content == null) {
                log.warn("[getFileContent][configId({}) path({}) 文件不存在]", configId, path);
                response.setStatus(HttpStatus.NOT_FOUND.value());
                return;
            }
        }


        // 检查文件是否为MP4视频
        if (request != null && StrUtil.endWithIgnoreCase(path, ".mp4")) {
            try {
                // 使用视频流工具处理MP4文件（包含缓存控制）
                log.info("[getFileContent][configId({}) path({}) 使用流式播放处理MP4视频]", configId, path);
                if(localFileClient != null){
                    VideoStreamUtils.streamVideoFile(request, response,localFileClient.getFile(path));
                }else{
                    VideoStreamUtils.streamVideoContent(request, response, content, path);
                }

            } catch (Exception e) {
                // 检查是否是客户端断开连接的异常
                if (VideoStreamUtils.isClientDisconnectedException(e)) {
                    // 客户端断开连接是正常情况，只记录debug级别日志
                    log.debug("[writeResponse][客户端断开连接] configId: {}, path: {}, error: {}", configId, path, e.getMessage());
                    return; // 不抛出异常，避免传播到全局异常处理器
                }

                log.error("[writeResponse][处理MP4视频流时发生异常] configId: {}, path: {} , error: {}", configId, path, e.getMessage());
                // 如果响应还没有提交，重置并设置错误状态
                if (!response.isCommitted()) {
                    response.reset();
                    response.setStatus(HttpStatus.INTERNAL_SERVER_ERROR.value());
                    response.setContentType("application/json;charset=UTF-8");
                }
                throw e;
            }
        } else {
            // 非MP4文件或请求为null，使用原有方式处理
            // 设置缓存控制，提高性能
            response.setHeader("Cache-Control", "public, max-age=31536000");
            if (content == null) {
                content = fileClient.getContent(path);
            }
            writeAttachment(response, path, content);
        }
    }
    @Override
    public String genTempDownloadFormat(Long configId, String path, Duration timeout)   {
        FileConfigDO fileConfig = fileConfigService.getFileConfig(configId);
        FileClientConfig fileConfigConfig = fileConfig.getConfig();
        Object domain = BeanUtil.getFieldValue(fileConfigConfig, "domain");
        String url = StrUtil.toStringOrEmpty(domain);
        url = StrUtil.removeSuffix(url, "/");
        String uuid = IdUtil.nanoId();
        String redisKey = formatKey(uuid);
        stringRedisTemplate.opsForValue().set(redisKey,configId+ NOT_IN_URL_CHARACTER_SET+path,timeout);
        return url + "%s" + uuid ;
    }

    @Override
    public TempFileRespBO getTempFileContent(String uuid, boolean once) throws Exception {
        String redisKey = formatKey(uuid);
        String value = stringRedisTemplate.opsForValue().get(redisKey);
        TempFileRespBO respVO = new TempFileRespBO();
        if (StrUtil.isNotBlank(value)) {
            // 一次性就删除
            if(once) stringRedisTemplate.delete(redisKey);
            //
            String[] array = StrUtil.splitToArray(value, NOT_IN_URL_CHARACTER_SET);
            String path = array[1];
            if(StrUtil.isNotBlank(path)){
                respVO.setContent(getFileContent( NumberUtils.parseLong(array[0]), path)) ;
                respVO.setPath(path);
            }
        }
        return respVO;
    }

    private static String formatKey(String uuid) {
        return String.format("infra_file:tempDown:%s",uuid);
    }

    @Override
    public FilePresignedUrlRespVO getFilePresignedUrl(String path) throws Exception {
        FileClient fileClient = fileConfigService.getMasterFileClient();
        FilePresignedUrlRespDTO presignedObjectUrl = fileClient.getPresignedObjectUrl(path);
        return BeanUtils.toBean(presignedObjectUrl, FilePresignedUrlRespVO.class,
                object -> object.setConfigId(fileClient.getId()));
    }

}
