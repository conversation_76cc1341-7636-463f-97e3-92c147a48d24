package cn.iocoder.yudao.module.infra.framework.file.core.client.deduplication;

import cn.hutool.core.util.ReflectUtil;
import cn.hutool.extra.spring.SpringUtil;
import cn.iocoder.yudao.module.infra.dal.dataobject.file.FileIndexDO;
import cn.iocoder.yudao.module.infra.dal.mysql.file.FileIndexMapper;
import cn.iocoder.yudao.module.infra.service.file.ReplacementDeduplicationService;
import cn.iocoder.yudao.module.infra.framework.file.core.client.AbstractFileClient;
import cn.iocoder.yudao.module.infra.framework.file.core.client.FileClient;
import cn.iocoder.yudao.module.infra.framework.file.core.client.s3.FilePresignedUrlRespDTO;
import cn.iocoder.yudao.module.infra.framework.file.core.enums.FileStorageEnum;

import cn.iocoder.yudao.module.infra.framework.file.core.utils.FileHashUtils;
import cn.iocoder.yudao.module.infra.service.file.bo.DeduplicationRespBO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.transaction.annotation.Transactional;

import java.io.File;

/**
 * 去重文件客户端
 * 基于文件哈希值实现全局文件去重
 *
 * <AUTHOR>
 */
@Slf4j
public class DeduplicationFileClient extends AbstractFileClient<DeduplicationFileClientConfig> {

    private FileIndexMapper fileIndexMapper;
    private FileClient underlyingClient;

    public DeduplicationFileClient(Long id, DeduplicationFileClientConfig config) {
        super(id, config);
    }

    @Override
    protected void doInit() {
        fileIndexMapper = SpringUtil.getBean(FileIndexMapper.class);

        // 初始化底层文件客户端
        FileStorageEnum storageEnum = FileStorageEnum.getByStorage(config.getUnderlyingStorage());
        if (storageEnum == null) {
            throw new IllegalArgumentException("不支持的存储器类型: " + config.getUnderlyingStorage());
        }

        underlyingClient = (FileClient) ReflectUtil.newInstance(
            storageEnum.getClientClass(),
            getId(),
            config.getUnderlyingConfig()
        );

        // 初始化底层客户端
        if (underlyingClient instanceof AbstractFileClient) {
            ((AbstractFileClient<?>) underlyingClient).init();
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public String upload(byte[] content, String path, String type) throws Exception {
        // 如果未启用去重，直接使用底层存储器
        if (!Boolean.TRUE.equals(config.getEnableDeduplication())) {
            return underlyingClient.upload(content, path, type);
        }

        // 计算文件哈希值和大小
        FileHashUtils.FileHashResult hashResult = FileHashUtils.calculateHashAndSize(content);
        String sha256 = hashResult.getHash();
        long size = hashResult.getSize();

        log.debug("[upload][开始上传文件] path: {}, size: {}, sha256: {}", path, size, sha256);

        // 使用替换式智能去重服务检查文件
        ReplacementDeduplicationService deduplicationService = SpringUtil.getBean(ReplacementDeduplicationService.class);
        DeduplicationRespBO deduplicationRespBO = deduplicationService.checkDeduplication(content);

        if (deduplicationRespBO.isFound()) {
            // 文件已存在，增加引用计数
            FileIndexDO existingIndex = deduplicationRespBO.getFileIndex();
            int updated = fileIndexMapper.incrementRefCount(existingIndex.getId());
            if (updated > 0) {
                // 获取文件路径
                String filePath = deduplicationRespBO.getFilePath();

                log.info("[upload][文件去重成功] path: {}, existingPath: {}, sha256: {}, refCount: {}, isCompressed: {}, savedSpace: {}",
                    path, filePath, sha256, existingIndex.getRefCount() + 1, deduplicationRespBO.isCompressed(), deduplicationRespBO.getSavedSpace());

                // 返回文件的访问URL
                return formatFileUrl(config.getDomain(), filePath);
            } else {
                log.warn("[upload][增加引用计数失败] indexId: {}, path: {}", existingIndex.getId(), path);
            }
        }

        // 文件不存在或引用计数更新失败，需要上传新文件
        try {
            // 使用底层存储器上传文件
            String url = underlyingClient.upload(content, path, type);
            
            // 创建文件索引记录（替换式架构）
            FileIndexDO fileIndex = FileIndexDO.builder()
                .originalHash(sha256)
                .currentHash(sha256)
                .originalSize(size)
                .currentSize(size)
                .storagePath(path)
                .configId(getId())
                .refCount(1)
                .isCompressed(false)
                .build();

            fileIndexMapper.insert(fileIndex);
            
            log.info("[upload][文件上传成功] path: {}, size: {}, sha256: {}", path, size, sha256);
            return url;
            
        } catch (Exception e) {
            log.error("[upload][文件上传失败] path: {}, size: {}, sha256: {}", path, size, sha256, e);
            throw e;
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void delete(String path) throws Exception {
        log.debug("[delete][开始删除文件] path: {}", path);

        // 如果未启用去重，直接使用底层存储器
        if (!Boolean.TRUE.equals(config.getEnableDeduplication())) {
            underlyingClient.delete(path);
            return;
        }

        // 查找文件索引记录
        FileIndexDO fileIndex = fileIndexMapper.selectByStoragePathAndConfigId(path, getId());
        if (fileIndex == null) {
            log.warn("[delete][文件索引不存在] path: {}", path);
            // 尝试直接删除物理文件（兼容旧数据）
            try {
                underlyingClient.delete(path);
            } catch (Exception e) {
                log.warn("[delete][直接删除物理文件失败] path: {}", path, e);
            }
            return;
        }

        // 减少引用计数
        int updated = fileIndexMapper.decrementRefCount(fileIndex.getId());
        if (updated > 0) {
            // 重新查询引用计数
            FileIndexDO updatedIndex = fileIndexMapper.selectById(fileIndex.getId());
            if (updatedIndex != null && updatedIndex.getRefCount() <= 0) {
                // 引用计数为0，删除物理文件和索引记录
                try {
                    underlyingClient.delete(path);
                    fileIndexMapper.deleteById(fileIndex.getId());
                    log.info("[delete][物理文件删除成功] path: {}, sha256: {}", path, fileIndex.getSha256());
                } catch (Exception e) {
                    log.error("[delete][物理文件删除失败] path: {}, sha256: {}", path, fileIndex.getSha256(), e);
                    // 恢复引用计数
                    fileIndexMapper.incrementRefCount(fileIndex.getId());
                    throw e;
                }
            } else {
                log.info("[delete][减少引用计数成功] path: {}, sha256: {}, refCount: {}", 
                    path, fileIndex.getSha256(), updatedIndex != null ? updatedIndex.getRefCount() : 0);
            }
        } else {
            log.warn("[delete][减少引用计数失败] indexId: {}, path: {}", fileIndex.getId(), path);
        }
    }

    @Override
    public byte[] getContent(String path) throws Exception {
        return underlyingClient.getContent(path);
    }

    @Override
    public File getFile(String path) throws Exception {
        return underlyingClient.getFile(path);
    }

    @Override
    public FilePresignedUrlRespDTO getPresignedObjectUrl(String path) throws Exception {
        return underlyingClient.getPresignedObjectUrl(path);
    }

    /**
     * 获取底层文件客户端
     *
     * @return 底层文件客户端
     */
    public FileClient getUnderlyingClient() {
        return underlyingClient;
    }

    /**
     * 清理引用计数为0的文件
     * 
     * @return 清理的文件数量
     */
    public int cleanupUnreferencedFiles() {
        if (!Boolean.TRUE.equals(config.getEnableDeduplication())) {
            return 0;
        }

        java.util.List<FileIndexDO> unreferencedFiles = fileIndexMapper.selectListByRefCountZero();
        int cleanedCount = 0;
        
        for (FileIndexDO fileIndex : unreferencedFiles) {
            try {
                underlyingClient.delete(fileIndex.getStoragePath());
                fileIndexMapper.deleteById(fileIndex.getId());
                cleanedCount++;
                log.info("[cleanupUnreferencedFiles][清理文件成功] path: {}, sha256: {}", 
                    fileIndex.getStoragePath(), fileIndex.getSha256());
            } catch (Exception e) {
                log.error("[cleanupUnreferencedFiles][清理文件失败] path: {}, sha256: {}", 
                    fileIndex.getStoragePath(), fileIndex.getSha256(), e);
            }
        }
        
        log.info("[cleanupUnreferencedFiles][清理完成] total: {}, cleaned: {}",
            unreferencedFiles.size(), cleanedCount);
        return cleanedCount;
    }



}
