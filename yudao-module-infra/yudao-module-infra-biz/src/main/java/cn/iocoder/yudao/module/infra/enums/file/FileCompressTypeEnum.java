package cn.iocoder.yudao.module.infra.enums.file;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 文件压缩类型枚举
 *
 * <AUTHOR>
 */
@AllArgsConstructor
@Getter
public enum FileCompressTypeEnum {

    MP4_COMPRESS("mp4_compress", "MP4视频压缩", "video"),
    IMAGE_AVIF("image_avif", "图片转AVIF格式", "image"),
    IMAGE_WEBP("image_webp", "图片转WebP格式", "image"),
    PDF_COMPRESS("pdf_compress", "PDF文档压缩", "document"),
    AUDIO_COMPRESS("audio_compress", "音频压缩", "audio");

    /**
     * 压缩类型代码
     */
    private final String code;
    
    /**
     * 压缩类型名称
     */
    private final String name;
    
    /**
     * 文件类型
     */
    private final String fileType;

    public static FileCompressTypeEnum getByCode(String code) {
        for (FileCompressTypeEnum typeEnum : values()) {
            if (typeEnum.getCode().equals(code)) {
                return typeEnum;
            }
        }
        return null;
    }

    /**
     * 根据文件扩展名推荐压缩类型
     *
     * @param fileExtension 文件扩展名（不含点号）
     * @return 推荐的压缩类型，如果不支持则返回null
     */
    public static FileCompressTypeEnum getRecommendedType(String fileExtension) {
        if (fileExtension == null) {
            return null;
        }
        
        String ext = fileExtension.toLowerCase();
        switch (ext) {
            case "mp4":
            case "avi":
            case "mov":
            case "mkv":
                return MP4_COMPRESS;
            case "jpg":
            case "jpeg":
            case "png":
            case "bmp":
            case "tiff":
                return IMAGE_AVIF;
            case "pdf":
                return PDF_COMPRESS;
            case "mp3":
            case "wav":
            case "flac":
                return AUDIO_COMPRESS;
            default:
                return null;
        }
    }

    /**
     * 检查文件类型是否支持压缩
     *
     * @param fileExtension 文件扩展名
     * @return 是否支持压缩
     */
    public static boolean isCompressSupported(String fileExtension) {
        return getRecommendedType(fileExtension) != null;
    }

}
