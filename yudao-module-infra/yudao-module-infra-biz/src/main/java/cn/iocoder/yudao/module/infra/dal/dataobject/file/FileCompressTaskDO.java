package cn.iocoder.yudao.module.infra.dal.dataobject.file;

import cn.iocoder.yudao.framework.mybatis.core.dataobject.BaseDO;
import cn.iocoder.yudao.module.infra.enums.file.FileCompressTaskStatusEnum;
import cn.iocoder.yudao.module.infra.enums.file.FileCompressTypeEnum;
import com.baomidou.mybatisplus.annotation.KeySequence;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.*;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 文件压缩任务表
 *
 * <AUTHOR>
 */
@TableName("infra_file_compress_task")
@KeySequence("infra_file_compress_task_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class FileCompressTaskDO extends BaseDO {

    /**
     * 任务ID，数据库自增
     */
    private Long id;

    /**
     * 文件ID，关联 {@link FileDO#getId()}
     */
    private Long fileId;

    /**
     * 原始文件路径
     */
    private String filePath;

    /**
     * 原始文件大小（字节）
     */
    private Long fileSize;

    /**
     * 文件类型（video/image/document/audio）
     */
    private String fileType;

    /**
     * 压缩类型
     * 枚举 {@link FileCompressTypeEnum}
     */
    private String compressType;

    /**
     * 任务状态
     * 枚举 {@link FileCompressTaskStatusEnum}
     */
    private Integer status;

    /**
     * 处理进度（0-100）
     */
    private Integer progress;

    /**
     * 压缩后文件路径
     */
    private String compressedPath;

    /**
     * 压缩后文件大小（字节）
     */
    private Long compressedSize;

    /**
     * 压缩比例（百分比）
     */
    private BigDecimal compressRatio;

    /**
     * 开始处理时间
     */
    private LocalDateTime startTime;

    /**
     * 结束处理时间
     */
    private LocalDateTime endTime;

    /**
     * 错误信息
     */
    private String errorMessage;

    /**
     * 重试次数
     */
    private Integer retryCount;

    /**
     * 最大重试次数
     */
    private Integer maxRetryCount;

    /**
     * 任务优先级（1-10，数字越小优先级越高）
     */
    private Integer priority;

    /**
     * 压缩配置参数（JSON格式）
     */
    private String configParams;

    // ========== 便利方法 ==========

    /**
     * 获取任务状态枚举
     */
    public FileCompressTaskStatusEnum getStatusEnum() {
        return FileCompressTaskStatusEnum.valueOf(status);
    }

    /**
     * 设置任务状态枚举
     */
    public void setStatusEnum(FileCompressTaskStatusEnum statusEnum) {
        this.status = statusEnum.getStatus();
    }

    /**
     * 获取压缩类型枚举
     */
    public FileCompressTypeEnum getCompressTypeEnum() {
        return FileCompressTypeEnum.getByCode(compressType);
    }

    /**
     * 设置压缩类型枚举
     */
    public void setCompressTypeEnum(FileCompressTypeEnum compressTypeEnum) {
        this.compressType = compressTypeEnum.getCode();
    }

    /**
     * 是否可以重试
     */
    public boolean canRetry() {
        return retryCount < maxRetryCount && 
               (status.equals(FileCompressTaskStatusEnum.FAILED.getStatus()) || 
                status.equals(FileCompressTaskStatusEnum.PENDING.getStatus()));
    }

    /**
     * 是否已完成（成功或失败且不能重试）
     */
    public boolean isCompleted() {
        return status.equals(FileCompressTaskStatusEnum.SUCCESS.getStatus()) ||
               status.equals(FileCompressTaskStatusEnum.CANCELLED.getStatus()) ||
               (status.equals(FileCompressTaskStatusEnum.FAILED.getStatus()) && !canRetry());
    }

    /**
     * 计算处理耗时（毫秒）
     */
    public Long getProcessingDuration() {
        if (startTime == null) {
            return null;
        }
        LocalDateTime end = endTime != null ? endTime : LocalDateTime.now();
        return java.time.Duration.between(startTime, end).toMillis();
    }

}
