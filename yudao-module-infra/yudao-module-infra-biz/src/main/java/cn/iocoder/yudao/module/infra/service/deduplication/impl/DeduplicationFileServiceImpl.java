package cn.iocoder.yudao.module.infra.service.deduplication.impl;

import cn.hutool.core.util.IdUtil;
import cn.iocoder.yudao.framework.common.exception.ServiceException;
import cn.iocoder.yudao.module.infra.api.deduplication.dto.*;
import cn.iocoder.yudao.module.infra.dal.dataobject.file.FileDerivativeDO;
import cn.iocoder.yudao.module.infra.dal.dataobject.file.FileIndexDO;
import cn.iocoder.yudao.module.infra.dal.dataobject.file.FileProcessingTaskDO;
import cn.iocoder.yudao.module.infra.dal.dataobject.file.UserCloudDiskDO;
import cn.iocoder.yudao.module.infra.dal.mysql.file.FileDerivativeMapper;
import cn.iocoder.yudao.module.infra.dal.mysql.file.FileIndexMapper;
import cn.iocoder.yudao.module.infra.dal.mysql.file.FileProcessingTaskMapper;
import cn.iocoder.yudao.module.infra.dal.mysql.file.UserCloudDiskMapper;
import cn.iocoder.yudao.module.infra.service.deduplication.DeduplicationFileService;
import cn.iocoder.yudao.module.infra.service.file.FileService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 去重文件服务实现类（重新设计）
 *
 * <AUTHOR>
 */
@Service
@Slf4j
public class DeduplicationFileServiceImpl implements DeduplicationFileService {

    @Resource
    private FileIndexMapper fileIndexMapper;
    
    @Resource
    private FileDerivativeMapper fileDerivativeMapper;
    
    @Resource
    private UserCloudDiskMapper userCloudDiskMapper;
    
    @Resource
    private FileProcessingTaskMapper fileProcessingTaskMapper;
    
    @Resource
    private FileService fileService;

    @Override
    public FileHashCheckRespDTO checkFileHash(FileHashCheckReqDTO reqDTO) {
        log.info("[checkFileHash][前端校验文件哈希] sha256Hash: {}, fileSize: {}", reqDTO.getSha256Hash(), reqDTO.getFileSize());
        
        // 查询是否存在重复文件
        FileIndexDO existingFile = fileIndexMapper.selectByHashAndSize(reqDTO.getSha256Hash(), reqDTO.getFileSize());
        
        FileHashCheckRespDTO.FileHashCheckRespDTOBuilder builder = FileHashCheckRespDTO.builder()
                .isDuplicate(existingFile != null)
                .canSkipUpload(existingFile != null);
        
        if (existingFile != null) {
            // 构建重复文件信息
            FileHashCheckRespDTO.DuplicateFileInfo duplicateFileInfo = FileHashCheckRespDTO.DuplicateFileInfo.builder()
                    .fileId(existingFile.getId())
                    .fileName(existingFile.getName())
                    .fileSize(existingFile.getSize())
                    .contentType(existingFile.getType())
                    .fileUrl(existingFile.getUrl())
                    .createTime(existingFile.getCreateTime())
                    .referenceCount(existingFile.getReferenceCount())
                    .build();
            
            builder.duplicateFileId(existingFile.getId())
                   .duplicateFileInfo(duplicateFileInfo)
                   .suggestedAction(FileHashCheckRespDTO.SuggestedAction.USE_EXISTING);
            
            // 查询可用的衍生版本
            List<FileDerivativeDO> derivatives = fileDerivativeMapper.selectByOriginalFileId(existingFile.getId());
            List<FileHashCheckRespDTO.DerivativeVersionInfo> availableVersions = derivatives.stream()
                    .filter(d -> FileDerivativeDO.Status.SUCCESS.equals(d.getStatus()))
                    .map(this::convertToDerivativeVersionInfo)
                    .collect(Collectors.toList());
            builder.availableVersions(availableVersions);
            
            // 如果需要检查用户云盘
            if (reqDTO.getCheckUserCloudDisk() && reqDTO.getUserId() != null) {
                List<UserCloudDiskDO> cloudDiskFiles = userCloudDiskMapper.selectByUserIdAndFileId(reqDTO.getUserId(), existingFile.getId()) != null 
                        ? Collections.singletonList(userCloudDiskMapper.selectByUserIdAndFileId(reqDTO.getUserId(), existingFile.getId()))
                        : Collections.emptyList();
                
                List<FileHashCheckRespDTO.CloudDiskFileInfo> cloudDiskFileInfos = cloudDiskFiles.stream()
                        .map(this::convertToCloudDiskFileInfo)
                        .collect(Collectors.toList());
                builder.cloudDiskFiles(cloudDiskFileInfos);
                
                if (!cloudDiskFileInfos.isEmpty()) {
                    builder.suggestedAction(FileHashCheckRespDTO.SuggestedAction.ADD_TO_CLOUD_DISK);
                }
            }
        } else {
            builder.suggestedAction(FileHashCheckRespDTO.SuggestedAction.UPLOAD_NEW);
        }
        
        return builder.build();
    }

    @Override
    @Transactional
    public DeduplicationFileUploadRespDTO uploadFile(DeduplicationFileUploadReqDTO reqDTO) {
        log.info("[uploadFile][开始去重上传] name: {}, size: {}", reqDTO.getName(), reqDTO.getEffectiveContentLength());

        // 1. 计算文件哈希
        String sha256Hash = calculateSHA256Hash(reqDTO.getEffectiveInputStream());

        // 2. 检查是否存在重复文件
        FileIndexDO existingFile = fileIndexMapper.selectByHashAndSize(sha256Hash, reqDTO.getEffectiveContentLength());
        
        if (existingFile != null) {
            // 文件已存在，增加引用计数
            fileIndexMapper.incrementReferenceCount(existingFile.getId());
            log.info("[uploadFile][文件去重成功] fileId: {}, referenceCount: {}", existingFile.getId(), existingFile.getReferenceCount() + 1);
            
            // 处理云盘保存
            if (reqDTO.getCloudDiskOptions() != null && reqDTO.getCloudDiskOptions().getSaveToCloudDisk()) {
                saveToCloudDisk(existingFile.getId(), reqDTO.getCloudDiskOptions());
            }
            
            // 处理文件处理选项
            String taskId = null;
            if (reqDTO.getProcessingOptions() != null && needsProcessing(reqDTO.getProcessingOptions())) {
                taskId = createDerivativeFilesAndTask(existingFile.getId(), reqDTO.getProcessingOptions());
            }
            
            return DeduplicationFileUploadRespDTO.builder()
                    .fileId(existingFile.getId())
                    .url(existingFile.getUrl())
                    .isDuplicate(true)
                    .originalFileId(existingFile.getId())
                    .processingTaskId(taskId)
                    .build();
        } else {
            // 新文件，执行实际上传
            byte[] content = cn.hutool.core.io.FileUtil.readBytes(reqDTO.getTempFile());
            String url = fileService.createFile(reqDTO.getName(), reqDTO.getPath(), content);

            // 保存文件索引
            FileIndexDO fileIndex = FileIndexDO.builder()
                    .configId(reqDTO.getConfigId())
                    .name(reqDTO.getName())
                    .path(reqDTO.getPath())
                    .url(url)
                    .type(reqDTO.getType())
                    .size(reqDTO.getEffectiveContentLength())
                    .sha256Hash(sha256Hash)
                    .referenceCount(1)
                    .build();

            fileIndexMapper.insert(fileIndex);
            log.info("[uploadFile][新文件上传成功] fileId: {}, url: {}", fileIndex.getId(), url);

            // 立即创建衍生版本的文件索引记录（占位）
            String taskId = null;
            if (reqDTO.getProcessingOptions() != null && needsProcessing(reqDTO.getProcessingOptions())) {
                taskId = createDerivativeFilesAndTask(fileIndex.getId(), reqDTO.getProcessingOptions());
            }

            // 处理云盘保存
            Long cloudDiskId = null;
            if (reqDTO.getCloudDiskOptions() != null && reqDTO.getCloudDiskOptions().getSaveToCloudDisk()) {
                cloudDiskId = saveToCloudDisk(fileIndex.getId(), reqDTO.getCloudDiskOptions());
            }

            return DeduplicationFileUploadRespDTO.builder()
                    .fileId(fileIndex.getId())
                    .url(url)
                    .name(fileIndex.getName())
                    .size(fileIndex.getSize())
                    .type(fileIndex.getType())
                    .isDuplicate(false)
                    .originalFileId(fileIndex.getId())
                    .processingTaskId(taskId)
                    .cloudDiskId(cloudDiskId)
                    .createTime(fileIndex.getCreateTime())
                    .build();
        }
    }

    @Override
    public DeduplicationFileRespDTO getFileInfo(Long fileId) {
        FileIndexDO fileIndex = fileIndexMapper.selectById(fileId);
        if (fileIndex == null) {
            throw new ServiceException(404, "文件不存在");
        }
        
        return convertToFileRespDTO(fileIndex);
    }

    @Override
    public Map<Long, DeduplicationFileRespDTO> getFileInfoBatch(List<Long> fileIds) {
        List<FileIndexDO> fileIndexes = fileIndexMapper.selectBatchIds(fileIds);
        return fileIndexes.stream()
                .collect(Collectors.toMap(
                        FileIndexDO::getId,
                        this::convertToFileRespDTO
                ));
    }

    @Override
    public String batchProcessFiles(List<Long> fileIds, List<String> processingTypes, Map<String, Object> processingParams) {
        String taskId = IdUtil.simpleUUID();
        
        // 创建批量处理任务
        FileProcessingTaskDO task = FileProcessingTaskDO.builder()
                .taskId(taskId)
                .originalFileId(fileIds.get(0)) // 批量任务使用第一个文件ID作为主文件
                .processingTypes(String.join(",", processingTypes))
                .processingParams(processingParams.toString())
                .status(FileProcessingTaskDO.Status.PENDING)
                .progress(0)
                .build();
        
        fileProcessingTaskMapper.insert(task);
        
        // TODO: 异步处理文件
        
        return taskId;
    }

    @Override
    public Map<String, Object> getProcessingTaskStatus(String taskId) {
        FileProcessingTaskDO task = fileProcessingTaskMapper.selectByTaskId(taskId);
        if (task == null) {
            throw new ServiceException(404, "任务不存在");
        }
        
        Map<String, Object> result = new HashMap<>();
        result.put("taskId", task.getTaskId());
        result.put("status", task.getStatus());
        result.put("progress", task.getProgress());
        result.put("startTime", task.getStartTime());
        result.put("endTime", task.getEndTime());
        result.put("errorMessage", task.getErrorMessage());
        
        return result;
    }

    @Override
    public List<Map<String, Object>> getFileDerivatives(Long fileId) {
        List<FileDerivativeDO> derivatives = fileDerivativeMapper.selectByOriginalFileId(fileId);
        return derivatives.stream()
                .map(this::convertToDerivativeMap)
                .collect(Collectors.toList());
    }

    @Override
    public DeduplicationStatsRespDTO getDeduplicationStats() {
        Long totalFiles = fileIndexMapper.selectTotalFileCount();
        Long totalSize = fileIndexMapper.selectTotalStorageSize();
        Long deduplicatedFiles = fileIndexMapper.selectDeduplicatedFileCount();
        Long savedSize = fileIndexMapper.selectSavedStorageSize();
        
        DeduplicationStatsRespDTO result = new DeduplicationStatsRespDTO();
        result.setTotalFileCount(totalFiles);
        result.setTotalStorageSize(totalSize);
        result.setDeduplicatedFileCount(deduplicatedFiles);
        result.setSavedStorageSize(savedSize);
        return result;
    }

    @Override
    @Transactional
    public Boolean deleteFile(Long fileId) {
        FileIndexDO fileIndex = fileIndexMapper.selectById(fileId);
        if (fileIndex == null) {
            return false;
        }
        
        // 减少引用计数
        int affected = fileIndexMapper.decrementReferenceCount(fileId);
        if (affected > 0) {
            // 如果引用计数变为0，可以考虑删除实际文件（这里暂时保留）
            FileIndexDO updated = fileIndexMapper.selectById(fileId);
            if (updated.getReferenceCount() <= 0) {
                log.info("[deleteFile][文件引用计数为0] fileId: {}, 可以清理", fileId);
                // TODO: 异步清理实际文件
            }
        }
        
        return true;
    }

    @Override
    public Integer cleanupZeroRefFiles() {
        List<FileIndexDO> zeroRefFiles = fileIndexMapper.selectZeroRefCountFiles(100);
        int cleanedCount = 0;
        
        for (FileIndexDO fileIndex : zeroRefFiles) {
            try {
                // 删除实际文件
                fileService.deleteFile(fileIndex.getId());
                
                // 删除衍生版本
                fileDerivativeMapper.deleteByOriginalFileId(fileIndex.getId());
                
                // 删除文件索引
                fileIndexMapper.deleteById(fileIndex.getId());
                
                cleanedCount++;
                log.info("[cleanupZeroRefFiles][清理文件成功] fileId: {}", fileIndex.getId());
            } catch (Exception e) {
                log.error("[cleanupZeroRefFiles][清理文件失败] fileId: {}", fileIndex.getId(), e);
            }
        }
        
        return cleanedCount;
    }

    // 私有辅助方法
    
    private FileHashCheckRespDTO.DerivativeVersionInfo convertToDerivativeVersionInfo(FileDerivativeDO derivative) {
        FileIndexDO derivativeFile = fileIndexMapper.selectById(derivative.getFileId());
        return FileHashCheckRespDTO.DerivativeVersionInfo.builder()
                .derivativeId(derivative.getId())
                .derivativeType(derivative.getDerivativeType())
                .fileId(derivative.getFileId())
                .quality(derivative.getQuality())
                .resolution(derivative.getResolution())
                .fileSize(derivativeFile != null ? derivativeFile.getSize() : null)
                .fileUrl(derivativeFile != null ? derivativeFile.getUrl() : null)
                .status(derivative.getStatus())
                .build();
    }
    
    private FileHashCheckRespDTO.CloudDiskFileInfo convertToCloudDiskFileInfo(UserCloudDiskDO cloudDisk) {
        return FileHashCheckRespDTO.CloudDiskFileInfo.builder()
                .cloudDiskId(cloudDisk.getId())
                .fileName(cloudDisk.getFileName())
                .filePath(cloudDisk.getFilePath())
                .fileSize(cloudDisk.getFileSize())
                .isFavorite(cloudDisk.getIsFavorite())
                .createTime(cloudDisk.getCreateTime())
                .build();
    }
    
    private DeduplicationFileRespDTO convertToFileRespDTO(FileIndexDO fileIndex) {
        DeduplicationFileRespDTO result = new DeduplicationFileRespDTO();
        result.setId(fileIndex.getId());
        result.setName(fileIndex.getName());
        result.setPath(fileIndex.getPath());
        result.setUrl(fileIndex.getUrl());
        result.setType(fileIndex.getType());
        result.setSize(fileIndex.getSize());
        result.setRefCount(fileIndex.getReferenceCount());
        result.setCreateTime(fileIndex.getCreateTime());
        return result;
    }
    
    private Map<String, Object> convertToDerivativeMap(FileDerivativeDO derivative) {
        Map<String, Object> map = new HashMap<>();
        map.put("id", derivative.getId());
        map.put("derivativeType", derivative.getDerivativeType());
        map.put("quality", derivative.getQuality());
        map.put("resolution", derivative.getResolution());
        map.put("format", derivative.getFormat());
        map.put("status", derivative.getStatus());
        map.put("createTime", derivative.getCreateTime());
        return map;
    }
    
    private boolean needsProcessing(DeduplicationFileUploadReqDTO.FileProcessingOptions options) {
        return (options.getEnableCompress() != null && options.getEnableCompress()) ||
               (options.getEnableWatermark() != null && options.getEnableWatermark()) ||
               (options.getEnableHls() != null && options.getEnableHls());
    }
    
    /**
     * 立即创建衍生版本文件索引并创建处理任务
     */
    private String createDerivativeFilesAndTask(Long originalFileId, DeduplicationFileUploadReqDTO.FileProcessingOptions options) {
        String taskId = IdUtil.simpleUUID();

        List<String> processingTypes = new ArrayList<>();
        List<Long> derivativeFileIds = new ArrayList<>();

        FileIndexDO originalFile = fileIndexMapper.selectById(originalFileId);

        // 立即创建衍生版本的文件索引记录（占位）
        if (options.getEnableCompress() != null && options.getEnableCompress()) {
            processingTypes.add(FileProcessingTaskDO.ProcessingType.COMPRESS);
            Long derivativeFileId = createDerivativeFileIndex(originalFile, "COMPRESSED");
            derivativeFileIds.add(derivativeFileId);
        }

        if (options.getEnableWatermark() != null && options.getEnableWatermark()) {
            processingTypes.add(FileProcessingTaskDO.ProcessingType.WATERMARK);
            Long derivativeFileId = createDerivativeFileIndex(originalFile, "WATERMARKED");
            derivativeFileIds.add(derivativeFileId);
        }

        if (options.getEnableHls() != null && options.getEnableHls()) {
            processingTypes.add(FileProcessingTaskDO.ProcessingType.HLS);
            // HLS可能生成多个质量版本
            if (options.getHlsConfig() != null) {
                if (options.getHlsConfig().getEnableHigh()) {
                    Long derivativeFileId = createDerivativeFileIndex(originalFile, "HLS_HIGH");
                    derivativeFileIds.add(derivativeFileId);
                }
                if (options.getHlsConfig().getEnableMedium()) {
                    Long derivativeFileId = createDerivativeFileIndex(originalFile, "HLS_MEDIUM");
                    derivativeFileIds.add(derivativeFileId);
                }
                if (options.getHlsConfig().getEnableLow()) {
                    Long derivativeFileId = createDerivativeFileIndex(originalFile, "HLS_LOW");
                    derivativeFileIds.add(derivativeFileId);
                }
            }
        }

        // 创建处理任务
        FileProcessingTaskDO task = FileProcessingTaskDO.builder()
                .taskId(taskId)
                .originalFileId(originalFileId)
                .processingTypes(String.join(",", processingTypes))
                .processingParams(options.toString())
                .status(FileProcessingTaskDO.Status.PENDING)
                .progress(0)
                // .resultDerivativeIds(derivativeFileIds.toString()) // 字段不存在，暂时注释
                .build();

        fileProcessingTaskMapper.insert(task);

        // TODO: 异步执行实际的文件处理

        return taskId;
    }

    /**
     * 创建衍生版本的文件索引记录（占位）
     */
    private Long createDerivativeFileIndex(FileIndexDO originalFile, String derivativeType) {
        // 生成衍生文件的临时URL（实际文件还未生成）
        String derivativeUrl = originalFile.getUrl().replace(originalFile.getName(),
                derivativeType.toLowerCase() + "_" + originalFile.getName());

        // 创建衍生文件的索引记录
        FileIndexDO derivativeFile = FileIndexDO.builder()
                .configId(originalFile.getConfigId())
                .name(derivativeType.toLowerCase() + "_" + originalFile.getName())
                .path(originalFile.getPath())
                .url(derivativeUrl)
                .type(originalFile.getType())
                .size(0L) // 占位，实际大小后续更新
                .sha256Hash("") // 占位，实际哈希后续更新
                .referenceCount(1)
                .build();

        fileIndexMapper.insert(derivativeFile);

        // 创建衍生版本关系记录
        FileDerivativeDO derivative = FileDerivativeDO.builder()
                .originalFileId(originalFile.getId())
                .fileId(derivativeFile.getId())
                .derivativeType(derivativeType)
                .status(FileDerivativeDO.Status.PROCESSING)
                .build();

        fileDerivativeMapper.insert(derivative);

        return derivativeFile.getId();
    }

    private Long saveToCloudDisk(Long fileId, DeduplicationFileUploadReqDTO.CloudDiskOptions options) {
        // TODO: 实现云盘保存逻辑
        log.info("[saveToCloudDisk][保存到云盘] fileId: {}, cloudFileName: {}", fileId, options.getCloudFileName());
        return 1L; // 临时返回值
    }

    /**
     * 计算文件SHA256哈希值
     */
    private String calculateSHA256Hash(java.io.InputStream inputStream) {
        try {
            return cn.hutool.crypto.digest.DigestUtil.sha256Hex(inputStream);
        } catch (Exception e) {
            log.error("[calculateSHA256Hash][计算文件哈希失败]", e);
            throw new ServiceException(500, "计算文件哈希失败");
        }
    }

}
