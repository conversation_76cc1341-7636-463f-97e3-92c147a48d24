package cn.iocoder.yudao.module.infra.service.file.dto;

import lombok.Data;

/**
 * 文件压缩配置 DTO
 *
 * <AUTHOR>
 */
@Data
public class FileCompressConfigDTO {

    // ========== 视频压缩配置 ==========

    /**
     * 视频码率（kbps）
     * 默认值：1000
     */
    private Integer videoBitrate = 1000;

    /**
     * 音频码率（kbps）
     * 默认值：128
     */
    private Integer audioBitrate = 128;

    /**
     * 视频分辨率缩放
     * 格式：宽x高，如 "1280x720"
     * 默认值：null（保持原分辨率）
     */
    private String videoScale;

    /**
     * 视频编码器
     * 默认值：libx264
     */
    private String videoCodec = "libx264";

    /**
     * 音频编码器
     * 默认值：aac
     */
    private String audioCodec = "aac";

    /**
     * 视频质量参数（CRF值）
     * 范围：0-51，数值越小质量越高
     * 默认值：23
     */
    private Integer videoQuality = 23;

    // ========== 图片压缩配置 ==========

    /**
     * 图片质量
     * 范围：1-100，数值越高质量越好
     * 默认值：80
     */
    private Integer imageQuality = 80;

    /**
     * 图片最大宽度
     * 默认值：2048
     */
    private Integer maxWidth = 2048;

    /**
     * 图片最大高度
     * 默认值：2048
     */
    private Integer maxHeight = 2048;

    /**
     * 是否保持宽高比
     * 默认值：true
     */
    private Boolean keepAspectRatio = true;

    // ========== 通用配置 ==========

    /**
     * 是否启用多线程处理
     * 默认值：true
     */
    private Boolean enableMultiThread = true;

    /**
     * 处理线程数
     * 默认值：0（自动检测）
     */
    private Integer threadCount = 0;

    /**
     * 处理超时时间（秒）
     * 默认值：300（5分钟）
     */
    private Integer timeoutSeconds = 300;

    /**
     * 是否删除原文件
     * 默认值：false
     */
    private Boolean deleteOriginal = false;

}
