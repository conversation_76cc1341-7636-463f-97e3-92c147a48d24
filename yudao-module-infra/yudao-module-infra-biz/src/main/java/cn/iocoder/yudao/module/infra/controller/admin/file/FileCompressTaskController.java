package cn.iocoder.yudao.module.infra.controller.admin.file;

import cn.iocoder.yudao.framework.common.pojo.CommonResult;
import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.common.util.object.BeanUtils;
import cn.iocoder.yudao.module.infra.controller.admin.file.vo.compress.*;
import cn.iocoder.yudao.module.infra.dal.dataobject.file.FileCompressTaskDO;
import cn.iocoder.yudao.module.infra.enums.file.FileCompressTaskStatusEnum;
import cn.iocoder.yudao.module.infra.service.file.FileCompressTaskManager;
import cn.iocoder.yudao.module.infra.service.file.FileService;
import cn.iocoder.yudao.module.infra.service.file.AsyncFileCompressProcessor;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.Map;

import static cn.iocoder.yudao.framework.common.pojo.CommonResult.success;

/**
 * 管理后台 - 文件压缩任务管理
 *
 * <AUTHOR>
 */
@Tag(name = "管理后台 - 文件压缩任务管理")
@RestController
@RequestMapping("/infra/file-compress-task")
@Validated
@Slf4j
public class FileCompressTaskController {

    @Resource
    private FileCompressTaskManager taskManager;

    @Resource
    private FileService fileService;

    @Resource
    private AsyncFileCompressProcessor asyncFileCompressProcessor;

    @PostMapping("/create")
    @Operation(summary = "创建文件压缩任务")
    @PreAuthorize("@ss.hasPermission('infra:file:create')")
    public CommonResult<Long> createCompressTask(@Valid @RequestBody FileCompressTaskCreateReqVO createReqVO) {
        Long taskId = fileService.createCompressTask(
            createReqVO.getFileId(),
            createReqVO.getCompressType(),
            createReqVO.getPriority(),
            createReqVO.getConfigParams()
        );
        return success(taskId);
    }

    @GetMapping("/page")
    @Operation(summary = "获得文件压缩任务分页")
    @PreAuthorize("@ss.hasPermission('infra:file:query')")
    public CommonResult<PageResult<FileCompressTaskRespVO>> getCompressTaskPage(@Valid FileCompressTaskPageReqVO pageReqVO) {
        PageResult<FileCompressTaskDO> pageResult = taskManager.getTaskPage(pageReqVO);
        return success(BeanUtils.toBean(pageResult, FileCompressTaskRespVO.class, this::convertToRespVO));
    }

    @GetMapping("/get/{id}")
    @Operation(summary = "获得文件压缩任务")
    @Parameter(name = "id", description = "任务ID", required = true)
    @PreAuthorize("@ss.hasPermission('infra:file:query')")
    public CommonResult<FileCompressTaskRespVO> getCompressTask(@PathVariable("id") Long id) {
        FileCompressTaskDO task = taskManager.getTask(id);
        return success(convertToRespVO(task));
    }

    @PostMapping("/retry/{id}")
    @Operation(summary = "重试文件压缩任务")
    @Parameter(name = "id", description = "任务ID", required = true)
    @PreAuthorize("@ss.hasPermission('infra:file:update')")
    public CommonResult<Boolean> retryCompressTask(@PathVariable("id") Long id) {
        boolean success = taskManager.retryTask(id);
        if (success) {
            // 异步执行重试任务
            asyncFileCompressProcessor.processCompressTask(id);
        }
        return success(success);
    }

    @PostMapping("/cancel/{id}")
    @Operation(summary = "取消文件压缩任务")
    @Parameter(name = "id", description = "任务ID", required = true)
    @PreAuthorize("@ss.hasPermission('infra:file:update')")
    public CommonResult<Boolean> cancelCompressTask(@PathVariable("id") Long id) {
        boolean success = taskManager.cancelTask(id);
        return success(success);
    }

    @GetMapping("/stats")
    @Operation(summary = "获取文件压缩统计信息")
    @PreAuthorize("@ss.hasPermission('infra:file:query')")
    public CommonResult<FileCompressStatsRespVO> getCompressStats() {
        // 获取状态统计
        Map<Integer, Long> statusCount = taskManager.getStatusStatistics();
        
        // 计算各状态数量
        Long pendingCount = statusCount.getOrDefault(FileCompressTaskStatusEnum.PENDING.getStatus(), 0L);
        Long processingCount = statusCount.getOrDefault(FileCompressTaskStatusEnum.PROCESSING.getStatus(), 0L);
        Long successCount = statusCount.getOrDefault(FileCompressTaskStatusEnum.SUCCESS.getStatus(), 0L);
        Long failedCount = statusCount.getOrDefault(FileCompressTaskStatusEnum.FAILED.getStatus(), 0L);
        Long cancelledCount = statusCount.getOrDefault(FileCompressTaskStatusEnum.CANCELLED.getStatus(), 0L);
        Long totalCount = statusCount.values().stream().mapToLong(Long::longValue).sum();
        
        // 计算成功率
        BigDecimal successRate = BigDecimal.ZERO;
        if (totalCount > 0) {
            successRate = BigDecimal.valueOf(successCount)
                .divide(BigDecimal.valueOf(totalCount), 4, RoundingMode.HALF_UP)
                .multiply(BigDecimal.valueOf(100));
        }
        
        // 获取其他统计信息
        Long savedStorageSize = taskManager.getSavedStorageSize();
        BigDecimal avgCompressRatio = taskManager.getAvgCompressRatio();
        
        // 构建响应对象
        FileCompressStatsRespVO stats = new FileCompressStatsRespVO();
        stats.setStatusCount(statusCount);
        stats.setPendingCount(pendingCount);
        stats.setProcessingCount(processingCount);
        stats.setSuccessCount(successCount);
        stats.setFailedCount(failedCount);
        stats.setCancelledCount(cancelledCount);
        stats.setTotalCount(totalCount);
        stats.setSuccessRate(successRate.setScale(2, RoundingMode.HALF_UP));
        stats.setSavedStorageSize(savedStorageSize != null ? savedStorageSize : 0L);
        stats.setAvgCompressRatio(avgCompressRatio != null ? avgCompressRatio.setScale(2, RoundingMode.HALF_UP) : BigDecimal.ZERO);
        
        return success(stats);
    }

    @PostMapping("/auto-create/{fileId}")
    @Operation(summary = "自动创建文件压缩任务")
    @Parameter(name = "fileId", description = "文件ID", required = true)
    @PreAuthorize("@ss.hasPermission('infra:file:create')")
    public CommonResult<Integer> autoCreateCompressTasks(@PathVariable("fileId") Long fileId) {
        int createdCount = fileService.autoCreateCompressTasks(fileId);
        return success(createdCount);
    }

    // ==================== 私有方法 ====================

    /**
     * 转换为响应VO
     *
     * @param task 任务DO
     * @return 响应VO
     */
    private FileCompressTaskRespVO convertToRespVO(FileCompressTaskDO task) {
        if (task == null) {
            return null;
        }
        
        FileCompressTaskRespVO respVO = BeanUtils.toBean(task, FileCompressTaskRespVO.class);
        
        // 设置状态名称
        try {
            FileCompressTaskStatusEnum statusEnum = FileCompressTaskStatusEnum.valueOf(task.getStatus());
            respVO.setStatusName(statusEnum.getName());
        } catch (Exception e) {
            respVO.setStatusName("未知状态");
        }
        
        // 设置处理耗时
        respVO.setProcessingDuration(task.getProcessingDuration());
        
        // 设置是否可以重试和是否已完成
        respVO.setCanRetry(task.canRetry());
        respVO.setIsCompleted(task.isCompleted());
        
        return respVO;
    }

}
