package cn.iocoder.yudao.module.infra.dal.mysql.file;

import cn.iocoder.yudao.framework.mybatis.core.mapper.BaseMapperX;
import cn.iocoder.yudao.framework.mybatis.core.query.LambdaQueryWrapperX;
import cn.iocoder.yudao.module.infra.dal.dataobject.file.FileDerivativeDO;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
 * 文件衍生版本 Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface FileDerivativeMapper extends BaseMapperX<FileDerivativeDO> {

    /**
     * 根据原始文件ID查询衍生版本列表
     *
     * @param originalFileId 原始文件ID
     * @return 衍生版本列表
     */
    default List<FileDerivativeDO> selectByOriginalFileId(Long originalFileId) {
        return selectList(FileDerivativeDO::getOriginalFileId, originalFileId);
    }

    /**
     * 根据原始文件ID和衍生类型查询衍生版本
     *
     * @param originalFileId 原始文件ID
     * @param derivativeType 衍生类型
     * @return 衍生版本列表
     */
    default List<FileDerivativeDO> selectByOriginalFileIdAndType(Long originalFileId, String derivativeType) {
        return selectList(new LambdaQueryWrapperX<FileDerivativeDO>()
                .eq(FileDerivativeDO::getOriginalFileId, originalFileId)
                .eq(FileDerivativeDO::getDerivativeType, derivativeType));
    }

    /**
     * 根据原始文件ID、衍生类型和质量查询衍生版本
     *
     * @param originalFileId 原始文件ID
     * @param derivativeType 衍生类型
     * @param quality 质量等级
     * @return 衍生版本
     */
    default FileDerivativeDO selectByOriginalFileIdAndTypeAndQuality(Long originalFileId, String derivativeType, String quality) {
        return selectOne(new LambdaQueryWrapperX<FileDerivativeDO>()
                .eq(FileDerivativeDO::getOriginalFileId, originalFileId)
                .eq(FileDerivativeDO::getDerivativeType, derivativeType)
                .eq(FileDerivativeDO::getQuality, quality));
    }

    /**
     * 根据衍生文件ID查询衍生版本
     *
     * @param derivativeFileId 衍生文件ID
     * @return 衍生版本
     */
    default FileDerivativeDO selectByDerivativeFileId(Long derivativeFileId) {
        return selectOne(FileDerivativeDO::getDerivativeFileId, derivativeFileId);
    }

    /**
     * 根据状态查询衍生版本列表
     *
     * @param status 状态
     * @param limit 限制数量
     * @return 衍生版本列表
     */
    default List<FileDerivativeDO> selectByStatus(Integer status, int limit) {
        return selectList(new LambdaQueryWrapperX<FileDerivativeDO>()
                .eq(FileDerivativeDO::getStatus, status)
                .orderByAsc(FileDerivativeDO::getCreateTime)
                .last("LIMIT " + limit));
    }

    /**
     * 根据衍生类型查询衍生版本列表
     *
     * @param derivativeType 衍生类型
     * @param limit 限制数量
     * @return 衍生版本列表
     */
    default List<FileDerivativeDO> selectByDerivativeType(String derivativeType, int limit) {
        return selectList(new LambdaQueryWrapperX<FileDerivativeDO>()
                .eq(FileDerivativeDO::getDerivativeType, derivativeType)
                .orderByDesc(FileDerivativeDO::getCreateTime)
                .last("LIMIT " + limit));
    }

    /**
     * 删除原始文件的所有衍生版本
     *
     * @param originalFileId 原始文件ID
     * @return 影响行数
     */
    default int deleteByOriginalFileId(Long originalFileId) {
        return delete(new LambdaQueryWrapperX<FileDerivativeDO>()
                .eq(FileDerivativeDO::getOriginalFileId, originalFileId));
    }

}
