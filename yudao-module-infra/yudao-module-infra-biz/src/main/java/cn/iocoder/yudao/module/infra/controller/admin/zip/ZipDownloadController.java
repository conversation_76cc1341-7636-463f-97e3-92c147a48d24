package cn.iocoder.yudao.module.infra.controller.admin.zip;

import cn.iocoder.yudao.framework.common.pojo.CommonResult;
import cn.iocoder.yudao.framework.operatelog.core.annotations.OperateLog;
import cn.iocoder.yudao.module.infra.api.zip.ZipDownloadApi;
import cn.iocoder.yudao.module.infra.api.zip.dto.ZipDownloadReqDTO;
import cn.iocoder.yudao.module.infra.api.zip.dto.ZipDownloadRespDTO;
import cn.iocoder.yudao.module.infra.service.zip.ZipDownloadService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;

import static cn.iocoder.yudao.framework.common.pojo.CommonResult.success;
import static cn.iocoder.yudao.framework.operatelog.core.enums.OperateTypeEnum.EXPORT;

/**
 * ZIP下载控制器
 *
 * <AUTHOR>
 */
@Tag(name = "管理后台 - ZIP下载")
@RestController
@RequestMapping("/infra/zip")
@Validated
@Slf4j
public class ZipDownloadController implements ZipDownloadApi {

    @Resource
    private ZipDownloadService zipDownloadService;

    @PostMapping("/download")
    @Operation(summary = "下载文件并打包成ZIP")
    @PreAuthorize("@ss.hasPermission('infra:zip:download')")
    @OperateLog(type = EXPORT)
    @Override
    public CommonResult<ZipDownloadRespDTO> downloadZip(@Valid @RequestBody ZipDownloadReqDTO reqDTO) {
        ZipDownloadRespDTO result = zipDownloadService.downloadZip(reqDTO);
        return success(result);
    }

    @PostMapping("/download/stream")
    @Operation(summary = "下载文件并直接返回ZIP流")
    @PreAuthorize("@ss.hasPermission('infra:zip:download')")
    @OperateLog(type = EXPORT)
    @Override
    public void downloadZipStream(@Valid @RequestBody ZipDownloadReqDTO reqDTO, HttpServletResponse response) {
        zipDownloadService.downloadZipStream(reqDTO, response);
    }

    @PostMapping("/download/async")
    @Operation(summary = "异步下载文件并打包成ZIP")
    @PreAuthorize("@ss.hasPermission('infra:zip:download')")
    @OperateLog(type = EXPORT)
    @Override
    public CommonResult<String> downloadZipAsync(@Valid @RequestBody ZipDownloadReqDTO reqDTO) {
        String taskId = zipDownloadService.downloadZipAsync(reqDTO);
        return success(taskId);
    }

    @GetMapping("/task/{taskId}")
    @Operation(summary = "查询异步下载任务状态")
    @Parameter(name = "taskId", description = "任务ID", required = true)
    @PreAuthorize("@ss.hasPermission('infra:zip:download')")
    @Override
    public CommonResult<ZipDownloadRespDTO> getAsyncTaskStatus(@PathVariable("taskId") String taskId) {
        ZipDownloadRespDTO result = zipDownloadService.getAsyncTaskStatus(taskId);
        return success(result);
    }

}
