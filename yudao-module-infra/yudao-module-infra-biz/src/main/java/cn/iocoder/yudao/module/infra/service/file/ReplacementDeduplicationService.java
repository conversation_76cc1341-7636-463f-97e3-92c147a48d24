package cn.iocoder.yudao.module.infra.service.file;

import cn.iocoder.yudao.module.infra.dal.dataobject.file.FileIndexDO;
import cn.iocoder.yudao.module.infra.dal.mysql.file.FileIndexMapper;
import cn.iocoder.yudao.module.infra.framework.file.core.utils.FileHashUtils;
import cn.iocoder.yudao.module.infra.service.deduplication.bo.DeduplicationRespBO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * 替换式智能去重服务
 * 支持原文件和压缩文件的智能去重，压缩后直接替换原文件
 *
 * <AUTHOR>
 */
@Service
@Slf4j
public class ReplacementDeduplicationService {

    @Resource
    private FileIndexMapper fileIndexMapper;

    /**
     * 智能去重检查
     * 检查上传的文件是否已存在（包括原文件和压缩文件）
     *
     * @param content 文件内容
     * @return 去重结果
     */
    public DeduplicationRespBO checkDeduplication(byte[] content) {
        // 计算文件哈希
        FileHashUtils.FileHashResult hashResult = FileHashUtils.calculateHashAndSize(content);
        String hash = hashResult.getHash();
        Long size = hashResult.getSize();

        log.debug("[checkDeduplication][开始去重检查] hash: {}, size: {}", hash, size);

        // 使用双哈希查询：同时检查原文件哈希和当前文件哈希
        FileIndexDO existingIndex = fileIndexMapper.selectByHashAndSize(hash, size);
        
        if (existingIndex != null) {
            // 判断匹配的是原文件还是压缩文件
            boolean isOriginalMatch = hash.equals(existingIndex.getOriginalHash()) && 
                                    size.equals(existingIndex.getOriginalSize());
            boolean isCompressedMatch = hash.equals(existingIndex.getCurrentHash()) && 
                                      size.equals(existingIndex.getCurrentSize()) &&
                                      existingIndex.isCompressed();

            if (isOriginalMatch && !existingIndex.isCompressed()) {
                // 匹配到原文件，且未被压缩
                log.info("[checkDeduplication][找到相同原文件] indexId: {}, path: {}", 
                    existingIndex.getId(), existingIndex.getStoragePath());
                return DeduplicationRespBO.foundOriginal(existingIndex);
            } else if (isCompressedMatch) {
                // 匹配到压缩文件
                log.info("[checkDeduplication][找到相同压缩文件] indexId: {}, path: {}", 
                    existingIndex.getId(), existingIndex.getStoragePath());
                return DeduplicationRespBO.foundCompressed(existingIndex);
            } else if (isOriginalMatch && existingIndex.isCompressed()) {
                // 匹配到原文件，但已被压缩替换，返回压缩版本
                log.info("[checkDeduplication][找到原文件但已压缩] indexId: {}, path: {}", 
                    existingIndex.getId(), existingIndex.getStoragePath());
                return DeduplicationRespBO.foundCompressed(existingIndex);
            }
        }

        log.debug("[checkDeduplication][未找到重复文件] hash: {}", hash);
        return DeduplicationRespBO.notFound();
    }

}
