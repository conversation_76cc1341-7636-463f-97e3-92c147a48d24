package cn.iocoder.yudao.module.infra.service.file;

import cn.iocoder.yudao.module.infra.dal.dataobject.file.FileIndexDO;
import cn.iocoder.yudao.module.infra.dal.mysql.file.FileIndexMapper;
import cn.iocoder.yudao.module.infra.framework.file.core.utils.FileHashUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * 替换式智能去重服务
 * 支持原文件和压缩文件的智能去重，压缩后直接替换原文件
 *
 * <AUTHOR>
 */
@Service
@Slf4j
public class ReplacementDeduplicationService {

    @Resource
    private FileIndexMapper fileIndexMapper;

    /**
     * 智能去重检查
     * 检查上传的文件是否已存在（包括原文件和压缩文件）
     *
     * @param content 文件内容
     * @return 去重结果
     */
    public DeduplicationResult checkDeduplication(byte[] content) {
        // 计算文件哈希
        FileHashUtils.FileHashResult hashResult = FileHashUtils.calculateHashAndSize(content);
        String hash = hashResult.getHash();
        Long size = hashResult.getSize();

        log.debug("[checkDeduplication][开始去重检查] hash: {}, size: {}", hash, size);

        // 使用双哈希查询：同时检查原文件哈希和当前文件哈希
        FileIndexDO existingIndex = fileIndexMapper.selectByHashAndSize(hash, size);
        
        if (existingIndex != null) {
            // 判断匹配的是原文件还是压缩文件
            boolean isOriginalMatch = hash.equals(existingIndex.getOriginalHash()) && 
                                    size.equals(existingIndex.getOriginalSize());
            boolean isCompressedMatch = hash.equals(existingIndex.getCurrentHash()) && 
                                      size.equals(existingIndex.getCurrentSize()) &&
                                      existingIndex.isCompressed();

            if (isOriginalMatch && !existingIndex.isCompressed()) {
                // 匹配到原文件，且未被压缩
                log.info("[checkDeduplication][找到相同原文件] indexId: {}, path: {}", 
                    existingIndex.getId(), existingIndex.getStoragePath());
                return DeduplicationResult.foundOriginal(existingIndex);
            } else if (isCompressedMatch) {
                // 匹配到压缩文件
                log.info("[checkDeduplication][找到相同压缩文件] indexId: {}, path: {}", 
                    existingIndex.getId(), existingIndex.getStoragePath());
                return DeduplicationResult.foundCompressed(existingIndex);
            } else if (isOriginalMatch && existingIndex.isCompressed()) {
                // 匹配到原文件，但已被压缩替换，返回压缩版本
                log.info("[checkDeduplication][找到原文件但已压缩] indexId: {}, path: {}", 
                    existingIndex.getId(), existingIndex.getStoragePath());
                return DeduplicationResult.foundCompressed(existingIndex);
            }
        }

        log.debug("[checkDeduplication][未找到重复文件] hash: {}", hash);
        return DeduplicationResult.notFound();
    }

    /**
     * 去重结果
     */
    public static class DeduplicationResult {
        private final boolean found;
        private final boolean isCompressed;
        private final FileIndexDO fileIndex;

        private DeduplicationResult(boolean found, boolean isCompressed, FileIndexDO fileIndex) {
            this.found = found;
            this.isCompressed = isCompressed;
            this.fileIndex = fileIndex;
        }

        public static DeduplicationResult notFound() {
            return new DeduplicationResult(false, false, null);
        }

        public static DeduplicationResult foundOriginal(FileIndexDO fileIndex) {
            return new DeduplicationResult(true, false, fileIndex);
        }

        public static DeduplicationResult foundCompressed(FileIndexDO fileIndex) {
            return new DeduplicationResult(true, true, fileIndex);
        }

        // Getters
        public boolean isFound() { return found; }
        public boolean isCompressed() { return isCompressed; }
        public FileIndexDO getFileIndex() { return fileIndex; }

        /**
         * 获取文件路径
         */
        public String getFilePath() {
            return found ? fileIndex.getStoragePath() : null;
        }

        /**
         * 获取文件大小
         */
        public Long getFileSize() {
            return found ? fileIndex.getCurrentSize() : null;
        }

        /**
         * 获取压缩节省的空间
         */
        public Long getSavedSpace() {
            return found && isCompressed ? fileIndex.getSavedSpace() : 0L;
        }
    }

}
