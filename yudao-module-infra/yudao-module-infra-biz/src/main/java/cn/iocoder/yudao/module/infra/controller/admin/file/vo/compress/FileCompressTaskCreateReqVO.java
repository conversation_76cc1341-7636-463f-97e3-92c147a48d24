package cn.iocoder.yudao.module.infra.controller.admin.file.vo.compress;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotNull;
import javax.validation.constraints.Max;
import javax.validation.constraints.Min;

/**
 * 管理后台 - 文件压缩任务创建 Request VO
 *
 * <AUTHOR>
 */
@Schema(description = "管理后台 - 文件压缩任务创建 Request VO")
@Data
public class FileCompressTaskCreateReqVO {

    @Schema(description = "文件ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "1024")
    @NotNull(message = "文件ID不能为空")
    private Long fileId;

    @Schema(description = "压缩类型", requiredMode = Schema.RequiredMode.REQUIRED, example = "mp4_compress")
    @NotNull(message = "压缩类型不能为空")
    private String compressType;

    @Schema(description = "任务优先级（1-10，数字越小优先级越高）", example = "5")
    @Min(value = 1, message = "优先级最小值为1")
    @Max(value = 10, message = "优先级最大值为10")
    private Integer priority = 5;

    @Schema(description = "压缩配置参数（JSON格式）", example = "{\"videoBitrate\":1000,\"audioBitrate\":128}")
    private String configParams;

}
