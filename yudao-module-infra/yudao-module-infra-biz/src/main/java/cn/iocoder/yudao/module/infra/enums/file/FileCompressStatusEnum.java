package cn.iocoder.yudao.module.infra.enums.file;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 文件压缩状态枚举
 *
 * <AUTHOR>
 */
@AllArgsConstructor
@Getter
public enum FileCompressStatusEnum {

    NOT_COMPRESSED(0, "未压缩"),
    COMPRESSING(1, "压缩中"),
    COMPRESSED(2, "已压缩"),
    COMPRESS_FAILED(3, "压缩失败");

    /**
     * 状态值
     */
    private final Integer status;
    
    /**
     * 状态名
     */
    private final String name;

    public static FileCompressStatusEnum valueOf(Integer status) {
        for (FileCompressStatusEnum statusEnum : values()) {
            if (statusEnum.getStatus().equals(status)) {
                return statusEnum;
            }
        }
        throw new IllegalArgumentException("未知的文件压缩状态: " + status);
    }

}
