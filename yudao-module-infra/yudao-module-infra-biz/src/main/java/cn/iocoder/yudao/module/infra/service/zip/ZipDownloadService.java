package cn.iocoder.yudao.module.infra.service.zip;

import cn.iocoder.yudao.module.infra.api.zip.dto.ZipDownloadReqDTO;
import cn.iocoder.yudao.module.infra.api.zip.dto.ZipDownloadRespDTO;

import javax.servlet.http.HttpServletResponse;
import java.io.File;

/**
 * ZIP下载服务接口
 *
 * <AUTHOR>
 */
public interface ZipDownloadService {

    /**
     * 下载文件并打包成ZIP
     *
     * @param reqDTO 下载请求
     * @return ZIP下载结果
     */
    ZipDownloadRespDTO downloadZip(ZipDownloadReqDTO reqDTO);

    /**
     * 下载文件并直接返回ZIP流
     *
     * @param reqDTO 下载请求
     * @param response HTTP响应
     */
    void downloadZipStream(ZipDownloadReqDTO reqDTO, HttpServletResponse response);

    /**
     * 异步下载文件并打包成ZIP
     *
     * @param reqDTO 下载请求
     * @return 任务ID
     */
    String downloadZipAsync(ZipDownloadReqDTO reqDTO);

    /**
     * 查询异步下载任务状态
     *
     * @param taskId 任务ID
     * @return 任务状态和结果
     */
    ZipDownloadRespDTO getAsyncTaskStatus(String taskId);

    /**
     * 创建临时ZIP文件
     *
     * @param reqDTO 下载请求
     * @return ZIP文件
     */
    File createZipFile(ZipDownloadReqDTO reqDTO);

    /**
     * 清理临时文件
     *
     * @param zipFile ZIP文件
     * @param tempFiles 临时文件列表
     */
    void cleanupTempFiles(File zipFile, java.util.List<File> tempFiles);

}
