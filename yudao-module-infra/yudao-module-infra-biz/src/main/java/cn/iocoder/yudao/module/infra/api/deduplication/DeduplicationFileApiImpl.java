package cn.iocoder.yudao.module.infra.api.deduplication;

import cn.iocoder.yudao.module.infra.api.deduplication.dto.DeduplicationFileUploadReqDTO;
import cn.iocoder.yudao.module.infra.api.deduplication.dto.DeduplicationFileRespDTO;
import cn.iocoder.yudao.module.infra.api.deduplication.dto.DeduplicationStatsRespDTO;
import cn.iocoder.yudao.module.infra.service.deduplication.DeduplicationFileService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.validation.annotation.Validated;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;

/**
 * 去重文件 API 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
@Slf4j
public class DeduplicationFileApiImpl implements DeduplicationFileApi {

    @Resource
    private DeduplicationFileService deduplicationFileService;

    @Override
    public DeduplicationFileRespDTO uploadFile(DeduplicationFileUploadReqDTO uploadReq) {
        return deduplicationFileService.uploadFile(uploadReq);
    }

    @Override
    public DeduplicationFileRespDTO getFile(Long fileId) {
        return deduplicationFileService.getFile(fileId);
    }

    @Override
    public Map<Long, DeduplicationFileRespDTO> getFiles(List<Long> fileIds) {
        return deduplicationFileService.getFiles(fileIds);
    }

    @Override
    public String getFileUrl(Long fileId) {
        return deduplicationFileService.getFileUrl(fileId);
    }

    @Override
    public Map<Long, String> getFileUrls(List<Long> fileIds) {
        return deduplicationFileService.getFileUrls(fileIds);
    }

    @Override
    public Boolean deleteFile(Long fileId) {
        return deduplicationFileService.deleteFile(fileId);
    }

    @Override
    public Integer deleteFiles(List<Long> fileIds) {
        return deduplicationFileService.deleteFiles(fileIds);
    }

    @Override
    public DeduplicationStatsRespDTO getDeduplicationStats() {
        return deduplicationFileService.getDeduplicationStats();
    }

}
