package cn.iocoder.yudao.module.infra.api.deduplication;

import cn.iocoder.yudao.module.infra.api.deduplication.dto.*;
import cn.iocoder.yudao.module.infra.service.deduplication.DeduplicationFileService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.validation.annotation.Validated;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;

/**
 * 去重文件 API 实现类（重新设计）
 *
 * <AUTHOR>
 */
@Service
@Validated
@Slf4j
public class DeduplicationFileApiImpl implements DeduplicationFileApi {

    @Resource
    private DeduplicationFileService deduplicationFileService;

    @Override
    public FileHashCheckRespDTO checkFileHash(FileHashCheckReqDTO reqDTO) {
        return deduplicationFileService.checkFileHash(reqDTO);
    }

    @Override
    public DeduplicationFileUploadRespDTO uploadFile(DeduplicationFileUploadReqDTO reqDTO) {
        return deduplicationFileService.uploadFile(reqDTO);
    }

    @Override
    public DeduplicationFileRespDTO getFileInfo(Long fileId) {
        return deduplicationFileService.getFileInfo(fileId);
    }

    @Override
    public Map<Long, DeduplicationFileRespDTO> getFileInfoBatch(List<Long> fileIds) {
        return deduplicationFileService.getFileInfoBatch(fileIds);
    }

    @Override
    public List<Map<String, Object>> getFileDerivatives(Long fileId) {
        return deduplicationFileService.getFileDerivatives(fileId);
    }

    @Override
    public Map<String, Object> getProcessingTaskStatus(String taskId) {
        return deduplicationFileService.getProcessingTaskStatus(taskId);
    }

    @Override
    public String batchProcessFiles(List<Long> fileIds, List<String> processingTypes, Map<String, Object> processingParams) {
        return deduplicationFileService.batchProcessFiles(fileIds, processingTypes, processingParams);
    }

    @Override
    public Boolean deleteFile(Long fileId) {
        return deduplicationFileService.deleteFile(fileId);
    }

    @Override
    public DeduplicationStatsRespDTO getDeduplicationStats() {
        return deduplicationFileService.getDeduplicationStats();
    }

}