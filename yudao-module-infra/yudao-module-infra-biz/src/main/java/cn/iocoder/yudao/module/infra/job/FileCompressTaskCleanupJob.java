package cn.iocoder.yudao.module.infra.job;

import cn.iocoder.yudao.framework.quartz.core.handler.JobHandler;
import cn.iocoder.yudao.framework.tenant.core.job.TenantJob;
import cn.iocoder.yudao.module.infra.dal.dataobject.file.FileCompressTaskDO;
import cn.iocoder.yudao.module.infra.dal.mysql.file.FileCompressTaskMapper;
import cn.iocoder.yudao.module.infra.enums.file.FileCompressTaskStatusEnum;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 文件压缩任务清理调度器
 * 定时清理过期的压缩任务记录
 *
 * <AUTHOR>
 */
@Component
@Slf4j
public class FileCompressTaskCleanupJob implements JobHandler {

    @Resource
    private FileCompressTaskMapper fileCompressTaskMapper;

    /**
     * 每天凌晨2点执行一次，清理过期的任务记录
     */
    @Override
    @TenantJob
    public String execute(String param) throws Exception {
        log.info("[execute][开始执行文件压缩任务清理]");
        
        int cleanedCount = 0;
        
        try {
            // 清理30天前的成功任务
            cleanedCount += cleanupSuccessTasks(30);
            
            // 清理7天前的失败任务（已达到最大重试次数）
            cleanedCount += cleanupFailedTasks(7);
            
            // 清理7天前的取消任务
            cleanedCount += cleanupCancelledTasks(7);
            
            log.info("[execute][文件压缩任务清理完成] cleanedCount: {}", cleanedCount);
            return String.format("清理任务数量: %d", cleanedCount);
            
        } catch (Exception e) {
            log.error("[execute][文件压缩任务清理异常]", e);
            throw e;
        }
    }

    /**
     * 清理成功任务
     *
     * @param daysAgo 多少天前
     * @return 清理的任务数量
     */
    private int cleanupSuccessTasks(int daysAgo) {
        LocalDateTime cutoffTime = LocalDateTime.now().minusDays(daysAgo);
        
        List<FileCompressTaskDO> successTasks = fileCompressTaskMapper.selectList(
            wrapper -> wrapper
                .eq(FileCompressTaskDO::getStatus, FileCompressTaskStatusEnum.SUCCESS.getStatus())
                .lt(FileCompressTaskDO::getEndTime, cutoffTime)
                .last("LIMIT 1000") // 每次最多清理1000条
        );
        
        int cleanedCount = 0;
        for (FileCompressTaskDO task : successTasks) {
            try {
                fileCompressTaskMapper.deleteById(task.getId());
                cleanedCount++;
            } catch (Exception e) {
                log.error("[cleanupSuccessTasks][清理成功任务失败] taskId: {}", task.getId(), e);
            }
        }
        
        if (cleanedCount > 0) {
            log.info("[cleanupSuccessTasks][清理成功任务完成] cleanedCount: {}, daysAgo: {}", 
                cleanedCount, daysAgo);
        }
        
        return cleanedCount;
    }

    /**
     * 清理失败任务
     *
     * @param daysAgo 多少天前
     * @return 清理的任务数量
     */
    private int cleanupFailedTasks(int daysAgo) {
        LocalDateTime cutoffTime = LocalDateTime.now().minusDays(daysAgo);
        
        List<FileCompressTaskDO> failedTasks = fileCompressTaskMapper.selectList(
            wrapper -> wrapper
                .eq(FileCompressTaskDO::getStatus, FileCompressTaskStatusEnum.FAILED.getStatus())
                .apply("retry_count >= max_retry_count") // 已达到最大重试次数
                .lt(FileCompressTaskDO::getUpdateTime, cutoffTime)
                .last("LIMIT 1000") // 每次最多清理1000条
        );
        
        int cleanedCount = 0;
        for (FileCompressTaskDO task : failedTasks) {
            try {
                fileCompressTaskMapper.deleteById(task.getId());
                cleanedCount++;
            } catch (Exception e) {
                log.error("[cleanupFailedTasks][清理失败任务失败] taskId: {}", task.getId(), e);
            }
        }
        
        if (cleanedCount > 0) {
            log.info("[cleanupFailedTasks][清理失败任务完成] cleanedCount: {}, daysAgo: {}", 
                cleanedCount, daysAgo);
        }
        
        return cleanedCount;
    }

    /**
     * 清理取消任务
     *
     * @param daysAgo 多少天前
     * @return 清理的任务数量
     */
    private int cleanupCancelledTasks(int daysAgo) {
        LocalDateTime cutoffTime = LocalDateTime.now().minusDays(daysAgo);
        
        List<FileCompressTaskDO> cancelledTasks = fileCompressTaskMapper.selectList(
            wrapper -> wrapper
                .eq(FileCompressTaskDO::getStatus, FileCompressTaskStatusEnum.CANCELLED.getStatus())
                .lt(FileCompressTaskDO::getUpdateTime, cutoffTime)
                .last("LIMIT 1000") // 每次最多清理1000条
        );
        
        int cleanedCount = 0;
        for (FileCompressTaskDO task : cancelledTasks) {
            try {
                fileCompressTaskMapper.deleteById(task.getId());
                cleanedCount++;
            } catch (Exception e) {
                log.error("[cleanupCancelledTasks][清理取消任务失败] taskId: {}", task.getId(), e);
            }
        }
        
        if (cleanedCount > 0) {
            log.info("[cleanupCancelledTasks][清理取消任务完成] cleanedCount: {}, daysAgo: {}", 
                cleanedCount, daysAgo);
        }
        
        return cleanedCount;
    }

}
