package cn.iocoder.yudao.module.infra.framework.file.core.utils;

import cn.hutool.core.io.FileUtil;
import cn.hutool.core.util.RuntimeUtil;
import cn.hutool.core.util.StrUtil;
import lombok.extern.slf4j.Slf4j;

import java.io.File;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * HLS视频处理工具类
 * 
 * <AUTHOR>
 */
@Slf4j
public class HlsVideoUtils {

    /**
     * 将视频转换为HLS格式（多清晰度）
     *
     * @param inputFile 输入视频文件
     * @param outputDir 输出目录
     * @param qualities 清晰度配置列表
     * @return HLS文件信息映射
     */
    public static Map<String, HlsResult> convertToHls(File inputFile, File outputDir, List<QualityConfig> qualities) {
        log.info("[convertToHls][开始转换HLS] inputFile: {}, outputDir: {}, qualities: {}", 
            inputFile.getAbsolutePath(), outputDir.getAbsolutePath(), qualities.size());
        
        Map<String, HlsResult> results = new HashMap<>();
        
        // 确保输出目录存在
        if (!outputDir.exists()) {
            outputDir.mkdirs();
        }
        
        // 为每个清晰度生成HLS
        for (QualityConfig quality : qualities) {
            try {
                HlsResult result = convertSingleQuality(inputFile, outputDir, quality);
                results.put(quality.getQuality(), result);
                log.info("[convertToHls][转换完成] quality: {}, m3u8: {}", quality.getQuality(), result.getM3u8File());
            } catch (Exception e) {
                log.error("[convertToHls][转换失败] quality: {}", quality.getQuality(), e);
                results.put(quality.getQuality(), HlsResult.failed(quality.getQuality(), e.getMessage()));
            }
        }
        
        // 生成主播放列表
        try {
            File masterPlaylist = generateMasterPlaylist(outputDir, results);
            log.info("[convertToHls][生成主播放列表] masterPlaylist: {}", masterPlaylist.getAbsolutePath());
        } catch (Exception e) {
            log.error("[convertToHls][生成主播放列表失败]", e);
        }
        
        return results;
    }

    /**
     * 转换单个清晰度
     */
    private static HlsResult convertSingleQuality(File inputFile, File outputDir, QualityConfig quality) {
        String qualityDir = quality.getQuality().toLowerCase();
        File qualityOutputDir = new File(outputDir, qualityDir);
        if (!qualityOutputDir.exists()) {
            qualityOutputDir.mkdirs();
        }
        
        File m3u8File = new File(qualityOutputDir, "index.m3u8");
        String tsPattern = new File(qualityOutputDir, "segment_%03d.ts").getAbsolutePath();
        
        // 构建FFmpeg命令
        List<String> command = new ArrayList<>();
        command.add("ffmpeg");
        command.add("-y");
        command.add("-i");
        command.add(inputFile.getAbsolutePath());
        
        // 视频编码参数
        command.add("-c:v");
        command.add("libx264");
        command.add("-preset");
        command.add("medium");
        command.add("-crf");
        command.add("23");
        
        // 分辨率和比特率
        if (StrUtil.isNotBlank(quality.getResolution())) {
            command.add("-s");
            command.add(quality.getResolution());
        }
        if (quality.getVideoBitrate() != null) {
            command.add("-b:v");
            command.add(quality.getVideoBitrate() + "k");
        }
        if (quality.getMaxrate() != null) {
            command.add("-maxrate");
            command.add(quality.getMaxrate() + "k");
        }
        if (quality.getBufsize() != null) {
            command.add("-bufsize");
            command.add(quality.getBufsize() + "k");
        }
        
        // 音频编码参数
        command.add("-c:a");
        command.add("aac");
        if (quality.getAudioBitrate() != null) {
            command.add("-b:a");
            command.add(quality.getAudioBitrate() + "k");
        }
        command.add("-ar");
        command.add("44100");
        command.add("-ac");
        command.add("2");
        
        // HLS参数
        command.add("-f");
        command.add("hls");
        command.add("-hls_time");
        command.add(String.valueOf(quality.getSegmentDuration()));
        command.add("-hls_list_size");
        command.add("0");
        command.add("-hls_segment_filename");
        command.add(tsPattern);
        command.add(m3u8File.getAbsolutePath());
        
        // 执行命令
        String[] cmdArray = command.toArray(new String[0]);
        log.info("[convertSingleQuality][执行FFmpeg命令] quality: {}, command: {}", quality.getQuality(), String.join(" ", command));
        
        String result = RuntimeUtil.execForStr(cmdArray);
        log.debug("[convertSingleQuality][FFmpeg输出] quality: {}, result: {}", quality.getQuality(), result);
        
        // 检查输出文件
        if (!m3u8File.exists()) {
            throw new RuntimeException("HLS转换失败，未生成m3u8文件");
        }
        
        // 统计生成的文件
        File[] tsFiles = qualityOutputDir.listFiles((dir, name) -> name.endsWith(".ts"));
        int segmentCount = tsFiles != null ? tsFiles.length : 0;
        long totalSize = FileUtil.size(qualityOutputDir);
        
        return HlsResult.success(quality.getQuality(), m3u8File, qualityOutputDir, segmentCount, totalSize);
    }

    /**
     * 生成主播放列表
     */
    private static File generateMasterPlaylist(File outputDir, Map<String, HlsResult> results) {
        File masterPlaylist = new File(outputDir, "master.m3u8");
        
        StringBuilder content = new StringBuilder();
        content.append("#EXTM3U\n");
        content.append("#EXT-X-VERSION:3\n");
        
        // 按清晰度排序添加流信息
        String[] qualityOrder = {"HIGH", "MEDIUM", "LOW"};
        for (String quality : qualityOrder) {
            HlsResult result = results.get(quality);
            if (result != null && result.isSuccess()) {
                QualityConfig config = getQualityConfig(quality);
                content.append("#EXT-X-STREAM-INF:BANDWIDTH=").append(calculateBandwidth(config))
                       .append(",RESOLUTION=").append(config.getResolution())
                       .append(",NAME=\"").append(getQualityDisplayName(quality)).append("\"\n");
                content.append(quality.toLowerCase()).append("/index.m3u8\n");
            }
        }
        
        FileUtil.writeUtf8String(content.toString(), masterPlaylist);
        return masterPlaylist;
    }

    /**
     * 计算带宽
     */
    private static int calculateBandwidth(QualityConfig config) {
        int videoBitrate = config.getVideoBitrate() != null ? config.getVideoBitrate() : 1000;
        int audioBitrate = config.getAudioBitrate() != null ? config.getAudioBitrate() : 128;
        return (videoBitrate + audioBitrate) * 1000; // 转换为bps
    }

    /**
     * 获取清晰度配置
     */
    private static QualityConfig getQualityConfig(String quality) {
        switch (quality) {
            case "HIGH":
                return QualityConfig.builder()
                    .quality("HIGH")
                    .resolution("1920x1080")
                    .videoBitrate(3000)
                    .audioBitrate(128)
                    .build();
            case "MEDIUM":
                return QualityConfig.builder()
                    .quality("MEDIUM")
                    .resolution("1280x720")
                    .videoBitrate(1500)
                    .audioBitrate(128)
                    .build();
            case "LOW":
                return QualityConfig.builder()
                    .quality("LOW")
                    .resolution("854x480")
                    .videoBitrate(800)
                    .audioBitrate(96)
                    .build();
            default:
                return QualityConfig.builder()
                    .quality(quality)
                    .resolution("1280x720")
                    .videoBitrate(1500)
                    .audioBitrate(128)
                    .build();
        }
    }

    /**
     * 获取清晰度显示名称
     */
    private static String getQualityDisplayName(String quality) {
        switch (quality) {
            case "HIGH": return "高清";
            case "MEDIUM": return "标清";
            case "LOW": return "流畅";
            default: return quality;
        }
    }

    /**
     * 清晰度配置
     */
    public static class QualityConfig {
        private String quality;
        private String resolution;
        private Integer videoBitrate;
        private Integer audioBitrate;
        private Integer maxrate;
        private Integer bufsize;
        private Integer segmentDuration = 10;

        public static QualityConfigBuilder builder() {
            return new QualityConfigBuilder();
        }

        // Getters and Setters
        public String getQuality() { return quality; }
        public void setQuality(String quality) { this.quality = quality; }
        public String getResolution() { return resolution; }
        public void setResolution(String resolution) { this.resolution = resolution; }
        public Integer getVideoBitrate() { return videoBitrate; }
        public void setVideoBitrate(Integer videoBitrate) { this.videoBitrate = videoBitrate; }
        public Integer getAudioBitrate() { return audioBitrate; }
        public void setAudioBitrate(Integer audioBitrate) { this.audioBitrate = audioBitrate; }
        public Integer getMaxrate() { return maxrate; }
        public void setMaxrate(Integer maxrate) { this.maxrate = maxrate; }
        public Integer getBufsize() { return bufsize; }
        public void setBufsize(Integer bufsize) { this.bufsize = bufsize; }
        public Integer getSegmentDuration() { return segmentDuration; }
        public void setSegmentDuration(Integer segmentDuration) { this.segmentDuration = segmentDuration; }

        public static class QualityConfigBuilder {
            private QualityConfig config = new QualityConfig();

            public QualityConfigBuilder quality(String quality) { config.setQuality(quality); return this; }
            public QualityConfigBuilder resolution(String resolution) { config.setResolution(resolution); return this; }
            public QualityConfigBuilder videoBitrate(Integer videoBitrate) { config.setVideoBitrate(videoBitrate); return this; }
            public QualityConfigBuilder audioBitrate(Integer audioBitrate) { config.setAudioBitrate(audioBitrate); return this; }
            public QualityConfigBuilder maxrate(Integer maxrate) { config.setMaxrate(maxrate); return this; }
            public QualityConfigBuilder bufsize(Integer bufsize) { config.setBufsize(bufsize); return this; }
            public QualityConfigBuilder segmentDuration(Integer segmentDuration) { config.setSegmentDuration(segmentDuration); return this; }
            public QualityConfig build() { return config; }
        }
    }

    /**
     * HLS转换结果
     */
    public static class HlsResult {
        private String quality;
        private boolean success;
        private String errorMessage;
        private File m3u8File;
        private File outputDir;
        private int segmentCount;
        private long totalSize;

        public static HlsResult success(String quality, File m3u8File, File outputDir, int segmentCount, long totalSize) {
            HlsResult result = new HlsResult();
            result.quality = quality;
            result.success = true;
            result.m3u8File = m3u8File;
            result.outputDir = outputDir;
            result.segmentCount = segmentCount;
            result.totalSize = totalSize;
            return result;
        }

        public static HlsResult failed(String quality, String errorMessage) {
            HlsResult result = new HlsResult();
            result.quality = quality;
            result.success = false;
            result.errorMessage = errorMessage;
            return result;
        }

        // Getters
        public String getQuality() { return quality; }
        public boolean isSuccess() { return success; }
        public String getErrorMessage() { return errorMessage; }
        public File getM3u8File() { return m3u8File; }
        public File getOutputDir() { return outputDir; }
        public int getSegmentCount() { return segmentCount; }
        public long getTotalSize() { return totalSize; }
    }

}
