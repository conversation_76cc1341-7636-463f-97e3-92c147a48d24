package cn.iocoder.yudao.module.infra.job;

import cn.iocoder.yudao.framework.quartz.core.handler.JobHandler;
import cn.iocoder.yudao.framework.tenant.core.job.TenantJob;
import cn.iocoder.yudao.module.infra.dal.dataobject.file.FileIndexDO;
import cn.iocoder.yudao.module.infra.dal.mysql.file.FileIndexMapper;
import cn.iocoder.yudao.module.infra.enums.file.WatermarkStatusEnum;
import cn.iocoder.yudao.module.infra.service.file.ReplacementCompressProcessor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;

/**
 * 文件压缩和水印处理定时任务
 * 定时扫描需要压缩或添加水印的文件并进行处理
 *
 * <AUTHOR>
 */
@Component
@Slf4j
public class FileCompressionJob implements JobHandler {

    @Resource
    private FileIndexMapper fileIndexMapper;

    @Resource
    private ReplacementCompressProcessor compressProcessor;

    /**
     * 每小时执行一次，扫描并处理需要压缩或添加水印的文件
     */
    @Override
    @TenantJob
    public String execute(String param) throws Exception {
        log.info("[execute][开始执行文件压缩和水印处理定时任务]");

        int processedCount = 0;
        int compressionCount = 0;
        int watermarkCount = 0;

        try {
            // 查找需要处理的文件（每次处理50个）
            List<FileIndexDO> pendingFiles = findPendingProcessFiles(50);

            for (FileIndexDO fileIndex : pendingFiles) {
                try {
                    boolean needsCompression = !fileIndex.getIsCompressed();
                    boolean needsWatermark = needsWatermarkProcessing(fileIndex);

                    if (needsCompression || needsWatermark) {
                        log.debug("[execute][开始处理文件] fileId: {}, path: {}, compression: {}, watermark: {}",
                            fileIndex.getId(), fileIndex.getStoragePath(), needsCompression, needsWatermark);

                        // 异步执行压缩和水印处理
                        compressProcessor.processFileCompression(fileIndex);
                        processedCount++;

                        if (needsCompression) compressionCount++;
                        if (needsWatermark) watermarkCount++;
                    }

                } catch (Exception e) {
                    log.error("[execute][处理文件失败] fileId: {}, path: {}",
                        fileIndex.getId(), fileIndex.getStoragePath(), e);
                }
            }

            log.info("[execute][文件处理定时任务完成] processedCount: {}, compression: {}, watermark: {}",
                processedCount, compressionCount, watermarkCount);
            return String.format("处理文件数量: %d (压缩: %d, 水印: %d)", processedCount, compressionCount, watermarkCount);

        } catch (Exception e) {
            log.error("[execute][文件处理定时任务异常]", e);
            throw e;
        }
    }

    /**
     * 查找需要处理的文件（压缩或水印）
     *
     * @param limit 限制数量
     * @return 需要处理的文件列表
     */
    private List<FileIndexDO> findPendingProcessFiles(int limit) {
        return fileIndexMapper.selectPendingProcessFiles(limit);
    }

    /**
     * 判断是否需要水印处理
     *
     * @param fileIndex 文件索引
     * @return 是否需要水印处理
     */
    private boolean needsWatermarkProcessing(FileIndexDO fileIndex) {
        // 如果已经有水印，不需要再添加
        if (Boolean.TRUE.equals(fileIndex.getHasWatermark())) {
            return false;
        }

        // 如果水印状态为处理中，不需要重复处理
        if (WatermarkStatusEnum.PROCESSING.getCode().equals(fileIndex.getWatermarkStatus())) {
            return false;
        }

        // 检查是否有水印配置且状态为未处理或处理失败（超过1小时）
        if (fileIndex.getWatermarkPath() == null || fileIndex.getWatermarkPath().trim().isEmpty()) {
            return false;
        }

        // 未处理状态
        if (WatermarkStatusEnum.NOT_PROCESSED.getCode().equals(fileIndex.getWatermarkStatus())) {
            return true;
        }

        // 处理失败且超过1小时，允许重试
        if (WatermarkStatusEnum.FAILED.getCode().equals(fileIndex.getWatermarkStatus())
            && fileIndex.getWatermarkProcessedAt() != null) {
            return fileIndex.getWatermarkProcessedAt().isBefore(
                java.time.LocalDateTime.now().minusHours(1));
        }

        return false;
    }

}
