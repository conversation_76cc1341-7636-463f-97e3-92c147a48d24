package cn.iocoder.yudao.module.infra.service.file;

import cn.hutool.core.io.FileUtil;
import cn.iocoder.yudao.module.infra.dal.dataobject.file.FileCompressTaskDO;
import cn.iocoder.yudao.module.infra.dal.dataobject.file.FileIndexDO;
import cn.iocoder.yudao.module.infra.dal.mysql.file.FileIndexMapper;
import cn.iocoder.yudao.module.infra.enums.file.FileCompressTaskStatusEnum;
import cn.iocoder.yudao.module.infra.enums.file.FileCompressTypeEnum;
import cn.iocoder.yudao.module.infra.framework.file.core.client.FileClient;
import cn.iocoder.yudao.module.infra.framework.file.core.utils.FileCompressUtils;
import cn.iocoder.yudao.module.infra.framework.file.core.utils.FileHashUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.io.File;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDateTime;

/**
 * 替换式压缩处理器
 * 压缩完成后直接替换原文件，节约磁盘空间
 *
 * <AUTHOR>
 */
@Service
@Slf4j
public class ReplacementCompressProcessor {

    @Resource
    private FileCompressTaskManager taskManager;

    @Resource
    private FileConfigService fileConfigService;

    @Resource
    private FileIndexMapper fileIndexMapper;

    /**
     * 异步处理压缩任务（替换模式）
     *
     * @param taskId 任务ID
     */
    @Async
    @Transactional(rollbackFor = Exception.class)
    public void processCompressTaskWithReplacement(Long taskId) {
        FileCompressTaskDO task = taskManager.getTask(taskId);
        if (task == null) {
            log.error("[processCompressTaskWithReplacement][任务不存在] taskId: {}", taskId);
            return;
        }

        // 检查任务状态
        if (!taskManager.startProcessing(taskId)) {
            log.warn("[processCompressTaskWithReplacement][任务状态不正确，跳过处理] taskId: {}", taskId);
            return;
        }

        try {
            log.info("[processCompressTaskWithReplacement][开始处理压缩任务] taskId: {}, compressType: {}", 
                taskId, task.getCompressType());

            // 获取原文件索引
            FileIndexDO fileIndex = fileIndexMapper.selectById(task.getFileId());
            if (fileIndex == null) {
                throw new RuntimeException("文件索引不存在: " + task.getFileId());
            }

            // 检查是否已经被压缩
            if (fileIndex.isCompressed()) {
                log.info("[processCompressTaskWithReplacement][文件已被压缩，跳过处理] taskId: {}", taskId);
                taskManager.completeFailed(taskId, "文件已被压缩");
                return;
            }

            // 下载原始文件到临时目录
            File originalFile = downloadFileToTemp(task, fileIndex);
            if (originalFile == null) {
                throw new RuntimeException("下载原始文件失败");
            }

            try {
                // 执行压缩处理
                File compressedFile = executeCompress(task, originalFile);
                if (compressedFile == null) {
                    throw new RuntimeException("压缩处理失败");
                }

                // 检查压缩效果
                if (!FileCompressUtils.isCompressionEffective(originalFile.length(), compressedFile.length(), 
                    task.getCompressTypeEnum())) {
                    log.warn("[processCompressTaskWithReplacement][压缩效果不佳，跳过替换] taskId: {}, " +
                        "originalSize: {}, compressedSize: {}", 
                        taskId, originalFile.length(), compressedFile.length());
                    taskManager.completeFailed(taskId, "压缩效果不佳");
                    return;
                }

                // 替换原文件
                String newPath = replaceOriginalFile(task, fileIndex, compressedFile, originalFile);
                
                // 完成任务
                taskManager.completeSuccess(taskId, newPath, compressedFile.length(), originalFile.length());
                
                log.info("[processCompressTaskWithReplacement][压缩替换成功] taskId: {}, " +
                    "originalSize: {}, compressedSize: {}, savedSpace: {}", 
                    taskId, originalFile.length(), compressedFile.length(), 
                    originalFile.length() - compressedFile.length());

            } finally {
                // 清理临时文件
                cleanupTempFile(originalFile);
            }

        } catch (Exception e) {
            log.error("[processCompressTaskWithReplacement][压缩任务处理失败] taskId: {}", taskId, e);
            taskManager.completeFailed(taskId, e.getMessage());
        }
    }

    /**
     * 替换原文件
     *
     * @param task 压缩任务
     * @param fileIndex 文件索引
     * @param compressedFile 压缩后的文件
     * @param originalFile 原始文件
     * @return 新的文件路径
     */
    private String replaceOriginalFile(FileCompressTaskDO task, FileIndexDO fileIndex, 
                                     File compressedFile, File originalFile) {
        try {
            // 读取压缩文件内容
            byte[] compressedContent = FileUtil.readBytes(compressedFile);
            
            // 计算压缩文件哈希
            FileHashUtils.FileHashResult compressedHashResult = FileHashUtils.calculateHashAndSize(compressedContent);
            String compressedHash = compressedHashResult.getHash();
            
            // 获取文件客户端
            FileClient fileClient = fileConfigService.getMasterFileClient();
            
            // 生成新的文件路径（保持原路径或生成新路径）
            String newPath = generateReplacementPath(task, fileIndex);
            
            // 上传压缩文件到新路径
            fileClient.upload(compressedContent, newPath, getContentType(compressedFile));
            
            // 删除原文件（如果路径不同）
            if (!newPath.equals(fileIndex.getStoragePath())) {
                try {
                    fileClient.delete(fileIndex.getStoragePath());
                    log.debug("[replaceOriginalFile][删除原文件成功] originalPath: {}", fileIndex.getStoragePath());
                } catch (Exception e) {
                    log.warn("[replaceOriginalFile][删除原文件失败] originalPath: {}", fileIndex.getStoragePath(), e);
                }
            }
            
            // 更新文件索引记录
            updateFileIndexAfterCompression(fileIndex, compressedHash, compressedFile.length(), 
                task.getCompressTypeEnum(), newPath, originalFile.length(), compressedFile.length());
            
            log.info("[replaceOriginalFile][文件替换成功] originalPath: {}, newPath: {}, " +
                "originalSize: {}, compressedSize: {}", 
                fileIndex.getStoragePath(), newPath, originalFile.length(), compressedFile.length());
            
            return newPath;

        } catch (Exception e) {
            log.error("[replaceOriginalFile][文件替换失败] taskId: {}", task.getId(), e);
            throw new RuntimeException("文件替换失败: " + e.getMessage());
        }
    }

    /**
     * 更新文件索引记录
     */
    private void updateFileIndexAfterCompression(FileIndexDO fileIndex, String compressedHash, 
                                               Long compressedSize, FileCompressTypeEnum compressType,
                                               String newPath, Long originalSize, Long compressedSize2) {
        // 计算压缩比例
        BigDecimal compressRatio = BigDecimal.valueOf(compressedSize)
            .divide(BigDecimal.valueOf(originalSize), 4, RoundingMode.HALF_UP)
            .multiply(BigDecimal.valueOf(100));

        // 更新索引记录
        fileIndex.setCurrentHash(compressedHash);
        fileIndex.setCurrentSize(compressedSize);
        fileIndex.setStoragePath(newPath);
        fileIndex.setIsCompressed(true);
        fileIndex.setCompressType(compressType.getCode());
        fileIndex.setCompressRatio(compressRatio);
        
        fileIndexMapper.updateById(fileIndex);
        
        log.debug("[updateFileIndexAfterCompression][更新文件索引成功] indexId: {}, compressRatio: {}%", 
            fileIndex.getId(), compressRatio);
    }

    /**
     * 生成替换文件路径
     */
    private String generateReplacementPath(FileCompressTaskDO task, FileIndexDO fileIndex) {
        // 可以选择保持原路径或生成新路径
        // 这里选择生成新路径以区分压缩文件
        String originalPath = fileIndex.getStoragePath();
        return FileCompressUtils.generateCompressedPath(originalPath, task.getCompressTypeEnum());
    }

    /**
     * 下载文件到临时目录
     */
    private File downloadFileToTemp(FileCompressTaskDO task, FileIndexDO fileIndex) {
        // 实现与 AsyncFileCompressProcessor 相同的逻辑
        // 这里简化处理，实际应该复用代码
        return null; // TODO: 实现下载逻辑
    }

    /**
     * 执行压缩处理
     */
    private File executeCompress(FileCompressTaskDO task, File originalFile) {
        // 实现与 AsyncFileCompressProcessor 相同的逻辑
        // 这里简化处理，实际应该复用代码
        return null; // TODO: 实现压缩逻辑
    }

    /**
     * 获取文件的Content-Type
     */
    private String getContentType(File file) {
        try {
            return java.nio.file.Files.probeContentType(file.toPath());
        } catch (Exception e) {
            return "application/octet-stream";
        }
    }

    /**
     * 清理临时文件
     */
    private void cleanupTempFile(File tempFile) {
        if (tempFile != null && tempFile.exists()) {
            try {
                FileUtil.del(tempFile);
                log.debug("[cleanupTempFile][清理临时文件成功] file: {}", tempFile.getAbsolutePath());
            } catch (Exception e) {
                log.warn("[cleanupTempFile][清理临时文件失败] file: {}", tempFile.getAbsolutePath(), e);
            }
        }
    }

}
