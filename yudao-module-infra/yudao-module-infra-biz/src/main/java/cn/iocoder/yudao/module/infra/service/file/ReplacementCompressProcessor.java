package cn.iocoder.yudao.module.infra.service.file;

import cn.hutool.core.io.FileUtil;
import cn.iocoder.yudao.module.infra.dal.dataobject.file.FileIndexDO;
import cn.iocoder.yudao.module.infra.dal.mysql.file.FileIndexMapper;
import cn.iocoder.yudao.module.infra.enums.file.FileCompressTypeEnum;
import cn.iocoder.yudao.module.infra.framework.file.core.client.FileClient;
import cn.iocoder.yudao.module.infra.framework.file.core.utils.FileCompressUtils;
import cn.iocoder.yudao.module.infra.api.deduplication.dto.CompressConfigDTO;
import com.fasterxml.jackson.databind.ObjectMapper;
import cn.iocoder.yudao.module.infra.framework.file.core.utils.FileHashUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.io.File;
import java.math.BigDecimal;
import java.math.RoundingMode;


/**
 * 替换式压缩处理器
 * 压缩完成后直接替换原文件，节约磁盘空间
 *
 * <AUTHOR>
 */
@Service
@Slf4j
public class ReplacementCompressProcessor {

    @Resource
    private FileConfigService fileConfigService;

    @Resource
    private FileIndexMapper fileIndexMapper;

    /**
     请你在认真的重新阅读DeduplicationFileServiceImpl代码，我认为你直接就是临时文件的形式来实现所有的上传流程，不要再有inputStream和content啦。 判断hash-> 上传 -> 是否同步压缩。
     因为你现在的同步压缩，

     */

    /**
     * 异步处理文件压缩（替换模式）
     *
     * @param fileIndex 文件索引
     */
    @Async
    @Transactional(rollbackFor = Exception.class)
    public void processFileCompression(FileIndexDO fileIndex) {
        if (fileIndex == null) {
            log.error("[processFileCompression][文件索引不存在]");
            return;
        }

        // 检查是否已经被压缩
        if (fileIndex.isCompressed()) {
            log.debug("[processFileCompression][文件已被压缩，跳过处理] fileId: {}", fileIndex.getId());
            return;
        }

        // 检查文件是否支持压缩
        FileCompressTypeEnum compressType = FileCompressUtils.getRecommendedCompressType(fileIndex.getStoragePath());
        if (compressType == null) {
            log.debug("[processFileCompression][文件不支持压缩] fileId: {}, path: {}",
                fileIndex.getId(), fileIndex.getStoragePath());
            return;
        }

        try {
            log.info("[processFileCompression][开始处理文件压缩] fileId: {}, compressType: {}",
                fileIndex.getId(), compressType.getCode());

            // 下载原始文件到临时目录
            File originalFile = downloadFileToTemp(fileIndex);
            if (originalFile == null) {
                throw new RuntimeException("下载原始文件失败");
            }

            try {
                // 执行压缩处理
                File compressedFile = executeCompress(fileIndex, originalFile, compressType);
                if (compressedFile == null) {
                    throw new RuntimeException("压缩处理失败");
                }

                // 检查压缩效果
                if (!FileCompressUtils.isCompressionEffective(originalFile.length(), compressedFile.length(), compressType)) {
                    log.warn("[processFileCompression][压缩效果不佳，跳过替换] fileId: {}, " +
                        "originalSize: {}, compressedSize: {}",
                        fileIndex.getId(), originalFile.length(), compressedFile.length());
                    return;
                }

                // 替换原文件
                replaceOriginalFile(fileIndex, compressedFile, originalFile, compressType);

                log.info("[processFileCompression][压缩替换成功] fileId: {}, " +
                    "originalSize: {}, compressedSize: {}, savedSpace: {}",
                    fileIndex.getId(), originalFile.length(), compressedFile.length(),
                    originalFile.length() - compressedFile.length());

            } finally {
                // 清理临时文件
                cleanupTempFile(originalFile);
            }

        } catch (Exception e) {
            log.error("[processFileCompression][文件压缩处理失败] fileId: {}", fileIndex.getId(), e);
        }
    }

    /**
     * 替换原文件
     *
     * @param fileIndex 文件索引
     * @param compressedFile 压缩后的文件
     * @param originalFile 原始文件
     * @param compressType 压缩类型
     */
    private void replaceOriginalFile(FileIndexDO fileIndex, File compressedFile,
                                   File originalFile, FileCompressTypeEnum compressType) {
        try {
            // 读取压缩文件内容
            byte[] compressedContent = FileUtil.readBytes(compressedFile);

            // 计算压缩文件哈希
            FileHashUtils.FileHashResult compressedHashResult = FileHashUtils.calculateHashAndSize(compressedContent);
            String compressedHash = compressedHashResult.getHash();

            // 获取文件客户端
            FileClient fileClient = fileConfigService.getMasterFileClient();

            // 生成新的文件路径
            String newPath = FileCompressUtils.generateCompressedPath(fileIndex.getStoragePath(), compressType);

            // 上传压缩文件到新路径
            fileClient.upload(compressedContent, newPath, getContentType(compressedFile));

            // 删除原文件
            try {
                fileClient.delete(fileIndex.getStoragePath());
                log.debug("[replaceOriginalFile][删除原文件成功] originalPath: {}", fileIndex.getStoragePath());
            } catch (Exception e) {
                log.warn("[replaceOriginalFile][删除原文件失败] originalPath: {}", fileIndex.getStoragePath(), e);
            }

            // 更新文件索引记录
            updateFileIndexAfterCompression(fileIndex, compressedHash, compressedFile.length(),
                compressType, newPath, originalFile.length());

            log.info("[replaceOriginalFile][文件替换成功] originalPath: {}, newPath: {}, " +
                "originalSize: {}, compressedSize: {}",
                fileIndex.getStoragePath(), newPath, originalFile.length(), compressedFile.length());

        } catch (Exception e) {
            log.error("[replaceOriginalFile][文件替换失败] fileId: {}", fileIndex.getId(), e);
            throw new RuntimeException("文件替换失败: " + e.getMessage());
        }
    }

    /**
     * 更新文件索引记录
     */
    private void updateFileIndexAfterCompression(FileIndexDO fileIndex, String compressedHash,
                                               Long compressedSize, FileCompressTypeEnum compressType,
                                               String newPath, Long originalSize) {
        // 计算压缩比例
        BigDecimal compressRatio = BigDecimal.valueOf(compressedSize)
            .divide(BigDecimal.valueOf(originalSize), 4, RoundingMode.HALF_UP)
            .multiply(BigDecimal.valueOf(100));

        // 更新索引记录
        fileIndex.setCurrentHash(compressedHash);
        fileIndex.setCurrentSize(compressedSize);
        fileIndex.setStoragePath(newPath);
        fileIndex.setIsCompressed(true);
        fileIndex.setCompressType(compressType.getCode());
        fileIndex.setCompressRatio(compressRatio);

        fileIndexMapper.updateById(fileIndex);

        log.debug("[updateFileIndexAfterCompression][更新文件索引成功] indexId: {}, compressRatio: {}%",
            fileIndex.getId(), compressRatio);
    }

    /**
     * 下载文件到临时目录
     */
    private File downloadFileToTemp(FileIndexDO fileIndex) {
        try {
            // 获取文件客户端
            FileClient fileClient = fileConfigService.getFileClient(fileIndex.getConfigId());

            // 下载文件内容
            byte[] content = fileClient.getContent(fileIndex.getStoragePath());

            // 创建临时文件
            String fileName = getFileNameFromPath(fileIndex.getStoragePath());
            File tempFile = cn.iocoder.yudao.module.infra.framework.file.core.utils.TempFileManager.createTempFile("download_", "_" + fileName);

            // 写入临时文件
            try (java.io.FileOutputStream fos = new java.io.FileOutputStream(tempFile)) {
                fos.write(content);
            }

            log.debug("[downloadFileToTemp][下载文件到临时目录成功] path: {}, tempFile: {}",
                fileIndex.getStoragePath(), tempFile.getAbsolutePath());

            return tempFile;

        } catch (Exception e) {
            log.error("[downloadFileToTemp][下载文件到临时目录失败] path: {}", fileIndex.getStoragePath(), e);
            return null;
        }
    }

    /**
     * 执行压缩处理
     */
    private File executeCompress(FileIndexDO fileIndex, File originalFile, FileCompressTypeEnum compressType) {
        try {
            String fileName = originalFile.getName();
            String baseName = getFileBaseName(fileName);

            File compressedFile = null;

            switch (compressType) {
                case IMAGE_AVIF:
                    // 图片转换为AVIF格式
                    compressedFile = FileCompressUtils.convertImageToAvif(originalFile, originalFile.getParent());
                    if (compressedFile == null) {
                        log.warn("[executeCompress][图片AVIF转换失败] file: {}", fileName);
                        return null;
                    }
                    break;

                case MP4_COMPRESS:
                    // 视频压缩
                    String outputFileName = baseName + "_compressed.mp4";
                    compressedFile = new File(originalFile.getParent(), outputFileName);

                    // 解析压缩配置参数
                    CompressConfigDTO.VideoCompressConfig videoConfig = parseVideoConfig(null);

                    // 执行视频压缩
                    boolean success = FileCompressUtils.compressVideo(
                        originalFile,
                        compressedFile,
                        videoConfig.getVideoBitrate(),
                        videoConfig.getAudioBitrate(),
                        videoConfig.getResolution()
                    );
                    if (!success) {
                        log.warn("[executeCompress][视频压缩失败] file: {}", fileName);
                        return null;
                    }
                    break;

                default:
                    log.warn("[executeCompress][不支持的压缩类型] type: {}, file: {}", compressType, fileName);
                    return null;
            }

            if (compressedFile != null && compressedFile.exists() && compressedFile.length() > 0) {
                log.info("[executeCompress][压缩处理成功] original: {}, compressed: {}, type: {}",
                    fileName, compressedFile.getName(), compressType);
                return compressedFile;
            } else {
                log.warn("[executeCompress][压缩文件无效] file: {}", fileName);
                return null;
            }

        } catch (Exception e) {
            log.error("[executeCompress][压缩处理异常] file: {}, type: {}", originalFile.getName(), compressType, e);
            return null;
        }
    }

    /**
     * 获取文件的Content-Type
     */
    private String getContentType(File file) {
        try {
            return java.nio.file.Files.probeContentType(file.toPath());
        } catch (Exception e) {
            return "application/octet-stream";
        }
    }

    /**
     * 清理临时文件
     */
    private void cleanupTempFile(File tempFile) {
        if (tempFile != null && tempFile.exists()) {
            try {
                FileUtil.del(tempFile);
                log.debug("[cleanupTempFile][清理临时文件成功] file: {}", tempFile.getAbsolutePath());
            } catch (Exception e) {
                log.warn("[cleanupTempFile][清理临时文件失败] file: {}", tempFile.getAbsolutePath(), e);
            }
        }
    }

    /**
     * 从文件路径中提取文件名
     */
    private String getFileNameFromPath(String filePath) {
        if (filePath == null || filePath.trim().isEmpty()) {
            return "unknown";
        }
        int lastSlash = Math.max(filePath.lastIndexOf('/'), filePath.lastIndexOf('\\'));
        return lastSlash >= 0 ? filePath.substring(lastSlash + 1) : filePath;
    }

    /**
     * 获取文件基础名称（不含扩展名）
     */
    private String getFileBaseName(String fileName) {
        if (fileName == null || fileName.trim().isEmpty()) {
            return "unknown";
        }
        int lastDot = fileName.lastIndexOf('.');
        return lastDot > 0 ? fileName.substring(0, lastDot) : fileName;
    }

    /**
     * 解析视频压缩配置
     */
    private CompressConfigDTO.VideoCompressConfig parseVideoConfig(String configJson) {
        CompressConfigDTO.VideoCompressConfig config = new CompressConfigDTO.VideoCompressConfig();

        if (configJson != null && !configJson.trim().isEmpty()) {
            try {
                ObjectMapper mapper = new ObjectMapper();
                config = mapper.readValue(configJson, CompressConfigDTO.VideoCompressConfig.class);
            } catch (Exception e) {
                log.warn("[parseVideoConfig][解析视频压缩配置失败，使用默认配置] config: {}", configJson, e);
            }
        }

        return config;
    }

    /**
     * 解析图片压缩配置
     */
    private CompressConfigDTO.ImageCompressConfig parseImageConfig(String configJson) {
        CompressConfigDTO.ImageCompressConfig config = new CompressConfigDTO.ImageCompressConfig();

        if (configJson != null && !configJson.trim().isEmpty()) {
            try {
                ObjectMapper mapper = new ObjectMapper();
                config = mapper.readValue(configJson, CompressConfigDTO.ImageCompressConfig.class);
            } catch (Exception e) {
                log.warn("[parseImageConfig][解析图片压缩配置失败，使用默认配置] config: {}", configJson, e);
            }
        }

        return config;
    }

}
