package cn.iocoder.yudao.module.infra.framework.file.core.client.local;

import cn.hutool.core.io.FileUtil;
import cn.iocoder.yudao.module.infra.framework.file.core.client.AbstractFileClient;

import java.io.File;

/**
 * 本地文件客户端
 *
 * <AUTHOR>
 */
public class LocalFileClient extends AbstractFileClient<LocalFileClientConfig> {

    public LocalFileClient(Long id, LocalFileClientConfig config) {
        super(id, config);
    }

    @Override
    protected void doInit() {
        // 补全风格。例如说 Linux 是 /，Windows 是 \
        if (!config.getBasePath().endsWith(File.separator)) {
            config.setBasePath(config.getBasePath() + File.separator);
        }
    }

    @Override
    public String upload(byte[] content, String path, String type) {
        // 执行写入
        String filePath = getFilePath(path);
        FileUtil.writeBytes(content, filePath);
        // 拼接返回路径
        return super.formatFileUrl(config.getDomain(), path);
    }

    @Override
    public void delete(String path) {
        String filePath = getFilePath(path);
        FileUtil.del(filePath);
    }

    @Override
    public byte[] getContent(String path) {
        String filePath = getFilePath(path);
        return FileUtil.readBytes(filePath);
    }

    @Override
    public File getFile(String path) throws Exception {
        String filePath = getFilePath(path);
        return FileUtil.file(filePath);
    }

    @Override
    public String uploadStream(java.io.InputStream inputStream, String path, String type) throws Exception {
        // 执行流式写入
        String filePath = getFilePath(path);

        // 确保目录存在
        FileUtil.mkParentDirs(filePath);

        // 使用流式写入，避免大文件OOM
        try (java.io.FileOutputStream fos = new java.io.FileOutputStream(filePath);
             java.io.BufferedOutputStream bos = new java.io.BufferedOutputStream(fos)) {

            byte[] buffer = new byte[8192];
            int bytesRead;
            while ((bytesRead = inputStream.read(buffer)) != -1) {
                bos.write(buffer, 0, bytesRead);
            }
            bos.flush();
        }

        // 拼接返回路径
        return super.formatFileUrl(config.getDomain(), path);
    }

    @Override
    public String uploadStream(java.io.InputStream inputStream, String path, String type, long contentLength) throws Exception {
        // 本地文件存储不需要预先知道文件大小，直接调用不带大小的方法
        return uploadStream(inputStream, path, type);
    }

    private String getFilePath(String path) {
        return config.getBasePath() + path;
    }

}
