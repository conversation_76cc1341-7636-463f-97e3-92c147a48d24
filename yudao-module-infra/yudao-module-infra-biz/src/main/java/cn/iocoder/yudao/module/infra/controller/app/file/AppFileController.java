package cn.iocoder.yudao.module.infra.controller.app.file;

import cn.hutool.core.io.IoUtil;
import cn.hutool.core.util.StrUtil;
import cn.iocoder.yudao.framework.common.pojo.CommonResult;
import cn.iocoder.yudao.framework.security.core.filter.TokenAuthenticationFilter;
import cn.iocoder.yudao.module.infra.controller.admin.file.vo.file.FileCreateReqVO;
import cn.iocoder.yudao.module.infra.controller.admin.file.vo.file.FilePresignedUrlRespVO;
import cn.iocoder.yudao.module.infra.controller.app.file.vo.AppFileUploadReqVO;
import cn.iocoder.yudao.module.infra.framework.file.core.utils.VideoStreamUtils;
import cn.iocoder.yudao.module.infra.service.file.FileService;
import cn.iocoder.yudao.module.infra.service.file.bo.TempFileRespBO;
import cn.iocoder.yudao.module.system.api.validity.RequestValidityApi;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpStatus;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import javax.annotation.security.PermitAll;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;

import static cn.iocoder.yudao.framework.common.pojo.CommonResult.success;
import static cn.iocoder.yudao.module.infra.framework.file.core.utils.FileTypeUtils.writeAttachment;

@Tag(name = "用户 App - 文件存储")
@RestController
@RequestMapping("/infra/file")
@Validated
@Slf4j
public class AppFileController {

    @Resource
    private FileService fileService;
    @Resource
    private TokenAuthenticationFilter authenticationTokenFilter;
    @Resource
    private RequestValidityApi requestValidityApi;

    @PostMapping("/upload")
    @Operation(summary = "上传文件")
    public CommonResult<String> uploadFile(AppFileUploadReqVO uploadReqVO) throws Exception {
        MultipartFile file = uploadReqVO.getFile();
        String path = uploadReqVO.getPath();
        return success(fileService.createFile(file.getOriginalFilename(), path, IoUtil.readBytes(file.getInputStream())));
    }

    @GetMapping("/presigned-url")
    @Operation(summary = "获取文件预签名地址", description = "模式二：前端上传文件：用于前端直接上传七牛、阿里云 OSS 等文件存储器")
    public CommonResult<FilePresignedUrlRespVO> getFilePresignedUrl(@RequestParam("path") String path) throws Exception {
        return success(fileService.getFilePresignedUrl(path));
    }

    @PostMapping("/create")
    @Operation(summary = "创建文件", description = "模式二：前端上传文件：配合 presigned-url 接口，记录上传了上传的文件")
    public CommonResult<Long> createFile(@Valid @RequestBody FileCreateReqVO createReqVO) {
        return success(fileService.createFile(createReqVO));
    }

    @GetMapping("/{configId}/get/**")
    @Operation(summary = "下载文件")
    @Parameter(name = "configId", description = "配置编号", required = true)
    @PermitAll
    public void getFileContent(HttpServletRequest request,
                               HttpServletResponse response,
                               @PathVariable("configId") Long configId) throws Exception {
        // 采用这个方式去检验token,这样admin的token也是有效的。
//        authenticationTokenFilter.checkAccessToken(request);
//        if (!requestValidityApi.isRequestValid()) {
//            response.setStatus(HttpStatus.UNAUTHORIZED.value());
//        }
        // 获取请求的路径
        String path = StrUtil.subAfter(request.getRequestURI(), "/get/", false);
        // 使用支持流式播放的方法
        fileService.writeResponse(request, configId, path, response);
    }

    @GetMapping("/pull/{uuid}")
    @PermitAll
    @Operation(summary = "下载文件")
    @Parameter(name = "uuid", description = "临时id", required = true)
    public void getFileContent(HttpServletRequest request, HttpServletResponse response, @PathVariable("uuid") String uuid) throws Exception {
        donwTempContent(request, response, uuid, false);
    }

    @GetMapping("/fetch/{uuid}")
    @PermitAll
    @Operation(summary = "提取文件")
    @Parameter(name = "uuid", description = "临时id", required = true)
    public void getOnceFileContent(HttpServletRequest request, HttpServletResponse response, @PathVariable("uuid") String uuid) throws Exception {
        donwTempContent(request, response, uuid, true);
    }

    // 读取内容
    private void donwTempContent(HttpServletRequest request, HttpServletResponse response, String uuid, boolean once) throws Exception {
        TempFileRespBO respVO = fileService.getTempFileContent(uuid, once);
        byte[] content = respVO.getContent();
        String path = respVO.getPath();
        if (content == null) {
            log.warn("[getFileContent][uuid({}) path({}) ) 文件不存在]", uuid, path);
            response.setStatus(HttpStatus.NOT_FOUND.value());
            return;
        }

        // 检查文件是否为MP4视频
        if (StrUtil.endWithIgnoreCase(path, ".mp4")) {
            try {
                // 使用视频流工具处理MP4文件（包含缓存控制）
                log.info("[getFileContent][uuid({}) path({}) 使用流式播放处理MP4视频]", uuid, path);
                VideoStreamUtils.streamVideoContent(request, response, content, path);
            } catch (Exception e) {
                // 检查是否是客户端断开连接的异常
                if (VideoStreamUtils.isClientDisconnectedException(e)) {
                    // 客户端断开连接是正常情况，只记录debug级别日志
                    log.debug("[donwTempContent][客户端断开连接] uuid: {}, path: {}, error: {}", uuid, path, e.getMessage());
                    return; // 不抛出异常，避免传播到全局异常处理器
                }

                log.error("[donwTempContent][处理MP4视频流时发生异常] uuid: {}, path: {}", uuid, path, e);
                // 如果响应还没有提交，重置并设置错误状态
                if (!response.isCommitted()) {
                    response.reset();
                    response.setStatus(HttpStatus.INTERNAL_SERVER_ERROR.value());
                    response.setContentType("application/json;charset=UTF-8");
                }
                throw e;
            }
        } else {
            // 非MP4文件使用原有方式处理
            // 设置缓存控制，提高性能
            response.setHeader("Cache-Control", "public, max-age=31536000");
            writeAttachment(response, path, content);
        }
    }

}
