package cn.iocoder.yudao.module.infra.controller.admin.deduplication;

import cn.iocoder.yudao.framework.common.pojo.CommonResult;
import cn.iocoder.yudao.module.infra.api.deduplication.DeduplicationFileApi;
import cn.iocoder.yudao.module.infra.api.deduplication.dto.DeduplicationFileUploadReqDTO;
import cn.iocoder.yudao.module.infra.api.deduplication.dto.DeduplicationFileRespDTO;
import cn.iocoder.yudao.module.infra.api.deduplication.dto.DeduplicationStatsRespDTO;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.io.IOException;
import java.util.List;
import java.util.Map;

import static cn.iocoder.yudao.framework.common.pojo.CommonResult.success;

/**
 * 管理后台 - 去重文件管理
 * 演示如何使用去重文件API
 *
 * <AUTHOR>
 */
@Tag(name = "管理后台 - 去重文件管理")
@RestController
@RequestMapping("/infra/deduplication-file")
@Validated
@Slf4j
public class DeduplicationFileController {

    @Resource
    private DeduplicationFileApi deduplicationFileApi;

    @PostMapping("/upload")
    @Operation(summary = "上传文件（支持去重）")
    @PreAuthorize("@ss.hasPermission('infra:file:create')")
    public CommonResult<DeduplicationFileRespDTO> uploadFile(
            @RequestParam("file") MultipartFile file,
            @RequestParam(value = "path", required = false) String path,
            @RequestParam(value = "type", required = false) String type) throws IOException {
        
        DeduplicationFileUploadReqDTO uploadReq = new DeduplicationFileUploadReqDTO();
        uploadReq.setName(file.getOriginalFilename());
        uploadReq.setPath(path);
        uploadReq.setContent(file.getBytes());
        uploadReq.setType(type != null ? type : file.getContentType());
        
        DeduplicationFileRespDTO result = deduplicationFileApi.uploadFile(uploadReq);
        return success(result);
    }

    @PostMapping("/upload-batch")
    @Operation(summary = "批量上传文件（支持去重）")
    @PreAuthorize("@ss.hasPermission('infra:file:create')")
    public CommonResult<List<DeduplicationFileRespDTO>> uploadFiles(
            @RequestParam("files") MultipartFile[] files) throws IOException {
        
        List<DeduplicationFileRespDTO> results = new java.util.ArrayList<>();
        
        for (MultipartFile file : files) {
            DeduplicationFileUploadReqDTO uploadReq = new DeduplicationFileUploadReqDTO();
            uploadReq.setName(file.getOriginalFilename());
            uploadReq.setContent(file.getBytes());
            uploadReq.setType(file.getContentType());
            
            DeduplicationFileRespDTO result = deduplicationFileApi.uploadFile(uploadReq);
            results.add(result);
        }
        
        return success(results);
    }

    @GetMapping("/{id}")
    @Operation(summary = "获取文件信息")
    @Parameter(name = "id", description = "文件ID", required = true)
    @PreAuthorize("@ss.hasPermission('infra:file:query')")
    public CommonResult<DeduplicationFileRespDTO> getFile(@PathVariable("id") Long id) {
        DeduplicationFileRespDTO file = deduplicationFileApi.getFile(id);
        return success(file);
    }

    @PostMapping("/batch-get")
    @Operation(summary = "批量获取文件信息")
    @PreAuthorize("@ss.hasPermission('infra:file:query')")
    public CommonResult<Map<Long, DeduplicationFileRespDTO>> getFiles(@RequestBody @Valid List<Long> fileIds) {
        Map<Long, DeduplicationFileRespDTO> files = deduplicationFileApi.getFiles(fileIds);
        return success(files);
    }

    @GetMapping("/url/{id}")
    @Operation(summary = "获取文件访问URL")
    @Parameter(name = "id", description = "文件ID", required = true)
    @PreAuthorize("@ss.hasPermission('infra:file:query')")
    public CommonResult<String> getFileUrl(@PathVariable("id") Long id) {
        String url = deduplicationFileApi.getFileUrl(id);
        return success(url);
    }

    @PostMapping("/urls")
    @Operation(summary = "批量获取文件访问URL")
    @PreAuthorize("@ss.hasPermission('infra:file:query')")
    public CommonResult<Map<Long, String>> getFileUrls(@RequestBody @Valid List<Long> fileIds) {
        Map<Long, String> urls = deduplicationFileApi.getFileUrls(fileIds);
        return success(urls);
    }

    @DeleteMapping("/{id}")
    @Operation(summary = "删除文件")
    @Parameter(name = "id", description = "文件ID", required = true)
    @PreAuthorize("@ss.hasPermission('infra:file:delete')")
    public CommonResult<Boolean> deleteFile(@PathVariable("id") Long id) {
        Boolean result = deduplicationFileApi.deleteFile(id);
        return success(result);
    }

    @DeleteMapping("/batch")
    @Operation(summary = "批量删除文件")
    @PreAuthorize("@ss.hasPermission('infra:file:delete')")
    public CommonResult<Integer> deleteFiles(@RequestBody @Valid List<Long> fileIds) {
        Integer deletedCount = deduplicationFileApi.deleteFiles(fileIds);
        return success(deletedCount);
    }

    @GetMapping("/stats")
    @Operation(summary = "获取去重统计信息")
    @PreAuthorize("@ss.hasPermission('infra:file:query')")
    public CommonResult<DeduplicationStatsRespDTO> getDeduplicationStats() {
        DeduplicationStatsRespDTO stats = deduplicationFileApi.getDeduplicationStats();
        return success(stats);
    }

}
