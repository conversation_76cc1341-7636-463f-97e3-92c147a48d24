package cn.iocoder.yudao.module.infra.controller.app.clouddisk.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * 用户云盘创建文件夹请求VO
 *
 * <AUTHOR>
 */
@Schema(description = "用户 APP - 云盘创建文件夹请求VO")
@Data
public class AppUserCloudDiskCreateFolderReqVO {

    @Schema(description = "文件夹名", requiredMode = Schema.RequiredMode.REQUIRED, example = "我的文档")
    @NotBlank(message = "文件夹名不能为空")
    private String folderName;

    @Schema(description = "父目录ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "0")
    @NotNull(message = "父目录ID不能为空")
    private Long parentId;

}
