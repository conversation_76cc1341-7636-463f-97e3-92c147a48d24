package cn.iocoder.yudao.module.infra.enums.file;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 文件索引类型枚举
 *
 * <AUTHOR>
 */
@AllArgsConstructor
@Getter
public enum FileIndexTypeEnum {

    ORIGINAL("original", "原文件"),
    COMPRESSED("compressed", "压缩文件");

    /**
     * 类型代码
     */
    private final String code;
    
    /**
     * 类型名称
     */
    private final String name;

    public static FileIndexTypeEnum getByCode(String code) {
        for (FileIndexTypeEnum typeEnum : values()) {
            if (typeEnum.getCode().equals(code)) {
                return typeEnum;
            }
        }
        return null;
    }

}
