package cn.iocoder.yudao.module.infra.service.deduplication;

import cn.hutool.core.util.StrUtil;
import cn.iocoder.yudao.framework.common.util.object.BeanUtils;
import cn.iocoder.yudao.module.infra.api.deduplication.dto.DeduplicationFileUploadReqDTO;
import cn.iocoder.yudao.module.infra.api.deduplication.dto.DeduplicationFileRespDTO;
import cn.iocoder.yudao.module.infra.api.deduplication.dto.DeduplicationStatsRespDTO;
import cn.iocoder.yudao.module.infra.dal.dataobject.file.FileConfigDO;
import cn.iocoder.yudao.module.infra.dal.dataobject.file.FileDO;
import cn.iocoder.yudao.module.infra.dal.dataobject.file.FileIndexDO;
import cn.iocoder.yudao.module.infra.dal.mysql.file.FileIndexMapper;
import cn.iocoder.yudao.module.infra.framework.file.core.client.FileClient;
import cn.iocoder.yudao.module.infra.framework.file.core.utils.FileHashUtils;
import cn.iocoder.yudao.module.infra.framework.file.core.utils.StreamHashUtils;
import cn.iocoder.yudao.module.infra.service.file.FileConfigService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 去重文件服务实现类
 *
 * <AUTHOR>
 */
@Service
@Slf4j
public class DeduplicationFileServiceImpl implements DeduplicationFileService {

//    @Resource
//    private FileMapper fileMapper;

    @Resource
    private FileIndexMapper fileIndexMapper;

    @Resource
    private FileConfigService fileConfigService;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public DeduplicationFileRespDTO uploadFile(DeduplicationFileUploadReqDTO uploadReq) {
        Long fileSize = uploadReq.getEffectiveContentLength();
        log.info("[uploadFile][开始上传文件] name: {}, size: {}, useStream: {}",
            uploadReq.getName(), fileSize, uploadReq.shouldUseStreamUpload());

        // 根据上传方式选择不同的处理逻辑
        if (uploadReq.shouldUseStreamUpload()) {
            return uploadFileWithStream(uploadReq);
        } else {
            return uploadFileWithBytes(uploadReq);
        }
    }

    /**
     * 字节数组方式上传文件（小文件）
     */
    private DeduplicationFileRespDTO uploadFileWithBytes(DeduplicationFileUploadReqDTO uploadReq) {
        // 计算文件哈希
        FileHashUtils.FileHashResult hashResult = FileHashUtils.calculateHashAndSize(uploadReq.getContent());
        String hash = hashResult.getHash();
        Long size = hashResult.getSize();

        // 检查去重
        FileIndexDO existingIndex = fileIndexMapper.selectByHashAndSize(hash, size);

        if (existingIndex != null) {
            // 文件已存在，增加引用计数
            fileIndexMapper.incrementRefCount(existingIndex.getId());

            log.info("[uploadFileWithBytes][文件去重成功] name: {}, existingPath: {}, size: {}",
                uploadReq.getName(), existingIndex.getStoragePath(), size);

            return convertToRespDTO(null, existingIndex);
        } else {
            // 文件不存在，需要上传新文件
            return uploadNewFileWithBytes(uploadReq, hash, size);
        }
    }

    /**
     * 流式方式上传文件（大文件）
     */
    private DeduplicationFileRespDTO uploadFileWithStream(DeduplicationFileUploadReqDTO uploadReq) {
        java.io.InputStream inputStream = uploadReq.getEffectiveInputStream();
        if (inputStream == null) {
            throw new IllegalArgumentException("输入流不能为空");
        }

        try {
            // 创建支持重置的流用于哈希计算
            java.io.InputStream resettableStream = StreamHashUtils.createResettableStream(
                inputStream, 100 * 1024 * 1024); // 最大100MB

            // 计算文件哈希
            StreamHashUtils.StreamHashResult hashResult =
                StreamHashUtils.calculateHashAndSizeWithReset(resettableStream);

            // 检查去重
            FileIndexDO existingIndex = fileIndexMapper.selectByHashAndSize(hashResult.getHash(), hashResult.getSize());

            if (existingIndex != null) {
                // 文件已存在，增加引用计数
                fileIndexMapper.incrementRefCount(existingIndex.getId());

                log.info("[uploadFileWithStream][文件去重成功] name: {}, existingPath: {}, size: {}",
                    uploadReq.getName(), existingIndex.getStoragePath(), hashResult.getSize());

                return convertToRespDTO(null, existingIndex);
            } else {
                // 文件不存在，需要上传新文件
                return uploadNewFileWithStream(uploadReq, resettableStream, hashResult);
            }

        } catch (Exception e) {
            log.error("[uploadFileWithStream][流式上传失败] name: {}", uploadReq.getName(), e);
            throw new RuntimeException("流式上传失败: " + e.getMessage());
        }
    }

    @Override
    public DeduplicationFileRespDTO getFile(Long fileId) {
//        FileDO file = fileMapper.selectById(fileId);
        FileIndexDO fileIndex = fileIndexMapper.selectById(fileId);
        if (fileIndex == null) {
            return null;
        }

        return convertToRespDTO( fileIndex);
    }

    @Override
    public Map<Long, DeduplicationFileRespDTO> getFiles(List<Long> fileIds) {
        Map<Long, DeduplicationFileRespDTO> result = new HashMap<>();
        if (fileIds == null || fileIds.isEmpty()) {
            return result;
        }

        for (Long fileId : fileIds) {
            DeduplicationFileRespDTO fileInfo = getFile(fileId);
            if (fileInfo != null) {
                result.put(fileId, fileInfo);
            }
        }
        return result;
    }

    @Override
    public String getFileUrl(Long fileId) {
        FileIndexDO file = fileIndexMapper.selectById(fileId);
        return formatFileUrl(file);
    }

    @Override
    public Map<Long, String> getFileUrls(List<Long> fileIds) {
        Map<Long, String> result = new HashMap<>();
        if (fileIds == null || fileIds.isEmpty()) {
            return result;
        }
        List<FileIndexDO> fileIndexDOS = fileIndexMapper.selectBatchIds(fileIds);
        fileIndexDOS.forEach(x -> result.put(x.getId(), formatFileUrl(x)));
        return result;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean deleteFile(Long fileId) {
        try {
            FileIndexDO fileIndex = fileIndexMapper.selectById(fileId);
            if (fileIndex != null) {
                // 减少引用计数
                int updatedIndex = fileIndexMapper.decrementRefCount(fileIndex.getId());
                
                if ( updatedIndex <= 0 ) {
                    // 引用计数为0，删除物理文件和索引记录
                    try {
                        FileClient fileClient = fileConfigService.getMasterFileClient();
                        fileClient.delete(fileIndex.getStoragePath());
                        fileIndexMapper.deleteById(fileIndex.getId());
                        log.info("[deleteFile][物理文件删除成功] fileId: {}, path: {}", fileId, fileIndex.getStoragePath());
                    } catch (Exception e) {
                        log.error("[deleteFile][物理文件删除失败] fileId: {}, path: {}", fileId, fileIndex.getStoragePath(), e);
                        // 恢复引用计数
                        fileIndexMapper.incrementRefCount(fileIndex.getId());
                        throw e;
                    }
                } else {
                    log.info("[deleteFile][减少引用计数成功] fileId: {}, refCount: {}", 
                        fileId, updatedIndex );
                }
            }

            return true;

        } catch (Exception e) {
            log.error("[deleteFile][删除文件失败] fileId: {}", fileId, e);
            return false;
        }
    }

    @Override
    public Integer deleteFiles(List<Long> fileIds) {
        if (fileIds == null || fileIds.isEmpty()) {
            return 0;
        }

        int deletedCount = 0;
        for (Long fileId : fileIds) {
            if (deleteFile(fileId)) {
                deletedCount++;
            }
        }
        return deletedCount;
    }

    @Override
    public DeduplicationStatsRespDTO getDeduplicationStats() {
        DeduplicationStatsRespDTO stats = new DeduplicationStatsRespDTO();

        // 总文件数量
        Long totalFileCount = fileIndexMapper.selectCount();
        stats.setTotalFileCount(totalFileCount);

        // 去重文件数量（引用计数>1的文件）
        Long deduplicatedFileCount = fileIndexMapper.selectDeduplicatedFileCount();
        stats.setDeduplicatedFileCount(deduplicatedFileCount);

        // 去重率
        if (totalFileCount > 0) {
            BigDecimal deduplicationRate = BigDecimal.valueOf(deduplicatedFileCount)
                .divide(BigDecimal.valueOf(totalFileCount), 4, RoundingMode.HALF_UP)
                .multiply(BigDecimal.valueOf(100));
            stats.setDeduplicationRate(deduplicationRate);
        }

        // 存储统计
        Long totalStorageSize = fileIndexMapper.selectTotalStorageSize();
        stats.setTotalStorageSize(totalStorageSize);

        Long savedStorageSize = fileIndexMapper.selectSavedStorageSize();
        stats.setSavedStorageSize(savedStorageSize);

        Long compressSavedSize = fileIndexMapper.selectCompressSavedSize();
        stats.setCompressSavedSize(compressSavedSize);

        Long totalSavedSize = savedStorageSize + compressSavedSize;
        stats.setTotalSavedSize(totalSavedSize);

        // 存储节省率
        if (totalStorageSize > 0) {
            BigDecimal storageSaveRate = BigDecimal.valueOf(totalSavedSize)
                .divide(BigDecimal.valueOf(totalStorageSize + totalSavedSize), 4, RoundingMode.HALF_UP)
                .multiply(BigDecimal.valueOf(100));
            stats.setStorageSaveRate(storageSaveRate);
        }

        // 压缩统计
        Long compressedFileCount = fileIndexMapper.selectCompressedFileCount();
        stats.setCompressedFileCount(compressedFileCount);

        if (totalFileCount > 0) {
            BigDecimal compressionRate = BigDecimal.valueOf(compressedFileCount)
                .divide(BigDecimal.valueOf(totalFileCount), 4, RoundingMode.HALF_UP)
                .multiply(BigDecimal.valueOf(100));
            stats.setCompressionRate(compressionRate);
        }

        return stats;
    }



    /**
     * 字节数组方式上传新文件
     */
    private DeduplicationFileRespDTO uploadNewFileWithBytes(DeduplicationFileUploadReqDTO uploadReq, String hash, Long size) {
        // 获取文件客户端
        Long uploadReqConfigId = uploadReq.getConfigId();
        FileClient fileClient = uploadReqConfigId != null ? fileConfigService.getFileClient(uploadReqConfigId) : fileConfigService.getMasterFileClient();
        Long configId = fileClient.getId();

        // 生成文件路径
        String path = StrUtil.isNotBlank(uploadReq.getPath()) ? uploadReq.getPath() :
            generateFilePath(uploadReq.getName());

        try {
            // 上传文件
            fileClient.upload(uploadReq.getContent(), path, uploadReq.getType());

            // 创建文件索引记录
            FileIndexDO fileIndex = FileIndexDO.builder()
                .originalHash(hash)
                .currentHash(hash)
                .originalSize(size)
                .currentSize(size)
                .storagePath(path)
                .configId(configId)
                .refCount(1)
                .isCompressed(false)
                .build();
            fileIndexMapper.insert(fileIndex);

            log.info("[uploadNewFileWithBytes][新文件上传成功] name: {}, path: {}, size: {}",
                uploadReq.getName(), path, size);

            return convertToRespDTO(fileIndex);

        } catch (Exception e) {
            log.error("[uploadNewFileWithBytes][新文件上传失败] name: {}", uploadReq.getName(), e);
            throw new RuntimeException("文件上传失败: " + e.getMessage());
        }
    }

    /**
     * 流式方式上传新文件
     */
    private DeduplicationFileRespDTO uploadNewFileWithStream(DeduplicationFileUploadReqDTO uploadReq,
                                                           java.io.InputStream inputStream,
                                                           StreamHashUtils.StreamHashResult hashResult) {
        // 获取文件客户端
        Long uploadReqConfigId = uploadReq.getConfigId();
        FileClient fileClient = uploadReqConfigId != null ? fileConfigService.getFileClient(uploadReqConfigId) : fileConfigService.getMasterFileClient();
        Long configId = fileClient.getId();

        // 生成文件路径
        String path = StrUtil.isNotBlank(uploadReq.getPath()) ? uploadReq.getPath() :
            generateFilePath(uploadReq.getName());

        try {
            // 流式上传文件
            Long contentLength = uploadReq.getEffectiveContentLength();
            if (contentLength != null) {
                fileClient.uploadStream(inputStream, path, uploadReq.getType(), contentLength);
            } else {
                fileClient.uploadStream(inputStream, path, uploadReq.getType());
            }

            // 创建文件索引记录
            FileIndexDO fileIndex = FileIndexDO.builder()
                .originalHash(hashResult.getHash())
                .currentHash(hashResult.getHash())
                .originalSize(hashResult.getSize())
                .currentSize(hashResult.getSize())
                .storagePath(path)
                .configId(configId)
                .refCount(1)
                .isCompressed(false)
                .build();
            fileIndexMapper.insert(fileIndex);

            log.info("[uploadNewFileWithStream][新文件流式上传成功] name: {}, path: {}, size: {}",
                uploadReq.getName(), path, hashResult.getSize());

            return convertToRespDTO(fileIndex);

        } catch (Exception e) {
            log.error("[uploadNewFileWithStream][新文件流式上传失败] name: {}", uploadReq.getName(), e);
            throw new RuntimeException("文件流式上传失败: " + e.getMessage());
        }
    }



    /**
     * 转换为响应DTO（仅使用FileIndexDO）
     */
    private DeduplicationFileRespDTO convertToRespDTO(FileIndexDO fileIndex) {
        DeduplicationFileRespDTO respDTO = new DeduplicationFileRespDTO();

        if (fileIndex != null) {
            respDTO.setId(fileIndex.getId());
            respDTO.setPath(fileIndex.getStoragePath());
            respDTO.setUrl(formatFileUrl(fileIndex.getConfigId(), fileIndex.getStoragePath()));
            respDTO.setSize(fileIndex.getCurrentSize());
            respDTO.setOriginalSize(fileIndex.getOriginalSize());
            respDTO.setIsCompressed(fileIndex.isCompressed());
            respDTO.setCompressType(fileIndex.getCompressType());
            respDTO.setCompressRatio(fileIndex.getCompressRatio());
            respDTO.setSavedSpace(fileIndex.getSavedSpace());
            respDTO.setRefCount(fileIndex.getRefCount());
            respDTO.setIsDuplicated(fileIndex.getRefCount() > 1);
            respDTO.setCreateTime(fileIndex.getCreateTime());
            respDTO.setUpdateTime(fileIndex.getUpdateTime());
        }

        return respDTO;
    }

    /**
     * 转换为响应DTO（使用FileDO和FileIndexDO）
     */
    private DeduplicationFileRespDTO convertToRespDTO(FileDO file, FileIndexDO fileIndex) {
        DeduplicationFileRespDTO respDTO = BeanUtils.toBean(file, DeduplicationFileRespDTO.class);

        if (fileIndex != null) {
            respDTO.setSize(fileIndex.getCurrentSize());
            respDTO.setOriginalSize(fileIndex.getOriginalSize());
            respDTO.setIsCompressed(fileIndex.isCompressed());
            respDTO.setCompressType(fileIndex.getCompressType());
            respDTO.setCompressRatio(fileIndex.getCompressRatio());
            respDTO.setSavedSpace(fileIndex.getSavedSpace());
            respDTO.setRefCount(fileIndex.getRefCount());
            respDTO.setIsDuplicated(fileIndex.getRefCount() > 1);
        } else {
            // 兼容处理
            respDTO.setSize(file.getSize().longValue());
            respDTO.setOriginalSize(file.getSize().longValue());
            respDTO.setIsCompressed(false);
            respDTO.setSavedSpace(0L);
            respDTO.setRefCount(1);
            respDTO.setIsDuplicated(false);
        }

        return respDTO;
    }



    /**
     * 生成文件路径
     */
    private String generateFilePath(String fileName) {
        return LocalDateTime.now().getYear() + "/" + 
               LocalDateTime.now().getMonthValue() + "/" + 
               LocalDateTime.now().getDayOfMonth() + "/" + 
               System.currentTimeMillis() + "_" + fileName;
    }
    private String formatFileUrl(FileIndexDO file) {
        return formatFileUrl(file.getConfigId(),file.getStoragePath());
    }
    /**
     * 格式化文件URL
     */
    private String formatFileUrl(Long configId,String path) {
        FileConfigDO fileConfig = fileConfigService.getFileConfig(configId);
        return fileConfig.getConfig().getDomain() + "/" + path;
    }

}
