package cn.iocoder.yudao.module.infra.service.deduplication;

import cn.hutool.core.codec.Base64;
import cn.hutool.core.io.FileUtil;
import cn.hutool.core.util.RandomUtil;
import cn.hutool.core.util.StrUtil;
import cn.iocoder.yudao.framework.common.util.object.BeanUtils;
import cn.iocoder.yudao.module.infra.api.deduplication.dto.DeduplicationFileRespDTO;
import cn.iocoder.yudao.module.infra.api.deduplication.dto.DeduplicationFileUploadReqDTO;
import cn.iocoder.yudao.module.infra.api.deduplication.dto.DeduplicationStatsRespDTO;
import cn.iocoder.yudao.module.infra.dal.dataobject.file.FileConfigDO;
import cn.iocoder.yudao.module.infra.dal.dataobject.file.FileDO;
import cn.iocoder.yudao.module.infra.dal.dataobject.file.FileIndexDO;
import cn.iocoder.yudao.module.infra.dal.mysql.file.FileIndexMapper;
import cn.iocoder.yudao.module.infra.enums.file.FileCompressTypeEnum;
import cn.iocoder.yudao.module.infra.framework.file.core.client.FileClient;
import cn.iocoder.yudao.module.infra.framework.file.core.utils.FileCompressUtils;
import cn.iocoder.yudao.module.infra.framework.file.core.utils.FileHashUtils;
import cn.iocoder.yudao.module.infra.service.file.FileConfigService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.io.File;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.function.Function;

/**
 * 去重文件服务实现类
 *
 * <AUTHOR>
 */
@Service
@Slf4j
public class DeduplicationFileServiceImpl implements DeduplicationFileService {

//    @Resource
//    private FileMapper fileMapper;

    @Resource
    private FileIndexMapper fileIndexMapper;

    @Resource
    private FileConfigService fileConfigService;

    @Override
    @Transactional(propagation = Propagation.REQUIRES_NEW,rollbackFor = Exception.class)
    public DeduplicationFileRespDTO uploadFile(DeduplicationFileUploadReqDTO uploadReq) {
        Long fileSize = uploadReq.getEffectiveContentLength();
        log.info("[uploadFile][开始上传文件] name: {}, size: {}, syncCompress: {}",
            uploadReq.getName(), fileSize, uploadReq.getSyncCompress());

        // 统一使用临时文件流程
        return processFileWithTempFile(uploadReq);
    }

    /**
     * 统一的临时文件处理流程
     * 1. 写入临时文件并计算hash
     * 2. 检查去重
     * 3. 同步压缩（可选）
     * 4. 上传最终文件
     */
    private DeduplicationFileRespDTO processFileWithTempFile(DeduplicationFileUploadReqDTO uploadReq) {

        try {
            Long originalSize = uploadReq.getEffectiveContentLength();
            String originalHash = null;
            // 1. 写入临时文件并计算hash
            try(java.io.InputStream inputStream = uploadReq.getEffectiveInputStream();){
                originalHash = FileHashUtils.calculateSha256(inputStream);
            }

            log.info("[processFileWithTempFile][哈希计算完成] name: {}, hash: {}, size: {}",
                uploadReq.getName(), originalHash, originalSize);

            // 2. 检查去重
            FileIndexDO existingIndex = fileIndexMapper.selectByHashAndSize(originalHash, originalSize);
            if (existingIndex != null) {
                // 文件已存在，增加引用计数
                fileIndexMapper.incrementRefCount(existingIndex.getId());
                log.info("[processFileWithTempFile][文件去重成功] name: {}, existingPath: {}",
                    uploadReq.getName(), existingIndex.getStoragePath());
                return convertToRespDTO(existingIndex);
            }

            // 3. 同步压缩处理（可选）
            CompressResult compressResult = handleSyncCompress(uploadReq);

            // 4. 上传最终文件
            DeduplicationFileRespDTO deduplicationFileRespDTO = uploadFinalFile(uploadReq, originalHash, compressResult);

            // 5. 删除文件
            if( Boolean.TRUE.equals(uploadReq.getAutoCleanTempFile()) ){
                FileUtil.del(uploadReq.getTempFile());
            }

            return deduplicationFileRespDTO;
        } catch (Exception e) {
            log.error("[processFileWithTempFile][处理失败] name: {}", uploadReq.getName(), e);
            throw new RuntimeException("文件处理失败: " + e.getMessage());
        }
    }

    @Override
    public DeduplicationFileRespDTO getFile(Long fileId) {
//        FileDO file = fileMapper.selectById(fileId);
        FileIndexDO fileIndex = fileIndexMapper.selectById(fileId);
        if (fileIndex == null) {
            return null;
        }

        return convertToRespDTO( fileIndex);
    }

    @Override
    public Map<Long, DeduplicationFileRespDTO> getFiles(List<Long> fileIds) {
        Map<Long, DeduplicationFileRespDTO> result = new HashMap<>();
        if (fileIds == null || fileIds.isEmpty()) {
            return result;
        }

        for (Long fileId : fileIds) {
            DeduplicationFileRespDTO fileInfo = getFile(fileId);
            if (fileInfo != null) {
                result.put(fileId, fileInfo);
            }
        }
        return result;
    }

    @Override
    public String getFileUrl(Long fileId) {
        FileIndexDO file = fileIndexMapper.selectById(fileId);
        return formatFileUrl(file);
    }

    @Override
    public Map<Long, String> getFileUrls(List<Long> fileIds) {
        Map<Long, String> result = new HashMap<>();
        if (fileIds == null || fileIds.isEmpty()) {
            return result;
        }
        List<FileIndexDO> fileIndexDOS = fileIndexMapper.selectBatchIds(fileIds);
        fileIndexDOS.forEach(x -> result.put(x.getId(), formatFileUrl(x)));
        return result;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean deleteFile(Long fileId) {
        try {
            FileIndexDO fileIndex = fileIndexMapper.selectById(fileId);
            if (fileIndex != null) {
                // 减少引用计数
                int updatedIndex = fileIndexMapper.decrementRefCount(fileIndex.getId());
                
                if ( updatedIndex <= 0 ) {
                    // 引用计数为0，删除物理文件和索引记录
                    try {
                        FileClient fileClient = fileConfigService.getMasterFileClient();
                        fileClient.delete(fileIndex.getStoragePath());
                        fileIndexMapper.deleteById(fileIndex.getId());
                        log.info("[deleteFile][物理文件删除成功] fileId: {}, path: {}", fileId, fileIndex.getStoragePath());
                    } catch (Exception e) {
                        log.error("[deleteFile][物理文件删除失败] fileId: {}, path: {}", fileId, fileIndex.getStoragePath(), e);
                        // 恢复引用计数
                        fileIndexMapper.incrementRefCount(fileIndex.getId());
                        throw e;
                    }
                } else {
                    log.info("[deleteFile][减少引用计数成功] fileId: {}, refCount: {}", 
                        fileId, updatedIndex );
                }
            }

            return true;

        } catch (Exception e) {
            log.error("[deleteFile][删除文件失败] fileId: {}", fileId, e);
            return false;
        }
    }

    @Override
    public Integer deleteFiles(List<Long> fileIds) {
        if (fileIds == null || fileIds.isEmpty()) {
            return 0;
        }

        int deletedCount = 0;
        for (Long fileId : fileIds) {
            if (deleteFile(fileId)) {
                deletedCount++;
            }
        }
        return deletedCount;
    }

    @Override
    public DeduplicationStatsRespDTO getDeduplicationStats() {
        DeduplicationStatsRespDTO stats = new DeduplicationStatsRespDTO();

        // 总文件数量
        Long totalFileCount = fileIndexMapper.selectCount();
        stats.setTotalFileCount(totalFileCount);

        // 去重文件数量（引用计数>1的文件）
        Long deduplicatedFileCount = fileIndexMapper.selectDeduplicatedFileCount();
        stats.setDeduplicatedFileCount(deduplicatedFileCount);

        // 去重率
        if (totalFileCount > 0) {
            BigDecimal deduplicationRate = BigDecimal.valueOf(deduplicatedFileCount)
                .divide(BigDecimal.valueOf(totalFileCount), 4, RoundingMode.HALF_UP)
                .multiply(BigDecimal.valueOf(100));
            stats.setDeduplicationRate(deduplicationRate);
        }

        // 存储统计
        Long totalStorageSize = fileIndexMapper.selectTotalStorageSize();
        stats.setTotalStorageSize(totalStorageSize);

        Long savedStorageSize = fileIndexMapper.selectSavedStorageSize();
        stats.setSavedStorageSize(savedStorageSize);

        Long compressSavedSize = fileIndexMapper.selectCompressSavedSize();
        stats.setCompressSavedSize(compressSavedSize);

        Long totalSavedSize = savedStorageSize + compressSavedSize;
        stats.setTotalSavedSize(totalSavedSize);

        // 存储节省率
        if (totalStorageSize > 0) {
            BigDecimal storageSaveRate = BigDecimal.valueOf(totalSavedSize)
                .divide(BigDecimal.valueOf(totalStorageSize + totalSavedSize), 4, RoundingMode.HALF_UP)
                .multiply(BigDecimal.valueOf(100));
            stats.setStorageSaveRate(storageSaveRate);
        }

        // 压缩统计
        Long compressedFileCount = fileIndexMapper.selectCompressedFileCount();
        stats.setCompressedFileCount(compressedFileCount);

        if (totalFileCount > 0) {
            BigDecimal compressionRate = BigDecimal.valueOf(compressedFileCount)
                .divide(BigDecimal.valueOf(totalFileCount), 4, RoundingMode.HALF_UP)
                .multiply(BigDecimal.valueOf(100));
            stats.setCompressionRate(compressionRate);
        }

        return stats;
    }



    /**
     * 处理同步压缩
     */
    private CompressResult handleSyncCompress(DeduplicationFileUploadReqDTO uploadReq) {
        if (!Boolean.TRUE.equals(uploadReq.getSyncCompress())) {
            return new CompressResult(false, null, null, null, null);
        }

        try {
            // 确定压缩类型
            FileCompressTypeEnum compressType = determineCompressType(uploadReq.getName(), uploadReq.getCompressType());
            if (compressType == null) {
                log.debug("[handleSyncCompress][文件不支持压缩] name: {}", uploadReq.getName());
                return new CompressResult(false, null, null, null, null);
            }

            log.info("[handleSyncCompress][开始同步压缩] name: {}, compressType: {}",
                uploadReq.getName(), compressType.getCode());

            // 执行压缩
            java.io.File compressedFile = executeCompress(uploadReq.getTempFile(), compressType, uploadReq.getCompressConfig());
            if (compressedFile == null) {
                log.warn("[handleSyncCompress][压缩失败] name: {}", uploadReq.getName());
                return new CompressResult(false, null, null, null, null);
            }

            // 计算压缩文件哈希
            String compressedHash;
            try (java.io.FileInputStream fis = new java.io.FileInputStream(compressedFile)) {
                compressedHash = FileHashUtils.calculateSha256(fis);
            }

            log.info("[handleSyncCompress][压缩成功] name: {}, originalSize: {}, compressedSize: {}, savedSpace: {}",
                uploadReq.getName(), uploadReq.getEffectiveContentLength(), compressedFile.length(),
                    uploadReq.getEffectiveContentLength() - compressedFile.length());

            return new CompressResult(true, compressedFile, compressedHash,
                    FileUtil.size(compressedFile), compressType);

        } catch (Exception e) {
            log.error("[handleSyncCompress][压缩处理异常] name: {}", uploadReq.getName(), e);
            return new CompressResult(false, null, null, null, null);
        }
    }

    /**
     * 上传最终文件
     */
    private DeduplicationFileRespDTO uploadFinalFile(DeduplicationFileUploadReqDTO uploadReq, String originalHash, CompressResult compressResult) {
        try {
            // 获取文件客户端
            Long uploadReqConfigId = uploadReq.getConfigId();
            FileClient fileClient = uploadReqConfigId != null ? fileConfigService.getFileClient(uploadReqConfigId) : fileConfigService.getMasterFileClient();
            Long configId = fileClient.getId();

            // 生成文件路径
            String path = StrUtil.isNotBlank(uploadReq.getPath()) ? uploadReq.getPath() :
                generateFilePath(uploadReq.getName());

            // 选择要上传的文件（压缩文件优先）
            java.io.File fileToUpload;
            String finalHash;
            Long finalSize;
            boolean isCompressed = false;
            FileCompressTypeEnum compressType = null;
            java.math.BigDecimal compressRatio = null;

            if (compressResult.isCompressed()) {
                // 使用压缩文件
                fileToUpload = compressResult.getCompressedFile();
                finalHash = compressResult.getCompressedHash();
                finalSize = compressResult.getCompressedSize();
                isCompressed = true;
                compressType = compressResult.getCompressType();

                // 计算压缩比例
                compressRatio = java.math.BigDecimal.valueOf(finalSize)
                    .divide(java.math.BigDecimal.valueOf(uploadReq.getEffectiveContentLength()), 4, java.math.RoundingMode.HALF_UP)
                    .multiply(java.math.BigDecimal.valueOf(100));

                // 更新文件路径以反映压缩格式
                path = updatePathForCompression(path, compressType);
            } else {
                // 使用原文件
                fileToUpload = uploadReq.getTempFile();
                finalHash = originalHash;
                finalSize = uploadReq.getEffectiveContentLength();
            }

            // 上传文件
            try (java.io.FileInputStream fis = new java.io.FileInputStream(fileToUpload)) {
                fileClient.uploadStream(fis, path, uploadReq.getType(), finalSize);
            }

            // 创建文件索引记录
            FileIndexDO fileIndex = FileIndexDO.builder()
                .originalHash(originalHash)
                .currentHash(finalHash)
                .originalSize(uploadReq.getEffectiveContentLength())
                .currentSize(finalSize)
                .storagePath(path)
                .configId(configId)
                .refCount(1)
                .isCompressed(isCompressed)
                .compressType(compressType != null ? compressType.getCode() : null)
                .compressRatio(compressRatio)
                .build();
            fileIndexMapper.insert(fileIndex);

            log.info("[uploadFinalFile][文件上传成功] name: {}, path: {}, originalSize: {}, finalSize: {}, compressed: {}",
                uploadReq.getName(), path, uploadReq.getEffectiveContentLength(), finalSize, isCompressed);

            return convertToRespDTO(fileIndex);

        } catch (Exception e) {
            log.error("[uploadFinalFile][文件上传失败] name: {}", uploadReq.getName(), e);
            throw new RuntimeException("文件上传失败: " + e.getMessage());
        } finally {
            // 清理压缩文件
            if (compressResult.isCompressed() && compressResult.getCompressedFile() != null) {
                try {
                    compressResult.getCompressedFile().delete();
                } catch (Exception e) {
                    log.warn("[uploadFinalFile][清理压缩文件失败] file: {}",
                        compressResult.getCompressedFile().getAbsolutePath(), e);
                }
            }
        }
    }



    /**
     * 转换为响应DTO（仅使用FileIndexDO）
     */
    private DeduplicationFileRespDTO convertToRespDTO(FileIndexDO fileIndex) {
        DeduplicationFileRespDTO respDTO = new DeduplicationFileRespDTO();

        if (fileIndex != null) {
            respDTO.setId(fileIndex.getId());
            respDTO.setPath(fileIndex.getStoragePath());
            respDTO.setUrl(formatFileUrl(fileIndex.getConfigId(), fileIndex.getStoragePath()));
            respDTO.setSize(fileIndex.getCurrentSize());
            respDTO.setOriginalSize(fileIndex.getOriginalSize());
            respDTO.setIsCompressed(fileIndex.isCompressed());
            respDTO.setCompressType(fileIndex.getCompressType());
            respDTO.setCompressRatio(fileIndex.getCompressRatio());
            respDTO.setSavedSpace(fileIndex.getSavedSpace());
            respDTO.setRefCount(fileIndex.getRefCount());
            respDTO.setIsDuplicated(fileIndex.getRefCount() > 1);
            respDTO.setCreateTime(fileIndex.getCreateTime());
            respDTO.setUpdateTime(fileIndex.getUpdateTime());
        }

        return respDTO;
    }

    /**
     * 转换为响应DTO（使用FileDO和FileIndexDO）
     */
    private DeduplicationFileRespDTO convertToRespDTO(FileDO file, FileIndexDO fileIndex) {
        DeduplicationFileRespDTO respDTO = BeanUtils.toBean(file, DeduplicationFileRespDTO.class);

        if (fileIndex != null) {
            respDTO.setSize(fileIndex.getCurrentSize());
            respDTO.setOriginalSize(fileIndex.getOriginalSize());
            respDTO.setIsCompressed(fileIndex.isCompressed());
            respDTO.setCompressType(fileIndex.getCompressType());
            respDTO.setCompressRatio(fileIndex.getCompressRatio());
            respDTO.setSavedSpace(fileIndex.getSavedSpace());
            respDTO.setRefCount(fileIndex.getRefCount());
            respDTO.setIsDuplicated(fileIndex.getRefCount() > 1);
        } else {
            // 兼容处理
            respDTO.setSize(file.getSize().longValue());
            respDTO.setOriginalSize(file.getSize().longValue());
            respDTO.setIsCompressed(false);
            respDTO.setSavedSpace(0L);
            respDTO.setRefCount(1);
            respDTO.setIsDuplicated(false);
        }

        return respDTO;
    }



    /**
     * 生成文件路径
     */
    private String generateFilePath(String fileName) {
        String finalName  = StrUtil.isNotBlank(fileName) ? fileName : RandomUtil.randomString(10);
        return LocalDateTime.now().getYear() + "/" +
               LocalDateTime.now().getMonthValue() + "/" +
               LocalDateTime.now().getDayOfMonth() + "/" +
               LocalDateTime.now().getHour() + "/" +
                Base64.encode(finalName) + '.' + StrUtil.subAfter(finalName, '.', true);
    }
    private String formatFileUrl(FileIndexDO file) {
        return formatFileUrl(file.getConfigId(),file.getStoragePath());
    }
    /**
     * 格式化文件URL
     */
    private String formatFileUrl(Long configId,String path) {
        FileConfigDO fileConfig = fileConfigService.getFileConfig(configId);
        return fileConfig.getConfig().getDomain() + "/" + path;
    }



    /**
     * 确定压缩类型
     */
    private cn.iocoder.yudao.module.infra.enums.file.FileCompressTypeEnum determineCompressType(String filePath, String specifiedType) {
        // 如果指定了压缩类型，优先使用指定的类型
        if (StrUtil.isNotBlank(specifiedType)) {
            try {
                return cn.iocoder.yudao.module.infra.enums.file.FileCompressTypeEnum.valueOf(specifiedType.toUpperCase());
            } catch (IllegalArgumentException e) {
                log.warn("[determineCompressType][指定的压缩类型无效] type: {}", specifiedType);
            }
        }

        // 根据文件扩展名自动判断
        String extension = getFileExtension(filePath);
        if (extension == null) {
            return null;
        }

        switch (extension.toLowerCase()) {
            case "jpg":
            case "jpeg":
            case "png":
            case "bmp":
            case "gif":
            case "webp":
                return cn.iocoder.yudao.module.infra.enums.file.FileCompressTypeEnum.IMAGE_AVIF;

            case "mp4":
            case "avi":
            case "mov":
            case "wmv":
            case "flv":
            case "mkv":
                return cn.iocoder.yudao.module.infra.enums.file.FileCompressTypeEnum.MP4_COMPRESS;

            default:
                return null;
        }
    }

    /**
     * 获取文件扩展名
     */
    private String getFileExtension(String filePath) {
        if (filePath == null || filePath.trim().isEmpty()) {
            return null;
        }
        int lastDot = filePath.lastIndexOf('.');
        if (lastDot > 0 && lastDot < filePath.length() - 1) {
            return filePath.substring(lastDot + 1);
        }
        return null;
    }

    /**
     * 执行压缩处理
     */
    private java.io.File executeCompress(java.io.File originalFile, FileCompressTypeEnum compressType, String configJson) {
        try {
            String fileName = originalFile.getName();
            String baseName = getFileBaseName(fileName);

            java.io.File compressedFile = null;

            switch (compressType) {
                case IMAGE_AVIF:
                    // 图片转换为AVIF格式
                    compressedFile = FileCompressUtils.convertImageToAvif(originalFile, originalFile.getParent());
                    break;

                case MP4_COMPRESS:
                    // 视频压缩
                    String outputFileName = baseName + "_compressed.mp4";
                    compressedFile = new java.io.File(originalFile.getParent(), outputFileName);

                    // 解析压缩配置参数
                    cn.iocoder.yudao.module.infra.api.deduplication.dto.CompressConfigDTO.VideoCompressConfig videoConfig =
                        parseVideoConfig(configJson);

                    // 执行视频压缩
                    boolean success = FileCompressUtils.compressVideo(
                        originalFile,
                        compressedFile,
                        videoConfig.getVideoBitrate(),
                        videoConfig.getAudioBitrate(),
                        videoConfig.getResolution()
                    );
                    if (!success) {
                        return null;
                    }
                    break;

                default:
                    log.warn("[executeCompress][不支持的压缩类型] type: {}", compressType);
                    return null;
            }

            if (compressedFile != null && compressedFile.exists() && compressedFile.length() > 0) {
                // 检查压缩效果
                if (!isCompressionEffective(originalFile.length(), compressedFile.length())) {
                    log.warn("[executeCompress][压缩效果不佳，跳过] original: {}, compressed: {}",
                        originalFile.length(), compressedFile.length());
                    compressedFile.delete();
                    return null;
                }

                log.info("[executeCompress][压缩处理成功] original: {}, compressed: {}, type: {}",
                    fileName, compressedFile.getName(), compressType);
                return compressedFile;
            } else {
                log.warn("[executeCompress][压缩文件无效] file: {}", fileName);
                return null;
            }

        } catch (Exception e) {
            log.error("[executeCompress][压缩处理异常] file: {}, type: {}", originalFile.getName(), compressType, e);
            return null;
        }
    }

    /**
     * 解析视频压缩配置
     */
    private cn.iocoder.yudao.module.infra.api.deduplication.dto.CompressConfigDTO.VideoCompressConfig parseVideoConfig(String configJson) {
        cn.iocoder.yudao.module.infra.api.deduplication.dto.CompressConfigDTO.VideoCompressConfig config =
            new cn.iocoder.yudao.module.infra.api.deduplication.dto.CompressConfigDTO.VideoCompressConfig();

        if (configJson != null && !configJson.trim().isEmpty()) {
            try {
                com.fasterxml.jackson.databind.ObjectMapper mapper = new com.fasterxml.jackson.databind.ObjectMapper();
                config = mapper.readValue(configJson, cn.iocoder.yudao.module.infra.api.deduplication.dto.CompressConfigDTO.VideoCompressConfig.class);
            } catch (Exception e) {
                log.warn("[parseVideoConfig][解析视频压缩配置失败，使用默认配置] config: {}", configJson, e);
            }
        }

        return config;
    }

    /**
     * 获取文件基础名称（不含扩展名）
     */
    private String getFileBaseName(String fileName) {
        if (fileName == null || fileName.trim().isEmpty()) {
            return "unknown";
        }
        int lastDot = fileName.lastIndexOf('.');
        return lastDot > 0 ? fileName.substring(0, lastDot) : fileName;
    }

    /**
     * 检查压缩效果是否有效
     */
    private boolean isCompressionEffective(long originalSize, long compressedSize) {
        if (compressedSize >= originalSize) {
            return false; // 压缩后反而更大
        }

        double compressionRatio = (double) compressedSize / originalSize;
        return compressionRatio < 0.95; // 至少节省5%空间才认为有效
    }

    /**
     * 更新文件路径以反映压缩格式
     */
    private String updatePathForCompression(String originalPath, FileCompressTypeEnum compressType) {
        if (compressType == FileCompressTypeEnum.IMAGE_AVIF) {
            // 将扩展名改为.avif
            int lastDot = originalPath.lastIndexOf('.');
            if (lastDot > 0) {
                return originalPath.substring(0, lastDot) + ".avif";
            }
        }
        return originalPath;
    }

    /**
     * 压缩结果类
     */
    private static class CompressResult {
        private final boolean compressed;
        private final java.io.File compressedFile;
        private final String compressedHash;
        private final Long compressedSize;
        private final FileCompressTypeEnum compressType;

        public CompressResult(boolean compressed, java.io.File compressedFile, String compressedHash,
                            Long compressedSize, FileCompressTypeEnum compressType) {
            this.compressed = compressed;
            this.compressedFile = compressedFile;
            this.compressedHash = compressedHash;
            this.compressedSize = compressedSize;
            this.compressType = compressType;
        }

        public boolean isCompressed() { return compressed; }
        public java.io.File getCompressedFile() { return compressedFile; }
        public String getCompressedHash() { return compressedHash; }
        public Long getCompressedSize() { return compressedSize; }
        public FileCompressTypeEnum getCompressType() { return compressType; }
    }

}
