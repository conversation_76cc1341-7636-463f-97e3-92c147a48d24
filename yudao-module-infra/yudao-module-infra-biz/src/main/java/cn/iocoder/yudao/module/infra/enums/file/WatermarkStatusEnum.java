package cn.iocoder.yudao.module.infra.enums.file;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 水印处理状态枚举
 *
 * <AUTHOR>
 */
@AllArgsConstructor
@Getter
public enum WatermarkStatusEnum {

    /**
     * 未处理
     */
    NOT_PROCESSED(0, "未处理"),

    /**
     * 处理中
     */
    PROCESSING(1, "处理中"),

    /**
     * 处理成功
     */
    SUCCESS(2, "处理成功"),

    /**
     * 处理失败
     */
    FAILED(3, "处理失败");

    /**
     * 状态码
     */
    private final Integer code;

    /**
     * 状态描述
     */
    private final String description;

    /**
     * 根据状态码获取枚举
     */
    public static WatermarkStatusEnum getByCode(Integer code) {
        if (code == null) {
            return null;
        }
        for (WatermarkStatusEnum status : values()) {
            if (status.getCode().equals(code)) {
                return status;
            }
        }
        return null;
    }

    /**
     * 是否为最终状态（成功或失败）
     */
    public boolean isFinalStatus() {
        return this == SUCCESS || this == FAILED;
    }

    /**
     * 是否为处理中状态
     */
    public boolean isProcessing() {
        return this == PROCESSING;
    }

}
