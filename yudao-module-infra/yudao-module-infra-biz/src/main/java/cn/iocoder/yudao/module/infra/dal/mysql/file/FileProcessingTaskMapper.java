package cn.iocoder.yudao.module.infra.dal.mysql.file;

import cn.iocoder.yudao.framework.mybatis.core.mapper.BaseMapperX;
import cn.iocoder.yudao.framework.mybatis.core.query.LambdaQueryWrapperX;
import cn.iocoder.yudao.module.infra.dal.dataobject.file.FileProcessingTaskDO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Update;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 文件处理任务 Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface FileProcessingTaskMapper extends BaseMapperX<FileProcessingTaskDO> {

    /**
     * 根据任务ID查询任务
     *
     * @param taskId 任务ID
     * @return 任务记录
     */
    default FileProcessingTaskDO selectByTaskId(String taskId) {
        return selectOne(FileProcessingTaskDO::getTaskId, taskId);
    }

    /**
     * 根据原始文件ID查询任务列表
     *
     * @param originalFileId 原始文件ID
     * @return 任务列表
     */
    default List<FileProcessingTaskDO> selectByOriginalFileId(Long originalFileId) {
        return selectList(new LambdaQueryWrapperX<FileProcessingTaskDO>()
                .eq(FileProcessingTaskDO::getOriginalFileId, originalFileId)
                .orderByDesc(FileProcessingTaskDO::getCreateTime));
    }

    /**
     * 根据状态查询任务列表
     *
     * @param status 状态
     * @param limit 限制数量
     * @return 任务列表
     */
    default List<FileProcessingTaskDO> selectByStatus(Integer status, int limit) {
        return selectList(new LambdaQueryWrapperX<FileProcessingTaskDO>()
                .eq(FileProcessingTaskDO::getStatus, status)
                .orderByAsc(FileProcessingTaskDO::getCreateTime)
                .last("LIMIT " + limit));
    }

    /**
     * 查询待处理的任务
     *
     * @param limit 限制数量
     * @return 待处理任务列表
     */
    default List<FileProcessingTaskDO> selectPendingTasks(int limit) {
        return selectByStatus(FileProcessingTaskDO.Status.PENDING, limit);
    }

    /**
     * 查询处理中的任务
     *
     * @param limit 限制数量
     * @return 处理中任务列表
     */
    default List<FileProcessingTaskDO> selectProcessingTasks(int limit) {
        return selectByStatus(FileProcessingTaskDO.Status.PROCESSING, limit);
    }

    /**
     * 查询超时的处理中任务
     *
     * @param timeoutMinutes 超时分钟数
     * @param limit 限制数量
     * @return 超时任务列表
     */
    default List<FileProcessingTaskDO> selectTimeoutProcessingTasks(int timeoutMinutes, int limit) {
        LocalDateTime timeoutTime = LocalDateTime.now().minusMinutes(timeoutMinutes);
        return selectList(new LambdaQueryWrapperX<FileProcessingTaskDO>()
                .eq(FileProcessingTaskDO::getStatus, FileProcessingTaskDO.Status.PROCESSING)
                .lt(FileProcessingTaskDO::getStartTime, timeoutTime)
                .orderByAsc(FileProcessingTaskDO::getStartTime)
                .last("LIMIT " + limit));
    }

    /**
     * 更新任务状态
     *
     * @param taskId 任务ID
     * @param status 新状态
     * @param progress 进度
     * @return 影响行数
     */
    @Update("UPDATE infra_file_processing_task SET status = #{status}, progress = #{progress}, update_time = NOW() WHERE task_id = #{taskId}")
    int updateStatusAndProgress(@Param("taskId") String taskId, @Param("status") Integer status, @Param("progress") Integer progress);

    /**
     * 更新任务开始时间
     *
     * @param taskId 任务ID
     * @param startTime 开始时间
     * @return 影响行数
     */
    @Update("UPDATE infra_file_processing_task SET start_time = #{startTime}, update_time = NOW() WHERE task_id = #{taskId}")
    int updateStartTime(@Param("taskId") String taskId, @Param("startTime") LocalDateTime startTime);

    /**
     * 更新任务结束时间和结果
     *
     * @param taskId 任务ID
     * @param status 最终状态
     * @param endTime 结束时间
     * @param resultDerivativeIds 结果衍生版本ID数组JSON
     * @param errorMessage 错误信息（可选）
     * @return 影响行数
     */
    @Update("UPDATE infra_file_processing_task SET status = #{status}, end_time = #{endTime}, result_derivative_ids = #{resultDerivativeIds}, error_message = #{errorMessage}, update_time = NOW() WHERE task_id = #{taskId}")
    int updateResult(@Param("taskId") String taskId, 
                    @Param("status") Integer status, 
                    @Param("endTime") LocalDateTime endTime,
                    @Param("resultDerivativeIds") String resultDerivativeIds,
                    @Param("errorMessage") String errorMessage);

    /**
     * 删除过期的已完成任务
     *
     * @param expireDays 过期天数
     * @return 影响行数
     */
    default int deleteExpiredCompletedTasks(int expireDays) {
        LocalDateTime expireTime = LocalDateTime.now().minusDays(expireDays);
        return delete(new LambdaQueryWrapperX<FileProcessingTaskDO>()
                .in(FileProcessingTaskDO::getStatus, 
                    FileProcessingTaskDO.Status.SUCCESS, 
                    FileProcessingTaskDO.Status.FAILED)
                .lt(FileProcessingTaskDO::getEndTime, expireTime));
    }

    /**
     * 统计各状态任务数量
     *
     * @param status 状态
     * @return 任务数量
     */
    default Long countByStatus(Integer status) {
        return selectCount(new LambdaQueryWrapperX<FileProcessingTaskDO>()
                .eq(FileProcessingTaskDO::getStatus, status));
    }

}
