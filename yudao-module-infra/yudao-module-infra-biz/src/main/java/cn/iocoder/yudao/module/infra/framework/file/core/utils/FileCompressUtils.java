package cn.iocoder.yudao.module.infra.framework.file.core.utils;

import cn.hutool.core.io.FileUtil;
import cn.hutool.core.util.StrUtil;
import cn.iocoder.yudao.module.infra.enums.file.FileCompressTypeEnum;
import lombok.extern.slf4j.Slf4j;

import java.io.BufferedReader;
import java.io.File;
import java.io.InputStreamReader;
import java.util.Arrays;
import java.util.HashSet;
import java.util.Set;

/**
 * 文件压缩工具类
 * 提供智能压缩判断和文件格式检测功能
 *
 * <AUTHOR>
 */
@Slf4j
public class FileCompressUtils {

    /**
     * 已压缩的图片格式（不需要再次压缩）
     */
    private static final Set<String> COMPRESSED_IMAGE_FORMATS = new HashSet<>(Arrays.asList(
        "avif", "webp", "heic", "heif", "jxl"
    ));

    /**
     * 已压缩的视频格式（不需要再次压缩）
     */
    private static final Set<String> COMPRESSED_VIDEO_FORMATS = new HashSet<>(Arrays.asList(
        "h264", "h265", "av1", "vp9", "vp8"
    ));

    /**
     * 已压缩的音频格式（不需要再次压缩）
     */
    private static final Set<String> COMPRESSED_AUDIO_FORMATS = new HashSet<>(Arrays.asList(
        "mp3", "aac", "ogg", "opus", "m4a"
    ));

    /**
     * 高压缩率的文档格式（不建议再次压缩）
     */
    private static final Set<String> COMPRESSED_DOCUMENT_FORMATS = new HashSet<>(Arrays.asList(
        "docx", "xlsx", "pptx", "zip", "rar", "7z", "gz", "bz2"
    ));

    /**
     * 检查文件是否已经是压缩格式
     *
     * @param filePath 文件路径
     * @return 是否为已压缩格式
     */
    public static boolean isCompressedFormat(String filePath) {
        if (StrUtil.isBlank(filePath)) {
            return false;
        }

        String extension = getFileExtension(filePath);
        if (StrUtil.isBlank(extension)) {
            return false;
        }

        return COMPRESSED_IMAGE_FORMATS.contains(extension) ||
               COMPRESSED_VIDEO_FORMATS.contains(extension) ||
               COMPRESSED_AUDIO_FORMATS.contains(extension) ||
               COMPRESSED_DOCUMENT_FORMATS.contains(extension);
    }

    /**
     * 检查文件是否需要压缩
     *
     * @param filePath 文件路径
     * @param fileSize 文件大小（字节）
     * @return 是否需要压缩
     */
    public static boolean needsCompression(String filePath, Long fileSize) {
        // 已压缩格式不需要再次压缩
        if (isCompressedFormat(filePath)) {
            log.debug("[needsCompression][文件已是压缩格式，跳过压缩] filePath: {}", filePath);
            return false;
        }

        // 文件太小不需要压缩（小于100KB）
        if (fileSize != null && fileSize < 100 * 1024) {
            log.debug("[needsCompression][文件太小，跳过压缩] filePath: {}, size: {}", filePath, fileSize);
            return false;
        }

        // 检查是否支持压缩
        return FileCompressTypeEnum.isCompressSupported(getFileExtension(filePath));
    }

    /**
     * 获取推荐的压缩类型
     *
     * @param filePath 文件路径
     * @return 推荐的压缩类型，如果不支持则返回null
     */
    public static FileCompressTypeEnum getRecommendedCompressType(String filePath) {
        if (!needsCompression(filePath, null)) {
            return null;
        }

        String extension = getFileExtension(filePath);
        return FileCompressTypeEnum.getRecommendedType(extension);
    }

    /**
     * 检查文件是否可能是压缩后的文件（通过文件名特征判断）
     *
     * @param filePath 文件路径
     * @return 是否可能是压缩后的文件
     */
    public static boolean isPossiblyCompressedFile(String filePath) {
        if (StrUtil.isBlank(filePath)) {
            return false;
        }

        String fileName = getFileName(filePath).toLowerCase();
        
        // 检查文件名是否包含压缩标识
        return fileName.contains("_compressed") ||
               fileName.contains("_optimized") ||
               fileName.contains("_reduced") ||
               fileName.contains("_small") ||
               fileName.contains("_thumb") ||
               fileName.contains("_preview");
    }

    /**
     * 生成压缩后的文件路径
     *
     * @param originalPath 原文件路径
     * @param compressType 压缩类型
     * @return 压缩后的文件路径
     */
    public static String generateCompressedPath(String originalPath, FileCompressTypeEnum compressType) {
        if (StrUtil.isBlank(originalPath) || compressType == null) {
            return originalPath;
        }

        String dir = getFileDirectory(originalPath);
        String fileName = getFileNameWithoutExtension(originalPath);
        String newExtension = getCompressedExtension(compressType);

        return dir + "/" + fileName + "_compressed." + newExtension;
    }

    /**
     * 检查压缩是否有效（压缩后文件大小是否合理）
     *
     * @param originalSize 原文件大小
     * @param compressedSize 压缩后文件大小
     * @param compressType 压缩类型
     * @return 压缩是否有效
     */
    public static boolean isCompressionEffective(Long originalSize, Long compressedSize, FileCompressTypeEnum compressType) {
        if (originalSize == null || compressedSize == null || originalSize <= 0) {
            return false;
        }

        // 压缩后文件更大，压缩无效
        if (compressedSize >= originalSize) {
            log.warn("[isCompressionEffective][压缩后文件更大] originalSize: {}, compressedSize: {}, compressType: {}", 
                originalSize, compressedSize, compressType.getCode());
            return false;
        }

        // 计算压缩率
        double compressionRatio = (double) compressedSize / originalSize;
        
        // 根据压缩类型设置不同的有效性阈值
        double threshold = getCompressionThreshold(compressType);
        
        boolean effective = compressionRatio < threshold;
        log.debug("[isCompressionEffective][压缩效果检查] originalSize: {}, compressedSize: {}, ratio: {:.2f}, threshold: {:.2f}, effective: {}", 
            originalSize, compressedSize, compressionRatio, threshold, effective);
        
        return effective;
    }

    /**
     * 获取压缩有效性阈值
     *
     * @param compressType 压缩类型
     * @return 压缩率阈值（小于此值认为压缩有效）
     */
    private static double getCompressionThreshold(FileCompressTypeEnum compressType) {
        switch (compressType) {
            case MP4_COMPRESS:
                return 0.8; // 视频压缩至少要减少20%
            case IMAGE_AVIF:
            case IMAGE_WEBP:
                return 0.7; // 图片压缩至少要减少30%
            case PDF_COMPRESS:
                return 0.9; // PDF压缩至少要减少10%
            case AUDIO_COMPRESS:
                return 0.8; // 音频压缩至少要减少20%
            default:
                return 0.9; // 默认至少减少10%
        }
    }

    /**
     * 获取压缩后的文件扩展名
     *
     * @param compressType 压缩类型
     * @return 扩展名
     */
    private static String getCompressedExtension(FileCompressTypeEnum compressType) {
        switch (compressType) {
            case MP4_COMPRESS:
                return "mp4";
            case IMAGE_AVIF:
                return "avif";
            case IMAGE_WEBP:
                return "webp";
            case PDF_COMPRESS:
                return "pdf";
            case AUDIO_COMPRESS:
                return "mp3";
            default:
                return "bin";
        }
    }

    /**
     * 获取文件扩展名
     *
     * @param filePath 文件路径
     * @return 扩展名（不含点号）
     */
    private static String getFileExtension(String filePath) {
        if (StrUtil.isBlank(filePath)) {
            return "";
        }
        int lastDotIndex = filePath.lastIndexOf('.');
        if (lastDotIndex > 0 && lastDotIndex < filePath.length() - 1) {
            return filePath.substring(lastDotIndex + 1).toLowerCase();
        }
        return "";
    }

    /**
     * 获取文件名（含扩展名）
     *
     * @param filePath 文件路径
     * @return 文件名
     */
    private static String getFileName(String filePath) {
        if (StrUtil.isBlank(filePath)) {
            return "";
        }
        int lastSlashIndex = Math.max(filePath.lastIndexOf('/'), filePath.lastIndexOf('\\'));
        return lastSlashIndex >= 0 ? filePath.substring(lastSlashIndex + 1) : filePath;
    }

    /**
     * 获取文件名（不含扩展名）
     *
     * @param filePath 文件路径
     * @return 不含扩展名的文件名
     */
    private static String getFileNameWithoutExtension(String filePath) {
        String fileName = getFileName(filePath);
        int lastDotIndex = fileName.lastIndexOf('.');
        return lastDotIndex > 0 ? fileName.substring(0, lastDotIndex) : fileName;
    }

    /**
     * 获取文件目录
     *
     * @param filePath 文件路径
     * @return 文件目录
     */
    private static String getFileDirectory(String filePath) {
        if (StrUtil.isBlank(filePath)) {
            return "";
        }
        int lastSlashIndex = Math.max(filePath.lastIndexOf('/'), filePath.lastIndexOf('\\'));
        return lastSlashIndex >= 0 ? filePath.substring(0, lastSlashIndex) : "";
    }


    /**
     * 检查FFmpeg是否可用
     */
    private static final boolean FFMPEG_AVAILABLE;

    static {
        boolean available = false;
        try {
            // 使用简单的 ProcessBuilder 检查 FFmpeg 是否可用
            ProcessBuilder processBuilder = new ProcessBuilder("ffmpeg", "-version");
            processBuilder.redirectErrorStream(true);
            Process process = processBuilder.start();

            StringBuilder output = new StringBuilder();
            try (BufferedReader reader = new BufferedReader(new InputStreamReader(process.getInputStream()))) {
                String line;
                while ((line = reader.readLine()) != null) {
                    output.append(line).append("\n");
                }
            }

            int exitCode = process.waitFor();
            String version = output.toString();

            if (exitCode == 0 && StrUtil.isNotBlank(version) && version.contains("ffmpeg")) {
                available = true;
                log.info("[FFmpegVideoUtils][FFmpeg可用，版本信息: {}]", version.split("\\n")[0]);
            } else {
                log.warn("[FFmpegVideoUtils][FFmpeg不可用，退出码: {}, 输出: {}]", exitCode, version);
            }
        } catch (Exception e) {
            log.warn("[FFmpegVideoUtils][FFmpeg不可用: {}]", e.getMessage());
        }
        FFMPEG_AVAILABLE = available;
    }

    /**
     * 使用FFmpeg将图片（如jpg/png）优化为avif格式
     * @param imageFile 原始图片文件
     * @param outputDir 输出目录（为null则用系统临时目录）
     * @return 优化后的avif图片文件，失败返回null
     */
    public static File convertImageToAvif(File imageFile, String outputDir) {
        if (imageFile == null || !imageFile.exists()) {
            return null;
        }
        if (!FFMPEG_AVAILABLE) {
            log.warn("[optimizeImageToAvif][FFmpeg不可用，无法优化图片]");
            return null;
        }
        // 输出目录
        if (outputDir == null) {
            outputDir = System.getProperty("java.io.tmpdir");
        }
        File outputDirFile = new File(outputDir);
        if (!outputDirFile.exists()) {
            outputDirFile.mkdirs();
        }
        // 输出文件名
        String baseName = imageFile.getName();
        int dotIdx = baseName.lastIndexOf('.');
        if (dotIdx > 0) baseName = baseName.substring(0, dotIdx);
        String outputFileName = baseName + "_optimized.avif";
        File outputFile = new File(outputDirFile, outputFileName);
        try {
            executeFFmpegCommand("ffmpeg", "-y", "-i", quotePath(imageFile),
                    "-vf" ,"scale='if(gt(iw,2048),2048,iw)':-1" // 图片太大了（16464x10976，约 180MP 超高清），系统在转码过程中内存耗尽，被操作系统“强制终止”了 ==》〉 加缩放，如果原始宽度超过 4096，就缩小；否则保持原尺寸
                    , quotePath(outputFile) );
            if (outputFile.exists() && outputFile.length() > 0 ) {
                log.info("[optimizeImageToAvif][图片({})优化为avif成功]", imageFile.getName());
                return outputFile;
            } else {
                log.warn("[optimizeImageToAvif][图片({})优化为avif失败]", imageFile.getName());
                if (outputFile.exists()) FileUtil.del(outputFile);
                return null;
            }
        } catch (Exception e) {
            log.error("[optimizeImageToAvif][图片({})优化为avif异常]", imageFile.getName(), e);
            if (outputFile.exists()) FileUtil.del(outputFile);
            return null;
        }
    }

    /**
     * 压缩视频文件
     * @param inputFile 输入视频文件
     * @param outputFile 输出视频文件
     * @param videoBitrate 视频码率（kbps）
     * @param audioBitrate 音频码率（kbps）
     * @param videoScale 视频分辨率缩放（如"1280x720"）
     * @return 是否成功
     */
    public static boolean compressVideo(File inputFile, File outputFile, Integer videoBitrate, Integer audioBitrate, String videoScale) {
        if (inputFile == null || !inputFile.exists() || outputFile == null) {
            return false;
        }
        if (!FFMPEG_AVAILABLE) {
            log.warn("[compressVideo][FFmpeg不可用，无法压缩视频]");
            return false;
        }

        try {
            // 构建FFmpeg命令
            String[] command = buildCompressVideoCommand(inputFile, outputFile, videoBitrate, audioBitrate, videoScale);
            executeFFmpegCommand(command);

            if (outputFile.exists() && outputFile.length() > 0) {
                log.info("[compressVideo][视频压缩成功] input: {}, output: {}", inputFile.getName(), outputFile.getName());
                return true;
            } else {
                log.warn("[compressVideo][视频压缩失败，输出文件不存在或为空]");
                return false;
            }
        } catch (Exception e) {
            log.error("[compressVideo][视频压缩异常] input: {}", inputFile.getName(), e);
            return false;
        }
    }

    /**
     * 构建视频压缩命令
     */
    private static String[] buildCompressVideoCommand(File inputFile, File outputFile, Integer videoBitrate, Integer audioBitrate, String videoScale) {
        java.util.List<String> command = new java.util.ArrayList<>();
        command.add("ffmpeg");
        command.add("-y"); // 覆盖输出文件
        command.add("-i");
        command.add(quotePath(inputFile));

        // 视频编码参数
        command.add("-c:v");
        command.add("libx264");
        command.add("-preset");
        command.add("fast");

        // 视频码率
        if (videoBitrate != null && videoBitrate > 0) {
            command.add("-b:v");
            command.add(videoBitrate + "k");
        }

        // 视频分辨率
        if (videoScale != null && !videoScale.trim().isEmpty()) {
            command.add("-s");
            command.add(videoScale);
        }

        // 音频编码参数
        command.add("-c:a");
        command.add("aac");

        // 音频码率
        if (audioBitrate != null && audioBitrate > 0) {
            command.add("-b:a");
            command.add(audioBitrate + "k");
        }

        command.add(quotePath(outputFile));

        return command.toArray(new String[0]);
    }

    /**
     * 转换图片为WebP格式
     * @param inputFile 输入图片文件
     * @param outputFile 输出WebP文件
     * @param quality 质量（1-100）
     * @return 是否成功
     */
    public static boolean convertImageToWebp(File inputFile, File outputFile, Integer quality) {
        if (inputFile == null || !inputFile.exists() || outputFile == null) {
            return false;
        }
        if (!FFMPEG_AVAILABLE) {
            log.warn("[convertImageToWebp][FFmpeg不可用，无法转换图片]");
            return false;
        }

        try {
            executeFFmpegCommand("ffmpeg", "-y", "-i", quotePath(inputFile),
                    "-c:v", "libwebp",
                    "-quality", String.valueOf(quality != null ? quality : 80),
                    "-preset", "default",
                    quotePath(outputFile));

            if (outputFile.exists() && outputFile.length() > 0) {
                log.info("[convertImageToWebp][图片转WebP成功] input: {}, output: {}", inputFile.getName(), outputFile.getName());
                return true;
            } else {
                log.warn("[convertImageToWebp][图片转WebP失败，输出文件不存在或为空]");
                return false;
            }
        } catch (Exception e) {
            log.error("[convertImageToWebp][图片转WebP异常] input: {}", inputFile.getName(), e);
            return false;
        }
    }

    /**
     * 使用多参数方式执行 FFmpeg 命令，避免命令解析问题
     *
     * @param args 命令参数数组
     * @return 命令输出结果
     */
    private static String executeFFmpegCommand(String... args) {
        try {
            log.debug("[executeFFmpegCommand][执行FFmpeg命令，参数: {}]", String.join(" ", args));

            // 使用 ProcessBuilder 执行命令
            ProcessBuilder processBuilder = new ProcessBuilder(args);
            processBuilder.redirectErrorStream(true); // 将错误输出合并到标准输出

            Process process = processBuilder.start();

            // 读取输出
            StringBuilder output = new StringBuilder();
            try (BufferedReader reader = new BufferedReader(new InputStreamReader(process.getInputStream()))) {
                String line;
                while ((line = reader.readLine()) != null) {
                    output.append(line).append("\n");
                }
            }

            // 等待进程完成
            int exitCode = process.waitFor();
            String result = output.toString().trim();

            log.debug("[executeFFmpegCommand][命令执行完成，退出码: {}, 输出: {}]", exitCode, result);
            return result;
        } catch (Exception e) {
            log.error("[executeFFmpegCommand][FFmpeg命令执行异常，参数: {}]", String.join(" ", args), e);
            return "";
        }
    }

    private static String quotePath(File videoFile){
//        return String.format("\"%s\"", videoFile.getAbsolutePath());
        return videoFile.getAbsolutePath();
    }
}
