package cn.iocoder.yudao.module.infra.framework.file.core.utils;

import cn.hutool.core.io.FileUtil;
import cn.hutool.core.util.StrUtil;
import cn.iocoder.yudao.module.infra.enums.file.FileCompressTypeEnum;
import lombok.extern.slf4j.Slf4j;

import java.io.BufferedReader;
import java.io.File;
import java.io.InputStreamReader;
import java.util.Arrays;
import java.util.HashSet;
import java.util.Set;

/**
 * 文件压缩工具类
 * 提供智能压缩判断和文件格式检测功能
 *
 * <AUTHOR>
 */
@Slf4j
public class FileCompressUtils {

    /**
     * 已压缩的图片格式（不需要再次压缩）
     */
    private static final Set<String> COMPRESSED_IMAGE_FORMATS = new HashSet<>(Arrays.asList(
        "avif", "webp", "heic", "heif", "jxl"
    ));

    /**
     * 已压缩的视频格式（不需要再次压缩）
     */
    private static final Set<String> COMPRESSED_VIDEO_FORMATS = new HashSet<>(Arrays.asList(
        "h264", "h265", "av1", "vp9", "vp8"
    ));

    /**
     * 已压缩的音频格式（不需要再次压缩）
     */
    private static final Set<String> COMPRESSED_AUDIO_FORMATS = new HashSet<>(Arrays.asList(
        "mp3", "aac", "ogg", "opus", "m4a"
    ));

    /**
     * 高压缩率的文档格式（不建议再次压缩）
     */
    private static final Set<String> COMPRESSED_DOCUMENT_FORMATS = new HashSet<>(Arrays.asList(
        "docx", "xlsx", "pptx", "zip", "rar", "7z", "gz", "bz2"
    ));

    /**
     * 检查文件是否已经是压缩格式
     *
     * @param filePath 文件路径
     * @return 是否为已压缩格式
     */
    public static boolean isCompressedFormat(String filePath) {
        if (StrUtil.isBlank(filePath)) {
            return false;
        }

        String extension = getFileExtension(filePath);
        if (StrUtil.isBlank(extension)) {
            return false;
        }

        return COMPRESSED_IMAGE_FORMATS.contains(extension) ||
               COMPRESSED_VIDEO_FORMATS.contains(extension) ||
               COMPRESSED_AUDIO_FORMATS.contains(extension) ||
               COMPRESSED_DOCUMENT_FORMATS.contains(extension);
    }

    /**
     * 检查文件是否需要压缩
     *
     * @param filePath 文件路径
     * @param fileSize 文件大小（字节）
     * @return 是否需要压缩
     */
    public static boolean needsCompression(String filePath, Long fileSize) {
        // 已压缩格式不需要再次压缩
        if (isCompressedFormat(filePath)) {
            log.debug("[needsCompression][文件已是压缩格式，跳过压缩] filePath: {}", filePath);
            return false;
        }

        // 文件太小不需要压缩（小于100KB）
        if (fileSize != null && fileSize < 100 * 1024) {
            log.debug("[needsCompression][文件太小，跳过压缩] filePath: {}, size: {}", filePath, fileSize);
            return false;
        }

        // 检查是否支持压缩
        return FileCompressTypeEnum.isCompressSupported(getFileExtension(filePath));
    }

    /**
     * 获取推荐的压缩类型
     *
     * @param filePath 文件路径
     * @return 推荐的压缩类型，如果不支持则返回null
     */
    public static FileCompressTypeEnum getRecommendedCompressType(String filePath) {
        if (!needsCompression(filePath, null)) {
            return null;
        }

        String extension = getFileExtension(filePath);
        return FileCompressTypeEnum.getRecommendedType(extension);
    }

    /**
     * 检查文件是否可能是压缩后的文件（通过文件名特征判断）
     *
     * @param filePath 文件路径
     * @return 是否可能是压缩后的文件
     */
    public static boolean isPossiblyCompressedFile(String filePath) {
        if (StrUtil.isBlank(filePath)) {
            return false;
        }

        String fileName = getFileName(filePath).toLowerCase();
        
        // 检查文件名是否包含压缩标识
        return fileName.contains("_compressed") ||
               fileName.contains("_optimized") ||
               fileName.contains("_reduced") ||
               fileName.contains("_small") ||
               fileName.contains("_thumb") ||
               fileName.contains("_preview");
    }

    /**
     * 生成压缩后的文件路径
     *
     * @param originalPath 原文件路径
     * @param compressType 压缩类型
     * @return 压缩后的文件路径
     */
    public static String generateCompressedPath(String originalPath, FileCompressTypeEnum compressType) {
        if (StrUtil.isBlank(originalPath) || compressType == null) {
            return originalPath;
        }

        String dir = getFileDirectory(originalPath);
        String fileName = getFileNameWithoutExtension(originalPath);
        String newExtension = getCompressedExtension(compressType);

        return dir + "/" + fileName + "_compressed." + newExtension;
    }

    /**
     * 检查压缩是否有效（压缩后文件大小是否合理）
     *
     * @param originalSize 原文件大小
     * @param compressedSize 压缩后文件大小
     * @param compressType 压缩类型
     * @return 压缩是否有效
     */
    public static boolean isCompressionEffective(Long originalSize, Long compressedSize, FileCompressTypeEnum compressType) {
        if (originalSize == null || compressedSize == null || originalSize <= 0) {
            return false;
        }

        // 压缩后文件更大，压缩无效
        if (compressedSize >= originalSize) {
            log.warn("[isCompressionEffective][压缩后文件更大] originalSize: {}, compressedSize: {}, compressType: {}", 
                originalSize, compressedSize, compressType.getCode());
            return false;
        }

        // 计算压缩率
        double compressionRatio = (double) compressedSize / originalSize;
        
        // 根据压缩类型设置不同的有效性阈值
        double threshold = getCompressionThreshold(compressType);
        
        boolean effective = compressionRatio < threshold;
        log.debug("[isCompressionEffective][压缩效果检查] originalSize: {}, compressedSize: {}, ratio: {:.2f}, threshold: {:.2f}, effective: {}", 
            originalSize, compressedSize, compressionRatio, threshold, effective);
        
        return effective;
    }

    /**
     * 获取压缩有效性阈值
     *
     * @param compressType 压缩类型
     * @return 压缩率阈值（小于此值认为压缩有效）
     */
    private static double getCompressionThreshold(FileCompressTypeEnum compressType) {
        switch (compressType) {
            case MP4_COMPRESS:
                return 0.8; // 视频压缩至少要减少20%
            case IMAGE_AVIF:
            case IMAGE_WEBP:
                return 0.7; // 图片压缩至少要减少30%
            case PDF_COMPRESS:
                return 0.9; // PDF压缩至少要减少10%
            case AUDIO_COMPRESS:
                return 0.8; // 音频压缩至少要减少20%
            default:
                return 0.9; // 默认至少减少10%
        }
    }

    /**
     * 获取压缩后的文件扩展名
     *
     * @param compressType 压缩类型
     * @return 扩展名
     */
    private static String getCompressedExtension(FileCompressTypeEnum compressType) {
        switch (compressType) {
            case MP4_COMPRESS:
                return "mp4";
            case IMAGE_AVIF:
                return "avif";
            case IMAGE_WEBP:
                return "webp";
            case PDF_COMPRESS:
                return "pdf";
            case AUDIO_COMPRESS:
                return "mp3";
            default:
                return "bin";
        }
    }

    /**
     * 获取文件扩展名
     *
     * @param filePath 文件路径
     * @return 扩展名（不含点号）
     */
    private static String getFileExtension(String filePath) {
        if (StrUtil.isBlank(filePath)) {
            return "";
        }
        int lastDotIndex = filePath.lastIndexOf('.');
        if (lastDotIndex > 0 && lastDotIndex < filePath.length() - 1) {
            return filePath.substring(lastDotIndex + 1).toLowerCase();
        }
        return "";
    }

    /**
     * 获取文件名（含扩展名）
     *
     * @param filePath 文件路径
     * @return 文件名
     */
    private static String getFileName(String filePath) {
        if (StrUtil.isBlank(filePath)) {
            return "";
        }
        int lastSlashIndex = Math.max(filePath.lastIndexOf('/'), filePath.lastIndexOf('\\'));
        return lastSlashIndex >= 0 ? filePath.substring(lastSlashIndex + 1) : filePath;
    }

    /**
     * 获取文件名（不含扩展名）
     *
     * @param filePath 文件路径
     * @return 不含扩展名的文件名
     */
    private static String getFileNameWithoutExtension(String filePath) {
        String fileName = getFileName(filePath);
        int lastDotIndex = fileName.lastIndexOf('.');
        return lastDotIndex > 0 ? fileName.substring(0, lastDotIndex) : fileName;
    }

    /**
     * 获取文件目录
     *
     * @param filePath 文件路径
     * @return 文件目录
     */
    private static String getFileDirectory(String filePath) {
        if (StrUtil.isBlank(filePath)) {
            return "";
        }
        int lastSlashIndex = Math.max(filePath.lastIndexOf('/'), filePath.lastIndexOf('\\'));
        return lastSlashIndex >= 0 ? filePath.substring(0, lastSlashIndex) : "";
    }


    /**
     * 检查FFmpeg是否可用
     */
    private static final boolean FFMPEG_AVAILABLE;

    static {
        boolean available = false;
        try {
            // 使用简单的 ProcessBuilder 检查 FFmpeg 是否可用
            ProcessBuilder processBuilder = new ProcessBuilder("ffmpeg", "-version");
            processBuilder.redirectErrorStream(true);
            Process process = processBuilder.start();

            StringBuilder output = new StringBuilder();
            try (BufferedReader reader = new BufferedReader(new InputStreamReader(process.getInputStream()))) {
                String line;
                while ((line = reader.readLine()) != null) {
                    output.append(line).append("\n");
                }
            }

            int exitCode = process.waitFor();
            String version = output.toString();

            if (exitCode == 0 && StrUtil.isNotBlank(version) && version.contains("ffmpeg")) {
                available = true;
                log.info("[FFmpegVideoUtils][FFmpeg可用，版本信息: {}]", version.split("\\n")[0]);
            } else {
                log.warn("[FFmpegVideoUtils][FFmpeg不可用，退出码: {}, 输出: {}]", exitCode, version);
            }
        } catch (Exception e) {
            log.warn("[FFmpegVideoUtils][FFmpeg不可用: {}]", e.getMessage());
        }
        FFMPEG_AVAILABLE = available;
    }

    /**
     * 使用FFmpeg将图片（如jpg/png）优化为avif格式
     * @param imageFile 原始图片文件
     * @param outputDir 输出目录（为null则用系统临时目录）
     * @return 优化后的avif图片文件，失败返回null
     */
    public static File convertImageToAvif(File imageFile, String outputDir) {
        return convertImageToAvifWithWatermark(imageFile, outputDir, null, null, null, null);
    }

    /**
     * 使用FFmpeg将图片（如jpg/png）优化为avif格式，支持水印
     * @param imageFile 原始图片文件
     * @param outputDir 输出目录（为null则用系统临时目录）
     * @param watermarkPath 水印图片路径（可选）
     * @param watermarkPosition 水印位置（可选）
     * @param watermarkOpacity 水印透明度（可选）
     * @param watermarkScale 水印大小比例（可选）
     * @return 优化后的avif图片文件，失败返回null
     */
    public static File convertImageToAvifWithWatermark(File imageFile, String outputDir, String watermarkPath,
                                                     String watermarkPosition, Double watermarkOpacity, Double watermarkScale) {
        return convertImageToAvifWithWatermark(imageFile, outputDir, watermarkPath, watermarkPosition,
            watermarkOpacity, watermarkScale, null, null);
    }

    /**
     * 转换图片为AVIF格式，支持水印和logo覆盖
     */
    public static File convertImageToAvifWithWatermark(File imageFile, String outputDir, String watermarkPath,
                                                     String watermarkPosition, Double watermarkOpacity, Double watermarkScale,
                                                     String coverPosition, String coverSize) {
        if (imageFile == null || !imageFile.exists()) {
            log.error("[convertImageToAvifWithWatermark][输入文件不存在] file: {}", imageFile);
            return null;
        }
        if (!FFMPEG_AVAILABLE) {
            log.warn("[convertImageToAvifWithWatermark][FFmpeg不可用，无法优化图片]");
            return null;
        }

        // 检查水印文件
        boolean hasValidWatermark = false;
        if (watermarkPath != null && !watermarkPath.trim().isEmpty()) {
            File watermarkFile = new File(watermarkPath);
            if (watermarkFile.exists() && watermarkFile.isFile()) {
                hasValidWatermark = true;
                log.debug("[convertImageToAvifWithWatermark][使用水印] watermark: {}", watermarkPath);
            } else {
                log.warn("[convertImageToAvifWithWatermark][水印文件不存在，跳过水印处理] watermark: {}", watermarkPath);
            }
        }

        // 输出目录
        if (outputDir == null) {
            outputDir = System.getProperty("java.io.tmpdir");
        }
        File outputDirFile = new File(outputDir);
        if (!outputDirFile.exists()) {
            outputDirFile.mkdirs();
        }

        // 输出文件名
        String baseName = imageFile.getName();
        int dotIdx = baseName.lastIndexOf('.');
        if (dotIdx > 0) baseName = baseName.substring(0, dotIdx);
        String suffix = hasValidWatermark ? "_watermarked_optimized.avif" : "_optimized.avif";
        String outputFileName = baseName + suffix;
        File outputFile = new File(outputDirFile, outputFileName);

        try {
            // 构建FFmpeg命令（支持水印和logo覆盖）
            String[] command = buildAvifCommandWithWatermark(imageFile, outputFile,
                hasValidWatermark ? watermarkPath : null,
                watermarkPosition, watermarkOpacity, watermarkScale, coverPosition, coverSize);

            executeFFmpegCommand(command);
            boolean success = outputFile.exists() && outputFile.length() > 0;

            if (success && outputFile.exists() && outputFile.length() > 0) {
                log.info("[convertImageToAvifWithWatermark][图片转换成功] input: {}, output: {}, size: {} -> {}, watermark: {}",
                    imageFile.getName(), outputFile.getName(), imageFile.length(), outputFile.length(), hasValidWatermark);
                return outputFile;
            } else {
                log.error("[convertImageToAvifWithWatermark][图片转换失败] input: {}, success: {}", imageFile.getName(), success);
                if (outputFile.exists()) FileUtil.del(outputFile);
                return null;
            }
        } catch (Exception e) {
            log.error("[convertImageToAvifWithWatermark][图片转换异常] input: {}", imageFile.getName(), e);
            if (outputFile.exists()) FileUtil.del(outputFile);
            return null;
        }
    }

    /**
     * 使用FFmpeg将图片（如jpg/png）优化为avif格式，支持水印
     * @param imageFile 原始图片文件
     * @param outputDir 输出目录（为null则用系统临时目录）
     * @param watermarkPath 水印图片路径（可选）
     * @param watermarkPosition 水印位置（可选）
     * @param watermarkOpacity 水印透明度（可选）
     * @param watermarkScale 水印大小比例（可选）
     * @return 优化后的avif图片文件，失败返回null
     */
    public static File convertImageToAvif(File imageFile, String outputDir, String watermarkPath,
                                        String watermarkPosition, Double watermarkOpacity, Double watermarkScale) {
        if (imageFile == null || !imageFile.exists()) {
            return null;
        }
        if (!FFMPEG_AVAILABLE) {
            log.warn("[optimizeImageToAvif][FFmpeg不可用，无法优化图片]");
            return null;
        }
        // 输出目录
        if (outputDir == null) {
            outputDir = System.getProperty("java.io.tmpdir");
        }
        File outputDirFile = new File(outputDir);
        if (!outputDirFile.exists()) {
            outputDirFile.mkdirs();
        }
        // 输出文件名
        String baseName = imageFile.getName();
        int dotIdx = baseName.lastIndexOf('.');
        if (dotIdx > 0) baseName = baseName.substring(0, dotIdx);
        String outputFileName = baseName + "_optimized.avif";
        File outputFile = new File(outputDirFile, outputFileName);
        try {
            executeFFmpegCommand("ffmpeg", "-y", "-i", quotePath(imageFile),
                    "-vf" ,"scale='if(gt(iw,2048),2048,iw)':-1" // 图片太大了（16464x10976，约 180MP 超高清），系统在转码过程中内存耗尽，被操作系统“强制终止”了 ==》〉 加缩放，如果原始宽度超过 4096，就缩小；否则保持原尺寸
                    , quotePath(outputFile) );
            if (outputFile.exists() && outputFile.length() > 0 ) {
                log.info("[optimizeImageToAvif][图片({})优化为avif成功]", imageFile.getName());
                return outputFile;
            } else {
                log.warn("[optimizeImageToAvif][图片({})优化为avif失败]", imageFile.getName());
                if (outputFile.exists()) FileUtil.del(outputFile);
                return null;
            }
        } catch (Exception e) {
            log.error("[optimizeImageToAvif][图片({})优化为avif异常]", imageFile.getName(), e);
            if (outputFile.exists()) FileUtil.del(outputFile);
            return null;
        }
    }

    /**
     * 压缩视频文件
     * @param inputFile 输入视频文件
     * @param outputFile 输出视频文件
     * @param videoBitrate 视频码率（kbps）
     * @param audioBitrate 音频码率（kbps）
     * @param videoScale 视频分辨率缩放（如"1280x720"）
     * @return 是否成功
     */
    public static boolean compressVideo(File inputFile, File outputFile, Integer videoBitrate, Integer audioBitrate, String videoScale) {
        if (inputFile == null || !inputFile.exists() || outputFile == null) {
            return false;
        }
        if (!FFMPEG_AVAILABLE) {
            log.warn("[compressVideo][FFmpeg不可用，无法压缩视频]");
            return false;
        }

        try {
            // 构建FFmpeg命令
            String[] command = buildCompressVideoCommand(inputFile, outputFile, videoBitrate, audioBitrate, videoScale);
            executeFFmpegCommand(command);

            if (outputFile.exists() && outputFile.length() > 0) {
                log.info("[compressVideo][视频压缩成功] input: {}, output: {}", inputFile.getName(), outputFile.getName());
                return true;
            } else {
                log.warn("[compressVideo][视频压缩失败，输出文件不存在或为空]");
                return false;
            }
        } catch (Exception e) {
            log.error("[compressVideo][视频压缩异常] input: {}", inputFile.getName(), e);
            return false;
        }
    }

    /**
     * 构建视频压缩命令
     */
    private static String[] buildCompressVideoCommand(File inputFile, File outputFile, Integer videoBitrate, Integer audioBitrate, String videoScale) {
        java.util.List<String> command = new java.util.ArrayList<>();
        command.add("ffmpeg");
        command.add("-y"); // 覆盖输出文件
        command.add("-i");
        command.add(quotePath(inputFile));

        // 视频编码参数
        command.add("-c:v");
        command.add("libx264");
        command.add("-preset");
        command.add("fast");

        // 视频码率
        if (videoBitrate != null && videoBitrate > 0) {
            command.add("-b:v");
            command.add(videoBitrate + "k");
        }

        // 视频分辨率
        if (videoScale != null && !videoScale.trim().isEmpty()) {
            command.add("-s");
            command.add(videoScale);
        }

        // 音频编码参数
        command.add("-c:a");
        command.add("aac");

        // 音频码率
        if (audioBitrate != null && audioBitrate > 0) {
            command.add("-b:a");
            command.add(audioBitrate + "k");
        }

        // 在输出文件前添加 faststart
        command.add("-movflags");
        command.add("faststart");

        command.add(quotePath(outputFile));

        return command.toArray(new String[0]);
    }

    /**
     * 转换图片为WebP格式
     * @param inputFile 输入图片文件
     * @param outputFile 输出WebP文件
     * @param quality 质量（1-100）
     * @return 是否成功
     */
    public static boolean convertImageToWebp(File inputFile, File outputFile, Integer quality) {
        if (inputFile == null || !inputFile.exists() || outputFile == null) {
            return false;
        }
        if (!FFMPEG_AVAILABLE) {
            log.warn("[convertImageToWebp][FFmpeg不可用，无法转换图片]");
            return false;
        }

        try {
            executeFFmpegCommand("ffmpeg", "-y", "-i", quotePath(inputFile),
                    "-c:v", "libwebp",
                    "-quality", String.valueOf(quality != null ? quality : 80),
                    "-preset", "default",
                    quotePath(outputFile));

            if (outputFile.exists() && outputFile.length() > 0) {
                log.info("[convertImageToWebp][图片转WebP成功] input: {}, output: {}", inputFile.getName(), outputFile.getName());
                return true;
            } else {
                log.warn("[convertImageToWebp][图片转WebP失败，输出文件不存在或为空]");
                return false;
            }
        } catch (Exception e) {
            log.error("[convertImageToWebp][图片转WebP异常] input: {}", inputFile.getName(), e);
            return false;
        }
    }

    /**
     * 使用多参数方式执行 FFmpeg 命令，避免命令解析问题
     *
     * @param args 命令参数数组
     * @return 命令输出结果
     */
    private static String executeFFmpegCommand(String... args) {
        try {
            log.debug("[executeFFmpegCommand][执行FFmpeg命令，参数: {}]", String.join(" ", args));

            // 使用 ProcessBuilder 执行命令
            ProcessBuilder processBuilder = new ProcessBuilder(args);
            processBuilder.redirectErrorStream(true); // 将错误输出合并到标准输出

            Process process = processBuilder.start();

            // 读取输出
            StringBuilder output = new StringBuilder();
            try (BufferedReader reader = new BufferedReader(new InputStreamReader(process.getInputStream()))) {
                String line;
                while ((line = reader.readLine()) != null) {
                    output.append(line).append("\n");
                }
            }

            // 等待进程完成
            int exitCode = process.waitFor();
            String result = output.toString().trim();

            log.debug("[executeFFmpegCommand][命令执行完成，退出码: {}, 输出: {}]", exitCode, result);
            return result;
        } catch (Exception e) {
            log.error("[executeFFmpegCommand][FFmpeg命令执行异常，参数: {}]", String.join(" ", args), e);
            return "";
        }
    }

    private static String quotePath(File videoFile){
//        return String.format("\"%s\"", videoFile.getAbsolutePath());
        return videoFile.getAbsolutePath();
    }

    /**
     * 构建带水印的AVIF转换命令
     */
    private static String[] buildAvifCommandWithWatermark(File inputFile, File outputFile, String watermarkPath,
                                                        String watermarkPosition, Double watermarkOpacity, Double watermarkScale) {
        return buildAvifCommandWithWatermark(inputFile, outputFile, watermarkPath, watermarkPosition,
            watermarkOpacity, watermarkScale, null, null);
    }

    /**
     * 构建带水印的AVIF转换命令（支持logo覆盖）
     */
    private static String[] buildAvifCommandWithWatermark(File inputFile, File outputFile, String watermarkPath,
                                                        String watermarkPosition, Double watermarkOpacity, Double watermarkScale,
                                                        String coverPosition, String coverSize) {
        java.util.List<String> command = new java.util.ArrayList<>();
        command.add("ffmpeg");
        command.add("-y"); // 覆盖输出文件
        command.add("-i");
        command.add(quotePath(inputFile));

        boolean hasWatermark = watermarkPath != null && !watermarkPath.trim().isEmpty();
        File watermarkFile = null;

        // 如果有水印，添加水印输入
        if (hasWatermark) {
            watermarkFile = new File(watermarkPath);
            if (watermarkFile.exists()) {
                command.add("-i");
                command.add(quotePath(watermarkFile));
            } else {
                hasWatermark = false; // 水印文件不存在，取消水印
            }
        }

        // 构建滤镜
        if (hasWatermark) {
            // 使用复杂滤镜图处理水印和logo覆盖
            String filterComplex = buildImageWatermarkFilterComplex(watermarkPosition, watermarkOpacity, watermarkScale,
                coverPosition, coverSize);
            command.add("-filter_complex");
            command.add(filterComplex);
        } else if (coverPosition != null && !coverPosition.trim().isEmpty()) {
            // 只有logo覆盖，没有水印
            String filterComplex = buildImageWatermarkFilterComplex(null, null, null, coverPosition, coverSize);
            command.add("-filter_complex");
            command.add(filterComplex);
        } else {
            // 只有基础缩放
            command.add("-vf");
            command.add("scale='if(gt(iw,2048),2048,iw)':-1");
        }

        command.add(quotePath(outputFile));

        return command.toArray(new String[0]);
    }

    /**
     * 构建图片水印的复杂滤镜图（支持logo覆盖）
     */
    private static String buildImageWatermarkFilterComplex(String position, Double opacity, Double scale) {
        return buildImageWatermarkFilterComplex(position, opacity, scale, null, null);
    }

    /**
     * 构建图片水印的复杂滤镜图（支持logo覆盖）
     *
     * @param position 水印位置
     * @param opacity 水印透明度
     * @param scale 水印缩放比例
     * @param coverPosition 需要覆盖的原logo位置（可选）
     * @param coverSize 覆盖区域大小，格式"width:height"（可选）
     */
    private static String buildImageWatermarkFilterComplex(String position, Double opacity, Double scale,
                                                         String coverPosition, String coverSize) {
        // 设置默认值
        if (opacity == null) opacity = 0.8; // 提高默认透明度，更容易看见
        if (scale == null) scale = 0.1;

        StringBuilder filter = new StringBuilder();

        // 主图缩放
        filter.append("[0:v]scale='if(gt(iw,2048),2048,iw)':-1[main]");

        // 如果需要覆盖原logo，使用固定坐标的drawbox
        if (coverPosition != null && !coverPosition.trim().isEmpty()) {
            String drawboxParams = getFixedDrawboxParams(coverPosition, coverSize);
            filter.append(";[main]drawbox=").append(drawboxParams)
                  .append(":color=white@1:t=fill[clean]");
        } else {
            filter.append(";[main]null[clean]"); // 不覆盖时直接传递
        }

        // 如果有水印，处理水印
        if (position != null && !position.trim().isEmpty()) {
            filter.append(";");

            // 水印处理：使用固定大小缩放 + 透明度
            String fixedWidth = calculateWatermarkWidth(scale);
            filter.append("[1:v]scale=").append(fixedWidth).append(":-1");
            filter.append(",format=rgba");

            // 只有当透明度不是1.0时才添加透明度滤镜
            if (opacity < 1.0) {
                filter.append(",colorchannelmixer=aa=").append(opacity);
            }
            filter.append("[watermark];");

            // 叠加水印 - 使用overlay的相对坐标
            String overlayPosition = getOverlayPosition(position);
            filter.append("[clean][watermark]overlay=").append(overlayPosition);
        }

        return filter.toString();
    }

    /**
     * 构建视频水印的复杂滤镜图（支持logo覆盖）
     */
    private static String buildVideoWatermarkFilterComplex(String position, Double opacity, Double scale, String videoScale) {
        return buildVideoWatermarkFilterComplex(position, opacity, scale, videoScale, null, null);
    }

    /**
     * 构建视频水印的复杂滤镜图（支持logo覆盖）
     *
     * @param position 水印位置
     * @param opacity 水印透明度
     * @param scale 水印缩放比例
     * @param videoScale 视频缩放参数
     * @param coverPosition 需要覆盖的原logo位置（可选）
     * @param coverSize 覆盖区域大小，格式"width:height"（可选）
     */
    private static String buildVideoWatermarkFilterComplex(String position, Double opacity, Double scale, String videoScale,
                                                         String coverPosition, String coverSize) {
        // 设置默认值
        if (opacity == null) opacity = 0.8; // 提高默认透明度，更容易看见
        if (scale == null) scale = 0.1;

        StringBuilder filter = new StringBuilder();

        // 主视频缩放
        if (videoScale != null && !videoScale.trim().isEmpty()) {
            filter.append("[0:v]scale=").append(videoScale).append("[main]");
        } else {
            filter.append("[0:v]null[main]"); // 不缩放，直接传递
        }

        // 如果需要覆盖原logo，使用固定坐标的drawbox
        if (coverPosition != null && !coverPosition.trim().isEmpty()) {
            String drawboxParams = getFixedDrawboxParams(coverPosition, coverSize);
            filter.append(";[main]drawbox=").append(drawboxParams)
                  .append(":color=black@1:t=fill[clean]"); // 视频用黑色覆盖
        } else {
            filter.append(";[main]null[clean]"); // 不覆盖时直接传递
        }

        // 如果有水印，处理水印
        if (position != null && !position.trim().isEmpty()) {
            filter.append(";");

            // 水印处理：使用固定大小缩放 + 透明度
            String fixedWidth = calculateWatermarkWidth(scale);
            /*
            这个地方是 bug ⚠️
            这里的 iw、ih 指的是 logo 输入本身的宽高。
            scale=iw*1.0:-1 实际上相当于没缩放，但会让 ffmpeg 重新计算一次尺寸。
            某些情况下（比如 overlay 默认行为），可能会导致 ffmpeg 认为 水印对齐的基准点不是左上角，而是居中。
             */
            filter.append("[1:v]scale=").append(fixedWidth).append(":-1");
            filter.append(",format=rgba");

            // 只有当透明度不是1.0时才添加透明度滤镜
            if (opacity < 1.0) {
                filter.append(",colorchannelmixer=aa=").append(opacity);
            }
            filter.append("[watermark];");

            // 叠加水印 - 使用overlay的相对坐标
            String overlayPosition = getOverlayPosition(position);
            filter.append("[clean][watermark]overlay=").append(overlayPosition);
        }

        return filter.toString();
    }

    /**
     * 计算水印固定宽度
     *
     * @param scale 缩放比例参数
     * @return 固定宽度像素值，范围50-500px
     */
    private static String calculateWatermarkWidth(Double scale) {
//        if (scale == null) scale = 0.1;
//
//        // 基础计算：scale=0.1 对应 200px
//        // scale=0.05 -> 100px, scale=0.1 -> 200px, scale=0.15 -> 300px, scale=0.2 -> 400px
//        int baseWidth = (int) (200 * scale / 0.1);
//
//        // 限制在合理范围内：最小50px，最大500px
//        return Math.max(50, Math.min(500, baseWidth));
        if (scale == null) scale = 1d;
        if(scale > 1){
            return scale + ""; //  scale=160:-1 .. 把宽度缩放到 160 像素，高度自动按比例缩放（-1 表示保持纵横比）。
        }else{
            return "iw*" + scale; // scale=iw*0.3:-1  宽度缩放为原来的 30%，高度自动等比。
        }
    }

    /**
     * 获取固定坐标的drawbox参数（支持9宫格定位）
     * 使用固定坐标，适合已知尺寸的图片/视频
     *
     * @param position 位置（9宫格数字或名称）
     * @param coverSize 覆盖大小，格式"width:height"
     * @return 完整的drawbox参数字符串
     */
    private static String getFixedDrawboxParams(String position, String coverSize) {
        // 解析覆盖大小
        int coverWidth = 200;  // 默认宽度
        int coverHeight = 150; // 默认高度

        if (coverSize != null && coverSize.contains(":")) {
            String[] parts = coverSize.split(":");
            try {
                coverWidth = Integer.parseInt(parts[0]);
                coverHeight = Integer.parseInt(parts[1]);
            } catch (NumberFormatException e) {
                // 使用默认值
            }
        }

        String coords = position;//getFixedDrawboxPosition(position, coverWidth, coverHeight);
        return coords + ":w=" + coverWidth + ":h=" + coverHeight;
    }

    /**
     * 获取固定坐标的drawbox位置
     * 基于常见的图片/视频尺寸进行固定定位
     */
    private static String getFixedDrawboxPosition(String position, int coverWidth, int coverHeight) {
        if (position == null || position.trim().isEmpty()) {
            position = "top-right";
        }

        String pos = position.toLowerCase().trim();

        // 基于常见尺寸的固定坐标
        // 假设图片/视频尺寸范围：800x600 到 2048x1536
        switch (pos) {
            case "1":
            case "top-left":
            case "topleft":
                return "x=10:y=10";

            case "2":
            case "top-center":
            case "topcenter":
                return "x=924:y=10"; // 大约中心位置 (1920-200)/2 + 10

            case "3":
            case "top-right":
            case "topright":
                return "x=1710:y=10"; // 1920-200-10

            case "4":
            case "middle-left":
            case "middleleft":
            case "left":
                return "x=10:y=465"; // 大约中心位置 (1080-150)/2

            case "5":
            case "center":
            case "middle":
                return "x=860:y=465"; // 中心位置

            case "6":
            case "middle-right":
            case "middleright":
            case "right":
                return "x=1710:y=465";

            case "7":
            case "bottom-left":
            case "bottomleft":
                return "x=10:y=920"; // 1080-150-10

            case "8":
            case "bottom-center":
            case "bottomcenter":
                return "x=860:y=920";

            case "9":
            case "bottom-right":
            case "bottomright":
                return "x=1710:y=920";

            default:
                // 如果是数字格式 "x:y"，直接使用
                if (position.matches("\\d+:\\d+")) {
                    return "x=" + position.replace(":", ":y=");
                }
                return "x=1710:y=10"; // 默认右上角
        }
    }

    /**
     * 获取相对坐标的drawbox参数（支持9宫格定位）
     * 使用当前流的w、h变量进行相对定位
     *
     * @param position 位置（9宫格数字或名称）
     * @param coverSize 覆盖大小，格式"width:height"
     * @return [0]=完整的drawbox参数字符串
     */
    private static String[] getRelativeDrawboxCoords(String position, String coverSize) {
        // 解析覆盖大小
        int coverWidth = 160;  // 默认宽度
        int coverHeight = 60;  // 默认高度

        if (coverSize != null && coverSize.contains(":")) {
            String[] parts = coverSize.split(":");
            try {
                coverWidth = Integer.parseInt(parts[0]);
                coverHeight = Integer.parseInt(parts[1]);
            } catch (NumberFormatException e) {
                // 使用默认值
            }
        }

        String coords = getRelativeDrawboxPosition(position, coverWidth, coverHeight);
        return new String[]{coords + ":w=" + coverWidth + ":h=" + coverHeight};
    }

    /**
     * 获取相对坐标的drawbox位置
     */
    private static String getRelativeDrawboxPosition(String position, int coverWidth, int coverHeight) {
        if (position == null || position.trim().isEmpty()) {
            position = "top-right";
        }

        String pos = position.toLowerCase().trim();
        int margin = 10; // 边距

        // 使用当前流的w、h变量进行相对定位
        switch (pos) {
            case "1":
            case "top-left":
            case "topleft":
                return "x=" + margin + ":y=" + margin;

            case "2":
            case "top-center":
            case "topcenter":
                return "x=(w-" + coverWidth + ")/2:y=" + margin;

            case "3":
            case "top-right":
            case "topright":
                return "x=w-" + (coverWidth + margin) + ":y=" + margin;

            case "4":
            case "middle-left":
            case "middleleft":
            case "left":
                return "x=" + margin + ":y=(h-" + coverHeight + ")/2";

            case "5":
            case "center":
            case "middle":
                return "x=(w-" + coverWidth + ")/2:y=(h-" + coverHeight + ")/2";

            case "6":
            case "middle-right":
            case "middleright":
            case "right":
                return "x=w-" + (coverWidth + margin) + ":y=(h-" + coverHeight + ")/2";

            case "7":
            case "bottom-left":
            case "bottomleft":
                return "x=" + margin + ":y=h-" + (coverHeight + margin);

            case "8":
            case "bottom-center":
            case "bottomcenter":
                return "x=(w-" + coverWidth + ")/2:y=h-" + (coverHeight + margin);

            case "9":
            case "bottom-right":
            case "bottomright":
                return "x=w-" + (coverWidth + margin) + ":y=h-" + (coverHeight + margin);

            default:
                // 如果是数字格式 "x:y"，直接使用
                if (position.matches("\\d+:\\d+")) {
                    return "x=" + position.replace(":", ":y=");
                }
                return "x=w-" + (coverWidth + margin) + ":y=" + margin; // 默认右上角
        }
    }

    /**
     * 获取overlay滤镜的位置参数（支持9宫格定位）
     * overlay滤镜支持W、H、w、h变量
     */
    private static String getOverlayPosition(String position) {
        if (position == null || position.trim().isEmpty()) {
            position = "top-right";
        }

        String pos = position.toLowerCase().trim();

        // overlay滤镜支持W、H、w、h变量
        switch (pos) {
            case "1":
            case "top-left":
            case "topleft":
                return "x=10:y=10";

            case "2":
            case "top-center":
            case "topcenter":
                return "x=(W-w)/2:y=10";

            case "3":
            case "top-right":
            case "topright":
                return "x=W-w-10:y=10";

            case "4":
            case "middle-left":
            case "middleleft":
            case "left":
                return "x=10:y=(H-h)/2";

            case "5":
            case "center":
            case "middle":
                return "x=(W-w)/2:y=(H-h)/2";

            case "6":
            case "middle-right":
            case "middleright":
            case "right":
                return "x=W-w-10:y=(H-h)/2";

            case "7":
            case "bottom-left":
            case "bottomleft":
                return "x=10:y=H-h-10";

            case "8":
            case "bottom-center":
            case "bottomcenter":
                return "x=(W-w)/2:y=H-h-10";

            case "9":
            case "bottom-right":
            case "bottomright":
                return "x=W-w-10:y=H-h-10";

            default:
                // 如果是数字格式 "x:y"，直接使用
                if (position.matches("\\d+:\\d+")) {
                    return "x=" + position.replace(":", ":y=");
                }
                return "x=W-w-10:y=10"; // 默认右上角
        }
    }

    /**
     * 获取drawbox滤镜的位置参数（支持9宫格定位）
     * drawbox滤镜不支持overlay的W、H、w、h变量，需要使用固定坐标
     *
     * 9宫格位置示意图：
     * +---+---+---+
     * | 1 | 2 | 3 |  1=top-left, 2=top-center, 3=top-right
     * +---+---+---+
     * | 4 | 5 | 6 |  4=middle-left, 5=center, 6=middle-right
     * +---+---+---+
     * | 7 | 8 | 9 |  7=bottom-left, 8=bottom-center, 9=bottom-right
     * +---+---+---+
     */
    private static String getDrawboxPosition(String position) {
        if (position == null || position.trim().isEmpty()) {
            position = "top-right";
        }

        String pos = position.toLowerCase().trim();

        // drawbox使用固定坐标，不能使用W、H、w、h变量
        // 这里使用相对安全的固定位置
        switch (pos) {
            case "1":
            case "top-left":
            case "topleft":
                return "10:10";

            case "2":
            case "top-center":
            case "topcenter":
                return "512:10"; // 假设1024宽度的中心

            case "3":
            case "top-right":
            case "topright":
                return "814:10"; // 1024-200-10 = 814

            case "4":
            case "middle-left":
            case "middleleft":
            case "left":
                return "10:290"; // 假设576高度的中心

            case "5":
            case "center":
            case "middle":
                return "412:238"; // 中心位置

            case "6":
            case "middle-right":
            case "middleright":
            case "right":
                return "814:290";

            case "7":
            case "bottom-left":
            case "bottomleft":
                return "10:466"; // 576-100-10 = 466

            case "8":
            case "bottom-center":
            case "bottomcenter":
                return "412:466";

            case "9":
            case "bottom-right":
            case "bottomright":
                return "814:466";

            default:
                // 如果是数字格式 "x:y"，直接使用
                if (position.matches("\\d+:\\d+")) {
                    return position;
                }
                return "814:10"; // 默认右上角
        }
    }

    /**
     * 获取水印位置参数（支持9宫格定位）
     *
     * 9宫格位置示意图：
     * +---+---+---+
     * | 1 | 2 | 3 |  1=top-left, 2=top-center, 3=top-right
     * +---+---+---+
     * | 4 | 5 | 6 |  4=middle-left, 5=center, 6=middle-right
     * +---+---+---+
     * | 7 | 8 | 9 |  7=bottom-left, 8=bottom-center, 9=bottom-right
     * +---+---+---+
     */
    private static String getWatermarkPosition(String position) {
        if (position == null || position.trim().isEmpty()) {
            position = "top-right";
        }

        String pos = position.toLowerCase().trim();

        // 支持数字1-9的9宫格定位
        switch (pos) {
            case "1":
            case "top-left":
            case "topleft":
                return "10:10";

            case "2":
            case "top-center":
            case "topcenter":
                return "(W-w)/2:10";

            case "3":
            case "top-right":
            case "topright":
                return "W-w-10:10";

            case "4":
            case "middle-left":
            case "middleleft":
            case "left":
                return "10:(H-h)/2";

            case "5":
            case "center":
            case "middle":
                return "(W-w)/2:(H-h)/2";

            case "6":
            case "middle-right":
            case "middleright":
            case "right":
                return "W-w-10:(H-h)/2";

            case "7":
            case "bottom-left":
            case "bottomleft":
                return "10:H-h-10";

            case "8":
            case "bottom-center":
            case "bottomcenter":
                return "(W-w)/2:H-h-10";

            case "9":
            case "bottom-right":
            case "bottomright":
                return "W-w-10:H-h-10";

            default:
                // 如果是数字格式 "x:y"，直接使用
                if (position.matches("\\d+:\\d+")) {
                    return position;
                }
                return "W-w-10:10"; // 默认右上角(位置3)
        }
    }

    /**
     * 压缩视频文件，支持水印
     */
    public static boolean compressVideo(File inputFile, File outputFile, Integer videoBitrate,
                                      Integer audioBitrate, String videoScale, String watermarkPath,
                                      String watermarkPosition, Double watermarkOpacity, Double watermarkScale) {
        return compressVideo(inputFile, outputFile, videoBitrate, audioBitrate, videoScale,
            watermarkPath, watermarkPosition, watermarkOpacity, watermarkScale, null, null);
    }

    /**
     * 压缩视频文件，支持水印和logo覆盖
     */
    public static boolean compressVideo(File inputFile, File outputFile, Integer videoBitrate,
                                      Integer audioBitrate, String videoScale, String watermarkPath,
                                      String watermarkPosition, Double watermarkOpacity, Double watermarkScale,
                                      String coverPosition, String coverSize) {
        if (inputFile == null || !inputFile.exists() || outputFile == null) {
            log.error("[compressVideo][参数无效] inputFile: {}, outputFile: {}", inputFile, outputFile);
            return false;
        }
        if (!FFMPEG_AVAILABLE) {
            log.warn("[compressVideo][FFmpeg不可用，无法压缩视频]");
            return false;
        }

        // 检查水印文件
        boolean hasValidWatermark = false;
        if (watermarkPath != null && !watermarkPath.trim().isEmpty()) {
            File watermarkFile = new File(watermarkPath);
            if (watermarkFile.exists() && watermarkFile.isFile()) {
                hasValidWatermark = true;
                log.debug("[compressVideo][使用水印] watermark: {}", watermarkPath);
            } else {
                log.warn("[compressVideo][水印文件不存在，跳过水印处理] watermark: {}", watermarkPath);
            }
        }

        try {
            // 构建FFmpeg命令（支持水印和logo覆盖）
            String[] command = buildCompressVideoCommandWithWatermark(inputFile, outputFile, videoBitrate,
                audioBitrate, videoScale,
                hasValidWatermark ? watermarkPath : null,
                watermarkPosition, watermarkOpacity, watermarkScale, coverPosition, coverSize);
            executeFFmpegCommand(command);

            if (outputFile.exists() && outputFile.length() > 0) {
                log.info("[compressVideo][视频压缩成功] input: {}, output: {}, size: {} -> {}, watermark: {}",
                    inputFile.getName(), outputFile.getName(), inputFile.length(), outputFile.length(), hasValidWatermark);
                return true;
            } else {
                log.error("[compressVideo][视频压缩失败，输出文件不存在或为空] input: {}", inputFile.getName());
                if (outputFile.exists()) {
                    outputFile.delete();
                }
                return false;
            }
        } catch (Exception e) {
            log.error("[compressVideo][视频压缩异常] input: {}", inputFile.getName(), e);
            if (outputFile.exists()) {
                outputFile.delete();
            }
            return false;
        }
    }

    /**
     * 构建带水印的视频压缩命令
     */
    private static String[] buildCompressVideoCommandWithWatermark(File inputFile, File outputFile,
                                                                 Integer videoBitrate, Integer audioBitrate, String videoScale,
                                                                 String watermarkPath, String watermarkPosition,
                                                                 Double watermarkOpacity, Double watermarkScale) {
        return buildCompressVideoCommandWithWatermark(inputFile, outputFile, videoBitrate, audioBitrate, videoScale,
            watermarkPath, watermarkPosition, watermarkOpacity, watermarkScale, null, null);
    }

    /**
     * 构建带水印的视频压缩命令（支持logo覆盖）
     */
    private static String[] buildCompressVideoCommandWithWatermark(File inputFile, File outputFile,
                                                                 Integer videoBitrate, Integer audioBitrate, String videoScale,
                                                                 String watermarkPath, String watermarkPosition,
                                                                 Double watermarkOpacity, Double watermarkScale,
                                                                 String coverPosition, String coverSize) {
        java.util.List<String> command = new java.util.ArrayList<>();
        command.add("ffmpeg");
        command.add("-y"); // 覆盖输出文件
        command.add("-i");
        command.add(quotePath(inputFile));

        boolean hasWatermark = watermarkPath != null && !watermarkPath.trim().isEmpty();
        File watermarkFile = null;

        // 如果有水印，添加水印输入
        if (hasWatermark) {
            watermarkFile = new File(watermarkPath);
            if (watermarkFile.exists()) {
                command.add("-i");
                command.add(quotePath(watermarkFile));
            } else {
                hasWatermark = false; // 水印文件不存在，取消水印
            }
        }

        // 视频编码参数
        command.add("-c:v");
        command.add("libx264");
        command.add("-preset");
        command.add("medium");

        // 视频码率
        if (videoBitrate != null && videoBitrate > 0) {
            command.add("-b:v");
            command.add(videoBitrate + "k");
        }

        // 音频编码参数
        command.add("-c:a");
        command.add("aac");
        if (audioBitrate != null && audioBitrate > 0) {
            command.add("-b:a");
            command.add(audioBitrate + "k");
        }

        // 构建滤镜
        if (hasWatermark) {
            // 使用复杂滤镜图处理水印和logo覆盖
            String filterComplex = buildVideoWatermarkFilterComplex(watermarkPosition, watermarkOpacity, watermarkScale, videoScale,
                coverPosition, coverSize);
            command.add("-filter_complex");
            command.add(filterComplex);
        } else if (coverPosition != null && !coverPosition.trim().isEmpty()) {
            // 只有logo覆盖，没有水印
            String filterComplex = buildVideoWatermarkFilterComplex(null, null, null, videoScale, coverPosition, coverSize);
            command.add("-filter_complex");
            command.add(filterComplex);
        } else if (videoScale != null && !videoScale.trim().isEmpty()) {
            // 只有视频缩放
            command.add("-vf");
            command.add("scale=" + videoScale);
        }

        command.add(quotePath(outputFile));

        return command.toArray(new String[0]);
    }
}
