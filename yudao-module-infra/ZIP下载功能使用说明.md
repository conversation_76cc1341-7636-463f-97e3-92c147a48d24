# ZIP下载功能使用说明

## 🎯 功能概述

通用的ZIP下载功能，支持从多个网络URL下载文件并打包成ZIP文件，具有以下特性：

- ✅ **批量下载**：支持多个URL同时下载
- ✅ **ZIP打包**：自动打包成ZIP文件
- ✅ **密码保护**：支持ZIP文件密码保护
- ✅ **编码支持**：支持不同编码格式
- ✅ **压缩级别**：可配置压缩级别（0-9）
- ✅ **文件重命名**：支持自定义ZIP内文件名
- ✅ **目录结构**：支持创建目录结构
- ✅ **异步处理**：支持异步下载大量文件
- ✅ **流式下载**：支持直接返回ZIP流
- ✅ **错误处理**：支持跳过失败文件

## 🔧 API接口

### 1. 同步下载ZIP文件
```http
POST /infra/zip/download
Content-Type: application/json

{
  "urls": [
    "https://example.com/file1.jpg",
    "https://example.com/file2.pdf",
    "https://example.com/file3.txt"
  ],
  "zipFileName": "download.zip",
  "encoding": "UTF-8",
  "compressionLevel": 6,
  "password": "123456",
  "skipFailedFiles": true
}
```

### 2. 流式下载ZIP文件
```http
POST /infra/zip/download/stream
Content-Type: application/json

{
  "urls": ["https://example.com/file1.jpg"],
  "zipFileName": "download.zip"
}
```

### 3. 异步下载ZIP文件
```http
POST /infra/zip/download/async
Content-Type: application/json

{
  "urls": ["https://example.com/file1.jpg"],
  "zipFileName": "download.zip"
}
```

### 4. 查询异步任务状态
```http
GET /infra/zip/task/{taskId}
```

## 📋 请求参数详解

### ZipDownloadReqDTO 参数说明

| 参数名 | 类型 | 必填 | 默认值 | 说明 |
|--------|------|------|--------|------|
| urls | List<String> | ✅ | - | URL集合 |
| urlToFileNameMap | Map<String,String> | ❌ | - | URL到文件名映射 |
| zipFileName | String | ✅ | - | ZIP文件名 |
| encoding | String | ❌ | UTF-8 | ZIP内文件编码格式 |
| compressionLevel | Integer | ❌ | 6 | 压缩级别(0-9) |
| password | String | ❌ | - | ZIP解压密码 |
| createDirectories | Boolean | ❌ | false | 是否创建目录结构 |
| directoryPrefix | String | ❌ | - | 目录前缀 |
| downloadTimeout | Integer | ❌ | 30 | 下载超时时间(秒) |
| maxFileSizeMB | Integer | ❌ | 100 | 最大文件大小(MB) |
| maxFileCount | Integer | ❌ | 50 | 最大文件数量 |
| skipFailedFiles | Boolean | ❌ | true | 是否跳过失败文件 |
| userAgent | String | ❌ | YudaoDownloader/1.0 | 用户代理 |
| headers | Map<String,String> | ❌ | - | 请求头信息 |

## 🎨 使用示例

### 示例1：基础下载
```json
{
  "urls": [
    "https://example.com/image1.jpg",
    "https://example.com/document.pdf"
  ],
  "zipFileName": "files.zip"
}
```

### 示例2：带密码保护
```json
{
  "urls": [
    "https://example.com/secret1.txt",
    "https://example.com/secret2.txt"
  ],
  "zipFileName": "secrets.zip",
  "password": "mypassword123",
  "compressionLevel": 9
}
```

### 示例3：自定义文件名
```json
{
  "urls": [
    "https://example.com/abc123.jpg",
    "https://example.com/def456.pdf"
  ],
  "urlToFileNameMap": {
    "https://example.com/abc123.jpg": "photo.jpg",
    "https://example.com/def456.pdf": "document.pdf"
  },
  "zipFileName": "renamed_files.zip"
}
```

### 示例4：创建目录结构
```json
{
  "urls": [
    "https://example.com/file1.jpg",
    "https://example.com/file2.txt"
  ],
  "zipFileName": "organized.zip",
  "createDirectories": true,
  "directoryPrefix": "downloads/2024/"
}
```

### 示例5：自定义请求头
```json
{
  "urls": ["https://api.example.com/protected/file.jpg"],
  "zipFileName": "protected.zip",
  "headers": {
    "Authorization": "Bearer your-token",
    "X-Custom-Header": "custom-value"
  },
  "userAgent": "MyApp/1.0"
}
```

## 📊 响应格式

### ZipDownloadRespDTO 响应说明

```json
{
  "code": 0,
  "data": {
    "zipFilePath": "/tmp/zip_download_xxx/download.zip",
    "zipFileSize": 1048576,
    "zipFileName": "download.zip",
    "successCount": 2,
    "failedCount": 0,
    "totalCount": 2,
    "startTime": "2024-01-01T10:00:00",
    "endTime": "2024-01-01T10:00:30",
    "totalTimeMs": 30000,
    "successFiles": [
      {
        "url": "https://example.com/file1.jpg",
        "fileName": "file1.jpg",
        "fileSize": 524288,
        "status": "SUCCESS",
        "downloadTimeMs": 15000
      }
    ],
    "failedFiles": []
  },
  "msg": "操作成功"
}
```

## 🚀 Java客户端调用示例

### 1. 同步下载
```java
@Resource
private ZipDownloadService zipDownloadService;

public void downloadFiles() {
    ZipDownloadReqDTO reqDTO = new ZipDownloadReqDTO();
    reqDTO.setUrls(Arrays.asList(
        "https://example.com/file1.jpg",
        "https://example.com/file2.pdf"
    ));
    reqDTO.setZipFileName("download.zip");
    reqDTO.setPassword("123456");
    reqDTO.setCompressionLevel(6);
    
    ZipDownloadRespDTO result = zipDownloadService.downloadZip(reqDTO);
    
    System.out.println("下载完成: " + result.getZipFilePath());
    System.out.println("成功: " + result.getSuccessCount());
    System.out.println("失败: " + result.getFailedCount());
}
```

### 2. 异步下载
```java
public void downloadFilesAsync() {
    ZipDownloadReqDTO reqDTO = new ZipDownloadReqDTO();
    reqDTO.setUrls(Arrays.asList("https://example.com/largefile.zip"));
    reqDTO.setZipFileName("async_download.zip");
    
    // 提交异步任务
    String taskId = zipDownloadService.downloadZipAsync(reqDTO);
    
    // 轮询任务状态
    ZipDownloadRespDTO result;
    do {
        Thread.sleep(1000);
        result = zipDownloadService.getAsyncTaskStatus(taskId);
    } while (result.getEndTime() == null);
    
    System.out.println("异步下载完成: " + result.getZipFilePath());
}
```

## ⚠️ 注意事项

### 1. **文件大小限制**
- 单个文件默认最大100MB
- 总文件数量默认最大50个
- 可通过参数调整限制

### 2. **超时设置**
- 下载超时默认30秒
- 建议根据文件大小调整超时时间

### 3. **内存使用**
- 大文件下载会占用较多内存
- 建议使用异步方式处理大量文件

### 4. **临时文件清理**
- 系统会自动清理临时文件
- 异步任务完成后建议及时获取结果

### 5. **权限控制**
- 需要`infra:zip:download`权限
- 建议对下载功能进行适当的权限控制

## 🔍 错误处理

### 常见错误码
- `400`: 参数错误（文件数量超限、文件大小超限等）
- `500`: 服务器错误（下载失败、ZIP创建失败等）

### 错误处理策略
- `skipFailedFiles=true`: 跳过失败文件，继续处理其他文件
- `skipFailedFiles=false`: 任何文件失败都会导致整个任务失败

现在你拥有了完整的ZIP下载功能！可以灵活处理各种文件下载和打包需求。
