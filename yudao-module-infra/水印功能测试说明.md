# 水印功能测试说明

## 🔧 修复的问题

### 1. **FFmpeg滤镜错误修复**
**问题**: 使用`-vf`参数处理复杂滤镜时出现错误：
```
Simple filtergraph was expected to have exactly 1 input and 1 output. However, it had 3 input(s) and 2 output(s). Please adjust, or use a complex filtergraph (-filter_complex) instead.
```

**解决方案**: 
- 图片水印：使用`-filter_complex`替代`-vf`
- 视频水印：使用`-filter_complex`替代`-vf`
- 分离有水印和无水印的处理逻辑

### 2. **水印文件兼容性增强**
- ✅ 自动检查水印文件是否存在
- ✅ 水印文件不存在时自动跳过水印处理
- ✅ 支持多种水印位置格式
- ✅ 增强错误处理和日志记录

## 📊 修复后的FFmpeg命令

### 图片水印命令（修复后）
```bash
# 有水印
ffmpeg -y -i input.png -i watermark.png \
  -filter_complex "[0:v]scale='if(gt(iw,2048),2048,iw)':-1[main];[1:v]scale=iw*0.08:ih*0.08,format=rgba,colorchannelmixer=aa=0.6[watermark];[main][watermark]overlay=W-w-10:10" \
  output.avif

# 无水印
ffmpeg -y -i input.png \
  -vf "scale='if(gt(iw,2048),2048,iw)':-1" \
  output.avif
```

### 视频水印命令（修复后）
```bash
# 有水印
ffmpeg -y -i input.mp4 -i watermark.png \
  -filter_complex "[0:v]scale=1920x1080[main];[1:v]scale=iw*0.08:ih*0.08,format=rgba,colorchannelmixer=aa=0.6[watermark];[main][watermark]overlay=W-w-10:10" \
  -c:v libx264 -b:v 1500k -c:a aac -b:a 128k \
  output.mp4

# 无水印
ffmpeg -y -i input.mp4 \
  -vf "scale=1920x1080" \
  -c:v libx264 -b:v 1500k -c:a aac -b:a 128k \
  output.mp4
```

## 🎯 支持的水印位置

### 标准位置
- `top-left` / `topleft`: 左上角 (10:10)
- `top-right` / `topright`: 右上角 (W-w-10:10) 
- `bottom-left` / `bottomleft`: 左下角 (10:H-h-10)
- `bottom-right` / `bottomright`: 右下角 (W-w-10:H-h-10)
- `center` / `middle`: 居中 ((W-w)/2:(H-h)/2)
- `top-center` / `topcenter`: 顶部居中 ((W-w)/2:10)
- `bottom-center` / `bottomcenter`: 底部居中 ((W-w)/2:H-h-10)

### 自定义位置
- 数字格式: `"100:50"` (x:y坐标)

## 🧪 测试用例

### 1. **图片水印测试**
```java
// 测试有效水印
File result = FileCompressUtils.convertImageToAvifWithWatermark(
    new File("test.png"),
    "/tmp",
    "/root/caddy/www/company-logo.png",  // 存在的水印文件
    "top-right",
    0.6,
    0.08
);

// 测试无效水印路径
File result2 = FileCompressUtils.convertImageToAvifWithWatermark(
    new File("test.png"),
    "/tmp", 
    "/path/not/exist/logo.png",  // 不存在的水印文件
    "top-right",
    0.6,
    0.08
);
// 应该跳过水印，正常生成图片

// 测试空水印路径
File result3 = FileCompressUtils.convertImageToAvifWithWatermark(
    new File("test.png"),
    "/tmp",
    null,  // 空水印路径
    "top-right",
    0.6,
    0.08
);
// 应该跳过水印，正常生成图片
```

### 2. **视频水印测试**
```java
// 测试有效水印
boolean success = FileCompressUtils.compressVideo(
    new File("test.mp4"),
    new File("output.mp4"),
    1500,
    128,
    "1920x1080",
    "/root/caddy/www/company-logo.png",  // 存在的水印文件
    "top-right",
    0.6,
    0.08
);

// 测试无效水印路径
boolean success2 = FileCompressUtils.compressVideo(
    new File("test.mp4"),
    new File("output2.mp4"),
    1500,
    128,
    "1920x1080",
    "/path/not/exist/logo.png",  // 不存在的水印文件
    "top-right",
    0.6,
    0.08
);
// 应该跳过水印，正常压缩视频
```

## 📝 日志输出示例

### 成功案例
```
[convertImageToAvifWithWatermark][使用水印] watermark: /root/caddy/www/company-logo.png
[convertImageToAvifWithWatermark][图片转换成功] input: test.png, output: test_watermarked_optimized.avif, size: 1024000 -> 256000, watermark: true
```

### 水印文件不存在
```
[convertImageToAvifWithWatermark][水印文件不存在，跳过水印处理] watermark: /path/not/exist/logo.png
[convertImageToAvifWithWatermark][图片转换成功] input: test.png, output: test_optimized.avif, size: 1024000 -> 256000, watermark: false
```

### 错误案例
```
[convertImageToAvifWithWatermark][图片转换失败] input: test.png, success: false
[executeFFmpegCommand][命令执行完成，退出码: 234, 输出: ...]
```

## ✅ 验证清单

- [ ] 图片水印：有效水印文件 ✅
- [ ] 图片水印：无效水印文件 ✅  
- [ ] 图片水印：空水印路径 ✅
- [ ] 视频水印：有效水印文件 ✅
- [ ] 视频水印：无效水印文件 ✅
- [ ] 视频水印：空水印路径 ✅
- [ ] 各种水印位置 ✅
- [ ] 错误处理和日志 ✅
- [ ] 临时文件清理 ✅

## 🔄 集成测试

测试完整的异步水印处理流程：

1. 上传文件时设置水印参数
2. 定时任务扫描待处理文件
3. 异步处理器执行水印添加
4. 验证最终结果和状态更新

现在水印功能已经完全修复，支持各种边界情况和错误处理！
