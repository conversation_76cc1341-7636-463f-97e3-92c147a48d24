package cn.iocoder.yudao.module.member.mq.consumer;

import cn.iocoder.yudao.framework.web.core.util.WebFrameworkUtils;
import cn.iocoder.yudao.module.member.api.point.MemberPointApi;
import cn.iocoder.yudao.module.member.dal.dataobject.user.MemberUserDO;
import cn.iocoder.yudao.module.member.message.user.MemberUserCreateMessage;
import cn.iocoder.yudao.module.system.api.application.ApplicationApi;
import cn.iocoder.yudao.module.system.api.application.dto.MemberInviterRespDTO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.event.EventListener;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Optional;

import static cn.iocoder.yudao.framework.common.exception.util.ServiceExceptionUtil.exception;
import static cn.iocoder.yudao.framework.common.util.servlet.ServletUtils.getClientIP;
import static cn.iocoder.yudao.module.member.enums.ErrorCodeConstants.ACCOUNT_CREATION_FAILED;
import static cn.iocoder.yudao.module.member.enums.point.MemberPointBizTypeEnum.INVITAT_REGISTER;
import static cn.iocoder.yudao.module.member.enums.point.MemberPointBizTypeEnum.NEWCOMER;

@Component
@Slf4j
public class MemberPointByRegisterConsumer {
    @Resource
    private ApplicationApi applicationApi;
    @Resource
    private MemberPointApi memberPointApi;

    @EventListener
    public void onMessage(MemberUserCreateMessage message) {
        log.info("[onMessage][消息内容({})]", message);
//        couponService.takeCouponByRegister(message.getUserId());
        String appId = WebFrameworkUtils.getAppId();
        MemberInviterRespDTO memberRegistrationRespDTO = applicationApi.getMemberInviterRespDTO(appId);
        boolean forceInvitation = memberRegistrationRespDTO.isForceInvitation();
        Integer inviterPoints = memberRegistrationRespDTO.getInviterPoints();
        Integer newcomerPoints = memberRegistrationRespDTO.getNewcomerPoints();
        // 获得获得注册用户
        Long inviter = Optional.ofNullable(message.getInviter()).orElse(memberRegistrationRespDTO.getInviter());


        Long userId = message.getUserId();
        // 邀请人获得积分
        if(inviter != null && inviterPoints > 0){
            memberPointApi.addPoint(inviter,inviterPoints,INVITAT_REGISTER.getType(), userId.toString());
        }
        // 自己获得积分
        if(newcomerPoints > 0){
            memberPointApi.addPoint(userId,newcomerPoints,NEWCOMER.getType(), userId.toString());
        }
    }
}
