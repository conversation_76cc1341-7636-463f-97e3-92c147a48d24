package cn.iocoder.yudao.module.member.controller.app.auth.vo;

import cn.iocoder.yudao.framework.common.enums.TerminalEnum;
import cn.iocoder.yudao.framework.common.validation.InEnum;
import cn.iocoder.yudao.module.system.enums.social.SocialTypeEnum;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;

@Schema(description = "用户 APP - 输入注册 Request VO")
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class AppAuthUsernameRegistrationReqVO {

    @Schema(description = "输入登陆账号", requiredMode = Schema.RequiredMode.REQUIRED, example = "hello")
    @NotEmpty(message = "登陆名 不能为空")
    private String loginName;

    @Schema(description = "登录密码", requiredMode = Schema.RequiredMode.REQUIRED, example = "word")
    @NotEmpty(message = "登录密码 不能为空")
    private String password;

    @Schema(description = "设备Id", requiredMode = Schema.RequiredMode.REQUIRED, example = "9b2ffbc1-7425-4155-9894-9d5c08541d62")
    @NotEmpty(message = "设备Id 不能为空")
    private String deviceId;

    @Schema(description = "邀请人", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
    private Long inviter;

    @Schema(description = "终端，参见 TerminalEnum 枚举值", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
    @InEnum(TerminalEnum.class)
    private Integer terminal;
}
